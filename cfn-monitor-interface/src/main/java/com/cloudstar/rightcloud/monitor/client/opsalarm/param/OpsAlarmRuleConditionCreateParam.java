package com.cloudstar.rightcloud.monitor.client.opsalarm.param;

import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * 告警规则条件创建参数
 *
 * @author: wanglang
 * @date: 2023/6/10 4:39 PM
 */
@Data
public class OpsAlarmRuleConditionCreateParam implements Serializable {

    /**
     * 告警规则指标id
     */
    @NotEmpty
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.MONITOR_METRIC_ID)
    private Long opsMonitorMetricId;

    /**
     * 函数运算符 sum 求和 max 最大值 min 最小值 avg 平均值
     */
    @NotEmpty
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.FUNCTION_OPERATOR)
    private String functionOperator;

    /**
     * 运算符 gt > ,ge >= ,lt< ,le <=, equals =, not_equals!=
     */
    @NotEmpty
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.OPERATOR)
    private String operator;
    /**
     * 阔值
     */
    @NotEmpty
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.BROAD_VALUE)
    private String broadValue;
    /**
     * 数据范围时间（分钟）
     */
    @NotEmpty
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.DATA_RANGE_TIME)
    private Integer dataRangeTime;
}
