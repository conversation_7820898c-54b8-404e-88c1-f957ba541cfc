package com.cloudstar.rightcloud.monitor.client.opsexporter.param;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * exporter资源配置
 *
 * @author: wanglang
 * @date: 2023/7/31 17:37
 */
@Data
public class MorExporterResourceConfigParam implements Serializable {

    /**
     * 资源实例id
     */
    private String instanceId;

    /**
     * 资源类型编码
     */
    private String code;

    /**
     * 资源指标维度
     * 可能一个资源需要采集多个维度的指标
     * 这里用map来标识，key是维度code，value是该资源在该维度下的标识
     */
    private Map<String, String> dimension;


    /**
     * 资源所属组织id
     */
    private String orgId;

    /**
     * 资源所属项目id
     */
    private String projectId;


    /**
     * 资源其他属性字段
     */
    private Map<String, Object> properties;


}
