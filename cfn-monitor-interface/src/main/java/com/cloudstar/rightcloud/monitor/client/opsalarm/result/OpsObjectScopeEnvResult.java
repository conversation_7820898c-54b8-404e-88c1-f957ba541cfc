package com.cloudstar.rightcloud.monitor.client.opsalarm.result;

import lombok.Data;

import java.io.Serializable;

/**
 * 告警规则对象范围
 *
 * @author: wanglang
 * @date: 2023/6/12 15:44
 */
@Data
public class OpsObjectScopeEnvResult implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 告警范围id
     */
    private String alarmTargetScopeId;


    /**
     * 云环境名称
     */
    private String cloudEnvName;


    /**
     * 云平台名称
     */
    private String defineEnvNameEn;

    /**
     * 云平台英文名称
     */
    private String defineEnvName;

    /**
     * 云平台编码
     */
    private String envCode;

    /**
     * 云平台版本
     */
    private String envVersion;

    /**
     * 云平台图标
     */
    private String envIcon;


}
