package com.cloudstar.rightcloud.monitor.client.opscollectrule.result;

import lombok.Data;

import java.io.Serializable;

/**
 * 监控指标列表数据
 *
 * @author: wanglang
 * @date: 2023/6/17 20:09
 */
@Data
public class OpsCollectMetricGetListResult implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 指标名称
     */
    private String name;
    /**
     * 采集指标统一编码名称
     */
    private String monitorCommonMetricName;

    /**
     * 采集指标统一编码id
     */
    private Long monitorCommonMetricId;

    /**
     * 采集指标统一编码
     */
    private String unifiedCoding;
    /**
     * 采集指标原始编码
     */
    private String originalCoding;

    /**
     * 云平台图标
     */
    private String envIcon;

    /**
     * 云平台
     */
    private String envCode;
    /**
     * 云平台版本号
     */
    private String envVersion;

    /**
     * 指标单位：bps/kbps/%等
     */
    private String unit;
}
