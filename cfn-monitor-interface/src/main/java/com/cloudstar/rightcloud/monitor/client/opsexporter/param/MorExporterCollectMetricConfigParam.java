package com.cloudstar.rightcloud.monitor.client.opsexporter.param;

import lombok.Data;

/**
 * exporter采集指标配置
 *
 * @author: wanglang
 * @date: 2023/7/31 17:56
 */
@Data
public class MorExporterCollectMetricConfigParam {

    /**
     * 指标id
     */
    private Long id;

    /**
     * 通用指标id
     */
    private Long commonMetricId;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 资源类型
     */
    private String resCode;

    /**
     * 通用指标名称
     */
    private String commonMetricName;

    /**
     * 指标名称
     */
    private String metricName;

    /**
     * 单位
     */
    private String unit;

    /**
     * 命名空间
     */
    private String namespace;

    /**
     * 指标维度
     */
    private String dimension;

    /**
     * 单位转换因子
     */
    private Double unitConvFactor;


}
