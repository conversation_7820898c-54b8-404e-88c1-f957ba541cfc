package com.cloudstar.rightcloud.monitor.client.opscollectrule.result;

import com.cloudstar.rightcloud.common.utils.LocaleLanguageContextUtil;

import org.apache.commons.lang3.StringUtils;

import lombok.Data;

import java.io.Serializable;

/**
 * 监控指标详情
 *
 * @author: wanglang
 * @date: 2023/6/16 15:57
 */
@Data
public class OpsCollectMetricDetailsResult implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 采集指标名称
     */
    private String name;
    /**
     * 采集指标名称
     */
    private String nameEn;


    /**
     * 状态
     */
    private String status;

    /**
     * 资源类型编码
     */
    private String resTypeCode;

    /**
     * 采集规则id
     */
    private Long opsCollectRuleId;

    /**
     * 维度id
     */
    private Long opsCollectRuleDimensionsId;
    /**
     * 采集指标统一编码名称
     */
    private String monitorCommonMetricName;
    /**
     * 采集指标统一编码名称
     */
    private String monitorCommonMetricNameEn;

    /**
     * 采集指标统一编码id
     */
    private Long monitorCommonMetricId;

    /**
     * 采集指标统一编码
     */
    private String unifiedCoding;
    /**
     * 采集指标原始编码
     */
    private String originalCoding;
    /**
     * 指标单位：bps/kbps/%等
     */
    private String unit;
    /**
     * 排序编码
     */
    private Integer sortRank;
    /**
     * 描述
     */
    private String description;
    /**
     * 描述
     */
    private String descriptionEn;
    /**
     * 单位转换因子
     */
    private Double unitConvFactor;
    /**
     * 指标命名空间
     */
    private String namespace;

    /**
     * 是否默认展示
     */
    private Boolean defaultDisplay;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 云平台图标
     */
    private String envIcon;

    /**
     * 云平台
     */
    private String envCode;
    /**
     * 云平台版本号
     */
    private String envVersion;


    public String getName() {
        return LocaleLanguageContextUtil.isEn() && StringUtils.isNotBlank(nameEn) ? nameEn : name;
    }

    public String getMonitorCommonMetricName() {
        return LocaleLanguageContextUtil.isEn() && StringUtils.isNotBlank(monitorCommonMetricNameEn)
                ? monitorCommonMetricNameEn : monitorCommonMetricName;
    }

    public String getDescription() {
        return LocaleLanguageContextUtil.isEn() && StringUtils.isNotBlank(descriptionEn) ? descriptionEn : description;
    }

}
