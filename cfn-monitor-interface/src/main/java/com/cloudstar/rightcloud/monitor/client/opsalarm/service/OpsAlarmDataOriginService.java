package com.cloudstar.rightcloud.monitor.client.opsalarm.service;

import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.MorAlarmDataOriginGetListParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsAlarmDataOriginCreateParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.MorAlarmDataOriginGetListResult;

import java.util.List;
import java.util.Locale;

/**
 * 原始告警数据接口
 *
 * @author: wanglang
 * @date: 2023/8/10 14:07
 */
public interface OpsAlarmDataOriginService {

    /**
     * 批量创建原始告警数据
     *
     * @param params 原始告警数据
     * @param source 原始告警来源
     * @return Long 告警数据id
     */
    RightCloudResult<Boolean> createByUpdateAlarmDataOrigin(
            List<OpsAlarmDataOriginCreateParam> params,
            String source
    );


    /**
     * 获取原生告警列表
     *
     * @param param 条件参数
     * @return MorAlarmDataOriginGetListResult 列表返回数据
     */
    RightCloudResult<PageResult<MorAlarmDataOriginGetListResult>> getPageList(MorAlarmDataOriginGetListParam param);

    /**
     * 查看告警内容
     *
     * @param id 告警数据id
     * @return String 告警内容
     */
    RightCloudResult<String> getContent(Long id);

    /**
     * 导出告警数据
     *
     * @return Long 任务id
     */
    RightCloudResult<Long> export();


    /**
     * 异步导出
     *
     * @param param  条件参数
     * @param locale 语言
     */
    void exportAsync(MorAlarmDataOriginGetListParam param, Locale locale, Long taskId);

}
