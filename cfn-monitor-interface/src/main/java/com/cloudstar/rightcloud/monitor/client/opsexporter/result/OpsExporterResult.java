package com.cloudstar.rightcloud.monitor.client.opsexporter.result;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 监控采集exporter
 *
 * <AUTHOR>
 * @date: 3/2/2022 6:04 PM
 */
@Data
public class OpsExporterResult implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    private Long id;
    /**
     * exporter名称
     */
    private String exporterName;
    /**
     * 采集频率（秒）
     */
    private Integer collectInterval;
    /**
     * 超时时间（秒）
     */
    private Integer timeout;
    /**
     * 描述
     */
    private String description;
    /**
     * 采集路径，默认：/metrics
     */
    private String collectorPath;
    /**
     * 记录创建时间
     */
    private Date createdDt;
    /**
     * 记录最后修改时间
     */
    private Date updatedDt;
    /**
     * 创建用户ID
     */
    private String createdBy;
    /**
     * 最后修改用户ID
     */
    private String updatedBy;

    /**
     * 组件名称
     */
    private String name;

    /**
     * exporter参数列表
     */
    private List<OpsExporterParamResult> opsExporterParamResultList;

}
