package com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param;

import com.cloudstar.rightcloud.monitor.common.em.MorMetricType;
import lombok.Data;

import java.util.Date;

/**
 * 小时汇总时间实体
 *
 * @author: wanglang
 * @date: 2025/1/16 16:52
 */
@Data
public class MorAnalysisHourPolicyTimeQuery {
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 相差时间
     */
    private Long time;

    /**
     * 指标名称
     */
    private String metric;

    /**
     * 类型 max min avg quantile
     */
    private MorMetricType type;

    /**
     * 指标名称
     */
    private String metricName;

    /**
     * 数量
     */
    private Integer count;
}
