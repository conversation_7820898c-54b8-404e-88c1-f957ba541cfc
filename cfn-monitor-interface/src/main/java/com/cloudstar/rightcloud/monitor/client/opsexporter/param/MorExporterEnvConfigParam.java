package com.cloudstar.rightcloud.monitor.client.opsexporter.param;

import lombok.Data;

import java.util.List;

/**
 * exporter云环境配置
 *
 * @author: wanglang
 * @date: 2023/7/31 17:28
 */
@Data
public class MorExporterEnvConfigParam {

    /**
     * 云环境id
     */
    private String envId;


    /**
     * 云环境名称`
     */
    private String envName;

    /**
     * 云平台
     */
    private String envCode;

    /**
     * 云平台版本号
     */
    private String envVersion;

    /**
     * 接口请求次数
     */
    private Integer limitCount;

    /**
     * 接口请求次数规定时间
     */
    private Integer limitCountExpireTime;

    /**
     * 代理配置
     */
    private MorExporterProxyConfigParam proxyConfig;

    /**
     * 资源类型编码:默认返回开启监控的资源类型编码
     */
    private List<String> resCodes;


    private String accessInfo;

    /**
     * 账号id
     */
    private String cloudEnvAccountId;



    /**
     * 组织ID
     */
    private Long orgId;



}
