package com.cloudstar.rightcloud.monitor.client.opsexporter.service;

import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.opsaccess.service.OpsEnvMonitorService;
import com.cloudstar.rightcloud.monitor.client.opsexporter.param.OpsCollectExporterCheckEnvParam;
import com.cloudstar.rightcloud.monitor.client.opsexporter.param.OpsExporterCreateParam;
import com.cloudstar.rightcloud.monitor.client.opsexporter.param.OpsExporterGetPageParam;
import com.cloudstar.rightcloud.monitor.client.opsexporter.param.OpsExporterUpdateParam;
import com.cloudstar.rightcloud.monitor.client.opsexporter.result.OpsExporterByParamGetListResult;
import com.cloudstar.rightcloud.monitor.client.opsexporter.result.OpsExporterGetDetailsResult;
import com.cloudstar.rightcloud.monitor.client.opsexporter.result.OpsExporterGetListResult;
import com.cloudstar.rightcloud.monitor.client.opsexporter.result.OpsExporterGetPageResult;

import java.util.List;

/**
 * exporter服务
 *
 * @author: wanglang
 * @date: 2023/2/15 11:06 AM
 */
public interface OpsExporterService extends OpsEnvMonitorService {


    /**
     * 创建exporter
     *
     * @param opsExporterCreateParam 创建exporter参数
     * @return Long exporter参数id
     */
    RightCloudResult<Long> createExporter(OpsExporterCreateParam opsExporterCreateParam);


    /**
     * 更新exporter
     *
     * @param opsExporterUpdateParam exporter更新参数
     * @return true 更新成功 false 更新失败
     */
    RightCloudResult<Boolean> updateExporter(OpsExporterUpdateParam opsExporterUpdateParam);


    /**
     * 获取exporter详情数据
     *
     * @param id exporterId
     * @return OpsExporterGetDetailsResult exporter详情数据
     */
    RightCloudResult<OpsExporterGetDetailsResult> getExporterDetails(Long id);


    /**
     * 获取exporter列表
     *
     * @param opsExporterGetPageParam exporter分页查询参数
     * @return OpsExporterGetPageResult exporter分页列表
     */
    RightCloudResult<PageResult<OpsExporterGetPageResult>> getExporterPage(OpsExporterGetPageParam opsExporterGetPageParam);


    /**
     * 删除exporter
     *
     * @param ids exporterId
     * @return true 删除成功 false 删除失败
     */
    RightCloudResult<Boolean> deleteExporter(List<Long> ids);

    /**
     * 获取采集组件及参数列表
     *
     * @return OpsExporterParamGetListResult 采集组件参数列表
     */
    RightCloudResult<List<OpsExporterByParamGetListResult>> getExporterByParams();


    /**
     * 获取采集组件列表
     *
     * @return OpsExporterGetListResult 采集组件列表
     */
    RightCloudResult<List<OpsExporterGetListResult>> getExporterList();

    /**
     * 校验云环境采集组件配置
     *
     * @param param 校验参数
     * @return true 校验成功 false 校验失败
     */
    RightCloudResult<Boolean> checkEnvExporterConfiguration(OpsCollectExporterCheckEnvParam param);
}
