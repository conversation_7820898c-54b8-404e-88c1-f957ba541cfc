package com.cloudstar.rightcloud.monitor.client.opscollectrule.service;

import java.util.List;

import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.opscollectrule.param.OpsCollectRuleCreateParam;
import com.cloudstar.rightcloud.monitor.client.opscollectrule.param.OpsCollectRuleGetPageParam;
import com.cloudstar.rightcloud.monitor.client.opscollectrule.param.OpsCollectRuleUpdateParam;
import com.cloudstar.rightcloud.monitor.client.opscollectrule.result.OpsCollectRuleGetDetailsResult;
import com.cloudstar.rightcloud.monitor.client.opscollectrule.result.OpsCollectRuleGetListResult;

/**
 * 采集规则接口
 *
 * @author: wanglang
 * @date: 2023/7/20 09:58
 */
public interface OpsCollectRuleService {

    /**
     * 创建采集规则
     *
     * @param param 采集规则参数
     * @return Long 采集规则id
     */
    RightCloudResult<Long> createCollectRule(OpsCollectRuleCreateParam param);

    /**
     * 修改采集规则
     *
     * @param param 采集规则参数
     * @return true 修改成功 false 修改失败
     */
    RightCloudResult<Boolean> updateCollectRule(OpsCollectRuleUpdateParam param);

    /**
     * 修改采集规则状态
     *
     * @param id 采集规则id
     * @return true 修改成功 false 修改失败
     */
    RightCloudResult<Boolean> updateCollectRuleStatus(Long id);

    /**
     * 批量删除采集规则
     *
     * @param ids 采集规则id
     * @return true 删除成功 false 删除失败
     */
    RightCloudResult<Boolean> deleteCollectRule(List<Long> ids);

    /**
     * 获取采集规则分页列表
     *
     * @param param 分页列表条件参数
     * @return OpsCollectRuleGetListResult 采集规则列表数据
     */
    RightCloudResult<PageResult<OpsCollectRuleGetListResult>> getCollectRulePage(OpsCollectRuleGetPageParam param);

    /**
     * 获取采集规则详情
     *
     * @param collectRuleId 采集规则id
     * @return OpsCollectRuleGetDetailsResult 采集规则详情数据
     */
    RightCloudResult<OpsCollectRuleGetDetailsResult> getCollectRuleDetails(Long collectRuleId);



}
