package com.cloudstar.rightcloud.monitor.client.feign.server.request;

import com.cloudstar.rightcloud.monitor.common.feign.result.PageForm;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 集群节点请求类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class QueryClusterNodeReq extends PageForm {

    /**
     * 资源池名称
     */
    private String poolName;

    /**
     * 节点名称
     */
    private String nodeName;

    // @NotNull
    private Long clusterId;

    /**
     * 节点名称准确查询
     */
    private String name;

    /**
     * 节点状态
     */
    private String status;

    /**
     * 节点名称数组查询
     */
    private List<String> nodeNames;
}
