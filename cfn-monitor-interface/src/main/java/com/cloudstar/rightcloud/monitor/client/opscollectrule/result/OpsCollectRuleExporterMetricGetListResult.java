package com.cloudstar.rightcloud.monitor.client.opscollectrule.result;

import lombok.Data;

/**
 * 采集规则exporter指标获取列表返回参数
 *
 * @author: wanglang
 * @date: 2023/7/21 15:14
 */
@Data
public class OpsCollectRuleExporterMetricGetListResult {
    /**
     * id
     */
    private Long id;
    /**
     * 采集指标名称
     */
    private String name;

    /**
     * 状态
     */
    private String status;

    /**
     * 资源类型编码
     */
    private String resTypeCode;

    /**
     * 采集指标统一编码名称
     */
    private String monitorCommonMetricName;

    /**
     * 采集指标统一编码id
     */
    private Long monitorCommonMetricId;

    /**
     * 采集指标统一编码
     */
    private String unifiedCoding;
    /**
     * 采集指标原始编码
     */
    private String originalCoding;
    /**
     * 指标单位：bps/kbps/%等
     */
    private String unit;
    /**
     * 排序编码
     */
    private Integer sortRank;
    /**
     * 描述
     */
    private String description;
    /**
     * 单位转换因子
     */
    private Double unitConvFactor;
    /**
     * 指标命名空间
     */
    private String namespace;

    /**
     * uuid
     */
    private String uuid;


    /**
     * 云平台图标
     */
    private String envIcon;

    /**
     * 云平台
     */
    private String envCode;
    /**
     * 云平台版本号
     */
    private String envVersion;


}
