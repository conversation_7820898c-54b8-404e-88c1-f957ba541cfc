package com.cloudstar.rightcloud.monitor.client.opsviews.result;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 视图指标数据
 *
 * @author: wanglang
 * @date: 2023/8/22 11:55
 */
@Data
public class OpsViewsMetricDataGetListResult {
    /**
     * id
     */
    private Long id;

    /**
     * 指标名称
     */
    private String name;

    /**
     * 指标单位：bps/kbps/%等
     */
    private String unit;

    /**
     * 指标数据
     */
    private List<OpsMetricDataResult> values;


    @Data
    public static class OpsMetricDataResult {
        /**
         * 时间
         */
        private Date time;

        /**
         * 值
         */
        private String value;
    }
}
