package com.cloudstar.rightcloud.monitor.client.opsalarm.result;

import com.cloudstar.rightcloud.common.annotation.BeanHelperField;
import lombok.Data;

import java.util.Date;

/**
 * 告警分页列表数据
 *
 * @author: wanglang
 * @date: 2023/6/2 3:50 PM
 */
@Data
public class OpsAlarmDataPageResult {
    /**
     * 告警数据id
     */
    private Long id;
    /**
     * 告警名称
     */
    private String name;

    /**
     * 告警对象
     */
    private String target;

    /**
     * 告警对象类型
     */
    @BeanHelperField(columnName = "objectType")
    private String targetType;


    /**
     * 告警级别
     * 从Code中获取：故障、严重、一般
     */
    @BeanHelperField(columnName = "alarmLevelStatus")
    private String level;


    /**
     * 告警对象实例id
     */
    private String objectInstanceId;

    /**
     * 告警状态
     */
    private String status;
    /**
     * 告警次数
     */
    private Integer count;

    /**
     * 告警通知策略
     */
    private String notifyPolicyName;

    /**
     * 告警规则id
     */
    private Long opsAlarmRuleId;

    /**
     * 开始时间
     */
    private Date startTime;


    /**
     * 最后发生时间
     */
    @BeanHelperField(columnName = "lastTime")
    private Date occurTime;

    /**
     * 持续时长
     */
    private String duration;

    /**
     * 告警处理状态
     */
    private String processingStatus;

    /**
     * 处理人
     */
    private String processingUser;
    /**
     * 所属组织名称
     */
    private String orgName;

    /**
     * 组织id
     */
    private Long orgId;

    /**
     * 确认时间
     */
    private Date confirmTime;

    /**
     * 告警来源
     */
    private String source;


    /**
     * 云环境id
     */
    private String envId;

    /**
     * 云环境名称
     */
    private String envName;
}
