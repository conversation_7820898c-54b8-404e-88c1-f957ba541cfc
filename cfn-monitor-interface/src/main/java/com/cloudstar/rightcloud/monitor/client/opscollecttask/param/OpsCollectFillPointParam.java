package com.cloudstar.rightcloud.monitor.client.opscollecttask.param;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 云环境监控指标补点接口参数
 * <AUTHOR>
 * @date 2025/2/5 14:07
 */
@Data
public class OpsCollectFillPointParam implements Serializable {

    /**
     * 云环境ID
     */
    private Long cloudEnvId;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
