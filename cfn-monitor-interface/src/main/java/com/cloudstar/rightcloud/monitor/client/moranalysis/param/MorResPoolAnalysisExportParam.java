package com.cloudstar.rightcloud.monitor.client.moranalysis.param;

import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.common.pojo.page.PageForm;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 资源闲置统计查询参数
 *
 * @author: hjy
 * @date: 2023/11/29 20:15
 */
@Data
public class MorResPoolAnalysisExportParam extends PageForm implements Serializable {
    /**
     * 开始日期
     */
    private String startDay;

    /**
     * 结束日期
     */
    private String endDay;

    /**
     * 云环境类型
     */
    private String envType;
    /**
     * 云平台
     */
    private String envTypeCode;
    /**
     * 云平台版本号
     */
    private String envVersion;
    /**
     * 云环境ID
     */
    private Long cloudEnvId;
    /**
     * 资源类型
     */
    @NotBlank
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.RES_TYPE_CODE)
    private String resTypeCode;
    /**
     * 组织ID
     */
    private Long orgId;
    /**
     * 指标比较条件
     */
    private List<MorOrgAnalysisMetricConditionParam> conditions;
    /**
     * 指标比较条件(字符串)
     */
    private String conditionsStr;

    /**
     * 统计范围
     */
    private String statisticScope;

    /**
     * 日期范围
     */
    private String range;


    /**资源池id**/
    private Long resPoolId;


    /**
     * 闲置率
     */
    private String idleField;

    /**
     * 条件
     */
    private String idleConditon;

    /**
     * 闲置值
     */
    private String idleValue;

    /**
     * 考核类型(考核项)
     */
    private String scoreType;

    /**
     * 资源名称模糊搜索
     */
    private String instanceNameLike;



    /**
     * 云账号ID
     */
    private Long cloudEnvAccountId;
    /**
     * 模糊查询  /cop/physicalResource/Analysis 页面前端使用 instanceName
     */
    private String instanceName;
    /**
     * 云平台组件
     */
    private String runCondition;
}
