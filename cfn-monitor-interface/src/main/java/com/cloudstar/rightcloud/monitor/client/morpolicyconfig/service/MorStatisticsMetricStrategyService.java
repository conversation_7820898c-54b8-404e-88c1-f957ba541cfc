package com.cloudstar.rightcloud.monitor.client.morpolicyconfig.service;

import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorStatisticsStrategyGetCloudEnvPageParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorStatisticsStrategyCloudEnvGetListResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsEnvGetListResult;

import java.util.List;

/**
 * 性能汇总统计
 *
 * @author: wanglang
 * @date: 2023/11/10 16:42
 */
public interface MorStatisticsMetricStrategyService {

    /**
     * 创建或者更新分析汇总定时任务
     *
     * @return 操作结果
     */
    RightCloudResult<Boolean> createByUpdateTak();

    /**
     * 获取云环境
     *
     * @param param 条件参数
     * @return OpsAlarmRuleCloudEnvGetListResult 云环境数据
     */
    RightCloudResult<PageResult<MorStatisticsStrategyCloudEnvGetListResult>> getCloudEnvInstance(MorStatisticsStrategyGetCloudEnvPageParam param);

    /**
     * 获取云平台列表
     *
     * @return OpsEnvGetListResult 云平台列表
     */
    RightCloudResult<List<OpsEnvGetListResult>> getEnvList();

}
