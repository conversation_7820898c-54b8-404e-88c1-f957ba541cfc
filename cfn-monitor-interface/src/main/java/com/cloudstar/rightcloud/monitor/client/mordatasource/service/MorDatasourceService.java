package com.cloudstar.rightcloud.monitor.client.mordatasource.service;

import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.mordatasource.param.MorDatasourceCreateParam;
import com.cloudstar.rightcloud.monitor.client.mordatasource.result.MorDatasourceGetDefaultResult;
import com.cloudstar.rightcloud.monitor.client.mordatasource.result.MorDatasourceGetInfoResult;
import com.cloudstar.rightcloud.monitor.client.mordatasource.result.MorDatasourceGetListResult;

import java.util.List;

/**
 * 数据源接口
 *
 * @author: wanglang
 * @date: 2024/4/15 14:56
 */
public interface MorDatasourceService {


    /**
     * 根据类型获取数据源
     *
     * @param type 类型
     * @return MorDatasourceGetListResult 数据源列表
     */
    RightCloudResult<List<MorDatasourceGetListResult>> getDatasource(String type);

    /**
     * 根据名称和类型获取数据源
     *
     * @param name 名称
     * @param type 类型
     * @return MorDatasourceGetInfoResult 数据源信息
     */
    RightCloudResult<MorDatasourceGetInfoResult> getDatasource(String name, String type);


    /**
     * 根据类型获取默认数据源
     *
     * @param type 类型
     * @return MorDatasourceGetInfoResult 数据源信息
     */
    RightCloudResult<MorDatasourceGetDefaultResult> defaultDatasource(String type);

    RightCloudResult<Integer> insertOrUpdate(MorDatasourceCreateParam param, String cloudEnvId, String resId);

    int deleteByEnv(String cloudEnvId);

    int deleteByRes(String resId);
}
