package com.cloudstar.rightcloud.monitor.client.moroptimization.result;

import lombok.Data;

import java.util.Date;

/**
 * 优化策略配置
 *
 * <AUTHOR> Lesao
 * @date : 2023/11/29
 */
@Data
public class OptimizationStrategyConfigResult {

    /**
     * id
     */
    private Long id;
    /**
     * 组织id
     */
    private Long orgId;
    /**
     * 历史天数
     */
    private Integer hisDays;
    /**
     * 日期范围(自然日，工作日)
     */
    private String dateRange;
    /**
     * 开始时间段
     */
    private String timeRangeStart;
    /**
     * 结束时间段
     */
    private String timeRangeEnd;
    /**
     * 费用显示, 1:启用,0:禁用
     */
    private Boolean showPrice;
    /**
     * 费用询价体系, spec:按规格,product:按产品
     */
    private String inquiryPriceType;
    /**
     * 付费方式，预付费、后付费
     */
    private String chargeType;
    /**
     * 计费方式, 按月、按天、按小时
     */
    private String payType;
    /**
     * 询价时长
     */
    private Integer inquiryDuration;
    /**
     * 询价单位
     */
    private String inquiryUnit;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 最后更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
    /**
     * 版本
     */
    private Long version;


}
