package com.cloudstar.rightcloud.monitor.client.opsalarm.result;

import com.cloudstar.rightcloud.common.annotation.BeanHelperField;
import lombok.Data;

import java.io.Serializable;

/**
 * 告警指标
 * @author: wanglang
 * @date: 2023/4/19 6:06 PM
 */
@Data
public class OpsPrometheusAlarmRuleMonitorMetricResult implements Serializable {

    /**
     * id
     */
    private Long id;
    /**
     * 采集指标名称
     */
    private String name;
    /**
     * 采集指标统一编码
     */
    @BeanHelperField(columnName = "code")
    private String unifiedCoding;
    /**
     * 指标单位：bps/kbps/%等
     */
    private String unit;
    /**
     * 排序编码
     */
    private Integer sortRank;
    /**
     * 描述
     */
    private String description;
    /**
     * 单位转换因子
     */
    private Double unitConvFactor;
    /**
     * 指标命名空间
     */
    private String namespace;

}
