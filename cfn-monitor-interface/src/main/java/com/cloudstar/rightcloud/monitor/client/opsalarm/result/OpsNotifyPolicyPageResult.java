package com.cloudstar.rightcloud.monitor.client.opsalarm.result;

import lombok.Data;


/**
 * 告警通知策略
 *
 * @author: 卢泳舟
 * @date: 2023/6/5 15:02
 */
@Data
public class OpsNotifyPolicyPageResult {

    /**
     * id
     */
    private Long id;

    /**
     * 通知策略名称
     */
    private String name;

    /**
     * 通知对象
     */
    private String opsNotifyTarget;

    /**
     * 告警通知开始时间段
     */
    private String alarmNotifyStartTimePeriod;

    /**
     * 告警通知结束时间段
     */
    private String alarmNotifyEndTimePeriod;

    /**
     * 重复通知状态
     */
    private String repetitionNotifyStatus;

    /**
     * 重复通知间隔时间
     */
    private String repetitionNotifyIntervalTime;


    /**
     * 告警升级策略名称
     */
    private String alarmNotifyUpgradePolicyName;

    /**
     * 启用状态
     */
    private String status;

    /**
     * 告警恢复通知状态
     */
    private String alarmRestorationNotifyStatus;
}
