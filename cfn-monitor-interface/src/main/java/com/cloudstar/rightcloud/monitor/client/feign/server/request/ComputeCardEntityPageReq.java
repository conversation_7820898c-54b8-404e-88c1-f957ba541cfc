package com.cloudstar.rightcloud.monitor.client.feign.server.request;


import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 查询集群
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class ComputeCardEntityPageReq {

    /**
     * 计算卡名称
     */
    private String cardName;
    /**
     * 计算卡名称(模糊查询)
     */
    private String cardNameLike;
    /**
     * 计算卡类型
     */
    private String cardType;
    /**
     * 状态
     */
    private String status;
    /**
     * 计算卡UUID
     */
    private String uuid;
    /**
     * 计算卡UUID列表
     */
    private List<String> uuids;
}
