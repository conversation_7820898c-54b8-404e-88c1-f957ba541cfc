package com.cloudstar.rightcloud.monitor.client.moroptimization.result;

import com.cloudstar.rightcloud.monitor.client.moroptimization.param.rule.StrategyRecommend;
import com.cloudstar.rightcloud.monitor.client.moroptimization.param.rule.StrategyRule;
import lombok.Data;

import java.util.Date;

/**
 * 优化策略
 *
 * <AUTHOR> Lesao
 * @date : 2023/11/29
 */
@Data
public class OptimizationStrategyDetailResult {

    /**
     * id
     */
    private Long id;
    /**
     * 策略配置id
     */
    private Long configId;
    /**
     * 组织id
     */
    private Long orgId;
    /**
     * 策略名称
     */
    private String name;
    /**
     * 策略类型
     */
    private String type;
    /**
     * 云资源类型
     */
    private String resTypeCode;
    /**
     * 策略规则
     */
    private StrategyRule rule;
    /**
     * 策略描述
     */
    private String description;
    /**
     * 是否开启推荐配置
     */
    private Boolean recommend;
    /**
     * 推荐配置
     */
    private StrategyRecommend recommendConfig;
    /**
     * 数据范围类型
     */
    private String scopeType;
    /**
     * 数据范围配置
     */
    private String scopeConfig;
    /**
     * 排除资源id集合
     */
    private String excludeResIds;
    /**
     * 是否启用
     */
    private Boolean enabled;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 最后更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
    /**
     * 版本
     */
    private Long version;
}
