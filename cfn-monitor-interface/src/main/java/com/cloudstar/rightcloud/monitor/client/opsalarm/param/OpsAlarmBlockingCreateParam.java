package com.cloudstar.rightcloud.monitor.client.opsalarm.param;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 创建告警屏蔽详情
 *
 * @author: 卢泳舟
 * @date: 2023/6/8 9:59
 */
@Data
public class OpsAlarmBlockingCreateParam {

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 资源类型
     */
    private String resourceType;

    /**
     * 屏蔽对象id
     */
    private List<String> blockingObjectIds;

    /**
     * 屏蔽对象类型
     */
    private String blockingObjectType;

    /**
     * 屏蔽周期
     */
    private String shieldingPeriod;

    /**
     * 屏蔽天数（按周：1-7，按月1-31；多天用逗号分隔）
     */
    private String shieldingDays;

    /**
     * 开始时间（屏蔽周期为永久的时候，只取时分秒）
     */
    private Date startDate;

    /**
     * 结束时间（屏蔽周期为永久的时候，只取时分秒）
     */
    private Date endDate;

}
