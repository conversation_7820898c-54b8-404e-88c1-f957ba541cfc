package com.cloudstar.rightcloud.monitor.client.moroptimization.result;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 优化建议数据
 *
 * <AUTHOR> Lesao
 * @date : 2023/12/12
 */
@Data
public class OptimizationDataDetailResult {


    /**
     * 主键ID
     */
    private Long id;

    /**
     * 费用计划ID
     */
    private Long costPlanId;

    /**
     * 策略ID
     */
    private Long strategyId;

    /**
     * 组织ID
     */
    private Long orgId;

    /**
     * 所属项目ID
     */
    private Long projectId;

    /**
     * 资源ID
     */
    private Long resId;

    /**
     * 资源UUID
     */
    private String resUuid;

    /**
     * 资源名称
     */
    private String resName;

    /**
     * 资源类型
     */
    private String resType;

    /**
     * 资源配置
     */
    private String resConfig;

    /**
     * 资源使用时长
     */
    private Integer usedTime;

    /**
     * 云环境ID
     */
    private Long cloudEnvId;

    /**
     * 云环境ID
     */
    private String cloudEnvName;

    /**
     * 推荐配置
     */
    private String recommendConfig;

    /**
     * 当前资源成本
     */
    private BigDecimal cost;

    /**
     * 优化后成本相差
     */
    private BigDecimal costDown;

    /**
     * 询价单位
     */
    private String inquiryUnit;

    /**
     * 计费模式
     */
    private String chargeType;

    /**
     * 优化类型
     */
    private String optimizationType;

    /**
     * 优化详情
     */
    private String optimizationDetail;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;

    /**
     * 版本
     */
    private Long version;

    /**
     * 推荐配置
     */
    private List<OptimizationRecommendResult> recommendList;
}
