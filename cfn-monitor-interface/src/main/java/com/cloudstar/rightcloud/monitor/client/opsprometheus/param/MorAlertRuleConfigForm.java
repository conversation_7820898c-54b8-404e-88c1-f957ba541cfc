package com.cloudstar.rightcloud.monitor.client.opsprometheus.param;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 告警规则
 *
 * @author: wanglang
 * @date: 2023/4/19 4:47 PM
 */
@Data
public class MorAlertRuleConfigForm {

    /**
     * 数据源id
     */
    @JsonProperty("datasource_ids")
    private List<Long> datasourceIds;

    /**
     * 集群名称
     */
    private String cluster;

    /**
     * 告警规则分类
     */
    private String cate;

    /**
     * 告警规则类型
     */
    private String prod;

    /**
     * 状态
     */
    private String status;

    /**
     * 规则配置
     */
    @JsonProperty("rule_config")
    private RuleConfig ruleConfig;

    /**
     * prom 执行频率 unit:s
     */
    @JsonProperty("prom_eval_interval")
    private Integer promEvalInterval;

    /**
     * 恢复通知 0: 不通知 1: 通知
     */
    @JsonProperty("notify_recovered")
    private Integer notifyRecovered;

    /**
     * 重复通知间隔, unit: min
     */
    @JsonProperty("notify_repeat_step")
    private Integer notifyRepeatStep;

    /**
     * 最大通知次数
     */
    @JsonProperty("notify_max_number")
    private Integer notifyMaxNumber;

    /**
     * 留观时长unit: s
     */
    @JsonProperty("recover_duration")
    private Integer recoverDuration;

    /**
     * 持续时长 for, unit:s
     */
    @JsonProperty("prom_for_duration")
    private Integer promForDuration;

    /**
     * 回调地址
     */
    private String callback;

    /**
     * 通知通道：email、mm、wecom、dingtalk
     */
    private String notifyChannels;

    /**
     * 严重程度
     */
    private Integer severity;

    /**
     * 标签
     */
    private String appendTags;

    /**
     * 元信息
     */
    private Map<String, String> annotations;


    @Data
    public static class RuleConfig {
        /**
         * 查询条件
         */
        private List<Queries> queries;
    }

    @Data
    public static class Queries {

        /**
         * promql
         */
        @JsonProperty("prom_ql")
        private String promQl;

        /**
         * promql
         */
        @JsonProperty("severity")
        private Integer severity;

    }
}
