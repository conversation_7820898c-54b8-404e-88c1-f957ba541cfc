package com.cloudstar.rightcloud.monitor.client.morpolicyconfig.service;

import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorEnvStatisticStrategyCreateParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorEnvStatisticStrategyPageParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorEnvStatisticStrategyUpdateParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorEnvStatisticStrategyUpdateStatusParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorEnvStatisticStrategyInfoResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorEnvStatisticStrategyPageResult;

/**
 * 环境策略配置统计规则接口
 *
 * @author: wanglang
 * @date: 2023/11/3 16:06
 */
public interface MorStatisticEnvStrategyService {

    /**
     * 分页查询环境策略配置统计规则
     * @param param 查询参数
     * @return
     */
    RightCloudResult<PageResult<MorEnvStatisticStrategyPageResult>> getPage(
            MorEnvStatisticStrategyPageParam param);

    /**
     * 查询环境策略配置统计规则信息
     *
     * @param id 环境策略配置统计规则Id
     * @return
     */
    RightCloudResult<MorEnvStatisticStrategyInfoResult> getDetail(Long id);

    /**
     * 新增环境策略配置统计规则
     *
     * @param param 新增参数
     */
    RightCloudResult<Long> create(MorEnvStatisticStrategyCreateParam param);

    /**
     * 修改环境策略配置统计规则
     *
     * @param param 修改参数
     */
    RightCloudResult<Void> update(MorEnvStatisticStrategyUpdateParam param);

    /**
     * 启用/禁用环境策略配置统计规则
     *
     * @param param 修改参数
     */
    RightCloudResult<Void> updateStatus(MorEnvStatisticStrategyUpdateStatusParam param);

    /**
     * 删除环境策略配置统计规则
     *
     * @param id 策略id
     */
    RightCloudResult<Void> delete(Long id);
}
