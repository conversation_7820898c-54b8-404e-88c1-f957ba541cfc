package com.cloudstar.rightcloud.monitor.client.opsalarm.result;

import lombok.Data;

@Data
public class OpsOriginAlarmMetricPageResult {

    /**
     * id
     */
    private Long id;
    /**
     * 告警名称
     */
    private String name;
    /**
     * 告警编码
     */
    private String alarmCode;
    /**
     * 状态
     */
    private String status;
    /**
     * 描述
     */
    private String description;
    /**
     * 告警来源
     */
    private String source;
    /**
     * 告警分类（env_code_alarm 云平台告警 res_type_code_alarm 云资源告警）
     */
    private String alarmClassification;
    /**
     * 告警级别状态
     */
    private String alarmLevelStatus;
    /**
     * 转译状态
     */
    private String translationalStatus;



    /**
     * 告警通知策略id
     */
    private Long opsNotifyPolicyId;

    /**
     * 通知策略名称
     */
    private String notifyPolicyName;

}
