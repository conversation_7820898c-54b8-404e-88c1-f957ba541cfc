package com.cloudstar.rightcloud.monitor.client.feign.server.result;


import com.cloudstar.rightcloud.monitor.common.em.feign.TrainingJobStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import lombok.Data;

/**
 * 作业返回resp
 *
 * <AUTHOR>
 * @date 2022/8/19 16:54
 */
@Data
public class TrainingJobEntityResp {

    /**
     * 作业id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 算力集群底层jobId
     */
    private String jobId;

    /**
     * 作业名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 用户id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userSid;


    /**
     * 运行时长
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long runDuration;

    /**
     * 作业状态
     */
    private TrainingJobStatusEnum status;

    /**
     * 作业状态名称
     */
    private String statusName;

    /**
     * 集群ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 集群类型
     */
    private String clusterType;
    /**
     * 所属作业组id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long groupId;

    /**
     * 所属作业组name
     */
    private String groupName;

    /**
     * 是否就绪
     */
    private String isReady;

    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updatedDt;

    /**
     * 底层作业创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date jobCreateTime;

    /**
     * 容器id
     */
    private String containerId;

    /**
     * 底层作业开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date jobStartTime;

    public String getStatusName() {
        if (status != null) {
            return status.getDesc();
        }
        return null;
    }
}
