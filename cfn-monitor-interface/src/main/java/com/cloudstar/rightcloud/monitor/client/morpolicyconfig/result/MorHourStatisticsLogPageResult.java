package com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 小时汇聚任务日志分页查询结果
 *
 * @author: hjy
 * @date: 2023/11/10 16:52
 */
@Data
public class MorHourStatisticsLogPageResult implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 资源类型
     */
    private String resTypeCode;
    /**
     * 统计指标项
     */
    private String performanceMetricIndicatorCode;
    /**
     * 监控采集指标id
     */
    private Long monitorCollectMetricId;

    /**
     * 监控采集指标
     */
    private String monitorMetricCode;
    /**
     * 监控采集指标
     */
    private String monitorCollectMetricName;
    /**
     * 汇聚时间
     */
    private Date mergeTime;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 开始时间
     */
    private Date taskStartTime;
    /**
     * 结束时间
     */
    private Date taskEndTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 异常信息
     */
    private String errorMsg;
    /**
     * 状态;success 成功 fail 失败
     */
    private String status;
    /**
     * 描述
     */
    private String description;

    /**
     * 执行sql
     */
    private String executeSql;

    /**
     * 创建用户ID
     */
    private String createdBy;
    /**
     * 记录创建时间
     */
    private Date createdDt;
    /**
     * 最后修改用户ID
     */
    private String updatedBy;
    /**
     * 记录修改时间
     */
    private Date updatedDt;
    /**
     * 版本号
     */
    private Long version;

}
