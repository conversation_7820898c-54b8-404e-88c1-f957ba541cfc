package com.cloudstar.rightcloud.monitor.client.opscmdb.result;

import com.alibaba.fastjson.JSONObject;
import com.cloudstar.rightcloud.monitor.client.opscollecttask.result.OpsResInstEnvPropertiesResult;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 获取云环境数据
 *
 * @author: wanglang
 * @date: 2023/8/18 16:19
 */
@Data
public class OpsCloudEnvGetInstanceResult {
    /**
     * 云环境id
     */
    private String cloudEnvId;

    /**
     * 云账号id
     */
    private String cloudEnvAccountId;


    /**
     * 云环境名称
     */
    private String cloudEnvName;


    /**
     * 基础监控数据采集频率(分钟)
     */
    private Long monitorCollectFrequency;

    /**
     * 基础监控告警采集频率(分钟)
     */
    private Long alarmCollectFrequency;

    /**
     * 接口请求次数
     */
    private Integer limitCount;

    /**
     * 接口请求次数规定时间
     */
    private Integer limitCountExpireTime;

    /**
     * 组织id
     */
    private String orgId;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 代理ID
     */
    private OpsResInstEnvPropertiesResult.ProxyConfig proxyConfig;


    public void setProxyConfig(String proxyConfig) {
        this.proxyConfig = JSONObject.parseObject(proxyConfig, OpsResInstEnvPropertiesResult.ProxyConfig.class);
    }

    /**
     * 包含区域的来自环境的属性
     */
    private String accessInfo;

    /**
     * 是否开启监控
     */
    private Boolean monitorEnable;
    /**
     * 云平台编码
     */
    private String envCode;

    /**
     * 云平台版本
     */
    private String envVersion;


    /**
     * 监控资源
     */
    private List<String> monitorResources;

    /**
     * 云平台图标
     */
    private String envIcon;

    /**
     * 云平台英文名称
     */
    private String defineEnvNameEn;


    /**
     * 云平台名称
     */
    private String defineEnvName;



    @Data
    @Accessors(chain = true)
    public static class ProxyConfig implements Serializable {

        private static final long serialVersionUID = 9027375574802826429L;
        /**
         * 开启代理
         */
        private Boolean proxyEnabled;

        /**
         * HTTP代理主机地址
         */
        private String httpProxyHost;

        /**
         * HTTP代理主机密端口
         */
        private String httpProxyPort;

        /**
         * HTTP代理主机密用户名
         */
        private String httpProxyUsername;

        /**
         * HTTP代理主机密密码
         */
        private String httpProxyPassword;

        /**
         * HTTPS代理主机地址
         */
        private String httpsProxyHost;

        /**
         * HTTPS代理主机密端口
         */
        private String httpsProxyPort;

        /**
         * HTTPS代理主机密用户名
         */
        private String httpsProxyUsername;

        /**
         * HTTPS代理主机密码
         */
        private String httpsProxyPassword;
    }
}
