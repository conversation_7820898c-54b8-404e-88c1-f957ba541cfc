package com.cloudstar.rightcloud.monitor.client.moroptimization.result;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 优化建议推荐配置
 *
 * <AUTHOR> <PERSON>ao
 * @date : 2023/12/12
 */
@Data
public class OptimizationRecommendResult {


    /**
     * ID
     */
    private Long id;

    /**
     * 资源优化建议ID
     */
    private Long dataId;

    /**
     * 推荐规格ID
     */
    private String specId;

    /**
     * 推荐配置
     */
    private String resConfig;

    /**
     * 推荐配置描述
     */
    private String resConfigName;

    /**
     * 推荐配置成本
     */
    private BigDecimal cost;

    /**
     * 对比原成本相差
     */
    private BigDecimal costDown;

    /**
     * 推荐星级
     */
    private Integer recommendLevel;

    /**
     * 推荐描述
     */
    private String recommendDesc;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 修改人
     */
    private String updatedBy;

    /**
     * 修改时间
     */
    private Date updatedDt;

    /**
     * 版本
     */
    private Long version;

}
