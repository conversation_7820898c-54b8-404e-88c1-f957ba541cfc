package com.cloudstar.rightcloud.monitor.client.morpolicyconfig.service;

import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MonDayStatisticsStrategyTaskParam;

/**
 * 性能汇总分析策略天接口
 *
 * @author: wanglang
 * @date: 2023/11/6 15:20
 */
public interface MorPerformanceAnalysisDayPolicyService {

    /**
     * 创建或者更新天分析汇总定时任务
     *
     * @return 操作结果
     */
    RightCloudResult<Boolean> createByUpdateDayTak();

    /**
     * 执行天分析汇总任务
     *
     * @param param 任务参数
     */
    void executeTak(MonDayStatisticsStrategyTaskParam param);

    /**
     * 执行天分析汇总定时任务
     */
    void executeDayTak();

    /**
     * 执行天分析汇总任务
     *
     * @param param 任务参数
     */
    void executeDayTak(MonDayStatisticsStrategyTaskParam param);

    /**
     * 检查任务执行状态
     */
    RightCloudResult<Boolean> getExecuteHourTakStatus();

}
