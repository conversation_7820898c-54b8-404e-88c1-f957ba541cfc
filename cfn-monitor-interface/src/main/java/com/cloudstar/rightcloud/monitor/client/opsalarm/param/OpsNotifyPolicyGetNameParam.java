package com.cloudstar.rightcloud.monitor.client.opsalarm.param;

import com.cloudstar.rightcloud.monitor.common.em.OperationStatus;
import com.cloudstar.rightcloud.monitor.common.util.StringUtils;
import lombok.Data;

/**
 * 告警通知策略获取条件
 *
 * @author: wanglang
 * @date: 2023/6/13 16:58
 */
@Data
public class OpsNotifyPolicyGetNameParam {

    /**
     * 告警通知策略名称
     */
    private String name;

    /**
     * 状态
     */
    private String status;

    public String getStatus() {
        String status = OperationStatus.ENABLE.status;
        if (StringUtils.isNotEmpty(this.status)) {
            status = this.status;
        }
        return status;
    }
}
