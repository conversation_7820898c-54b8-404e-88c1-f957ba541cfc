package com.cloudstar.rightcloud.monitor.client.opsviews.service;


import com.cloudstar.rightcloud.monitor.client.opsalarm.result.AlarmInstancePropertiesListResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsAlarmRuleObjectListParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsViewsComponentCategoryAlarmResult;

import java.util.List;

/**
 * 告警资源类型共通接口
 *
 * @author: hjy
 * @date: 2025/6/23 20:49
 */
public interface AlarmCategoryCommonService {


    List<OpsViewsComponentCategoryAlarmResult> getAlarmCategoryTypeList();

    List<AlarmInstancePropertiesListResult> getAlarmCategoryInstanceList();

    List<AlarmInstancePropertiesListResult> getAlarmInstanceListByParam(OpsAlarmRuleObjectListParam param);
}
