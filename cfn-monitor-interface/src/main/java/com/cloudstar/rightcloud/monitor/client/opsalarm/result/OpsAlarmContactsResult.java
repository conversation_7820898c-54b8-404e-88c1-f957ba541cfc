package com.cloudstar.rightcloud.monitor.client.opsalarm.result;

import com.cloudstar.rightcloud.common.utils.json.jackson.Mask;
import com.cloudstar.rightcloud.common.utils.json.jackson.MaskType;

import java.util.List;

import lombok.Data;

/**
 * 告警联系人
 *
 * @author: wanglang
 * @date: 2023/4/6 11:17 AM
 */
@Data
public class OpsAlarmContactsResult {

    /**
     * id
     */
    private Long id;

    /**
     * 姓名
     */
    private String userName;

    /**
     * 手机号
     */
    @Mask(MaskType.MOBILE_PHONE)
    private String userPhone;

    /**
     * 邮箱
     */
    @Mask(MaskType.EMAIL)
    private String userEmail;

    /**
     * 导入用户的id
     */
    private String importUserId;

    /**
     * 通知组名称
     */
    private List<String> groupNameList;

    /**
     * 描述
     */
    private String description;
}
