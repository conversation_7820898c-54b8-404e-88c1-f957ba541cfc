package com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result;

import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.Data;

import java.io.Serializable;

/**
 * 监控指标信息
 *
 * @author: hjy
 * @date: 2023/11/7 19:49
 */
@Data
public class MorCollectMetricInfoResult extends MapperAutoObject implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 采集指标名称
     */
    private String name;

    /**
     * 状态  enable 启用 disable 禁用
     */
    private String status;

    /**
     * 资源类型编码
     */
    private String resTypeCode;

    /**
     * 采集规则id
     */
    private Long opsCollectRuleId;
    /**
     * 采集规则维度id
     */
    private Long opsCollectRuleDimensionsId;

    /**
     * 采集指标统一编码
     */
    private String unifiedCoding;
    /**
     * 采集指标原始编码
     */
    private String originalCoding;
    /**
     * 指标单位：bps/kbps/%等
     */
    private String unit;
    /**
     * 排序编码
     */
    private Integer sortRank;

    /**
     * 是否默认展示
     */
    private Boolean defaultDisplay;

    /**
     * 描述
     */
    private String description;
    /**
     * 单位转换因子
     */
    private Double unitConvFactor;
    /**
     * 指标命名空间
     */
    private String namespace;

    /**
     * 云平台
     */
    private String envCode;
    /**
     * 云平台版本号
     */
    private String envVersion;

    /**
     * uuid
     */
    private String uuid;

}
