package com.cloudstar.rightcloud.monitor.client.opscollecttask.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * prometheus 任务实体
 *
 * @author: wanglang
 * @date: 2025/1/13 14:22
 */
@Data
public class MorPromTaskParam implements Serializable {
    /**
     * 任务名称
     */
    @JSONField(name = "job_name", ordinal = 1)
    private String jobName;
    /**
     * 采集时是否用指标数据的时间
     */
    @JSONField(name = "honor_timestamps", ordinal = 2)
    private Boolean honorTimestamps;
    /**
     * 采集间隔
     */
    @JSONField(name = "scrape_interval", ordinal = 3)
    private String scrapeInterval;
    /**
     * 采集超时时间
     */
    @JSONField(name = "scrape_timeout", ordinal = 4)
    private String scrapeTimeout;
    /**
     * 指标路径
     */
    @JSONField(name = "metrics_path", ordinal = 5)
    private String metricsPath;
    /**
     * 采集协议
     */
    @JSONField(ordinal = 6)
    private String scheme;
    /**
     * tls配置
     */
    @JSONField(name = "tls_config", ordinal = 6)
    private MorPromTlsConfigParam tlsConfig;
    /**
     * 采集参数
     */
    @JSONField(name = "params", ordinal = 7)
    private Map<String, String[]> params;
    /**
     * 静态配置
     */
    @JSONField(name = "static_configs", ordinal = 8)
    private List<MorPromStaticConfigsParam> staticConfigs;
    /**
     * relabel配置
     */
    @JSONField(name = "metric_relabel_configs", ordinal = 8)
    private List<MorPromMetricRelabelParam> metricRelabelConfigs;






}
