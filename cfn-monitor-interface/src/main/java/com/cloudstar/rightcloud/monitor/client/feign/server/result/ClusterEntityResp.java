package com.cloudstar.rightcloud.monitor.client.feign.server.result;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import lombok.Data;

/**
 * 集群响应值
 */
@Data
public class ClusterEntityResp {

    /**
     * 主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**
     * 集群名称;管理员端展示
     */
    private String clusterName;
    /**
     * 集群昵称;租户端展示
     */
    private String clusterNickName;
    /**
     * 集群类型;HCSO,SLURM
     */
    private String clusterType;
    /**
     * 适配器uuid
     */
    private String adapterUuid;
    /**
     * 集群标志;system:系统集群 calculate:算力集群
     */
    private String clusterFlag;
    /**
     * 集群标志中文名
     */
    private String clusterFlagName;
    /**
     * 集群地址
     */
    private String clusterAddress;
    /**
     * 状态;normal:正常abnormal:异常 offline:下线
     */
    private String status;
    /**
     * 状态中文名
     */
    private String statusName;
    /**
     * 同步状态;fail:同步失败 succeed：同步成功
     */
    private String syncStatus;

    /**
     * 同步时间
     */
    private Date syncTime;

    /**
     * 适配器地址
     */
    private String adapterAddress;

    /**
     * 适配器账号
     */
    private String adapterAccount;

    /**
     * 适配器密码
     */
    private String adapterPassword;

    /**
     * 同步状态中文名
     */
    private String syncStatusName;
    /**
     * 心跳检测频率;单位秒
     */
    private Integer heartCheck;
    /**
     * 数据采集频率;单位秒
     */
    private Integer dataCollect;
    /**
     * 监控采集频率;单位秒
     */
    private Integer monitorCollect;
    /**
     * 属性详情;属性详情json数据加密字段
     */
    private String attrData;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;

}
