package com.cloudstar.rightcloud.monitor.client.moranalysis.service;

import com.cloudstar.rightcloud.api.system.org.result.SysOrgListQueryFeignResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorAnalysisGetHideHeaderGeParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorOrgResAnalysisExportParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorOrgResAnalysisPageParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorProjectResAnalysisExportParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorProjectResAnalysisPageParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorResPoolAnalysisExportParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorResPoolAnalysisPageParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorResourceAnalysisExportParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorResourceAnalysisPageParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.result.MorAnalysisGetHideHeaderResult;
import com.cloudstar.rightcloud.monitor.client.moranalysis.result.MorOrgResAnalysisPageResult;
import com.cloudstar.rightcloud.monitor.client.moranalysis.result.MorProjectResAnalysisPageResult;
import com.cloudstar.rightcloud.monitor.client.moranalysis.result.MorResPoolAnalysisPageResult;
import com.cloudstar.rightcloud.monitor.client.moranalysis.result.MorResourceAnalysisPageResult;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;

import javax.servlet.http.HttpServletResponse;

/**
 * 云资源分析
 *
 * @author: hjy
 * @date: 2023/11/23 15:30
 */
public interface MorCloudResAnalysisService {

    /**
     * 组织维度性能统计
     */
    RightCloudResult<MorOrgResAnalysisPageResult> getOrgCloudResAnalysis(MorOrgResAnalysisPageParam param);

    /**
     * 组织维度性能统计
     */
    RightCloudResult<List<Map<String, Object>>> getOrgCloudResAnalysisList(MorOrgResAnalysisPageParam param);

    /**
     * 导出组织维度性能统计
     *
     * @param param    ResViewExportParam
     * @param response HttpServletResponse
     */
    RightCloudResult<Void> exportOrgCloudResAnalysis(MorOrgResAnalysisExportParam param, HttpServletResponse response);

    /**
     * 组织维度异步查询和导出
     */
    void composeOrgExportData(List<Map<String, Object>> resultList, MorOrgResAnalysisExportParam param,
                              String destFileName, Long taskId, Locale locale);

    /**
     * 设置资源顶层单位信息
     **/
    <T> void setTopOrgInfo(List<T> list, Function<T, Long> orgSidFn, BiConsumer<T, SysOrgListQueryFeignResult> write);

    /**
     * 项目维度性能统计
     */
    RightCloudResult<MorProjectResAnalysisPageResult> getProjectCloudResAnalysis(
            MorProjectResAnalysisPageParam param);


    /**
     * 项目维度性能统计列表
     */
    RightCloudResult<List<Map<String, Object>>> getProjectCloudResAnalysisList(MorProjectResAnalysisPageParam param);

    /**
     * 导出项目维度性能统计
     *
     * @param param    ResViewExportParam
     * @param response HttpServletResponse
     */
    RightCloudResult<Void> exportProjectCloudResAnalysis(MorProjectResAnalysisExportParam param,
                                                         HttpServletResponse response);

    /**
     * 项目维度异步查询和导出
     */
    void composeProjectExportData(List<Map<String, Object>> resultList, MorProjectResAnalysisExportParam param,
                                  String destFileName, Long taskId, Locale locale);

    /**
     * 资源维度性能统计
     */
    RightCloudResult<MorResourceAnalysisPageResult> getResourceCloudResAnalysis(
            MorResourceAnalysisPageParam param);

    /**
     * 导出资源维度性能统计
     *
     * @param param    ResViewExportParam
     * @param response HttpServletResponse
     */
    RightCloudResult<Void> exportResourceCloudResAnalysis(MorResourceAnalysisExportParam param,
                                                          HttpServletResponse response);

    /**
     * 资源维度异步查询和导出
     */
    void composeResourceExportData(List<Map<String, Object>> resultList, MorResourceAnalysisExportParam param,
                                   String destFileName, Long taskId, Locale locale);

    // /**
    // * 资源闲置率统计
    // */
    //RightCloudResult<MorResourceIdlePageResult> getResourceIdle(MorResourceIdlePageParam param);

    /**
     * 资源池维度性能统计
     *
     * @param param 资源池维度查询参数
     * @return 返回结果
     */
    RightCloudResult<MorResPoolAnalysisPageResult> getResourcePoolAnalysis(MorResPoolAnalysisPageParam param);

    /**
     * 导出资源池维度性能统计
     *
     * @param param    ResViewExportParam
     * @param response HttpServletResponse
     */
    RightCloudResult<Void> exportResPoolCloudResAnalysis(MorResPoolAnalysisExportParam param,
                                                         HttpServletResponse response);

    /**
     * 资源池维度异步查询和导出
     */
    void composeResPoolExportData(List<Map<String, Object>> resultList, MorResPoolAnalysisExportParam param,
                                  String destFileName, Long taskId, Locale locale);


    /**
     * 获取隐藏头部数据
     *
     * @param param 请求参数
     * @return MorAnalysisGetHideHeaderResult 隐藏头部数据
     */
    RightCloudResult<List<MorAnalysisGetHideHeaderResult>> getHideHeaderList(MorAnalysisGetHideHeaderGeParam param);

    /**
     * 组织项目维度性能统计联合查询
     * @param param 参数
     * @return
     */
    RightCloudResult<List<Map<String, Object>>> getOrgUnionProjectResAnalysisList(MorOrgResAnalysisPageParam param);

}
