package com.cloudstar.rightcloud.monitor.client.moranalysis.param;

import com.cloudstar.rightcloud.common.pojo.page.PageForm;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 资源维度性能统计参数
 *
 * @author: hjy
 * @date: 2023/11/23 11:31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MorResourceAnalysisPageParam extends PageForm implements Serializable {
    /**
     * 开始日期
     */
    private String startDay;

    /**
     * 结束日期
     */
    private String endDay;

    /**
     * 云环境类型
     */
    private String envType;
    /**
     * 云平台
     */
    private String envTypeCode;
    /**
     * 云平台版本号
     */
    private String envVersion;
    /**
     * 云环境ID
     */
    private Long envId;
    /**
     * 资源类型
     */
    private String resTypeCode;
    /**
     * 组织ID
     */
    private Long orgId;
    /**
     * 指标比较条件
     */
    private List<MorOrgAnalysisMetricConditionParam> conditions;
    /**
     * 指标比较条件(字符串)
     */
    private String conditionsStr;

    /**
     * 统计范围
     */
    private String statisticScope;

    /**
     * 日期范围
     */
    private String range;

    /**资源池id**/
    private Long resPoolId;


    /**
     * 考核类型(考核项)
     */
    private String scoreType;

    /**
     * 资源名称模糊搜索
     */
    private String resInstanceNameLike;

    /**
     * 组织路径模糊查询
     */
    private String orgIdPathLike;
    /**
     * 最小阈值天数
     */
    private Integer daysThreshold;
    /**
     * 环境统计策略
     */
    List<Map<String, Object>> envPolicyList;

    /**
     * 指标查询条件
     */
    private String metricCondition;

    private String metricRangeCondition;
    /**
     * 排序
     */
    private String orderByClause;

    /**
     * 云环境id列表
     */
    private List<Long> envIds;

    /**
     * 项目类型
     */
    private String projectType;
    /**
     * 项目id
     */
    private Long projectId;
}
