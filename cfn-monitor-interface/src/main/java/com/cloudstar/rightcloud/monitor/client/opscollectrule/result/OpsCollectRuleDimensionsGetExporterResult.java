package com.cloudstar.rightcloud.monitor.client.opscollectrule.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 采集规则维度详情数据
 *
 * @author: wanglang
 * @date: 2023/7/20 20:02
 */
@Data
public class OpsCollectRuleDimensionsGetExporterResult implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 维度名称
     */
    private String name;
    /**
     * 编码
     */
    private String code;

    /**
     * code 所对应的值表达式，格式为 {p1}_{p2}
     * 根据每个资源的propertiesMap进行替换
     */
    private String valueExpression;

    /**
     * 资源类型
     */
    private String resTypeCode;
    /**
     * 采集规则id
     */
    private Long opsCollectRuleId;
    /**
     * 描述
     */
    private String description;
    /**
     * 排序编码
     */
    private Integer sortRank;

    /**
     * 采集规则维度条件参数
     */
    private List<OpsCollectRuleDimensionsConditionGetExporterResult> opsCollectRuleDimensionsConditionGetDetailsResult;
}
