package com.example.monitor.dao.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.example.monitor.dao.OpsNotifyPolicyDao;
import com.example.monitor.entity.result.OpsTableNotifyPolicyResultDTO;
import com.example.monitor.mapper.OpsNotifyPolicyMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>描述: [OpsNotifyPolicy 服务实现层] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Repository
public class OpsNotifyPolicyDaoImpl implements OpsNotifyPolicyDao {
    
    private OpsNotifyPolicyMapper opsNotifyPolicyMapper;
    
    public OpsNotifyPolicyDaoImpl(OpsNotifyPolicyMapper opsNotifyPolicyMapper) {
        this.opsNotifyPolicyMapper = opsNotifyPolicyMapper;
    }

    @Override
    public List<OpsTableNotifyPolicyResultDTO> selectList(Wrapper<OpsTableNotifyPolicyResultDTO> queryWrapper) {
        return opsNotifyPolicyMapper.selectList(queryWrapper);
    }

    @Override
    public OpsTableNotifyPolicyResultDTO selectOne(Wrapper<OpsTableNotifyPolicyResultDTO> queryWrapper) {
        return opsNotifyPolicyMapper.selectOne(queryWrapper);
    }
}
