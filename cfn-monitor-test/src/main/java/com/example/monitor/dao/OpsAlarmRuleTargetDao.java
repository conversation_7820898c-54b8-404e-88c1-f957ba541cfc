package com.example.monitor.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.example.monitor.entity.result.OpsTableAlarmRuleTargetResultDTO;

import java.util.List;

/**
 * <p>描述: [OpsAlarmRuleTarget 服务层] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
public interface OpsAlarmRuleTargetDao {

    /**
     * 查询列表数据
     *
     * @param queryWrapper 条件构造器
     * @return List<OpsTableAlarmRuleTargetResultDTO>
     */
    List<OpsTableAlarmRuleTargetResultDTO> selectList(Wrapper<OpsTableAlarmRuleTargetResultDTO> queryWrapper);

    /**
     * 查询单挑数据
     *
     * @param queryWrapper 条件构造器
     * @return OpsTableAlarmRuleTargetResultDTO
     */
    OpsTableAlarmRuleTargetResultDTO selectOne(Wrapper<OpsTableAlarmRuleTargetResultDTO> queryWrapper);

}
