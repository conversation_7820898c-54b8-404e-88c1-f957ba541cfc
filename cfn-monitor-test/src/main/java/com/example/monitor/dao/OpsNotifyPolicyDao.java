package com.example.monitor.dao;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.example.monitor.entity.result.OpsTableNotifyPolicyResultDTO;

import java.util.List;

/**
 * <p>描述: [OpsNotifyPolicy 服务层] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
public interface OpsNotifyPolicyDao {

    /**
     * 查询列表数据
     *
     * @param queryWrapper 条件构造器
     * @return List<OpsTableNotifyPolicyResultDTO> 采集任务
     */
    List<OpsTableNotifyPolicyResultDTO> selectList(Wrapper<OpsTableNotifyPolicyResultDTO> queryWrapper);

    /**
     * 查询单挑数据
     *
     * @param queryWrapper 条件构造器
     * @return OpsTableNotifyPolicyResultDTO 采集任务
     */
    OpsTableNotifyPolicyResultDTO selectOne(Wrapper<OpsTableNotifyPolicyResultDTO> queryWrapper);

}
