package com.example.monitor.entity.result;

import java.io.Serializable;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>描述: [OpsNotifyTargetGroupContactsRel 实体类] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OpsTableNotifyTargetGroupContactsRelResultDTO implements Serializable {
    private static final long serialVersionUID = -47446634494162004L;
    /**
    * id;id
    */
    private String id;
    /**
    * 通知对象联系人id
    */
    private String opsNotifyTargetContactsId;
    /**
    * 通知对象联系人组id
    */
    private String opsNotifyTargetGroupId;
    /**
    * 版本号
    */
    private Long version;

}
