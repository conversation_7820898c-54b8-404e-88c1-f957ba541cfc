package com.example.monitor.entity.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>描述: [OpsMonitorMetric 实体类] </p>
 * <p>创建时间: 2023/04/19 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OpsTableMonitorMetricResultDto implements Serializable {
    private static final long serialVersionUID = 910202086580857277L;
    /**
    * id;id
    */
    private String id;
    /**
    * 采集指标名称;采集指标名称
    */
    private String name;
    /**
    * 采集指标统一编码;采集指标统一编码
    */
    private String unifiedCodeding;
    /**
    * 采集指标原始编码;采集指标原始编码
    */
    private String originalCodeding;
    /**
    * 云环境类型;cloud_type
    */
    private String cloudType;
    /**
    * 资源类型;资源类型
    */
    private String resourceTypeId;
    /**
    * 资源类别;资源类别
    */
    private String resourceCategory;
    /**
    * 资源子类别;资源子类别
    */
    private String resourceSubType;
    /**
    * 指标单位：bps/kbps/%等;指标单位：bps/kbps/%等
    */
    private String unit;
    /**
    * 排序编码;排序编码
    */
    private Integer sort;
    /**
    * 是否启用;是否启用
    */
    private Integer enabled;
    /**
    * 描述;描述
    */
    private String descript;
    /**
    * 单位转换因子;单位转换因子
    */
    private String unitConvFactor;
    /**
    * 数据类型;数据类型
    */
    private String dataType;
    /**
    * 指标uid，与底层做映射，主要针对不用指标名称，而是需要用指标id获取监控的情况;指标uid，与底层做映射，主要针对不用指标名称，而是需要用指标id获取监控的情况
    */
    private String metricUid;
    /**
    * 指标命名空间;指标命名空间
    */
    private String namespace;
    /**
    * 创建用户ID;创建用户ID
    */
    private String createdBy;
    /**
    * 记录创建时间;记录创建时间
    */
    private Date createdDt;
    /**
    * 最后修改用户ID;最后修改用户ID
    */
    private String updatedBy;
    /**
    * 记录修改时间;记录修改时间
    */
    private Date updatedDt;

}
