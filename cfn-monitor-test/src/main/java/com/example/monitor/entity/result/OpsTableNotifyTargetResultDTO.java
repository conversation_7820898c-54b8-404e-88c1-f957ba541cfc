package com.example.monitor.entity.result;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <p>描述: [OpsNotifyTarget 实体类] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OpsTableNotifyTargetResultDTO implements Serializable {
    private static final long serialVersionUID = 470050442617265824L;
    /**
    * id;id
    */
    private String id;
    /**
    * 通知策略id;包含升级策略、通知策略
    */
    private String notifyPolicyId;
    /**
    * 通知方式;1 邮件、2 短信 3 站内信，支持多个，多个用,分隔
    */
    private Integer notifyWay;
    /**
    * 通知联系人类别;1 联系人组 2 联系人
    */
    private Integer notifyContactsCategory;
    /**
    * 通知联系人id;根据通知联系人类别区分：联系人组id、联系人id
    */
    private String notifyId;
    /**
    * 创建用户ID;创建用户ID
    */
    private String createdBy;
    /**
    * 记录创建时间;记录创建时间
    */
    private Date createdDt;
    /**
    * 最后修改用户ID;最后修改用户ID
    */
    private String updatedBy;
    /**
    * 记录修改时间;记录修改时间
    */
    private Date updatedDt;
    /**
    * 版本号
    */
    private Long version;

}
