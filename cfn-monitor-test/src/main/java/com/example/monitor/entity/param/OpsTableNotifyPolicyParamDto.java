package com.example.monitor.entity.param;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.Date;

/**
 * <p>描述: [OpsNotifyPolicy 实体类] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class OpsTableNotifyPolicyParamDto implements Serializable {
    private static final long serialVersionUID = -48058374661211144L;
    /**
    * id;id
    */
    private String id;
    /**
    * 通知策略名称
    */
    private String name;
    /**
    * 告警恢复后是否通知;0  不发送恢复通知 1 发送恢复通知
    */
    private Integer alarmRestorationNotify;
    /**
    * 告警通知模版id
    */
    private String alarmNotifyTemplateId;
    /**
    * 告警恢复模版id
    */
    private String alarmRestorationTemplateId;
    /**
    * 告警通知开始时间段;格式：HH:mm:ss
    */
    private LocalTime alarmNotifyStartTimePeriod;
    /**
    * 告警通知结束时间段;格式：HH:mm:ss
    */
    private LocalTime alarmNotifyEndTimePeriod;
    /**
    * 是否重复通知;0 不需要，告警未恢复状态下只发送一次， 1档告警未恢复时，间隔多久进行重复通知
    */
    private Integer repetitionNotify;
    /**
    * 重复通知间隔时间;格式：HH:mm:ss
    */
    private LocalTime repetitionNotifyIntervalTime;
    /**
    * 是否告警升级;0 不需要 1 使用升级通知策略
    */
    private Integer alarmEscalation;
    /**
    * 告警升级策略id
    */
    private String alarmEscalationId;
    /**
    * 创建用户ID;创建用户ID
    */
    private String createdBy;
    /**
    * 记录创建时间;记录创建时间
    */
    private Date createdDt;
    /**
    * 最后修改用户ID;最后修改用户ID
    */
    private String updatedBy;
    /**
    * 记录修改时间;记录修改时间
    */
    private Date updatedDt;
    /**
    * 版本号
    */
    private Long version;

}
