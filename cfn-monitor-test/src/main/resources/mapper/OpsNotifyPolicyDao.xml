<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.operation.mapper.OpsNotifyPolicyMapper">

    <resultMap type="com.example.monitor.entity.result.OpsTableNotifyPolicyResultDTO" id="OpsNotifyPolicyMap">
        <result property="id" column="id" jdbcType=""/>
        <result property="name" column="name" jdbcType=""/>
        <result property="alarmRestorationNotify" column="alarm_restoration_notify" jdbcType=""/>
        <result property="alarmNotifyTemplateId" column="alarm_notify_template_id" jdbcType=""/>
        <result property="alarmRestorationTemplateId" column="alarm_restoration_template_id" jdbcType=""/>
        <result property="alarmNotifyStartTimePeriod" column="alarm_notify_start_time_period" jdbcType=""/>
        <result property="alarmNotifyEndTimePeriod" column="alarm_notify_end_time_period" jdbcType=""/>
        <result property="repetitionNotify" column="repetition_notify" jdbcType=""/>
        <result property="repetitionNotifyIntervalTime" column="repetition_notify_interval_time" jdbcType=""/>
        <result property="alarmEscalation" column="alarm_escalation" jdbcType=""/>
        <result property="alarmEscalationId" column="alarm_escalation_id" jdbcType=""/>
        <result property="createdBy" column="created_by" jdbcType=""/>
        <result property="createdDt" column="created_dt" jdbcType=""/>
        <result property="updatedBy" column="updated_by" jdbcType=""/>
        <result property="updatedDt" column="updated_dt" jdbcType=""/>
        <result property="version" column="version" jdbcType=""/>
    </resultMap>
    
</mapper>
