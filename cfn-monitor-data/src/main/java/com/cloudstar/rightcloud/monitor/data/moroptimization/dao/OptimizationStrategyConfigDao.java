package com.cloudstar.rightcloud.monitor.data.moroptimization.dao;

import com.cloudstar.rightcloud.monitor.data.moroptimization.dto.OptimizationStrategyConfig;

import java.util.List;

/**
 * 优化策略配置
 *
 * <AUTHOR> <PERSON>ao
 * @date : 2023/11/29
 */
public interface OptimizationStrategyConfigDao {


    /**
     * 根据组织id查询配置
     *
     * @param orgId 组织id
     * @return 配置数据
     */
    OptimizationStrategyConfig getByOrgId(Long orgId);

    /**
     * 新增配置
     *
     * @param config 配置
     */
    void insert(OptimizationStrategyConfig config);

    /**
     * 更新配置
     *
     * @param config 配置数据
     */
    void update(OptimizationStrategyConfig config);

    /**
     * 查询所有配置
     */
    List<OptimizationStrategyConfig> getAll();

    /**
     * 根据id查询数据*
     *
     * @param id 主键
     * @return 配置
     */
    OptimizationStrategyConfig getById(Long id);
}
