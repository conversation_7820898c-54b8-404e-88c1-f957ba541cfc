package com.cloudstar.rightcloud.monitor.data.moroptimization.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloudstar.rightcloud.monitor.data.moroptimization.dao.OptimizationStrategyConfigDao;
import com.cloudstar.rightcloud.monitor.data.moroptimization.dto.OptimizationStrategyConfig;
import com.cloudstar.rightcloud.monitor.data.moroptimization.mapper.OptimizationStrategyConfigMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 优化策略配置
 *
 * <AUTHOR> Lesao
 * @date : 2023/11/29
 */
@Repository
public class OptimizationStrategyConfigDaoImpl implements OptimizationStrategyConfigDao {

    @Resource
    private OptimizationStrategyConfigMapper optimizationStrategyConfigMapper;

    @Override
    public OptimizationStrategyConfig getByOrgId(Long orgId) {
        return optimizationStrategyConfigMapper.selectOne(Wrappers.lambdaQuery(OptimizationStrategyConfig.class)
                .eq(OptimizationStrategyConfig::getOrgId, orgId)
        );
    }

    @Override
    public void insert(OptimizationStrategyConfig config) {
        optimizationStrategyConfigMapper.insert(config);
    }

    @Override
    public void update(OptimizationStrategyConfig config) {
        optimizationStrategyConfigMapper.updateById(config);
    }

    @Override
    public List<OptimizationStrategyConfig> getAll() {
        return optimizationStrategyConfigMapper.selectList(Wrappers.lambdaQuery());
    }

    @Override
    public OptimizationStrategyConfig getById(Long id) {
        return optimizationStrategyConfigMapper.selectById(id);
    }
}
