package com.cloudstar.rightcloud.monitor.data.opsalarm.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 通知对象联系人
 *
 * @author: wanglang
 * @date: 2023/2/14 4:23 PM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("monitor_notify_target_contacts")
public class OpsTableNotifyTargetContactsResultDto extends MapperAutoObject implements Serializable {
    private static final long serialVersionUID = -55034945747531966L;
    /**
    * id
    */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
    * 用户名称
    */
    private String userName;

    /**
    * 用户手机号
    */
    private String userPhone;

    /**
    * 用户邮箱
    */
    private String userEmail;

    /**
     * 导入用户的id
     */
    private String importUserId;

    /**
     * 描述
     */
    private String description;

    /**
    * 组织id
    */
    @TableField(fill = FieldFill.INSERT)
    private Long orgId;

}
