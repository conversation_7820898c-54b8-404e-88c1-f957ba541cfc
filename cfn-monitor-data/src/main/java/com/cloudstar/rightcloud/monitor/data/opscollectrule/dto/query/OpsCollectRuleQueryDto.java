package com.cloudstar.rightcloud.monitor.data.opscollectrule.dto.query;


import com.cloudstar.rightcloud.monitor.common.annotation.Wrapper;
import com.cloudstar.rightcloud.monitor.common.annotation.WrapperField;
import com.cloudstar.rightcloud.monitor.common.em.WrapperType;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 采集规则
 *
 * @author: wanglang
 * @date: 2023/07/19 17:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Wrapper(orderDescField = {"createdDt", "updatedDt"})
public class OpsCollectRuleQueryDto extends MapperAutoObject implements Serializable {
    /**
     * id;id
     */
    private Long id;

    @WrapperField(type = WrapperType.IN, value = "id")
    private List<Long> ids;

    /**
     * id;id
     */
    @WrapperField(type = WrapperType.NOT_IN, value = "id")
    private List<Long> notIds;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 规则名称
     */
    @WrapperField(value = "name", type = WrapperType.EQ)
    private String checkName;

    /**
     * 规则名称
     */
    @WrapperField(value = "nameEn", type = WrapperType.EQ)
    private String checkNameEn;
    /**
     * 云平台
     */
    @WrapperField(value = "envCode", type = WrapperType.EQ)
    private String envCode;

    /**
     * 云平台
     */
    @WrapperField(value = "envCode", type = WrapperType.IN)
    private List<String> envCodes;

    /**
     * 云平台版本号
     */
    @WrapperField(value = "envVersion", type = WrapperType.EQ)
    private String envVersion;

    /**
     * 云平台版本号
     */
    @WrapperField(value = "envVersion", type = WrapperType.IN)
    private List<String> envVersions;
    /**
     * 资源类型编码
     */
    @WrapperField(type = WrapperType.EQ)
    private String resTypeCode;

    /**
     * 资源类型编码
     */
    @WrapperField("resTypeCode")
    private List<String> resTypeCodes;

    /**
     * 采集组件id
     */
    private Long opsExporterId;

    /**
     * 采集组件id
     */
    @WrapperField("opsExporterId")
    private List<Long> opsExporterIds;

    /**
     * 状态
     */
    @WrapperField(value = "status", type = WrapperType.EQ)
    private String status;
    /**
     * 描述
     */
    private String description;

    /**
     * 组装id
     */
    private Long orgId;
}
