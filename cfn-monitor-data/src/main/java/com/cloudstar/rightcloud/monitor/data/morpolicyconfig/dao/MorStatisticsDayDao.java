package com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dao;

import com.cloudstar.rightcloud.monitor.data.moranalysis.dto.query.MorOrgAnalysisPageQueryDto;
import com.cloudstar.rightcloud.monitor.data.moranalysis.dto.query.MorResAnalysisPageQueryDto;
import com.cloudstar.rightcloud.monitor.data.moranalysis.dto.query.MorResPoolAnalysisQueryDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsDayCreateCommonDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsDayCommonDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.query.MorStatisticsDayQueryDto;

import java.util.List;
import java.util.Map;

/**
 * 天统计接口
 *
 * @author: wanglang
 * @date: 2023/11/13 15:58
 */
public interface MorStatisticsDayDao {
    /**
     * 插入批量数据
     *
     * @param dtoList 数据集合
     * @return 操作结果
     */
    Boolean insertList(List<MorStatisticsDayCreateCommonDto> dtoList);

    /**
     * 删除数据
     *
     * @param query 查询条件
     * @return 操作结果
     */
    Boolean deleteByQueryDto(List<MorStatisticsDayQueryDto> query);


    /**
     * 查询基础数据
     *
     * @param queryDto 查询条件
     * @return MorStatisticsDayCommonDto 基础数据
     */
    List<MorStatisticsDayCommonDto> selectCommonList(MorStatisticsDayQueryDto queryDto);

    /**
     * 查询组织监控汇总
     *
     * @param dto 查询条件
     * @return 操作结果
     */
    List<Map<String, Object>> selectClickhouseOrgSummaryByParams(MorOrgAnalysisPageQueryDto dto);

    /**
     * 查询资源监控汇总
     *
     * @param dto 查询条件
     * @return 操作结果
     */
    List<Map<String, Object>> selectClickhouseResSummaryByParams(MorResAnalysisPageQueryDto dto);

    /**
     * 查询资源闲置率监控汇总
     */
    List<Map<String, Object>> selectClickhouseResPoolByParams(MorResPoolAnalysisQueryDto queryDto);

}
