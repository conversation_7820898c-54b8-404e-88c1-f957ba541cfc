package com.cloudstar.rightcloud.monitor.data.opsalarm.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.common.em.OperationStatus;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsNotifyTargetDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsNotifyTargetRelDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableNotifyTargetRelResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableNotifyTargetResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsAlarmRuleQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsNotifyPolicyQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsNotifyTargetQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsNotifyTargetRelQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.mapper.OpsNotifyPolicyMapper;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsAlarmRuleDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsNotifyPolicyDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsNotifyPolicyByRuleNameResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableAlarmRuleResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableNotifyPolicyResultDto;
import com.cloudstar.rightcloud.data.datascope.annotation.DataFilter;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>描述: [OpsNotifyPolicy 服务实现层] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@AllArgsConstructor
@Repository
public class OpsNotifyPolicyDaoImpl implements OpsNotifyPolicyDao {

    private final OpsNotifyPolicyMapper opsNotifyPolicyMapper;

    private final OpsAlarmRuleDao alarmRuleDao;

    private final OpsNotifyTargetRelDao opsNotifyTargetRelDao;

    private final OpsNotifyTargetDao opsNotifyTargetDao;


    @Override
    public RightCloudResult<List<OpsNotifyPolicyByRuleNameResultDto>> selectPolicyByRuleNameList(List<Long> ruleIds) {
        final List<OpsTableAlarmRuleResultDto> opsTableAlarmRuleResultDtos = alarmRuleDao.selectList(
                OpsAlarmRuleQueryDto.builder()
                        .ids(ruleIds)
                        .build()
        );
        List<OpsNotifyPolicyByRuleNameResultDto> opsNotifyPolicyByRuleNameResultDtos = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(opsTableAlarmRuleResultDtos)) {
            final List<Long> notifyPolicyIds = opsTableAlarmRuleResultDtos.stream()
                    .filter(Objects::nonNull)
                    .map(OpsTableAlarmRuleResultDto::getOpsNotifyPolicyId)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(notifyPolicyIds)) {
                final List<OpsTableNotifyPolicyResultDto> opsTableNotifyPolicyResultDtos = opsNotifyPolicyMapper.selectList(
                        Wrappers.<OpsTableNotifyPolicyResultDto>lambdaQuery()
                                .select(OpsTableNotifyPolicyResultDto::getId, OpsTableNotifyPolicyResultDto::getName)
                                .in(OpsTableNotifyPolicyResultDto::getId, notifyPolicyIds)
                );
                // 分组规则id和告警策略id
                if (CollectionUtil.isNotEmpty(opsTableNotifyPolicyResultDtos)) {
                    final Map<Long, String> notifyPolicyResultMap = opsTableNotifyPolicyResultDtos.stream()
                            .filter(Objects::nonNull)
                            .collect(Collectors.toMap(OpsTableNotifyPolicyResultDto::getId, OpsTableNotifyPolicyResultDto::getName));
                    for (OpsTableAlarmRuleResultDto opsTableAlarmRuleResultDto : opsTableAlarmRuleResultDtos) {
                        final String policyName = notifyPolicyResultMap.get(opsTableAlarmRuleResultDto.getOpsNotifyPolicyId());
                        if (StringUtils.isNotEmpty(policyName)) {
                            OpsNotifyPolicyByRuleNameResultDto policyByRuleIdResult = new OpsNotifyPolicyByRuleNameResultDto();
                            policyByRuleIdResult.setRuleId(opsTableAlarmRuleResultDto.getId());
                            policyByRuleIdResult.setPolicyName(policyName);
                            opsNotifyPolicyByRuleNameResultDtos.add(policyByRuleIdResult);
                        }
                    }
                }
            }
        }
        return RightCloudResult.success(opsNotifyPolicyByRuleNameResultDtos);
    }

    @Override
    public OpsTableNotifyPolicyResultDto selectPolicyByRuleName(Long id) {
        return opsNotifyPolicyMapper.selectOne(
                Wrappers.<OpsTableNotifyPolicyResultDto>lambdaQuery()
                        .eq(OpsTableNotifyPolicyResultDto::getId, id)
        );
    }

    @Override
    @DataFilter(ignoreProjectFilter = true, ignoreProjectEnvTypeFilter = true)
    public List<OpsTableNotifyPolicyResultDto> selectList(OpsNotifyPolicyQueryDto query) {
        return opsNotifyPolicyMapper.selectList(query);
    }

    @Override
    public List<OpsTableNotifyPolicyResultDto> selectListNoDataFilter(OpsNotifyPolicyQueryDto query) {
        return opsNotifyPolicyMapper.selectList(query);
    }

    @Override
    @DataFilter(ignoreProjectFilter = true, ignoreProjectEnvTypeFilter = true)
    public OpsTableNotifyPolicyResultDto selectOne(OpsNotifyPolicyQueryDto query) {
        return opsNotifyPolicyMapper.selectOne(query);
    }

    @Override
    public Boolean insertNotifyPolicy(OpsTableNotifyPolicyResultDto param) {
        return opsNotifyPolicyMapper.insert(param) == 1;
    }

    @Override
    public Boolean editNotifyPolicy(OpsTableNotifyPolicyResultDto paramDto) {
        return opsNotifyPolicyMapper.updateById(paramDto) == 1;
    }

    @Override
    public Boolean deleteByList(List<Long> ids) {
        return opsNotifyPolicyMapper.deleteBatchIds(ids) >= 1;
    }

    @Override
    public Boolean updateStatus(Long id, String status) {
        BizAssertUtils.notNull(id, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT, MonitorFieldKeyConstant.ID);
        final OperationStatus operationStatus = OperationStatus.buildAlarmStatus(status);
        BizAssertUtils.notNull(operationStatus, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                MonitorFieldKeyConstant.OPERATION_STATUS);
        final OpsTableNotifyPolicyResultDto opsTableNotifyPolicyResultDto = new OpsTableNotifyPolicyResultDto();
        opsTableNotifyPolicyResultDto.setStatus(status);
        // 更新状态
        return opsNotifyPolicyMapper.update(opsTableNotifyPolicyResultDto, Wrappers.<OpsTableNotifyPolicyResultDto>lambdaUpdate()
                .eq(OpsTableNotifyPolicyResultDto::getId, id)) > 1;
    }

    @Override
    public Boolean checkNotifyTarget(List<Long> notifyIds) {
        // 获取通知对象数据
        Boolean result = true;
        if (CollectionUtil.isNotEmpty(notifyIds)) {
            final List<OpsTableNotifyTargetRelResultDto> opsTableNotifyTargetRelResultDtoList = opsNotifyTargetRelDao.selectList(
                    OpsNotifyTargetRelQueryDto.builder()
                            .notifyIds(notifyIds)
                            .build()
            );
            if (CollectionUtil.isNotEmpty(opsTableNotifyTargetRelResultDtoList)) {
                final List<Long> notifyTargetIdList = opsTableNotifyTargetRelResultDtoList.stream()
                        .filter(Objects::nonNull)
                        .map(OpsTableNotifyTargetRelResultDto::getOpsNotifyTargetId)
                        .collect(Collectors.toList());

                if (CollectionUtil.isNotEmpty(notifyTargetIdList)) {
                    final List<OpsTableNotifyTargetResultDto> opsTableNotifyTargetResultDtoList = opsNotifyTargetDao.selectList(
                            OpsNotifyTargetQueryDto.builder()
                                    .ids(notifyTargetIdList)
                                    .build()
                    );
                    result = CollectionUtil.isEmpty(opsTableNotifyTargetResultDtoList);
                }
            }
        }
        return result;
    }
}
