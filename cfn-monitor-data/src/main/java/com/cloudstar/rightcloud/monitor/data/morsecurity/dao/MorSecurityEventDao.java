package com.cloudstar.rightcloud.monitor.data.morsecurity.dao;

import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.monitor.data.morsecurity.dto.MorSecurityEvent;
import com.cloudstar.rightcloud.monitor.data.morsecurity.dto.MorSecurityEventQueryDto;
import com.cloudstar.rightcloud.monitor.data.morsecurity.dto.MorSecurityEventResultDto;

import java.util.List;

public interface MorSecurityEventDao {

    public void insert(MorSecurityEvent morSecurityEvent);

    public List<MorSecurityEvent> list(MorSecurityEventQueryDto dto);

    public void deleteById(Long id);

    public void updateById(MorSecurityEvent morSecurityEvent);

    public MorSecurityEvent selectById(Long id);

    public PageResult<MorSecurityEventResultDto> page(MorSecurityEventQueryDto dto);

    public void confirm(MorSecurityEvent morSecurityEvent);

    public void resolve(MorSecurityEvent morSecurityEvent);

    void clean(MorSecurityEvent morSecurityEvent);

}
