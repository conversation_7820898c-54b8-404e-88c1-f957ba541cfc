package com.cloudstar.rightcloud.monitor.data.morpolicyconfig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsDayMetricStrategyInfoDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsDayMetricStrategyPageDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorTableStatisticsDayMetricStrategyDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.query.MorStatisticDayMetricPolicyPageQueryDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 天性能汇总指标策略配置
 *
 * @author: wanglang
 * @date: 2023/11/3 15:50
 */
public interface MorStatisticsDayMetricStrategyMapper extends BaseMapper<MorTableStatisticsDayMetricStrategyDto> {

    List<MorStatisticsDayMetricStrategyPageDto> queryList(@Param("dto") MorStatisticDayMetricPolicyPageQueryDto dto);

    MorStatisticsDayMetricStrategyInfoDto queryById(Long id);
}
