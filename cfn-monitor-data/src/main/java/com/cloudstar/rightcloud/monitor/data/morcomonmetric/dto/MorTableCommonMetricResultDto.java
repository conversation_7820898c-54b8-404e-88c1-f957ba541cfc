package com.cloudstar.rightcloud.monitor.data.morcomonmetric.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 通用指标
 *
 * @author: wanglang
 * @date: 2023/11/22 17:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("monitor_common_metric")
public class MorTableCommonMetricResultDto extends MapperAutoObject implements Serializable {
    /**
     * id;id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 通用指标名称
     */
    private String name;
    /**
     * 通用指标名称
     */
    private String nameEn;
    /**
     * 通用指标编码
     */
    private String code;

    /**
     * 资源类型编码
     */
    private String resTypeCode;
    /**
     * 状态
     */
    private String status;
    /**
     * 指标单位：bps/kbps/%等
     */
    private String unit;
    /**
     * 指标单位：bps/kbps/%等
     */
    private String unitEn;
    /**
     * 排序编码
     */
    private Integer sortRank;
    /**
     * 描述
     */
    private String description;
    /**
     * 描述
     */
    private String descriptionEn;
}
