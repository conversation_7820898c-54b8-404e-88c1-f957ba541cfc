package com.cloudstar.rightcloud.monitor.data.morpolicyconfig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsHourMetricStrategyInfoDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsHourMetricStrategyPageDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorTableStatisticsHourMetricStrategyDto;

import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.query.MorStatisticsHourMetricStrategyQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 日性能汇总指标策略配置
 *
 * @author: wanglang
 * @date: 2023/11/3 15:50
 */
public interface MorStatisticsHourMetricStrategyMapper extends BaseMapper<MorTableStatisticsHourMetricStrategyDto> {

    List<MorStatisticsHourMetricStrategyPageDto> queryList(@Param("dto") MorStatisticsHourMetricStrategyQueryDto dto);

    MorStatisticsHourMetricStrategyInfoDto queryById(Long id);
}
