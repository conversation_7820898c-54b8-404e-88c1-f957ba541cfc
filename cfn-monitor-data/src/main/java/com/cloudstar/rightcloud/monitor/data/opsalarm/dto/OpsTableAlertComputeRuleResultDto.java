package com.cloudstar.rightcloud.monitor.data.opsalarm.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>描述: [OpsAlarmRule 实体类] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("monitor_alert_compute_rules")
public class OpsTableAlertComputeRuleResultDto extends MapperAutoObject implements Serializable {
    /**
     * id;id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 告警名称
     */
    private String name;

    /**
     * 告警规则配置
     */
    private String ruleConfig;
}
