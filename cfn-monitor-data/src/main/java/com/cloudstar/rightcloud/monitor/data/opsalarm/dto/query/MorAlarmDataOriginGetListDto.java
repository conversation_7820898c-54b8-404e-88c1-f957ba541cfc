package com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 原生告警列表查询数据
 *
 * @author: wanglang
 * @date: 2024/4/7 15:16
 */
@Data
public class MorAlarmDataOriginGetListDto {

    /**
     * 开始发生时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startOccurTime;
    /**
     * 结束发生时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endOccurTime;


    /**
     * 持续时长
     */
    private String duration;

    /**
     * 告警名称
     */
    private String name;


    /**
     * 云环境id
     */
    private List<String> envIds;
}
