package com.cloudstar.rightcloud.monitor.data.opsalarm.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsAlarmBlockingRuleDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableAlarmBlockingRuleResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsAlarmBlockingRuleQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.mapper.OpsAlarmBlockingRuleMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * 告警屏蔽规则
 *
 * @author: 卢泳舟
 * @date: 2023/6/7 13:57
 */
@Repository
public class OpsAlarmBlockingRuleDaoImpl implements OpsAlarmBlockingRuleDao {

    @Resource
    private OpsAlarmBlockingRuleMapper opsAlarmBlockingRuleMapper;

    @Override
    public List<OpsTableAlarmBlockingRuleResultDto> selectList(OpsAlarmBlockingRuleQueryDto query) {
        return opsAlarmBlockingRuleMapper.selectList(query);
    }

    @Override
    public OpsTableAlarmBlockingRuleResultDto selectOne(OpsAlarmBlockingRuleQueryDto query) {
        return opsAlarmBlockingRuleMapper.selectOne(query);
    }


    @Override
    public Page<OpsTableAlarmBlockingRuleResultDto> selectBlockingRulePage(Page<OpsTableAlarmBlockingRuleResultDto> page,
                                                                           OpsAlarmBlockingRuleQueryDto query) {
        return opsAlarmBlockingRuleMapper.selectPage(page, query);
    }

    @Override
    public Boolean updateBlockingStatus(OpsTableAlarmBlockingRuleResultDto statusDto) {
        return opsAlarmBlockingRuleMapper.updateById(statusDto) == 1;
    }
}
