package com.cloudstar.rightcloud.monitor.data.opsalarm.mapper;

import com.cloudstar.rightcloud.monitor.data.mybatisplus.MonitorMapper;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsContactsResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsGroupResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableNotifyTargetContactsGroupResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsNotifyTargetContactsGroupQueryDto;

import java.util.List;

import io.lettuce.core.dynamic.annotation.Param;

/**
 * 通知对象联系人组
 *
 * @author: wanglang
 * @date: 2023/2/14 4:23 PM
 */

public interface OpsNotifyTargetContactsGroupMapper extends MonitorMapper<OpsTableNotifyTargetContactsGroupResultDto,
        OpsNotifyTargetContactsGroupQueryDto> {

    /**
     * 根据通知组id获取告警通知组联系人
     *
     * @param list 通知组id
     * @return the list
     */
    List<OpsContactsResultDto> selectGroupContacts(@Param("list") List<Long> list);

    /**
     * 根据联系人id获取其关联的通知组
     *
     * @param list 联系人id
     * @return the list
     */
    List<OpsGroupResultDto> selectContactsGroup(List<Long> list);

    /**
     * 根据通知组id获取当前通知组已关联的联系人
     *
     * @param list 通知组id
     * @return the list
     */
    List<OpsContactsResultDto> selectGroupContactsByGroupId(@Param("list") List<Long> list);
}
