package com.cloudstar.rightcloud.monitor.data.moroptimization.dao;


import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.monitor.data.moroptimization.dto.OptimizationData;
import com.cloudstar.rightcloud.monitor.data.moroptimization.dto.OptimizationDataListDto;

import java.util.List;

/**
 * 优化建议数据
 *
 * <AUTHOR> Lesao
 * @date : 2023/11/29
 */
public interface OptimizationDataDao {

    /**
     * 新增
     *
     * @param optimizationData 优化建议数据
     */
    void insert(OptimizationData optimizationData);

    /**
     * 根据策略id查询
     *
     * @param strategyId 策略id
     * @return 优化建议数据
     */
    List<OptimizationData> getByStrategyId(Long strategyId);

    /**
     * 根据策略id删除数据
     *
     * @param strategyId 策略id
     */
    void deleteByStrategyId(Long strategyId);

    /**
     * 批量删除
     *
     * @param ids 建议id
     */
    void batchDelete(List<Long> ids);

    /**
     * 分页查询
     *
     * @param listDto 参数
     * @return 出参
     */
    PageResult<OptimizationData> page(OptimizationDataListDto listDto);

    /**
     * 分页查询
     *
     * @param listDto 参数
     * @return 出参
     */
    List<OptimizationData> list(OptimizationDataListDto listDto);

    /**
     * 列表查询（权限）
     *
     * @param listDto 参数
     * @return 出参
     */
    List<OptimizationData> dataFilterList(OptimizationDataListDto listDto);

    /**
     * 根据id查询
     *
     * @param id id
     */
    OptimizationData getById(Long id);
}
