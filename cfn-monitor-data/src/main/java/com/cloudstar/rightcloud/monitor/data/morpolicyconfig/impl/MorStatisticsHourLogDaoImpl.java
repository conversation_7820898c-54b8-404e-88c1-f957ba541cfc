package com.cloudstar.rightcloud.monitor.data.morpolicyconfig.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.Db;
import com.cloudstar.rightcloud.common.properties.CommonProperties;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dao.MorStatisticsHourLogDao;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsHourLogPageDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorTableStatisticsHourLogDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.query.MorStatisticsHourLogPageQueryDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.query.MorStatisticsHourLogQueryDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.mapper.MorStatisticsHourLogMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 性能分析统计日志
 *
 * @author: wanglang
 * @date: 2023/11/6 11:00
 */
@Repository
//@AllArgsConstructor
public class MorStatisticsHourLogDaoImpl implements MorStatisticsHourLogDao {


    @Resource
    private MorStatisticsHourLogMapper morStatisticsHourLogMapper;

    @Resource
    private CommonProperties commonProperties;

    @Override
    public Date selectMaxMergeTime() {
        return morStatisticsHourLogMapper.selectMaxMergeTime();
    }

    @Override
    public List<MorTableStatisticsHourLogDto> selectList(MorStatisticsHourLogQueryDto queryDto) {
        final LambdaQueryWrapper<MorTableStatisticsHourLogDto> wrapper = Wrappers.<MorTableStatisticsHourLogDto>lambdaQuery()
                .eq(Objects.nonNull(queryDto.getId()), MorTableStatisticsHourLogDto::getId, queryDto.getId())
                .eq(Objects.nonNull(queryDto.getResTypeCode()), MorTableStatisticsHourLogDto::getResTypeCode,
                        queryDto.getResTypeCode())
                .eq(Objects.nonNull(queryDto.getPerformanceMetricIndicatorCode()),
                        MorTableStatisticsHourLogDto::getPerformanceMetricIndicatorCode,
                        queryDto.getPerformanceMetricIndicatorCode())
                .eq(Objects.nonNull(queryDto.getStatus()), MorTableStatisticsHourLogDto::getStatus, queryDto.getStatus())
                .in(CollectionUtil.isNotEmpty(queryDto.getStatusList()), MorTableStatisticsHourLogDto::getStatus, queryDto.getStatusList())
                .eq(Objects.nonNull(queryDto.getMergeTime()), MorTableStatisticsHourLogDto::getMergeTime, queryDto.getMergeTime())
                .ge(Objects.nonNull(queryDto.getMergeStartTime()), MorTableStatisticsHourLogDto::getMergeTime, queryDto.getMergeStartTime())
                .le(Objects.nonNull(queryDto.getMergeEndTime()), MorTableStatisticsHourLogDto::getMergeTime, queryDto.getMergeEndTime())
                .eq(Objects.nonNull(queryDto.getStartTime()), MorTableStatisticsHourLogDto::getStartTime, queryDto.getStartTime())
                .eq(Objects.nonNull(queryDto.getEndTime()), MorTableStatisticsHourLogDto::getEndTime, queryDto.getEndTime())
                .like(Objects.nonNull(queryDto.getErrorMsg()), MorTableStatisticsHourLogDto::getErrorMsg, queryDto.getErrorMsg())
                .like(Objects.nonNull(queryDto.getDescription()), MorTableStatisticsHourLogDto::getDescription, queryDto.getDescription());
        return morStatisticsHourLogMapper.selectList(wrapper);
    }

    @Override
    public List<MorTableStatisticsHourLogDto> selectByIdsList(List<Long> ids) {
        final LambdaQueryWrapper<MorTableStatisticsHourLogDto> wrapper = Wrappers.<MorTableStatisticsHourLogDto>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(ids), MorTableStatisticsHourLogDto::getId, ids);
        return morStatisticsHourLogMapper.selectList(wrapper);
    }

    @Override
    public List<MorTableStatisticsHourLogDto> selectByMetricIndicatorCodeList(List<String> performanceMetricIndicatorCodes) {
        final LambdaQueryWrapper<MorTableStatisticsHourLogDto> wrapper = Wrappers.<MorTableStatisticsHourLogDto>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(performanceMetricIndicatorCodes),
                        MorTableStatisticsHourLogDto::getPerformanceMetricIndicatorCode, performanceMetricIndicatorCodes);
        return morStatisticsHourLogMapper.selectList(wrapper);
    }

    @Override
    public List<MorTableStatisticsHourLogDto> selectByCollectMetricIdsList(List<Long> collectMetricIds) {
        final LambdaQueryWrapper<MorTableStatisticsHourLogDto> wrapper = Wrappers.<MorTableStatisticsHourLogDto>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(collectMetricIds), MorTableStatisticsHourLogDto::getMonitorCollectMetricId,
                        collectMetricIds);
        return morStatisticsHourLogMapper.selectList(wrapper);
    }

    @Override
    public List<MorTableStatisticsHourLogDto> selectByStatusList(List<String> status) {
        final LambdaQueryWrapper<MorTableStatisticsHourLogDto> wrapper = Wrappers.<MorTableStatisticsHourLogDto>lambdaQuery()
                .in(CollectionUtil.isNotEmpty(status), MorTableStatisticsHourLogDto::getStatus, status);
        return morStatisticsHourLogMapper.selectList(wrapper);
    }

    @Override
    public Boolean insertList(List<MorTableStatisticsHourLogDto> dtoList) {
        return Db.saveBatch(dtoList, commonProperties.getBatchSize());
    }

    @Override
    public Boolean updateList(List<MorTableStatisticsHourLogDto> dtoList) {
        return Db.updateBatchById(dtoList, commonProperties.getBatchSize());
    }

    @Override
    public MorTableStatisticsHourLogDto selectById(Long id) {
        return morStatisticsHourLogMapper.selectById(id);
    }

    @Override
    public Boolean updateById(MorTableStatisticsHourLogDto entity) {
        return morStatisticsHourLogMapper.updateById(entity) > 0;
    }

    /**
     * 查询小时汇聚任务日志列表(查询了关联的监控指标
     *
     * @param dto 查询参数
     */
    @Override
    public List<MorStatisticsHourLogPageDto> queryList(MorStatisticsHourLogPageQueryDto dto) {
        return morStatisticsHourLogMapper.queryList(dto);
    }

    /**
     * 批量删除小时汇聚任务日志信息
     * @param logIds 小时汇聚任务日志ID集合
     */
    @Override
    public Boolean deleteBatchIds(List<Long> logIds) {
        return morStatisticsHourLogMapper.deleteBatchIds(logIds) > 0;
    }
}
