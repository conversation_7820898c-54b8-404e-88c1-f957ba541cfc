package com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dao;

import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsHourMetricStrategyInfoDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsHourMetricStrategyPageDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorTableStatisticsHourMetricStrategyDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.query.MorStatisticsHourMetricStrategyQueryDto;

import java.util.List;

/**
 * 小时性能汇总指标策略配置
 *
 * @author: wanglang
 * @date: 2023/11/3 15:50
 */
public interface MorStatisticsHourMetricStrategyDao {

    /**
     * 查询列表
     *
     * @param queryDto 查询参数
     * @return MorTableHourStatisticsMetricStrategyDto 配置策略
     */
    List<MorTableStatisticsHourMetricStrategyDto> selectList(MorStatisticsHourMetricStrategyQueryDto queryDto);

    /**
     * 查询列表
     *
     * @param ids 查询参数
     * @return MorTableHourStatisticsMetricStrategyDto 配置策略
     */
    List<MorTableStatisticsHourMetricStrategyDto> selectByIdsList(List<Long> ids);


    /**
     * 查询列表
     *
     * @param performanceMetricIndicatorCodes 统计指标项
     * @return MorTableHourStatisticsMetricStrategyDto 配置策略
     */
    List<MorTableStatisticsHourMetricStrategyDto> selectByMetricIndicatorCodeList(List<String> performanceMetricIndicatorCodes);


    /**
     * 查询小时性能汇总指标策略列表(查询了关联的监控指标)
     *
     * @param dto 查询参数
     */
    List<MorStatisticsHourMetricStrategyPageDto> queryList(MorStatisticsHourMetricStrategyQueryDto dto);

    /**
     * 查询小时性能汇总指标策略详情
     *
     * @param id id
     */
    MorStatisticsHourMetricStrategyInfoDto queryById(Long id);

    /**
     * 插入属性不为空的记录
     *
     * @param dto 参数
     * @return MorTableHourStatisticsMetricStrategyDto 配置策略
     */
    Boolean insert(MorTableStatisticsHourMetricStrategyDto dto);

    /**
     * 根据主键更新
     *
     * @param dto 参数
     * @return MorTableHourStatisticsMetricStrategyDto 配置策略
     */
    Boolean update(MorTableStatisticsHourMetricStrategyDto dto);

    /**
     * 根据ID删除
     *
     * @param id ID
     * @return 删除结果
     */
    Boolean deleteById(Long id);

    /**
     * 统计数量
     * @param dto 查询条件
     *
     * @return 总数
     */
    long selectCount(MorStatisticsHourMetricStrategyQueryDto dto);


}
