package com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto;

import com.cloudstar.rightcloud.common.pojo.page.PageForm;
import com.cloudstar.rightcloud.monitor.common.annotation.Wrapper;
import com.cloudstar.rightcloud.monitor.common.annotation.WrapperField;
import com.cloudstar.rightcloud.monitor.common.em.WrapperType;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 考核规则策略实体类
 *
 * @author: hjy
 * @date: 2023/10/31 17:30
 */
@Data
@Wrapper(orderDescField = {"examineType", "examineItem"})
public class MorExamineRuleStrategyPageDto extends PageForm implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 组织id
     */
    private Long orgId;

    /**
     * 考核类型EXAMINE_RESOURCE_TYPE
     */
    @WrapperField(type = WrapperType.EQ)
    private String examineType;

    /**
     * 评分项,云主机考核项/弹性IP考核项/云数据库考核项
     */
    @WrapperField(type = WrapperType.EQ)
    private String examineItem;

    /**
     * 评分规则列表,运算类型OPS_COLLECT_RULE_CONDITION_OPERATOR
     */
    @WrapperField(type = WrapperType.EQ)
    private String examineRule;

    /**
     * 统计方式
     */
    @WrapperField(type = WrapperType.EQ)
    private String statisticType;

    /**
     * 状态
     */
    @WrapperField(type = WrapperType.EQ)
    private String status;

    /**
     * 描述
     */
    private String description;

    /**
     * 创建用户
     */
    private String createdBy;

    /**
     * 记录创建时间
     */
    private Date createdDt;

    /**
     * 最后修改用户
     */
    private String updatedBy;

    /**
     * 记录修改时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

}
