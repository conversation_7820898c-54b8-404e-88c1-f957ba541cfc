package com.cloudstar.rightcloud.monitor.data.opscollectrule.dao;


import cn.hutool.core.collection.CollectionUtil;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.monitor.data.opscollectrule.dto.OpsTableCollectMetricResultDto;
import com.cloudstar.rightcloud.monitor.data.opscollectrule.dto.query.OpsCollectMetricQueryDto;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.Supplier;

/**
 * 采集指标接口
 *
 * @author: wanglang
 * @date: 2023/4/19 5:13 PM
 */
public interface OpsCollectMetricDao {
    /**
     * 查询列表数据
     *
     * @param query 条件构造器
     * @return OpsTableNotifyUpgradePolicyResultDTO 采集任务
     */
    List<OpsTableCollectMetricResultDto> selectList(OpsCollectMetricQueryDto query);

    /**
     * 查询列表数据
     *
     * @param query 条件构造器
     * @return OpsTableNotifyUpgradePolicyResultDTO 采集任务
     */
    List<OpsTableCollectMetricResultDto> selectList(OpsTableCollectMetricResultDto query);


    /**
     * 默认查询列表数据
     *
     * @param query          条件构造器
     * @param targetSupplier 转换类型
     * @return T 采集任务
     */
    default <H> List<H> selectList(Supplier<H> targetSupplier, Boolean dataFilter, OpsCollectMetricQueryDto query) {
        List<OpsTableCollectMetricResultDto> opsTableCollectMetricResultDtoList = new ArrayList<>();
        if (dataFilter) {
            opsTableCollectMetricResultDtoList = this.selectList(query);
        } else {
            opsTableCollectMetricResultDtoList = this.selectList(query);
        }
        List<H> result = BeanHelperUtil.copyForList(targetSupplier, opsTableCollectMetricResultDtoList);
        if (CollectionUtil.isEmpty(result)) {
            result = new ArrayList<>();
        }
        return result;
    }

    /**
     * 根据 Wrapper 条件，查询总记录数
     *
     * @param query 实体对象封装操作类（可以为 null）
     * @return Long 条数
     */
    Long selectCount(OpsCollectMetricQueryDto query);

    /**
     * 查询列表数据
     *
     * @param query 条件构造器
     * @return OpsTableNotifyUpgradePolicyResultDTO 采集任务
     */
    List<OpsTableCollectMetricResultDto> selectListNoDataFilter(OpsCollectMetricQueryDto query);


    /**
     * 修改状态
     *
     * @param id     id
     * @param status 状态
     * @return
     */
    Boolean updateStatus(Long id, String status);




    /**
     * 更新指标数据
     *
     * @param opsTableCollectMetricResultDto 监控指标
     * @return true 更新成功 false 更新失败
     */
    Boolean update(OpsTableCollectMetricResultDto opsTableCollectMetricResultDto);


    /**
     * 插入指标数据
     *
     * @param opsTableCollectMetricResultDto 监控指标
     * @return true 添加成功 false 添加失败
     */
    Boolean insert(OpsTableCollectMetricResultDto opsTableCollectMetricResultDto);


    /**
     * 查询单挑数据
     *
     * @param query 条件构造器
     * @return OpsTableNotifyUpgradePolicyResultDTO 采集任务
     */
    OpsTableCollectMetricResultDto selectOne(OpsCollectMetricQueryDto query);

    /**
     * 查询单个数据
     *
     * @param query 查询条件
     * @return OpsTableCollectMetricResultDto 指标数据
     */
    OpsTableCollectMetricResultDto selectOne(OpsTableCollectMetricResultDto query);

    /**
     * 查询单挑数据
     *
     * @param query 条件构造器
     * @return OpsTableNotifyUpgradePolicyResultDTO 采集任务
     */
    OpsTableCollectMetricResultDto selectOneNoDataFilter(OpsCollectMetricQueryDto query);


    Boolean updateRuleInfo(Long ruleId, String envCode, String envVersion, String resTypeCode);

    /**
     * 查询（根据ID 批量查询）
     *
     * @param idList 主键ID列表(不能为 null 以及 empty)
     * @return OpsTableCollectMetricResultDto 告警指标数据
     */
    List<OpsTableCollectMetricResultDto> selectBatchIds(Collection<Long> idList);


    /**
     * 批量根据id删除数据
     *
     * @param idList id
     * @return true 删除成功 false 删除失败
     */
    Boolean deleteBatchIds(Collection<Long> idList);




}
