package com.cloudstar.rightcloud.monitor.data.opsalarm.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloudstar.rightcloud.data.datascope.annotation.DataFilter;
import com.cloudstar.rightcloud.monitor.common.em.AlarmStatus;
import com.cloudstar.rightcloud.monitor.common.util.StringUtils;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsAlarmDataOriginDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableAlarmDataOriginResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.MorAlarmDataOriginGetListDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsAlarmDataOriginQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.mapper.OpsAlarmDataOriginMapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 原始告警数据接口
 *
 * @author: wanglang
 * @date: 2023/7/28 15:35
 */
@AllArgsConstructor
@Repository
public class OpsAlarmDataOriginDaoImpl implements OpsAlarmDataOriginDao {

    private final OpsAlarmDataOriginMapper opsAlarmDataOriginMapper;

    @Override
    @DataFilter(ignoreUserScope = true, ignoreProjectEnvTypeFilter = true)
    public List<OpsTableAlarmDataOriginResultDto> selectList(OpsAlarmDataOriginQueryDto query) {
        return opsAlarmDataOriginMapper.selectList(this.getLambdaQueryWrapper(query));
    }

    @Override
    public List<OpsTableAlarmDataOriginResultDto> selectList(MorAlarmDataOriginGetListDto dto) {
        Date durationStartTime = null;
        Date durationEndTime = null;
        if (StringUtils.isNotEmpty(dto.getDuration())) {
            final Calendar newInstance = Calendar.getInstance();
            newInstance.setTime(new Date());
            newInstance.add(Calendar.MINUTE, Integer.parseInt("-" + dto.getDuration()));
            durationStartTime = newInstance.getTime();
            durationEndTime = new Date();
        }
        return opsAlarmDataOriginMapper.selectList(
                Wrappers.<OpsTableAlarmDataOriginResultDto>lambdaQuery()
                        .like(StringUtils.isNotEmpty(dto.getName()), OpsTableAlarmDataOriginResultDto::getName, dto.getName())
                        .ge(Objects.nonNull(dto.getStartOccurTime()), OpsTableAlarmDataOriginResultDto::getStartTime, dto.getStartOccurTime())
                        .le(Objects.nonNull(dto.getEndOccurTime()), OpsTableAlarmDataOriginResultDto::getStartTime, dto.getEndOccurTime())
                        .ge(Objects.nonNull(durationStartTime), OpsTableAlarmDataOriginResultDto::getLastTime, durationStartTime)
                        .le(Objects.nonNull(durationEndTime), OpsTableAlarmDataOriginResultDto::getLastTime, durationEndTime)
                        .in(CollectionUtil.isNotEmpty(dto.getEnvIds()), OpsTableAlarmDataOriginResultDto::getEnvId, dto.getEnvIds())
        );
    }

    @Override
    @DataFilter(ignoreUserScope = true, ignoreProjectEnvTypeFilter = true)
    public OpsTableAlarmDataOriginResultDto selectOne(OpsAlarmDataOriginQueryDto query) {
        return opsAlarmDataOriginMapper.selectOne(this.getLambdaQueryWrapper(query));
    }

    @Override
    @DataFilter(ignoreUserScope = true, ignoreProjectEnvTypeFilter = true)
    public Long selectCount(OpsAlarmDataOriginQueryDto query) {
        return opsAlarmDataOriginMapper.selectCount(this.getLambdaQueryWrapper(query));
    }

    @Override
    public Boolean checkRepetition(OpsAlarmDataOriginQueryDto query) {
        return this.selectCount(query) > 0;
    }

    @Override
    public Boolean deleteBatchIds(Collection<?> idList) {
        return opsAlarmDataOriginMapper.deleteBatchIds(idList) > 0;
    }

    @Override
    public Boolean updateAlarmDataOrigin(OpsTableAlarmDataOriginResultDto entity, OpsAlarmDataOriginQueryDto query) {
        return opsAlarmDataOriginMapper.update(entity, this.getLambdaQueryWrapper(query)) > 0;
    }

    @Override
    public Boolean updateAlarmDataOriginList(List<OpsTableAlarmDataOriginResultDto> entityList) {
        return opsAlarmDataOriginMapper.updateBatch(entityList);
    }

    @Override
    public Boolean insertAlarmDataOrigin(OpsTableAlarmDataOriginResultDto entity) {
        return opsAlarmDataOriginMapper.insert(entity) > 0;
    }

    @Override
    public Boolean insertAlarmDataOriginList(List<OpsTableAlarmDataOriginResultDto> entityList) {
        return opsAlarmDataOriginMapper.insertBatch(entityList);
    }


    @Override
    public List<String> getAlarmDataOriginEnvId(MorAlarmDataOriginGetListDto dto) {
        Date durationStartTime = null;
        Date durationEndTime = null;
        List<String> envIds = new ArrayList<>();
        if (StringUtils.isNotEmpty(dto.getDuration())) {
            final Calendar newInstance = Calendar.getInstance();
            newInstance.setTime(new Date());
            newInstance.add(Calendar.MINUTE, Integer.parseInt("-" + dto.getDuration()));
            durationStartTime = newInstance.getTime();
            durationEndTime = new Date();
        }
        final List<OpsTableAlarmDataOriginResultDto> opsTableAlarmDataOriginResultDtoList = opsAlarmDataOriginMapper.selectList(
                Wrappers.<OpsTableAlarmDataOriginResultDto>lambdaQuery()
                        .select(OpsTableAlarmDataOriginResultDto::getEnvId)
                        .like(StringUtils.isNotEmpty(dto.getName()), OpsTableAlarmDataOriginResultDto::getName, dto.getName())
                        .ge(Objects.nonNull(dto.getStartOccurTime()), OpsTableAlarmDataOriginResultDto::getStartTime, dto.getStartOccurTime())
                        .le(Objects.nonNull(dto.getEndOccurTime()), OpsTableAlarmDataOriginResultDto::getStartTime, dto.getEndOccurTime())
                        .ge(Objects.nonNull(durationStartTime), OpsTableAlarmDataOriginResultDto::getLastTime, durationStartTime)
                        .le(Objects.nonNull(durationEndTime), OpsTableAlarmDataOriginResultDto::getLastTime, durationEndTime)
        );
        if (CollectionUtil.isNotEmpty(opsTableAlarmDataOriginResultDtoList)) {
            envIds = opsTableAlarmDataOriginResultDtoList.stream()
                    .filter(Objects::nonNull)
                    .map(OpsTableAlarmDataOriginResultDto::getEnvId)
                    .map(String::valueOf)
                    .distinct()
                    .collect(Collectors.toList());

        }
        return envIds;
    }

    private QueryWrapper<OpsTableAlarmDataOriginResultDto> getLambdaQueryWrapper(OpsAlarmDataOriginQueryDto query) {
        final QueryWrapper<OpsTableAlarmDataOriginResultDto> queryWrapper = opsAlarmDataOriginMapper.getQueryWrapper(query);
        queryWrapper.ge(Objects.nonNull(query.getStartLastTime()), "lastTime", query.getStartLastTime());
        queryWrapper.le(Objects.nonNull(query.getEndLastTime()), "lastTime", query.getEndLastTime());
        queryWrapper.ge(Objects.nonNull(query.getStarRecoveredTime()), "recoveredTime", query.getStarRecoveredTime());
        queryWrapper.le(Objects.nonNull(query.getEndRecoveredTime()), "recoveredTime", query.getEndRecoveredTime());
        queryWrapper.notIn("status", AlarmStatus.DELETE.status);
        return queryWrapper;
    }


}
