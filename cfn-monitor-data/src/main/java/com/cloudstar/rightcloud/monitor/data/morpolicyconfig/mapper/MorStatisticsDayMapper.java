package com.cloudstar.rightcloud.monitor.data.morpolicyconfig.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudstar.rightcloud.monitor.data.moranalysis.dto.query.MorOrgAnalysisPageQueryDto;
import com.cloudstar.rightcloud.monitor.data.moranalysis.dto.query.MorResAnalysisPageQueryDto;
import com.cloudstar.rightcloud.monitor.data.moranalysis.dto.query.MorResPoolAnalysisQueryDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsDayCommonDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorTableStatisticsDayDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.query.MorStatisticsDayQueryDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 天统计mapper接口
 *
 * @author: wanglang
 * @date: 2023/11/13 15:53
 */
@DS("clickhouse")
public interface MorStatisticsDayMapper extends BaseMapper<MorTableStatisticsDayDto> {

    /**
     * 删除数据
     *
     * @param query 查询条件
     * @return 操作结果
     */
    Integer deleteByQueryDto(@Param("query") MorStatisticsDayQueryDto query, @Param("tableName") String tableName);

    /**
     * 批量动态插入数据
     *
     * @param dto 数据
     * @return 操作结果
     */
    Integer insert(@Param("dto") MorTableStatisticsDayDto dto, @Param("tableName") String tableName);

    /**
     * 查询组织监控汇总
     */
    List<Map<String, Object>> selectClickhouseOrgSummaryByParams(@Param("condition")
                                                                         MorOrgAnalysisPageQueryDto condition);

    /**
     * 查询基础数据
     *
     * @param queryDto 查询条件
     * @return MorStatisticsDayCommonDto 基础数据
     */
    List<MorStatisticsDayCommonDto> selectCommonList(@Param("query") MorStatisticsDayQueryDto queryDto);

    /**
     * 查询资源监控汇总
     */
    List<Map<String, Object>> selectClickhouseResSummaryByParams(@Param("condition")
                                                                         MorResAnalysisPageQueryDto condition);

    /**
     * 查询资源闲置率监控汇总
     */
    List<Map<String, Object>> selectClickhouseResPoolByParams(@Param("condition")
                                                                      MorResPoolAnalysisQueryDto condition);

}
