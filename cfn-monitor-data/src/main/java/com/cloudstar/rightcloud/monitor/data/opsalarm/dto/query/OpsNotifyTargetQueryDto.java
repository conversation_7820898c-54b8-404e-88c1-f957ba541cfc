package com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query;


import com.cloudstar.rightcloud.monitor.common.annotation.Wrapper;
import com.cloudstar.rightcloud.monitor.common.annotation.WrapperField;
import com.cloudstar.rightcloud.monitor.common.em.WrapperType;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 通知对象
 *
 * @author: wanglang
 * @date: 2023/2/14 4:23 PM
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Wrapper(orderDescField = {"createdDt", "updatedDt"})
public class OpsNotifyTargetQueryDto extends MapperAutoObject implements Serializable {
    /**
     * id;id
     */
    private Long id;

    /**
     * id;id
     */
    @WrapperField(type = WrapperType.IN, value = "id")
    private List<Long> ids;

    /**
     * 通知策略id;包含升级策略、通知策略
     */
    private Long notifyPolicyId;

    /**
     * 通知策略id;包含升级策略、通知策略
     */
    @WrapperField(type = WrapperType.IN, value = "notifyPolicyId")
    private List<Long> notifyPolicyIds;

    /**
     * 通知方式;mail 邮件、note 短信 newsletter 站内信，支持多个，多个用,分隔
     */
    @WrapperField(type = WrapperType.EQ)
    private String notifyWay;


}
