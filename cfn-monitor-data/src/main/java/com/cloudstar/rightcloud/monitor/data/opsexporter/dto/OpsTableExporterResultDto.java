package com.cloudstar.rightcloud.monitor.data.opsexporter.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.Data;

import java.io.Serializable;

/**
 * 监控采集exporter
 *
 * <AUTHOR>
 * @date: 3/2/2022 6:04 PM
 */
@Data
@TableName("monitor_exporter")
public class OpsTableExporterResultDto extends MapperAutoObject implements Serializable {
    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 组件名称
     */
    private String name;
    /**
     * 组件名称
     */
    private String nameEn;
    /**
     * exporter名称
     */
    private String exporterName;
    /**
     * exporter名称
     */
    private String exporterNameEn;
    /**
     * 采集频率（秒）
     */
    private String collectInterval;
    /**
     * 超时时间
     */
    private String timeout;

    /**
     * 云平台
     */
    private String envCode;
    /**
     * 云平台版本号
     */
    private String envVersion;

    /**
     * 云资源类型
     */
    private String resTypeCodes;

    /**
     * 描述
     */
    private String description;

    /**
     * 描述
     */
    private String descriptionEn;
    /**
     * 采集器名称
     */
    private String collectorName;

    /**
     * 采集器id
     */
    private Long collectorId;

    /**
     * 类型
     */
    private String type;

    /**
     * 采集路径
     */
    private String collectorPath;

    /**
     * 组装id
     */
    @TableField(fill = FieldFill.INSERT)
    private Long orgId;


}
