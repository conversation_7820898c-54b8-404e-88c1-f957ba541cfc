package com.cloudstar.rightcloud.monitor.data.mordatasource.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.Data;

import java.io.Serializable;

/**
 * 数据源
 *
 * @author: wanglang
 * @date: 2024/4/15 14:34
 */
@Data
@TableName("monitor_datasource")
public class MorTableDatasourceDto extends MapperAutoObject implements Serializable {
    /**
     * id;id
     */
    private Long id;
    /**
     * 名称;名称
     */
    private String name;
    /**
     * 状态;状态
     */
    private String status;
    /**
     * 类型;类型
     */
    private String type;
    /**
     * http配置
     */
    private String httpConfig;
    /**
     * 认证配置
     */
    private String authConfig;

    /**
     * 配置
     */
    private String settingsConfig;

    private String clusterName;

    /**
     * 是否默认;是否默认
     */
    private Boolean isDefault;
    /**
     * 描述;描述
     */
    private String description;
}
