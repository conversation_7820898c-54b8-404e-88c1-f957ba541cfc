package com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.cloudstar.rightcloud.monitor.common.annotation.Wrapper;
import com.cloudstar.rightcloud.monitor.common.annotation.WrapperField;
import com.cloudstar.rightcloud.monitor.common.em.WrapperType;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <p>描述: [OpsAlarmRule 实体类] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
@Wrapper(orderDescField = {"createdDt", "updatedDt"})
public class OpsAlarmRuleQueryDto extends MapperAutoObject implements Serializable {
    /**
     * id;id
     */
    private Long id;

    /**
     * id
     */
    @WrapperField(value = "id", type = WrapperType.NOT_IN)
    private List<Long> notInIds;

    /**
     * id
     */
    @WrapperField("id")
    private List<Long> ids;

    /**
     * 告警名称
     */
    private String name;

    /**
     * 告警名称
     */
    @WrapperField(value = "name", type = WrapperType.EQ)
    private String checkName;

    /**
     * 采集规则维度id
     */
    @WrapperField(updateStrategy = FieldStrategy.IGNORED)
    private Long opsCollectRuleDimensionsId;

    /**
     * 采集规则维度id
     */
    @WrapperField(value = "opsCollectRuleDimensionsId", updateStrategy = FieldStrategy.IGNORED)
    private List<Long> opsCollectRuleDimensionsIds;

    /**
     * 告警对象类型;根据cmdb数据结构code进行关联查询
     */
    @WrapperField(type = WrapperType.EQ)
    private String alarmTargetType;
    /**
     * 告警对象类型范围;all_alarm_object 所有告警对象 cloud_env_alarm_object 指定云环境下所有告警对象 alarm_object 指定告警对象
     */
    @WrapperField(type = WrapperType.EQ)
    private String alarmTargetScope;
    /**
     * 告警检测类型;static_wide_value 静态阔值 promql 自定义PromQL
     */
    @WrapperField(type = WrapperType.EQ)
    private String alarmDetectionType;
    /**
     * 状态;enable 启用 disable 禁用
     */
    @WrapperField(type = WrapperType.EQ)
    private String status;
    /**
     * PromQL;当告警检测类型为自定义PromQL时才会有值
     */
    private String promQl;

    /**
     * 告警触发规则;当告警检测类型为静态阔值时才会有值 all_conditions_are_met 满足所有条件 any_one_of_the_conditions_is_met 满足任意一个条件
     */
    @WrapperField(type = WrapperType.EQ)
    private String triggerRule;
    /**
     * 持续时间(分钟);当告警条件满足时，直接产生告警：0 当告警条件满足持续多久才产生告警：具体值
     */
    private Integer duration;
    /**
     * 告警级别状态
     */
    @WrapperField(type = WrapperType.EQ)
    private String alarmLevelStatus;
    /**
     * 告警内容
     */
    private String alarmContent;
    /**
     * 告警通知策略id
     */
    private Long opsNotifyPolicyId;

    /**
     * 告警通知策略id
     */
    @WrapperField("opsNotifyPolicyId")
    private List<Long> opsNotifyPolicyIds;

    /**
     * 组装id
     */
    private Long orgId;

}
