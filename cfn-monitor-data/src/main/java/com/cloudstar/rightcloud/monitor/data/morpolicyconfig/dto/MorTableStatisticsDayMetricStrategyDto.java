package com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 天性能汇总指标策略配置
 *
 * @author: wanglang
 * @date: 2023/11/3 16:52
 */
@Data
@TableName("monitor_statistics_day_metric_strategy")
public class MorTableStatisticsDayMetricStrategyDto implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 云平台
     */
    private String envCode;
    /**
     * 云平台版本号
     */
    private String envVersion;
    /**
     * 资源类型
     */
    private String resTypeCode;
    /**
     * 统计指标项
     */
    private String performanceMetricIndicatorCode;
    /**
     * 状态
     */
    private String status;
    /**
     * 描述
     */
    private String description;

    /**
     * 创建用户ID
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 记录创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createdDt;
    /**
     * 最后修改用户ID
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 记录修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date updatedDt;
    /**
     * 版本号
     */
    @TableField(fill = FieldFill.INSERT)
    private Long version;
}
