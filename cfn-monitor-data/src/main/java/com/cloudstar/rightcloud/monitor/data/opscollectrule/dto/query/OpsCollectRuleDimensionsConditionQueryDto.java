package com.cloudstar.rightcloud.monitor.data.opscollectrule.dto.query;


import com.cloudstar.rightcloud.monitor.common.annotation.WrapperField;
import com.cloudstar.rightcloud.monitor.common.em.WrapperType;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 采集规则维度条件
 *
 * @author: wanglang
 * @date: 2023/7/19 17:39
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@Builder
public class OpsCollectRuleDimensionsConditionQueryDto extends MapperAutoObject implements Serializable {

    /**
     * id;id
     */
    private Long id;

    /**
     * 采集规则维度id
     */
    private Long opsCollectRuleDimensionsId;

    /**
     * 采集规则维度id
     */
    @WrapperField("opsCollectRuleDimensionsId")
    private List<Long> opsCollectRuleDimensionsIds;

    /**
     * 资源实例编码
     */
    @WrapperField(type = WrapperType.EQ)
    private String resTypeInstanceCode;
    /**
     * 运算符;gt > ,ge >= ,lt< ,le <=, equals =, not_equals!=
     */
    @WrapperField(type = WrapperType.EQ)
    private String operator;
    /**
     * 比对值
     */
    @WrapperField(type = WrapperType.EQ)
    private String alignmentValue;
    /**
     * 逻辑运算符;&& 并且 ||或者
     */
    @WrapperField(type = WrapperType.EQ)
    private String logicOperator;

    /**
     * 排序编码
     */
    @WrapperField(type = WrapperType.ORDER_BY_ASC)
    private Boolean sortRank;

}
