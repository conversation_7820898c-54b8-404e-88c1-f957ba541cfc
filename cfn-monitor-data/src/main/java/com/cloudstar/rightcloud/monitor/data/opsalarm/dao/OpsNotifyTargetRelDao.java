package com.cloudstar.rightcloud.monitor.data.opsalarm.dao;

import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsNotifyTargetNameResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsNotifyTargetRelQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableNotifyTargetRelResultDto;

import java.util.List;

/**
 * 通知对象关联联系人/联系人组
 *
 * @author: 卢泳舟
 * @date: 2023/6/6 14:47
 */
public interface OpsNotifyTargetRelDao {

    /**
     * 根据通知对象id集合查询人名和组名集合
     *
     * @param ids 通知对象id集合
     * @return OpsNotifyTargetNameResultDto 信息集合
     */
    List<OpsNotifyTargetNameResultDto> getRelNameList(List<Long> ids);

    /**
     * 批量插入通知对象
     *
     * @param list 通知对象信息集合
     * @return Boolean true 成功 false 失败
     */
    Boolean batchInsert(List<OpsTableNotifyTargetRelResultDto> list);

    /**
     * 根据条件删除数据
     *
     * @param query 条件构造器
     * @return Boolean true 成功 false 失败
     */
    Boolean delete(OpsNotifyTargetRelQueryDto query);

    /**
     * 根据 entity 条件，查询全部记录
     *
     * @param query 实体对象封装操作类（可以为 null）
     * @return OpsTableNotifyTargetRelResultDto 通知对象中间表
     */
    List<OpsTableNotifyTargetRelResultDto> selectList(OpsNotifyTargetRelQueryDto query);


}
