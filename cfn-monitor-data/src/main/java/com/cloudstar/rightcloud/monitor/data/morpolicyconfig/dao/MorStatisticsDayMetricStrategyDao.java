package com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dao;

import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsDayMetricStrategyInfoDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorStatisticsDayMetricStrategyPageDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorTableStatisticsDayMetricStrategyDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.query.MorStatisticDayMetricPolicyPageQueryDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.query.MorStatisticsDayMetricStrategyQueryDto;

import java.util.List;

/**
 * 天性能汇总指标策略配置
 *
 * @author: wanglang
 * @date: 2023/11/3 15:50
 */
public interface MorStatisticsDayMetricStrategyDao {

    /**
     * 查询列表
     *
     * @param queryDto 查询参数
     * @return MorTableDayStatisticsMetricStrategyDto 配置策略
     */
    List<MorTableStatisticsDayMetricStrategyDto> selectList(MorStatisticsDayMetricStrategyQueryDto queryDto);

    /**
     * 查询列表
     *
     * @param ids 查询参数
     * @return MorTableDayStatisticsMetricStrategyDto 配置策略
     */
    List<MorTableStatisticsDayMetricStrategyDto> selectByIdsList(List<Long> ids);


    /**
     * 查询列表
     *
     * @param performanceMetricIndicatorCodes 统计指标项
     * @return MorTableDayStatisticsMetricStrategyDto 配置策略
     */
    List<MorTableStatisticsDayMetricStrategyDto> selectByMetricIndicatorCodeList(List<String> performanceMetricIndicatorCodes);

    /**
     * 查询天性能汇总指标策略列表(查询了关联的监控指标)
     *
     * @param dto 查询参数
     */
    List<MorStatisticsDayMetricStrategyPageDto> queryList(MorStatisticDayMetricPolicyPageQueryDto dto);

    /**
     * 查询天性能汇总指标策略详情（查询了关联的监控指标）
     *
     * @param id id
     */
    MorStatisticsDayMetricStrategyInfoDto queryById(Long id);

    /**
     * 插入属性不为空的记录
     *
     * @param dto 参数
     * @return MorTableDayStatisticsMetricStrategyDto 配置策略
     */
    Boolean insert(MorTableStatisticsDayMetricStrategyDto dto);

    /**
     * 根据主键更新
     *
     * @param dto 参数
     * @return MorTableDayStatisticsMetricStrategyDto 配置策略
     */
    Boolean update(MorTableStatisticsDayMetricStrategyDto dto);

    /**
     * 根据ID删除
     *
     * @param id ID
     * @return 删除结果
     */
    Boolean deleteById(Long id);

    /**
     * 统计数量
     * @param dto 查询条件
     *
     * @return 总数
     */
    long selectCount(MorTableStatisticsDayMetricStrategyDto dto);

}
