package com.cloudstar.rightcloud.monitor.data.opsexporterparam.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.monitor.data.opsexporterparam.dao.OpsExporterParamDao;
import com.cloudstar.rightcloud.monitor.data.opsexporterparam.dto.OpsTableExporterParamResultDto;
import com.cloudstar.rightcloud.monitor.data.opsexporterparam.dto.query.OpsExporterParamQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsexporterparam.mapper.OpsExporterParamMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 监控采集exporter参数
 *
 * @author: wanglang
 * @date: 2023/2/14 5:12 PM
 */
@Repository
public class OpsExporterParamDaoImpl implements OpsExporterParamDao {

    @Resource
    private OpsExporterParamMapper exporterParamMapper;

    @Override
    public List<OpsTableExporterParamResultDto> selectList(OpsExporterParamQueryDto query) {
        return exporterParamMapper.selectList(query);
    }

    @Override
    public OpsTableExporterParamResultDto selectOne(OpsExporterParamQueryDto query) {
        return exporterParamMapper.selectOne(query);
    }

    @Override
    public Boolean insertBatch(List<OpsTableExporterParamResultDto> list) {
        boolean result = false;
        if (CollectionUtil.isNotEmpty(list)) {
            if (exporterParamMapper.insertBatch(BeanHelperUtil.copyForList(OpsTableExporterParamResultDto::new, list))) {
                result = true;
            }
        }
        return result;
    }

    @Override
    public Boolean updateBatch(List<OpsTableExporterParamResultDto> list) {
        boolean result = false;
        if (CollectionUtil.isNotEmpty(list)) {
            final List<OpsTableExporterParamResultDto> opsTableExporterParamUpdateResultDtos = list.stream()
                    .filter(Objects::nonNull)
                    .filter(opsTableExporterParamResultDto -> Objects.nonNull(opsTableExporterParamResultDto.getId()))
                    .collect(Collectors.toList());
            // 更新
            if (CollectionUtil.isNotEmpty(opsTableExporterParamUpdateResultDtos)) {
                result = exporterParamMapper.updateBatch(opsTableExporterParamUpdateResultDtos);
            }
            // 插入
            final List<OpsTableExporterParamResultDto> opsTableExporterParamInsertResultDtos = list.stream()
                    .filter(Objects::nonNull)
                    .filter(opsTableExporterParamResultDto -> Objects.nonNull(opsTableExporterParamResultDto.getId()))
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(opsTableExporterParamInsertResultDtos)) {
                result = exporterParamMapper.updateBatch(opsTableExporterParamInsertResultDtos);
            }
        }
        return result;
    }

    @Override
    public Boolean deleteByIds(List<Long> ids) {
        return exporterParamMapper.deleteBatchIds(ids) >= 0;
    }

    @Override
    public Boolean delete(OpsExporterParamQueryDto query) {
        return exporterParamMapper.delete(query) >= 0;
    }
}
