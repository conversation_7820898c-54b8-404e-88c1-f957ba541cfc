package com.cloudstar.rightcloud.monitor.data.opsalarm.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.rightcloud.data.handler.MapperAutoObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalTime;

/**
 * <p>描述: [OpsNotifyUpgradePolicyRule 实体类] </p>
 * <p>创建时间: 2023/04/18 </p>
 *
 * <AUTHOR> href="mailto:" rel="nofollow">$author</a>
 * @version v1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("monitor_notify_upgrade_policy_rule")
public class OpsTableNotifyUpgradePolicyRuleResultDto extends MapperAutoObject implements Serializable {
    private static final long serialVersionUID = -69714830462528571L;
    /**
     * id;id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 通知升级策略id
     */
    private Long opsNotifyUpgradePolicyId;
    /**
     * 确认时间(分钟);状态确认：当告警多少{{duration}}分钟status时发送升级通知
     */
    private Integer duration;
    /**
     * 状态;not_confirmed 未确认 not_resolved 未解决
     */
    private String status;
    /**
     * 重试次数;默认为1，按确认时间(分钟)重复执行当前升级规则
     */
    private Integer retryNumber;
    /**
     * 告警通知开始时间段;格式：HH:mm:ss
     */
    private LocalTime alarmNotifyStartTimePeriod;
    /**
     * 告警通知结束时间段;格式：HH:mm:ss
     */
    private LocalTime alarmNotifyEndTimePeriod;

}
