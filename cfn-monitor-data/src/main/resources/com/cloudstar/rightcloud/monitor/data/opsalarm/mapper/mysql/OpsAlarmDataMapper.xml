<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cloudstar.rightcloud.monitor.data.opsalarm.mapper.OpsAlarmDataMapper">
    <resultMap id="BaseResultMap"
               type="com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableAlarmDataResultDto">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="source" jdbcType="VARCHAR" property="source" />
        <result column="status" jdbcType="VARCHAR" property="status" />
        <result column="confirm_user" jdbcType="BIGINT" property="confirmUser" />
        <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
        <result column="confirm_content" jdbcType="VARCHAR" property="confirmContent" />
        <result column="recovered_time" jdbcType="TIMESTAMP" property="recoveredTime" />
        <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
        <result column="created_dt" jdbcType="TIMESTAMP" property="createdDt" />
        <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
        <result column="updated_dt" jdbcType="TIMESTAMP" property="updatedDt" />
        <result column="version" jdbcType="BIGINT" property="version" />
    </resultMap>

    <select id="countLevelAlarmCount"
            resultType="com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsLevelAlarmCountDto">
        SELECT
            object_instance_id,
            alarm_level_status,
            count(object_instance_id) as total
        FROM monitor_alarm_data
        <where>
            <if test="query != null ">
                <if test="query.envId != null">
                    AND env_id =#{query.envId}
                </if>
                <if test="query.envIds != null and query.envIds.size() > 0">
                    AND env_id IN
                    <foreach collection="query.envIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.objectType != null">
                    AND object_type =#{query.objectType}
                </if>
                <if test="query.objectTypes != null and query.objectTypes.size() > 0">
                    AND object_type IN
                    <foreach collection="query.objectTypes" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.objectInstanceId != null">
                    AND object_instance_id =#{query.objectInstanceId}
                </if>
                <if test="query.objectInstanceIds != null and query.objectInstanceIds.size() > 0">
                    AND object_instance_id IN
                    <foreach collection="query.objectInstanceIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.envCode != null">
                    AND env_code =#{query.envCode}
                </if>
                <if test="query.envCodes != null and query.envCodes.size() > 0">
                    AND env_code IN
                    <foreach collection="query.envCodes" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.envVersion != null">
                    AND env_code =#{query.envVersion}
                </if>
                <if test="query.envVersions != null and query.envCodes.size() > 0">
                    AND env_version IN
                    <foreach collection="query.envVersions" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.status != null">
                    AND status =#{query.status}
                </if>
                <if test="query.status == null">
                    AND status ='delete'
                </if>
            </if>
        </where>
        group by
            object_instance_id,
            alarm_level_status;
    </select>
    <select id="countLevelAlarmCountByMetric"
            resultType="com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsLevelAlarmCountByMetricDto">
        SELECT
        common_metric_id,
        alarm_level_status,
        count(common_metric_id) as total
        FROM monitor_alarm_data
        <where>
            <if test="query != null ">
                <if test="query.envId != null">
                    AND env_id =#{query.envId}
                </if>
                <if test="query.envIds != null and query.envIds.size() > 0">
                    AND env_id IN
                    <foreach collection="query.envIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.objectType != null">
                    AND object_type =#{query.objectType}
                </if>
                <if test="query.objectTypes != null and query.objectTypes.size() > 0">
                    AND object_type IN
                    <foreach collection="query.objectTypes" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.objectInstanceId != null">
                    AND object_instance_id =#{query.objectInstanceId}
                </if>
                <if test="query.objectInstanceIds != null and query.objectInstanceIds.size() > 0">
                    AND object_instance_id IN
                    <foreach collection="query.objectInstanceIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.envCode != null">
                    AND env_code =#{query.envCode}
                </if>
                <if test="query.envCodes != null and query.envCodes.size() > 0">
                    AND env_code IN
                    <foreach collection="query.envCodes" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.envVersion != null">
                    AND env_code =#{query.envVersion}
                </if>
                <if test="query.envVersions != null and query.envCodes.size() > 0">
                    AND env_version IN
                    <foreach collection="query.envVersions" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="query.status != null">
                    AND status =#{query.status}
                </if>
                <if test="query.status == null">
                    AND status ='delete'
                </if>
                <if test="query.commonMetricIds != null and query.commonMetricIds.size() > 0">
                    AND common_metric_id IN
                    <foreach collection="query.commonMetricIds" index="index" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
        </where>
        group by
        common_metric_id,
        alarm_level_status;
    </select>
    <select id="countLevelCount"  resultType="com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsLevelAlarmLevelCountDto">
            SELECT
            alarm_level_status,
            count(alarm_level_status) as total
            FROM monitor_alarm_data
        <where>
            <if test="query.objectType != null">
                AND object_type =#{query.objectType}
            </if>
            <if test="query.objectInstanceId != null">
                AND object_instance_id =#{query.objectInstanceId}
            </if>
            <if test="query.status != null">
                AND status =#{query.status}
            </if>
            <if test="query.notStatus != null">
                AND status !=#{query.notStatus}
            </if>
            <if test="query.status == null and query.notStatus == null">
                AND status !='delete'
            </if>
            <if test="query.envIds != null">
                and env_id in
                <foreach collection="query.envIds" open="(" close=")" separator=" , " item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.objectInstanceIds != null">
                and object_instance_id in
                <foreach collection="query.objectInstanceIds" open="(" close=")" separator=" , " item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        group by
        alarm_level_status;
    </select>
    <select id="countLevelTimeCount"
            resultType="com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsLevelAlarmLevelTimeCountDto">
        SELECT
        last_time as time,
        alarm_level_status,
        count(alarm_level_status) as total
        FROM monitor_alarm_data
        <where>
            <if test="query.objectType != null">
                AND object_type =#{query.objectType}
            </if>
            <if test="query.objectInstanceId != null">
                AND object_instance_id =#{query.objectInstanceId}
            </if>
            <if test="query.status != null">
                AND status =#{query.status}
            </if>
            <if test="query.notStatus != null">
                AND status !=#{query.notStatus}
            </if>
            <if test="query.status == null and query.notStatus == null">
                AND status !='delete'
            </if>
            <if test="query.notStatus != null">
                AND status !=#{query.notStatus}
            </if>
            <if test="query.leLastTime != null">
                AND last_time <![CDATA[ <= ]]> #{query.leLastTime}
            </if>
            <if test="query.geLastTime != null">
                AND last_time <![CDATA[ >= ]]> #{query.geLastTime}
            </if>
            <if test="query.envIds != null">
                and env_id in
                <foreach collection="query.envIds" open="(" close=")" separator=" , " item="item">
                    #{item}
                </foreach>
            </if>
            <if test="query.objectInstanceIds != null">
                and object_instance_id in
                <foreach collection="query.objectInstanceIds" open="(" close=")" separator=" , " item="item">
                    #{item}
                </foreach>
            </if>
        </where>
        group by
        YEAR(last_time),
        MONTH(last_time),
        alarm_level_status;
    </select>

</mapper>
