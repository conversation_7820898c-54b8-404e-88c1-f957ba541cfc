<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.rightcloud.monitor.data.morsecurity.mapper.MorSecurityEventHistoryMapper">

    <resultMap id="BaseResultMap"
               type="com.cloudstar.rightcloud.monitor.data.morsecurity.dto.MorSecurityEventResultHistoryDto">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="originId" column="origin_id" jdbcType="VARCHAR"/>
        <result property="eventName" column="event_name" jdbcType="VARCHAR"/>
        <result property="eventType" column="event_type" jdbcType="VARCHAR"/>
        <result property="eventLevel" column="event_level" jdbcType="VARCHAR"/>
        <result property="reliabilityLevel" column="reliability_level" jdbcType="VARCHAR"/>
        <result property="eventTime" column="event_time" jdbcType="VARCHAR"/>
        <result property="desIp" column="des_ip" jdbcType="VARCHAR"/>
        <result property="desPort" column="des_port" jdbcType="VARCHAR"/>
        <result property="resourceId" column="resource_id" jdbcType="VARCHAR"/>
        <result property="resourceName" column="resource_name" jdbcType="VARCHAR"/>
        <result property="srcIp" column="src_ip" jdbcType="VARCHAR"/>
        <result property="srcPort" column="src_port" jdbcType="BIGINT"/>
        <result property="orgId" column="org_id" jdbcType="BIGINT"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT"/>
        <result property="bizSystemId" column="biz_system_id" jdbcType="BIGINT"/>
        <result property="confirmUser" column="confirm_user" jdbcType="VARCHAR"/>
        <result property="confirmUserId" column="confirm_user_id" jdbcType="BIGINT"/>
        <result property="confirmTime" column="confirm_time" jdbcType="TIMESTAMP"/>
        <result property="confirmContent" column="confirm_content" jdbcType="VARCHAR"/>
        <result property="resolveUser" column="resolve_user" jdbcType="VARCHAR"/>
        <result property="resolveUserId" column="resolve_user_id" jdbcType="BIGINT"/>
        <result property="resolveTime" column="resolve_time" jdbcType="TIMESTAMP"/>
        <result property="resolveContent" column="resolve_content" jdbcType="VARCHAR"/>
        <result property="recoveredTime" column="recovered_time" jdbcType="TIMESTAMP"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
        <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,origin_id,event_name,event_type,event_level,reliability_level,
        event_time,des_ip,des_port,resource_id,src_ip,src_port,org_id,project_id,
        biz_system_id,confirm_user,confirm_user_id,confirm_time,confirm_content,
        resolve_user,resolve_user_id,resolve_time,resolve_content,resource_name,recovered_time,
        created_dt,created_by,updated_by,updated_dt,version
    </sql>
    <sql id="Security_History_Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            and 1=1
            <if test="condition.id != null and condition.id != ''">
                and id = #{condition.id}
            </if>
            <if test="condition.originId != null and condition.originId != ''">
                and origin_id = #{condition.originId}
            </if>
            <if test="condition.eventNameLike != null and condition.eventNameLike != ''">
                and event_name LIKE concat('%', #{condition.eventNameLike}, '%')
            </if>
            <if test="condition.eventType != null and condition.eventType != ''">
                and event_type = #{condition.eventType}
            </if>
            <if test="condition.srcIp != null and condition.srcIp != ''">
                and src_ip = #{condition.srcIp}
            </if>
            <if test="condition.resourceNameLike != null and condition.resourceNameLike != ''">
                and resource_name LIKE concat('%', #{condition.resourceNameLike}, '%')
            </if>
            <if test="condition.desIp != null and condition.desIp != ''">
                and des_ip = #{condition.desIp}
            </if>
            <if test="condition.orgId != null and condition.orgId != ''">
                and org_id = #{condition.orgId}
            </if>
            <if test="condition.bizSystemId != null and condition.bizSystemId != ''">
                and biz_system_id = #{condition.bizSystemId}
            </if>
            <if test="condition.projectId != null and condition.projectId != ''">
                and project_id = #{condition.projectId}
            </if>
            <if test="condition.confirmUser != null and condition.confirmUser != ''">
                and confirm_user = #{condition.confirmUser}
            </if>
            <if test="condition.startEventTime != null and condition.endEventTime != null">
                and event_time between #{condition.startEventTime} and #{condition.endEventTime}
            </if>
            <if test="condition.reliabilityLevels != null and condition.reliabilityLevels.size() > 0">
                and reliability_level in
                <foreach item="item" index="index" collection="condition.reliabilityLevels"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="condition.eventLevels != null and condition.eventLevels.size() > 0">
                and event_level in
                <foreach item="item" index="index" collection="condition.eventLevels"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>
    <select id="page" resultMap="BaseResultMap">
        select    <include refid="Base_Column_List"/>
            from monitor_security_event_info_history
        <include refid="Security_History_Example_Where_Clause"/>
    </select>
    <select id="eventTrend"
            resultType="com.cloudstar.rightcloud.monitor.data.morsecurity.dto.MorSecurityEventHistroyTrendDto">
        SELECT DATE_FORMAT(event_time, '%Y-%m-%d') eventTime,
               count(1) count
        FROM
            `monitor_security_event_info_history`
        GROUP BY
            DATE_FORMAT (
            event_time,
            '%Y-%m-%d')
    </select>
    <select id="list" resultType="com.cloudstar.rightcloud.monitor.data.morsecurity.dto.MorSecurityEventResultDto">
        select    <include refid="Base_Column_List"/>
        from monitor_security_event_info_history
        <include refid="Security_History_Example_Where_Clause"/>
    </select>
</mapper>
