<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.rightcloud.monitor.data.opsalarm.mapper.OpsNotifyPolicyMapper">

    <resultMap type="com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableNotifyPolicyResultDto" id="OpsNotifyPolicyMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="alarmRestorationNotifyStatus" column="alarm_restoration_notify_status" jdbcType="VARCHAR"/>
        <result property="alarmNotifyTemplateCode" column="alarm_notify_template_code" jdbcType="VARCHAR"/>
        <result property="alarmRestorationTemplateCode" column="alarm_restoration_template_code" jdbcType="VARCHAR"/>
        <result property="alarmNotifyStartTimePeriod" column="alarm_notify_start_time_period" jdbcType="TIME"/>
        <result property="alarmNotifyEndTimePeriod" column="alarm_notify_end_time_period" jdbcType="TIME"/>
        <result property="repetitionNotifyStatus" column="repetition_notify_status" jdbcType="VARCHAR"/>
        <result property="repetitionNotifyIntervalTime" column="repetition_notify_interval_time" jdbcType="TIME"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="DATE"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="DATE"/>
        <result property="version" column="version" jdbcType="BIGINT" />
    </resultMap>
    
</mapper>
