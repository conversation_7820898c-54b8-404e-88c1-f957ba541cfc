<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.rightcloud.monitor.data.opsalarm.mapper.OpsNotifyTargetGroupContactsRelMapper">

    <resultMap type="com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableNotifyTargetGroupContactsRelResultDto" id="OpsNotifyTargetGroupContactsRelMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="opsNotifyTargetContactsId" column="ops_notify_target_contacts_id" jdbcType="BIGINT"/>
        <result property="opsNotifyTargetGroupId" column="ops_notify_target_group_id" jdbcType="BIGINT"/>
        <result property="version" column="version" jdbcType="BIGINT" />
    </resultMap>

    <select id="selectRelIdList" resultType="java.lang.Long">
        SELECT
            group_contacts_rel.id
        FROM
            monitor_notify_target_contacts contacts,
            monitor_notify_target_group_contacts_rel group_contacts_rel,
            monitor_notify_target_contacts_group contacts_group
        WHERE
            contacts.id = group_contacts_rel.ops_notify_target_contacts_id
        AND
            group_contacts_rel.ops_notify_target_group_id = contacts_group.id
        AND
            (group_contacts_rel.ops_notify_target_contacts_id, group_contacts_rel.ops_notify_target_group_id)
                IN (
                    <foreach collection="relParamDtoList" item="item" separator=",">
                        (#{item.opsNotifyTargetContactsId}, #{item.opsNotifyTargetGroupId})
                    </foreach>
                    )
    </select>
    
</mapper>
