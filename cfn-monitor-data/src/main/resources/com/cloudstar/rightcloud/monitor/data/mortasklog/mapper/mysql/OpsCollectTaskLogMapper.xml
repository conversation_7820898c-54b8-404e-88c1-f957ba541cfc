<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.rightcloud.monitor.data.mortasklog.mapper.MorCollectTaskLogMapper">

    <select id="selectTaskLogTime"
            resultType="com.cloudstar.rightcloud.monitor.data.mortasklog.dto.MorCollectTaskLogTimeDto">
        SELECT task_id, MAX(log_time) AS max_log_time
        FROM monitor_collect_task_log
        WHERE task_id  in
        <foreach collection="list" separator="," item="item" open="(" close=")">
            #{item}
        </foreach>
        GROUP BY task_id;
    </select>
</mapper>
