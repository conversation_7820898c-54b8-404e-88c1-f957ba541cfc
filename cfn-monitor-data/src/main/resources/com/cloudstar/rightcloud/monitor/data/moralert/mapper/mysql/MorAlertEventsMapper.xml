<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.rightcloud.monitor.data.moralert.mapper.MorAlertEventsMapper">

	<select id="infoCur" parameterType="string" resultType="com.cloudstar.rightcloud.monitor.data.moralert.dto.MorAlertEventsTableResultDto">
		select 	id,
		0 is_recovered,
		`hash`,
		rule_id,
		rule_name,
		rule_description,
		tags,
		severity,
		0 recover_time,
		first_trigger_time,
		trigger_time,
		processing_status,
		(UNIX_TIMESTAMP() - first_trigger_time) duration,
		confirm_time,
		resolve_time,
		confirm_content,
		resolve_content
		from monitor_alert_cur_events
		where is_delete = 0
		and `hash` = #{hash}
		limit 1
	</select>

	<select id="infoHis" parameterType="string" resultType="com.cloudstar.rightcloud.monitor.data.moralert.dto.MorAlertEventsTableResultDto">
		select 	id,
		is_recovered,
		`hash`,
		rule_id,
		rule_name,
		rule_description,
		tags,
		severity,
		recover_time,
		first_trigger_time,
		trigger_time,
		'resolve' processing_status,
		(recover_time - first_trigger_time) duration,
		confirm_time,
		resolve_time,
		confirm_content,
		resolve_content
		from monitor_alert_his_events
		where is_recovered = 1 and is_delete = 0
		and `hash` = #{hash}
		limit 1
	</select>

    <select id="list" parameterType="com.cloudstar.rightcloud.monitor.data.moralert.dto.query.MorAlertEventsTablePageDto" resultType="com.cloudstar.rightcloud.monitor.data.moralert.dto.MorAlertEventsTableResultDto">
<!--		只查询 his 表，并使用hash去重取最新的一条-->
<!--    SELECT-->
<!--	m.id,-->
<!--	m.is_recovered,-->
<!--	m.`hash`,-->
<!--	m.rule_id,-->
<!--	m.rule_name,-->
<!--	m.rule_description,-->
<!--	m.tags,-->
<!--	m.severity,-->
<!--	m.recover_time,-->
<!--	m.first_trigger_time,-->
<!--	m.trigger_time-->
<!--    FROM-->
<!--	`monitor_alert_his_events` m-->
<!--	JOIN ( SELECT `hash`, MAX( first_trigger_time ) AS max_time FROM `monitor_alert_his_events` GROUP BY `hash` ) sub ON m.`hash` = sub.`hash` AND m.first_trigger_time = sub.max_time-->
		SELECT
		id,
		is_recovered,
		`hash`,
		rule_id,
		rule_name,
		rule_description,
		tags,
		severity,
		recover_time,
		first_trigger_time,
		trigger_time,
		processing_status,
		duration,
		confirm_time,
		resolve_time,
		confirm_content,
		resolve_content
		FROM
		(
		select 	id,
		0 is_recovered,
		`hash`,
		rule_id,
		rule_name,
		rule_description,
		tags,
		severity,
		0 recover_time,
		first_trigger_time,
		trigger_time,
		processing_status,
		(UNIX_TIMESTAMP() - first_trigger_time) duration,
		confirm_time,
		resolve_time,
		confirm_content,
		resolve_content,
		group_id
		from monitor_alert_cur_events
		where is_delete = 0
		UNION ALL
		select 	id,
		is_recovered,
		`hash`,
		rule_id,
		rule_name,
		rule_description,
		tags,
		severity,
		recover_time,
		first_trigger_time,
		trigger_time,
		'resolve' processing_status,
		(recover_time - first_trigger_time) duration,
		confirm_time,
		resolve_time,
		confirm_content,
		resolve_content,
		group_id
		from monitor_alert_his_events
		where is_recovered = 1 and is_delete = 0
		) m
		<where>
			<trim>
				group_id = ( select id from monitor_alert_busi_group where label_value = 'default' limit 1 )
				<if test="alertName != null and alertName != ''">
					and m.rule_name like concat('%', #{alertName}, '%')
				</if>
				<if test="alertTarget != null and alertTarget != ''">
					and m.tags like concat('%instance=', #{alertTarget}, '%')
				</if>
				<if test="duration != null">
					and m.duration <![CDATA[ <= ]]> #{duration} * 60
				</if>
				<if test="triggerTimeStart != null and triggerTimeStart != ''">
					and FROM_UNIXTIME(m.first_trigger_time) <![CDATA[ >= ]]> #{triggerTimeStart}
				</if>
				<if test="triggerTimeEnd != null and triggerTimeEnd != ''">
					and FROM_UNIXTIME(m.first_trigger_time) <![CDATA[ <= ]]> #{triggerTimeEnd}
				</if>
				<if test="status != null">
					and m.is_recovered = #{status}
				</if>
				<if test="processingStatus != null and processingStatus != ''">
					and FIND_IN_SET(m.processing_status, #{processingStatus})
				</if>
				<if test="severity != null">
					and FIND_IN_SET(m.severity, #{severity})
				</if>
			</trim>
		</where>
		<if test="sortDataField == null or sortDataField == ''">
			order by is_recovered desc, trigger_time desc, severity asc
		</if>

	</select>

	<update id="processConfirmCur" parameterType="com.cloudstar.rightcloud.monitor.data.moralert.dto.MorAlertEventsProcessDto">
		UPDATE monitor_alert_cur_events
		SET processing_status = 'processing'
		,confirm_content = #{content}
		,confirm_time = NOW()
		WHERE
		FIND_IN_SET(`hash`, #{hash})
	</update>

	<update id="processConfirmHis" parameterType="com.cloudstar.rightcloud.monitor.data.moralert.dto.MorAlertEventsProcessDto">
		UPDATE monitor_alert_his_events
		SET confirm_content = #{content}
		,confirm_time = NOW()
		WHERE
		FIND_IN_SET(`hash`, #{hash})
	</update>

	<update id="processResolveCur" parameterType="com.cloudstar.rightcloud.monitor.data.moralert.dto.MorAlertEventsProcessDto">
		UPDATE monitor_alert_cur_events
		SET processing_status = 'resolve'
		,resolve_content = #{content}
		,resolve_time = NOW()
		WHERE
		FIND_IN_SET(`hash`, #{hash})
	</update>

	<update id="processResolveHis" parameterType="com.cloudstar.rightcloud.monitor.data.moralert.dto.MorAlertEventsProcessDto">
		UPDATE monitor_alert_his_events
		SET resolve_content = #{content}
		,resolve_time = NOW()
		WHERE
		FIND_IN_SET(`hash`, #{hash})
	</update>

	<update id="clearCur" parameterType="string">
		UPDATE monitor_alert_cur_events
		SET is_delete = 1
		WHERE
		FIND_IN_SET(`hash`, #{hashList})
	</update>

	<update id="clearHis" parameterType="string">
		UPDATE monitor_alert_his_events
		SET is_delete = 1
		WHERE
		FIND_IN_SET(`hash`, #{hashList})
	</update>

</mapper>
