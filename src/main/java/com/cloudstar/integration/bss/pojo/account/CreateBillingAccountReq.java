package com.cloudstar.integration.bss.pojo.account;

import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import lombok.Data;

/**
 * 创建计费帐户要求事情
 *
 * <AUTHOR>
 * @date 2023/06/26
 */
//@ApiModel(description = "创建客户")
@Data
public class CreateBillingAccountReq {

    //@ApiModelProperty("用户名")
    @NotBlank(message = "用户姓名不能为空")
    //@StartWithWord(message = "姓名不能已test，admin开头")
    @Pattern(regexp = "^[a-zA-Z][A-Za-z0-9_.-]{3,15}$", message = "用户名长度为4-16字符,以字母开头，由字母、数字、中划线、下划线以及点组成")
    private String account;

    //@ApiModelProperty("企业名称")
    @NotBlank
    //@StartWithWord(message = "企业名称不能已test，admin开头")
    @Length(min = 2, max = 64, message = "企业名称输入长度必须介于2~64字符")
    private String companyName;

    //@ApiModelProperty("企业联系人")
    @NotBlank
    //@StartWithWord(message = "企业联系人不能已test，admin开头")
    @Pattern(regexp = "^[\\u4e00-\\u9fa5a-zA-Z0-9]{2,64}$", message = "联系人长度为2-64个字符,并且不能包含特殊符号")
    private String contactName;

    //@ApiModelProperty("企业联系电话")
    @NotBlank
    @Pattern(regexp = "^(13[0-9]|14[********]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$", message = "请输入正确的手机号码")
    private String contactNumber;

    //@ApiModelProperty("邮箱")
    @NotBlank
    //@StartWithWord(message = "邮箱不能已test，admin开头")
    @Email(regexp = "^[a-zA-Z0-9_.-]{4,16}@[a-zA-Z0-9-]+(\\.[a-zA-Z0-9-]+)*\\.[a-zA-Z0-9]{2,6}$", message = "邮箱格式不正确")
    private String email;

    //@ApiModelProperty("密码")
    @NotBlank
    private String password;

    //@ApiModelProperty("主营业务")
    @Length(max = 64)
    private String business;

    //@ApiModelProperty("企业代码")
    private String companyCode;

    //@ApiModelProperty("身份证/执照")
    private String license;

    //@ApiModelProperty(value = "应用场景", required = true)
    @NotBlank(message = "应用场景不能为空")
    //@TypesRange(type = TypesConstant.APPLICATION_SCENARIO, message = "不合法的应用场景类型")
    private String applicationScenario;

    //@ApiModelProperty("人员规模")
    //@TypesRange(type = TypesConstant.PERSONNEL_SIZE, message = "不合法的人员规模类型")
    private String personnelSize;

    //@ApiModelProperty("所属行业")
    //@TypesRange(type = TypesConstant.INDUSTRY_TYPE, message = "不合法的所属行业类型")
    private String industryType;

    //@ApiModelProperty(value = "详细地址", required = true)
    @NotBlank(message = "详细地址不能为空")
    @Length(max = 128)
    private String address;

    //@ApiModelProperty(value = "解决方案")
    @Length(max = 50)
    private String solution;

}
