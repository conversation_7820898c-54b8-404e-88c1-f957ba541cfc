package com.cloudstar.integration.hcs.holder;

import com.cloudstar.integration.hcs.pojo.HcsTenant;

/**
 * hcs租户登录
 *
 * <AUTHOR>
 * @date 2025/3/11 11:44
 */
public class ThreadHcsTenantHolder {

    private static final InheritableThreadLocal<HcsTenant> TENANT_HOLDER = new InheritableThreadLocal<>();

    /**
     * 获得令牌
     *
     * @return {@link HcsTenant}
     */
    public static HcsTenant getHcsTenant() {
        return TENANT_HOLDER.get();
    }

    /**
     * 设置令牌
     *
     * @param tenant 令牌
     */
    public static void setHcsTenant(HcsTenant tenant) {
        TENANT_HOLDER.set(tenant);
    }

    /**
     * 清晰
     */
    public static void clear() {
        TENANT_HOLDER.remove();
    }
}
