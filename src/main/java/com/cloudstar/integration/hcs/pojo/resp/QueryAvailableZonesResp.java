package com.cloudstar.integration.hcs.pojo.resp;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询可用区
 *
 * <AUTHOR>
 * @date 2025/3/7 17:05
 */
@Data
public class QueryAvailableZonesResp {

    /**
     * 可用分区总数。
     */
    @JsonProperty("total")
    private int total;

    /**
     * 可用分区列表信息。
     */
    @JsonProperty("records")
    private List<AvailableZone> records;

    /**
     * AvailableZone对象
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class AvailableZone {

        /**
         * 可用分区ID，长度范围0~128字符。
         */
        @JsonProperty("az_id")
        private String azId;

        /**
         * Region ID，长度范围0~32字符。
         */
        @JsonProperty("region_id")
        private String regionId;

        /**
         * 云资源池id。长度范围0~64字符。
         */
        @JsonProperty("cloud_infra_id")
        private String cloudInfraId;

        /**
         * 可用分区名称，长度范围0~255字符，只能是中文、英文、中划线、下划线及.号组成。
         */
        @JsonProperty("name")
        private String name;

        /**
         * 可用分区类型：长度范围0~32字符。
         */
        @JsonProperty("type")
        private String type;

        /**
         * 可用分区状态：normal（正常），abnormal（异常）。
         */
        @JsonProperty("status")
        private String status;

        /**
         * 可用分区属性，长度范围0~512字符。
         */
        @JsonProperty("extend_param")
        private String extendParam;
    }
}
