package com.cloudstar.integration.hcs.util;


import com.cloudstar.ConfigService;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.config.HcsClientConfig;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 获取对应节点的域名
 *
 * <AUTHOR>
 * @date 2025/3/14 17:49
 */
@Slf4j
public class EndpointUtil {

    private static final String SWR = "swr-api";

    private static final String OBS = "obsv3";

    private static final String IAM = "iam";
    public static String getSwrEndpoint() {
        final HcsClientConfig hcsClientConfig = getHcsClientConfig();
        String uri = hcsClientConfig.getAddress().stringValue();
        String replacement = SWR + "." + hcsClientConfig.getRegion().stringValue();
        return replaceUri(uri, replacement);
    }

    public static String getObsEndpoint() {
        final HcsClientConfig hcsClientConfig = getHcsClientConfig();
        String uri = hcsClientConfig.getAddress().stringValue();
        String replacement = OBS + "." + hcsClientConfig.getRegion().stringValue();
        return replaceUri(uri, replacement);
    }

    public static String getIamEndpoint() {
        final HcsClientConfig hcsClientConfig = getHcsClientConfig();
        String uri = hcsClientConfig.getAddress().stringValue();
        String replacement = IAM + "." + hcsClientConfig.getRegion().stringValue();
        return replaceUri(uri, replacement);
    }

    private static HcsClientConfig getHcsClientConfig() {
        ConfigService configService = SpringUtil.getBean(ConfigService.class);
        final HcsClientConfig config = configService.getConfig(ConfigType.HCS_CLIENT_CONFIG);
        if (ObjectUtil.isEmpty(config) || ObjectUtil.isEmpty(config.getRegion()) || ObjectUtil.isEmpty(config.getAddress())) {
            throw new BizException("系统未配置SWR仓库地址");
        }
        return config;
    }

    private static String replaceUri(String uri, String replacement) {
        // 拆分协议和剩余部分
        int protocolIndex = uri.indexOf("://");
        if (protocolIndex == -1) {
            return null;
        }
        String protocol = uri.substring(0, protocolIndex + 3);
        String remaining = uri.substring(protocolIndex + 3);

        // 拆分域名和路径
        int pathIndex = remaining.indexOf("/");
        String domain;
        if (pathIndex != -1) {
            domain = remaining.substring(0, pathIndex);
        } else {
            domain = remaining;
        }
        // 替换域名中第一个.之前的内容
        int dotIndex = domain.indexOf(".");
        if (dotIndex != -1) {
            domain = replacement + domain.substring(dotIndex);
        }
        // 组装新的 URI
        return protocol + domain;
    }
}
