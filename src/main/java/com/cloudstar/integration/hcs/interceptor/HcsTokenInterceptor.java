package com.cloudstar.integration.hcs.interceptor;

import com.cloudstar.integration.hcs.constant.CommonConstant;
import com.cloudstar.integration.hcs.util.HcsAuthTokenUtil;
import com.github.lianjiatech.retrofit.spring.boot.interceptor.BasePathMatchInterceptor;

import org.springframework.stereotype.Component;

import java.io.IOException;

import cn.hutool.core.util.ObjectUtil;
import okhttp3.Request;
import okhttp3.Response;

/**
 * bss token拦截器
 *
 * <AUTHOR>
 * @description: TODO
 * @date 2023/5/22 15:31
 */
@Component
public class HcsTokenInterceptor extends BasePathMatchInterceptor {

    @Override
    protected Response doIntercept(Chain chain) throws IOException {
        String token = HcsAuthTokenUtil.getAuthToken();
        if (!ObjectUtil.isEmpty(token)) {
            Request request = chain.request();
            Request newRequest;
            newRequest = request.newBuilder()
                                .addHeader(CommonConstant.AUTH_TOKEN_REQUEST_HEADER_KEY, token)
                                .addHeader("Content-Type", "application/json")
                                .addHeader("Accept", "application/json")
                                .build();
            Response proceed = chain.proceed(newRequest);
            if (isTokenExpired(proceed)) {
                proceed.close();
                String newToken = HcsAuthTokenUtil.getAuthToken();
                //使用新的Token，创建新的请求
                newRequest = request.newBuilder()
                                    .addHeader(CommonConstant.AUTH_TOKEN_REQUEST_HEADER_KEY, newToken)
                                    .addHeader("Content-Type", "application/json")
                                    .addHeader("Accept", "application/json")
                                    .build();
                return chain.proceed(newRequest);
            }
            return proceed;
        }
        return chain.proceed(chain.request());
    }

    /**
     * 根据Response，判断Token是否失效 401表示token过期
     *
     * @param response 返回值
     */
    private boolean isTokenExpired(Response response) {
        if (response.code() == 401) {
            return true;
        }
        return false;
    }
}
