package com.cloudstar.integration.nexus.constant;

/**
 * bss客户端共同常量
 *
 * <AUTHOR>
 * @date 2023/5/22 14:02
 */
public interface CommonConstant {

    /**
     * redis缓存token key
     */
    String AUTH_TOKEN_REDIS_KEY = "BSS_AUTH_TOKEN";


    /**
     * http请求头Authorization
     */
    String AUTHORIZATION = "Authorization";


    /**
     * 第三方帐户
     */
    String THIRD_PARTY_ACCOUNT = "account";
    /**
     * 第三方密码
     */
    String THIRD_PARTY_PASSWORD = "password";

    /**
     * 响应码
     */
    String CODE = "code";

    /**
     * 状态
     */
    String STATUS = "status";
    /**
     * 消息
     */
    String MESSAGE = "message";
    /**
     * 数据
     */
    String DATA = "data";
}
