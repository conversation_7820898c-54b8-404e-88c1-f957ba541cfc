package com.cloudstar.integration.nexus.pojo.user;

import lombok.Data;

import java.util.List;

/**
 * ApiCreateUser
 */
@Data
public class CreateUserReq {
    /**
     * 与用户关联的电子邮件地址。
     */
    private String emailAddress;
    /**
     * 用户的名字。
     */
    private String firstName;
    /**
     * 用户的姓氏。
     */
    private String lastName;
    /**
     * 新用户的密码。
     */
    private String password;
    /**
     * 用户在Nexus中被分配的角色。
     */
    private List<String> roles;
    /**
     * 用户的状态，例如活动或禁用。
     * UserStatus  active , changepassword, disabled, locked
     */
    private String status;
    /**
     * 登录所需的用户ID。此值不能更改。
     */
    private String userId;
}
