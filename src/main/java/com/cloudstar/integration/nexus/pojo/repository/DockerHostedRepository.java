package com.cloudstar.integration.nexus.pojo.repository;

@lombok.Data
public class DockerHostedRepository {
    /**
     * 垃圾回收策略属性
     */
    private CleanupPolicyAttributes cleanup;
    /**
     * 组件属性
     */
    private ComponentAttributes component;
    /**
     * docker属性
     */
    private DockerAttributes docker;
    /**
     * 此存储库的唯一标识符
     */
    private String name;
    /**
     * 此存储库是否接受传入的请求
     */
    private boolean online;
    /**
     * 托管存储属性
     */
    private HostedStorageAttributes storage;
}
