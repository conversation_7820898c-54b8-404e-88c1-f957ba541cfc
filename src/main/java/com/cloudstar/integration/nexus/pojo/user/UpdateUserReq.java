package com.cloudstar.integration.nexus.pojo.user;

import com.cloudstar.integration.nexus.enums.UserStatus;
import lombok.Data;

import java.util.List;

/**
 * ApiUser
 */
@Data
public class UpdateUserReq {
    /**
     * 与用户关联的电子邮件地址。
     */
    private String emailAddress;
    /**
     * 用户在外部源（如LDAP组）中被分配的角色。这些不能在Nexus存储库管理器中更改。
     */
    private List<String> externalRoles;
    /**
     * 用户的名字。
     */
    private String firstName;
    /**
     * 用户的姓氏。
     */
    private String lastName;
    /**
     * 指示Nexus存储库管理器是否可以修改用户的属性。如果为false，则在更新期间仅考虑角色。
     */
    private Boolean readOnly;
    /**
     * 用户在Nexus中被分配的角色。
     */
    private List<String> roles;
    /**
     * 作为该用户来源的用户源。此值不能更改。
     */
    private String source;
    /**
     * 用户的状态，例如活动或禁用。
     */
    private UserStatus status;
    /**
     * 登录所需的用户ID。此值不能更改。
     */
    private String userId;
}
