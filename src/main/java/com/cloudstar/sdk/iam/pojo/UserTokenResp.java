package com.cloudstar.sdk.iam.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import lombok.Data;

/**
 * token响应实体
 *
 * <AUTHOR>
 * @date 2022/12/26 10:22
 */
@Data
public class UserTokenResp {

    /**
     * token
     */
    private String token;

    /**
     * 用户id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userSid;

    /**
     * 是否重置密码
     */
    private boolean isResetPassword;

    /**
     * 密码是否过期
     */
    private boolean isPasswordExpired;
    /**
     * 导航
     */
    private String navigate;

    /**
     * 角色名称
     */
    private String roleNames;
    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 最后登录ip
     */
    private String lastLoginIp;
    /**
     * 认证状态
     */
    private String authStatus;
}
