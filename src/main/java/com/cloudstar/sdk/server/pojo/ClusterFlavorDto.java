package com.cloudstar.sdk.server.pojo;

import lombok.Data;

/**
 * 集群规格dto
 *
 * <AUTHOR>
 * Created on 2022/8/26
 * @date 2022/08/26
 */
@Data
public class ClusterFlavorDto {
    /**
     * id
     */
    private Long id;
    /**
     * 底层资源规格id
     */
    private String specId;
    /**
     * 云资源的规格类型
     */
    private String specCode;
    /**
     * 资源规格最大节点数
     */
    private Integer maxNum;

    /**
     * 集群id
     */
    private Long clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 集群类型
     */
    private String clusterType;

    /**
     * 资源规格类型,CPU,NPU,GPU
     */
    private String specType;

    /**
     * 是否可见 true:可见 false:不可见
     */
    private String isVisible;

    /**
     * 资源池类型 share:共享资源池,exclusive:专属资源池,all：两者都是
     */
    private String resPoolType;
}
