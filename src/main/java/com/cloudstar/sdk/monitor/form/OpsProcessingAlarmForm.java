package com.cloudstar.sdk.monitor.form;

import com.cloudstar.common.base.annotations.I18nProperty;
import com.cloudstar.common.base.constant.MonitorFieldKeyConstant;
import com.cloudstar.sdk.monitor.annotations.EnumValue;
import com.cloudstar.sdk.monitor.annotations.HorizontalAuth;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 处理告警数据
 *
 * @author: wanglang
 * @date: 2023/6/5 8:23 PM
 */
@Data
public class OpsProcessingAlarmForm {
    /**
     * 处理类型  确认：confirm 解决：resolve
     */
    @NotEmpty
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.PROCESSING_TYPE)
    @EnumValue(strValues = {"confirm", "resolve"})
    private String processingType;

    /**
     * 处理内容
     */
    @Length(max = 255)
    private String processingContent;

    /**
     * 处理id
     */
    @NotEmpty
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.PROCESSING_ID)
    @HorizontalAuth(table = "monitor_alarm_data", ignoreUserScope = true)
    private List<String> processingIds;

    /**
     * 确认人id
     */
    private Long confirmUserId;

    /**
     * 解决人id
     */
    private Long resolveUserId;

}
