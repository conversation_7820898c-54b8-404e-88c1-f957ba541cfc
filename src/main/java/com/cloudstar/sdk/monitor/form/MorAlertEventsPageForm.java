package com.cloudstar.sdk.monitor.form;

import com.cloudstar.common.util.page.PageForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.io.Serializable;

@Data
@ToString
@EqualsAndHashCode
public class Mor<PERSON>lertEventsPageForm extends PageForm implements Serializable {

    /**
     * 告警名称
     */
    private String alertName;

    /**
     * 告警对象，精确匹配
     */
    private String alertTarget;

    /**
     * 持续时长
     */
    private Integer duration;

    /**
     * 发生开始时间
     */
    private String triggerTimeStart;

    /**
     * 发生结束时间
     */
    private String triggerTimeEnd;

    /**
     * 恢复状态: not_recovered 未恢复，recovered 已恢复
     */
    private String status;

    /**
     * 处理状态: to_be_confirmed 待确认, processing 处理中, resolve 已解决
     */
    private String processingStatus;

    /**
     * 告警级别1: 紧急 Emergency 2: 重要 significance 提示 3: pointOut 次要 4: minor
     */
    private String severity;

}
