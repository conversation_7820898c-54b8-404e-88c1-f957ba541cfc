package com.cloudstar.sdk.monitor.result;

import com.cloudstar.sdk.monitor.annotations.BeanHelperField;
import lombok.Data;

import java.util.Date;

/**
 * 告警数据详情
 *
 * @author: wanglang
 * @date: 2023/6/5 8:55 PM
 */
@Data
public class OpsAlarmDetailsResult {
    /**
     * 告警数据id
     */
    private Long id;
    /**
     * 告警内容
     */
    private String content;

    /**
     * 告警名称
     */
    private String name;

    /**
     * 告警对象
     */
    private String target;

    /**
     * 对象类型
     */
    @BeanHelperField(columnName = "objectType")
    private String targetType;

    /**
     * 告警对象实例id
     */
    private String objectInstanceId;

    /**
     * 告警级别
     * 从Code中获取：故障、严重、一般
     */
    @BeanHelperField(columnName = "alarmLevelStatus")
    private String level;

    /**
     * 告警状态
     */
    private String status;

    /**
     * 最后发生时间
     */
    @BeanHelperField(columnName = "lastTime")
    private Date occurTime;

    /**
     * 开始时间
     */
    private Date startTime;


    /**
     * 结束时间
     */
    @BeanHelperField(columnName = "recoveredTime")
    private Date endTime;

    /**
     * 确认人
     */
    private String confirmUser;

    /**
     * 解决人（处理人）
     */
    private String resolveUser;

    /**
     * 确认时间
     */
    private Date confirmTime;

    /**
     * 解决时间
     */
    private Date resolveTime;

    /**
     * 确认详情
     */
    private String confirmContent;
    /**
     * 解决详情
     */
    private String resolveContent;

    /**
     * 告警发送次数
     */
    @BeanHelperField(columnName = "count")
    private Integer sendNumber;

    /**
     * 告警处理状态
     */
    private String processingStatus;

    /**
     * 持续时长
     */
    private String duration;

    /**
     * 云环境id
     */
    private String envId;

    /**
     * 云环境名称
     */
    private String envName;
}
