package com.cloudstar.sdk.monitor.annotations;

import com.cloudstar.sdk.monitor.pojo.MaskSerializer;
import com.cloudstar.common.base.enums.MaskType;
import com.fasterxml.jackson.annotation.JacksonAnnotationsInside;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 掩码自定义注解
 * 
 * @author: zhangqiang
 * @date: 2022/10/17 15:27
 */
@Target({ElementType.METHOD, ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@JacksonAnnotationsInside
@JsonSerialize(using = MaskSerializer.class)
public @interface Mask {

    /**
     * 选择掩码类型
     * @return
     */
    MaskType value();
}