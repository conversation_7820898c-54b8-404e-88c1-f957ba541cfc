package com.cloudstar.sdk.monitor.validator;

import cn.hutool.core.util.ObjectUtil;
import com.cloudstar.sdk.monitor.annotations.HorizontalAuth;
import com.cloudstar.sdk.monitor.service.HorizontalAuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

@Slf4j
@Component
@SuppressWarnings("all")
public class HorizontalAuthValidator implements ConstraintValidator<HorizontalAuth, Object> {

    private HorizontalAuthDto horizontalAuthDto;

    @Autowired(required = false)
    private HorizontalAuthService horizontalAuthService;

    @Override
    public void initialize(HorizontalAuth dataAuthAnnotation) {
        horizontalAuthDto = coverDataAuthDto(dataAuthAnnotation);
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        // null直接判定通过，若不能为空，交给注解@NotNull去验证
        if (ObjectUtil.isEmpty(value)) {
            return true;
        }

        if (ObjectUtil.isNotNull(horizontalAuthService)) {
            try {
                HorizontalAuthThreadContext.setLocalContext(horizontalAuthDto);
                return horizontalAuthService.isValid(value, horizontalAuthDto);
            } finally {
                HorizontalAuthThreadContext.removeLocalContext();
            }
        }
        return true;
    }


    /**
     * 转换实体
     *
     * @param dataFilter dataAuthValid
     */
    private HorizontalAuthDto coverDataAuthDto(HorizontalAuth horizontalAuth) {
        if (ObjectUtil.isNotNull(horizontalAuth)) {
            HorizontalAuthDto horizontalAuthDto = new HorizontalAuthDto();
            horizontalAuthDto.setTable(horizontalAuth.table());
            horizontalAuthDto.setPrimaryKey(horizontalAuth.primaryKey());
            horizontalAuthDto.setTableAlias("");
            horizontalAuthDto.setOrgId(horizontalAuth.orgId());
            horizontalAuthDto.setOwnerAccount(horizontalAuth.ownerAccount());
            horizontalAuthDto.setProjectId(horizontalAuth.projectId());
            horizontalAuthDto.setIgnoreProjectFilter(horizontalAuth.ignoreProjectFilter());
            horizontalAuthDto.setContainParentOrg(horizontalAuth.containParentOrg());
            horizontalAuthDto.setBigDatafilter(horizontalAuth.bigDatafilter());
            horizontalAuthDto.setCloudEnvId(horizontalAuth.cloudEnvId());
            horizontalAuthDto.setUserIdAsValue(horizontalAuth.userIdAsValue());
            horizontalAuthDto.setIgnoreUserScope(horizontalAuth.ignoreUserScope());
            horizontalAuthDto.setOrOrgId(horizontalAuth.orOrgId());
            horizontalAuthDto.setOrProjectId(horizontalAuth.orProjectId());
            horizontalAuthDto.setOrOwnerAccount(horizontalAuth.orOwnerAccount());
            return horizontalAuthDto;
        }
        return null;
    }

}
