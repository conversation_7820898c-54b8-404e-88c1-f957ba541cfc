package com.cloudstar.pojo.user.resp;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 用户登录实体
 *
 * <AUTHOR>
 * @date 2022/7/15 16:47
 */
@Setter
@Getter
public class UserLoginResp {

    /**
     * token
     */
    private String token;

    /**
     * 用户id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userSid;

    /**
     * 是否重置密码
     */
    private boolean isResetPassword;

    /**
     * 密码是否过期
     */
    private boolean isPasswordExpired;
    /**
     * 导航
     */
    private String navigate;

    /**
     * 角色名称
     */
    private String roleNames;
    /**
     * 最后登录时间
     */
    private Date lastLoginTime;

    /**
     * 最后登录ip
     */
    private String lastLoginIp;
    /**
     * 认证状态
     */
    private String authStatus;

    /**
     * 会话id
     */
    private String sessionId;
}
