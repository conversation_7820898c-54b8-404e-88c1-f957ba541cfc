package com.cloudstar.controller.system;

import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.enums.ClusterTypeEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.CrytoUtilSimple;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.sdk.schedule.client.ScheduleClient;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountReqDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import com.cloudstar.service.facade.cluster.ClusterEntityService;
import com.cloudstar.service.facade.datastorage.HcsoObsService;
import com.cloudstar.service.facade.datastorage.SlurmAossService;
import com.cloudstar.service.pojo.vo.requestvo.obs.ObsEntityCreateFolderReq;
import com.cloudstar.service.pojo.vo.requestvo.obs.ObsEntityDeleteReq;
import com.cloudstar.service.pojo.vo.requestvo.obs.ObsEntityPageReq;
import com.cloudstar.service.pojo.vo.requestvo.obs.ObsTemporarySignatureReq;
import com.cloudstar.service.pojo.vo.responsevo.obs.ObsEntityRep;

import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Locale;
import java.util.Optional;

import javax.validation.Valid;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * obs临时授权
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/obs")
@Slf4j
@RequiredArgsConstructor
public class ObsTemporaryVoucherController {

    private final ServerClient serverClient;

    private final ScheduleClient scheduleClient;

    private final ClusterEntityService clusterEntityService;

    private final HcsoObsService hcsoObsService;

    private final SlurmAossService slurmAossService;


    /**
     * 获取obs临时授权访问凭证
     *
     * @param req req
     *
     * @return {@link Rest}
     *
     * <AUTHOR>
     * @date 2022/10/27
     **/
    @GetMapping("/temporary/voucher")
    @CustomerActionLog(Type = ActionLogTypeEnum.OBS_TEMPORARY_VOUCHER)
    public Rest<String> getObsTemporaryVoucher(@SpringQueryMap @Validated ObsTemporarySignatureReq req) {
        ClusterSubAccountRespDto clusterSubAccount = getClusterSubAccountRespDto(req.getClusterId());
        ClusterEntity clusterEntity = clusterEntityService.getById(req.getClusterId());
        if (ClusterTypeEnum.HCSO.getType().equals(clusterEntity.getClusterType())) {
            // 华为
            return Rest.ok(hcsoObsService.getObsTemporaryVoucher(clusterSubAccount, req));
        } else if (Arrays.asList(ClusterTypeEnum.SLURM.getType(), ClusterTypeEnum.BMS.getType())
                         .contains(clusterEntity.getClusterType())) {
            final String clusterStorageType = System.getenv("CLUSTER_STORAGE_TYPE");
            if (ObjectUtil.isNotEmpty(clusterStorageType) && "obs".equals(clusterStorageType.toLowerCase(Locale.ROOT))) {
                return Rest.ok(hcsoObsService.getObsTemporaryVoucher(clusterSubAccount, req));
            } else {
                return Rest.ok(slurmAossService.getObsTemporaryVoucher(clusterSubAccount, req, clusterEntity.getClusterType()));
            }
        }
        return Rest.ok();
    }

    /**
     * 列举对象
     */
    @GetMapping("/entities")
    @CustomerActionLog(Type = ActionLogTypeEnum.OBS_QUERY_ENTITIES)
    public Rest<ObsEntityRep> queryObject(@SpringQueryMap @Validated ObsEntityPageReq req) {
        ClusterSubAccountRespDto clusterSubAccount = getClusterSubAccountRespDto(req.getClusterId());
        ClusterEntity clusterEntity = clusterEntityService.getById(req.getClusterId());
        if (ClusterTypeEnum.HCSO.getType().equals(clusterEntity.getClusterType())) {
            // 华为
            return Rest.ok(hcsoObsService.listObjects(req, clusterSubAccount));

        } else if (Arrays.asList(ClusterTypeEnum.SLURM.getType(), ClusterTypeEnum.BMS.getType())
                         .contains(clusterEntity.getClusterType())) {
            final String clusterStorageType = System.getenv("CLUSTER_STORAGE_TYPE");
            if (ObjectUtil.isNotEmpty(clusterStorageType) && "obs".equals(clusterStorageType.toLowerCase(Locale.ROOT))) {
                return Rest.ok(hcsoObsService.listObjects(req, clusterSubAccount));
            } else {
                // 商汤
                return Rest.ok(slurmAossService.listObjects(req, clusterSubAccount, clusterEntity.getClusterType()));
            }
        }
        return Rest.e(null, "error");
    }

    /**
     * 创建目录
     */
    @PostMapping("/create/folder")
    @CustomerActionLog(Type = ActionLogTypeEnum.OBS_CREATE_FOLDER)
    public Rest<Boolean> createFolder(@RequestBody @Validated ObsEntityCreateFolderReq req) {
        ClusterSubAccountRespDto clusterSubAccount = getClusterSubAccountRespDto(req.getClusterId());
        try {
            ClusterEntity clusterEntity = clusterEntityService.getById(req.getClusterId());
            if (ClusterTypeEnum.HCSO.getType().equals(clusterEntity.getClusterType())) {
                // 华为
                hcsoObsService.createFolder(clusterSubAccount, req.getObjectKey());
            } else if (Arrays.asList(ClusterTypeEnum.SLURM.getType(), ClusterTypeEnum.BMS.getType())
                             .contains(clusterEntity.getClusterType())) {
                final String clusterStorageType = System.getenv("CLUSTER_STORAGE_TYPE");
                if (ObjectUtil.isNotEmpty(clusterStorageType) && "obs".equals(clusterStorageType.toLowerCase(Locale.ROOT))) {
                    // 华为
                    hcsoObsService.createFolder(clusterSubAccount, req.getObjectKey());
                } else {
                    // 商汤
                    slurmAossService.createFolder(clusterSubAccount, req.getObjectKey(), clusterEntity.getClusterType());
                }
            }
            return Rest.ok(true);
        } catch (Exception e) {
            log.error("创建文件目录失败,reason[{}], [userId:{},clusterId:{}]", e.getMessage(), clusterSubAccount.getUserId(), req.getClusterId());
            throw new BizException("创建文件目录失败");
        }
    }

    /**
     * obs对象删除
     */
    @PostMapping("/delete")
    public Rest<Boolean> deleteObsObject(@RequestBody @Valid ObsEntityDeleteReq req) {
        ClusterSubAccountRespDto clusterSubAccount = getClusterSubAccountRespDto(req.getClusterId());
        try {
            ClusterEntity clusterEntity = clusterEntityService.getById(req.getClusterId());
            if (ClusterTypeEnum.HCSO.getType().equals(clusterEntity.getClusterType())) {
                // 华为
                hcsoObsService.deleteObject(clusterSubAccount, req.getObjectKeys());
            } else if (Arrays.asList(ClusterTypeEnum.SLURM.getType(), ClusterTypeEnum.BMS.getType())
                             .contains(clusterEntity.getClusterType())) {
                final String clusterStorageType = System.getenv("CLUSTER_STORAGE_TYPE");
                if (ObjectUtil.isNotEmpty(clusterStorageType) && "obs".equals(clusterStorageType.toLowerCase(Locale.ROOT))) {
                    // 华为
                    hcsoObsService.deleteObject(clusterSubAccount, req.getObjectKeys());
                } else {
                    // 商汤
                    slurmAossService.deleteObject(clusterSubAccount, req.getObjectKeys(), clusterEntity.getClusterType());
                }
            }
            return Rest.ok(true);
        } catch (Exception e) {
            log.error("文件删除失败,reason[{}], [userId:{},clusterId:{}]", e.getMessage(), clusterSubAccount.getUserId(), req.getClusterId());
            throw new BizException("文件删除失败");
        }
    }

    /**
     * 同步观察存储
     *
     * @param dataStorageResourceId 数据存储资源id
     *
     * @return {@link Rest}<{@link Boolean}>
     */
    @GetMapping("/sync")
    public Rest<Boolean> syncObsStorage(@Valid Long dataStorageResourceId) {
        scheduleClient.syncDataStorageById(dataStorageResourceId);
        return Rest.ok(true);
    }

    /**
     * 获取集群信息
     */
    private ClusterSubAccountRespDto getClusterSubAccountRespDto(Long clusterId) {
        ClusterSubAccountReqDto clusterSubAccountReqDto = getClusterSubAccountReqDto(clusterId);
        ClusterSubAccountRespDto data = serverClient.getClusterSubAccount(clusterSubAccountReqDto).getData();
        data.setAccessKey(CrytoUtilSimple.decrypt(data.getAccessKey()));
        data.setSecretKey(CrytoUtilSimple.decrypt(data.getSecretKey()));
        return data;
    }

    /**
     * 根据集群id获取当前登录用户的集群子账户
     *
     * @param clusterId clusterId
     *
     * @return {@link ClusterSubAccountReqDto}
     *
     * <AUTHOR>
     * @date 2022/10/27
     **/
    private ClusterSubAccountReqDto getClusterSubAccountReqDto(Long clusterId) {
        ClusterSubAccountReqDto clusterSubAccountReqDto = new ClusterSubAccountReqDto();

        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Long userSid = Optional.ofNullable(authUser)
                               .map(AuthUser::getUserSid)
                               .orElseThrow(() -> new BizException(BizErrorEnum.MSG_1011_TOKEN_ERROR));
        clusterSubAccountReqDto.setUserSid(userSid);
        clusterSubAccountReqDto.setClusterId(clusterId);

        return clusterSubAccountReqDto;
    }

}
