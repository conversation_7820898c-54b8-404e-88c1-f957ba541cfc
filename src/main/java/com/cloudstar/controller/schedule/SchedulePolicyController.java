package com.cloudstar.controller.schedule;

import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.service.facade.scheudle.SchedulePolicyService;
import com.cloudstar.service.pojo.dto.schedule.QuerySchedulePolicyDto;
import com.cloudstar.service.pojo.dto.schedule.UpdateSchedulePolicyDto;
import com.cloudstar.service.pojo.dto.schedule.UpdateSchedulePolicyStatusDto;
import com.cloudstar.service.pojo.vo.requestvo.schedule.QuerySchedulePolicyReq;
import com.cloudstar.service.pojo.vo.requestvo.schedule.UpdateSchedulePolicyReq;
import com.cloudstar.service.pojo.vo.requestvo.schedule.UpdateSchedulePolicyStatusReq;
import com.cloudstar.service.pojo.vo.responsevo.schedule.SchedulePolicyDetailResp;
import com.cloudstar.service.pojo.vo.responsevo.schedule.SchedulePolicyResp;
import com.cloudstar.service.pojo.vo.responsevo.schedule.SchedulePolicyWeightResp;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 调度策略控制器
 *
 * <AUTHOR>
 * @date 2022/9/13 17:26
 */
@RestController
@RequestMapping("/schedule/policy")
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SchedulePolicyController {

    SchedulePolicyService schedulePolicyService;

    /**
     * 分页查询
     *
     * @param req 筛选条件
     * @param pageForm 分页对象
     *
     * @return 查询结果
     */
    @GetMapping
    public Rest<PageResult<SchedulePolicyResp>> page(QuerySchedulePolicyReq req, PageForm pageForm) {
        return Rest.ok(PageResult.of(
                schedulePolicyService.page(BeanUtil.toBean(req, QuerySchedulePolicyDto.class), pageForm),
                SchedulePolicyResp.class));
    }

    /**
     * 修改策略状态
     *
     * @param req 入参
     *
     * @return 返回值
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.SCHEDULE_POLICY_ENABLE)
    @PostMapping("/status")
    public Rest<Boolean> updateStatus(@Validated @RequestBody UpdateSchedulePolicyStatusReq req) {
        return Rest.ok(schedulePolicyService.updateStatus(BeanUtil.toBean(req, UpdateSchedulePolicyStatusDto.class)));
    }

    /**
     * 查询策略详情
     *
     * @param id id
     *
     * @return 返回值
     */
    @GetMapping("/{id}")
    public Rest<SchedulePolicyDetailResp> queryDetailById(@PathVariable Long id) {
        return Rest.ok(schedulePolicyService.queryDetailById(id));
    }

    /**
     * 修改策略
     *
     * @param req 入参
     *
     * @return 返回值
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.SCHEDULE_POLICY_UPDATE)
    @PostMapping
    public Rest<Boolean> update(@Validated @RequestBody UpdateSchedulePolicyReq req) {
        return Rest.ok(schedulePolicyService.update(BeanUtil.toBean(req, UpdateSchedulePolicyDto.class)));
    }

    /**
     * 作业提交 返回可用策略
     */
    @GetMapping("/enable")
    public Rest<List<SchedulePolicyResp>> queryStatusIsEnable() {
        return Rest.ok(BeanUtil.copyToList(schedulePolicyService.queryStatusIsEnable(), SchedulePolicyResp.class));
    }

    /**
     * 根据策略id 返回对应集群信息
     *
     * @param policyId 策略id
     */
    @GetMapping("/cluster")
    public Rest<List<SchedulePolicyWeightResp>> queryPolicyCluster(@RequestParam Long policyId,
                                                                   @RequestParam(required = false) String clusterType,
                                                                   @RequestParam(required = false, defaultValue = "false") Boolean isHpc) {
        return Rest.ok(schedulePolicyService.queryPolicyCluster(policyId, clusterType, isHpc));
    }
}
