package com.cloudstar.controller.algorithm;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.integration.bss.pojo.RestResult;
import com.cloudstar.integration.bss.pojo.algorithm.MarketSubPageResp;
import com.cloudstar.integration.bss.service.facade.AiMarketAlgorithmService;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.service.facade.algorithm.AlgorithmSubscribeService;
import com.cloudstar.service.pojo.vo.requestvo.algorithm.AlgorithmSubPageReq;
import com.cloudstar.service.pojo.vo.responsevo.algorithm.AlgorithmSubPageResp;
import com.cloudstar.service.pojo.vo.responsevo.bill.BizBillUsageItemPageResp;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 我的订阅
 *
 * <AUTHOR>
 * @date 2024-07-25
 */
@RestController
@RequestMapping("/algorithm_subscribe")
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AlgorithmSubscribeController {


    AlgorithmSubscribeService subscribeService;

    AiMarketAlgorithmService aiMarketAlgorithmService;

    /**
     * 订阅列表
     *
     * @param req 条件
     *
     * @return {@link Rest}<{@link PageResult}<{@link BizBillUsageItemPageResp}>>
     */
    @GetMapping
    public Rest<PageResult<AlgorithmSubPageResp>> getAlgorithmSubPage(AlgorithmSubPageReq req, PageForm pageForm) {
        return Rest.ok(PageResult.of(subscribeService.getPage(req, pageForm), AlgorithmSubPageResp.class));
    }


    /**
     * 查询我的订阅
     */
    @GetMapping("/my/page")
    public Rest myAlgorithmPage(AlgorithmSubPageReq req, PageForm pageForm) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("未获取到用户信息");
        }
        final RestResult result = aiMarketAlgorithmService.getAlgorithmSubscribePage(authUser.getAccount(), req.getName(),
                                                                                     pageForm.getPageNo(), pageForm.getPageSize(), 0);
        int totalRows = result.getTotalRows();
        List<MarketSubPageResp> respList = new ArrayList<>();
        final List list = result.dataToList(MarketSubPageResp.class);
        if (CollectionUtil.isNotEmpty(list)) {
            respList.addAll(list);
        }
        Page<MarketSubPageResp> page = new Page<>();
        page.setTotal(totalRows);
        page.setRecords(respList);
        page.setCurrent(pageForm.getPageNo());
        page.setSize(pageForm.getPageSize());
        return Rest.ok(PageResult.of(page));
    }


}
