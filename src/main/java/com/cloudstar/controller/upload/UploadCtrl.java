package com.cloudstar.controller.upload;

import cn.hutool.core.io.IoUtil;
import com.cloudstar.ConfigService;
import com.cloudstar.PasswordService;
import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.constant.SysMFilePathEnum;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.exception.BizError;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.file.storage.StorageService;
import com.cloudstar.common.file.storage.bean.enums.StoragePathEnum;
import com.cloudstar.common.file.storage.bean.vo.StorageResult;
import com.cloudstar.common.file.storage.utils.StorageUtil;
import com.cloudstar.config.FileSizeConfig;
import com.cloudstar.dao.model.SysMFilePath;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.service.pojo.vo.responsevo.file.FileLinkAndFileNameResp;
import com.cloudstar.service.sysmfilepath.SysMFilePathService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 上传接口.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/upload")
@Slf4j
@RequiredArgsConstructor
public class UploadCtrl {

    private final SysMFilePathService sysMFilePathService;

    private final StorageService storageService;

    private final PasswordService passwordService;

    private final ConfigService configService;


    /**
     * 帖子 浏览图片
     *
     * @param path 路径
     * @param files 文件
     *
     * @return {@link Rest}<{@link List}<{@link String}>>
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.UPLOAD_FILE)
    @PostMapping("/{path}")
    public Rest<List<String>> post(@PathVariable("path") String path, @RequestParam("file") MultipartFile[] files) {
        String operationType = "";
        boolean compress = false;
        // 校验工单附件上传
        if (StrUtil.equals(SysMFilePathEnum.USER_AUTH.getType(), path)) {
            verificationUserAuthUpload(files);
            operationType = SysMFilePathEnum.USER_AUTH.getType();
            compress = true;
        } else if (StrUtil.equals(SysMFilePathEnum.TICKET.getType(), path)) {
            verificationUserTicketUpload(files);
            operationType = SysMFilePathEnum.TICKET.getType();
        }
        return Rest.ok(uploadFile(path, files, operationType, compress));

    }

    /**
     * 上传文件
     *
     * @param path 路径
     * @param files 文件
     * @param type 类型
     * @param compress 压缩
     *
     * @return {@link List}<{@link String}>
     */
    private List<String> uploadFile(String path, MultipartFile[] files, String type, boolean compress) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        List<String> result = new ArrayList<>();
        for (MultipartFile file : files) {
            String password = passwordService.generatePasswordByDefault();
            StorageResult storageResult;

            String filePath = "";
            if (StrUtil.equals(path, SysMFilePathEnum.USER_AUTH.getType())) {
                filePath = StoragePathEnum.ID_CARD.getPath(path);
            } else if (StrUtil.equals(path, SysMFilePathEnum.TICKET.getType())) {
                filePath = StoragePathEnum.TICKET.getPath(path);
            }
            String fileName = file.getOriginalFilename();
            SysMFilePath sysMFilePath = SysMFilePath.builder()
                                                    .fileName(fileName)
                                                    .fileNum(UUID.randomUUID().toString())
                                                    .orgSid(authUser.getOrgSid())
                                                    .userSid(authUser.getUserSid())
                                                    .userType(authUser.getUserType())
                                                    .operationType(type)
                                                    .build();
            if (compress) {
                storageResult = storageService.saveCompress(file, password, filePath);
                sysMFilePath.setCompressPassword(password);
            } else {
                storageResult = storageService.saveMultipartFile(file, filePath);
            }
            assert storageResult != null;
            sysMFilePath.setFilePath(storageResult.getRelativeNginxUrl());
            sysMFilePathService.save(sysMFilePath);
            result.add(sysMFilePath.getId().toString());
        }
        return result;
    }

    private void verificationUserTicketUpload(MultipartFile[] files) {
        if (files.length > 5) {
            BizError.e(BizErrorEnum.MSG_1050_FILE_NUM_ERROR);
        }
        for (MultipartFile file : files) {
            String suffix = FileUtil.getSuffix(file.getOriginalFilename());
            if (!StorageUtil.fileType.contains(suffix)) {
                BizError.e(BizErrorEnum.MSG_1048_FILE_TYPE_ERROR);
            }
            double fileSize = (double) file.getSize() / 1048576;
            //获取配置
            FileSizeConfig config = configService.getConfig(ConfigType.FILE_SIZE_CONFIG);
            if (fileSize > config.getMax().intValue()) {
                BizError.e(BizErrorEnum.MSG_1049_FILE_SIZE_ERROR);
            }
        }
    }

    /**
     * 上传文件返回文件名及文件链接
     */
    @PostMapping("/file/{path}")
    public Rest<FileLinkAndFileNameResp> getFileLinkAndName(@PathVariable("path") String path, @RequestParam("file") MultipartFile[] files) {
        String operationType = "";
        boolean compress = false;
        List<String> strings = uploadFile(path, files, operationType, compress);
        String fileId = strings.get(0);

        // 获取文件路径
        SysMFilePath symFilePath = sysMFilePathService.getById(Long.valueOf(fileId));
        StorageResult file = storageService.getFile(symFilePath.getFilePath());

        String fileUrl = file.getFileUrl();
        String fileName = file.getFileName();

        FileLinkAndFileNameResp fileLinkAndFileNameResp = FileLinkAndFileNameResp
                .builder()
                .fileName(fileName)
                .fileLink(fileUrl)
                .build();
        return Rest.ok(fileLinkAndFileNameResp);
    }

    /**
     * 浏览图片
     *
     * @param response 响应
     * @param fileId 文件标识
     */
    @GetMapping("/image")
    public void downloadFile(HttpServletResponse response, String fileId) {
        SysMFilePath symFilePath = sysMFilePathService.getById(Long.valueOf(fileId));
        String fileName = symFilePath.getFileName();
        InputStream in = null;
        try {
            if (StrUtil.isNotBlank(symFilePath.getCompressPassword())) {
                Map<String, InputStream> decompress = storageService.getFileDecompressToMap(
                        symFilePath.getFilePath(), symFilePath.getCompressPassword());
                in = decompress.get(fileName);
            } else {
                StorageResult file = storageService.getFile(symFilePath.getFilePath());
                in = file.getInputStream();
            }
            StorageUtil.fillResponse(response, fileName);
            IoUtil.copy(in, response.getOutputStream());
            IoUtil.flush(response.getOutputStream());
        } catch (FileNotFoundException e) {
            log.error("文件未找到:{}", symFilePath.getFilePath());
            throw new BizException("文件未找到");
        } catch (Exception e) {
            log.error("预览图片异常", e);
            throw new BizException("预览图片失败");
        } finally {
            IoUtil.close(in);
        }
    }

    private void verificationUserAuthUpload(MultipartFile[] files) {
        if (files.length > 2) {
            BizError.e(BizErrorEnum.MSG_1050_FILE_NUM_ERROR);
        }
        for (MultipartFile file : files) {
            String suffix = FileUtil.getSuffix(file.getOriginalFilename());
            if (!StorageUtil.authFileType.contains(suffix)) {
                BizError.e(BizErrorEnum.MSG_1048_FILE_TYPE_ERROR);
            }
            double fileSize = (double) file.getSize() / 1048576;
            //获取配置
            FileSizeConfig config = configService.getConfig(ConfigType.FILE_SIZE_CONFIG);
            if (fileSize > config.getMax().intValue()) {
                BizError.e(BizErrorEnum.MSG_1049_FILE_SIZE_ERROR);
            }
        }
    }

}
