package com.cloudstar.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.dao.mapper.tenantmapping.ClusterSubAccountMapper;
import com.cloudstar.dao.mapper.tenantmapping.ClusterUserMappingMapper;
import com.cloudstar.dao.model.tenantmapping.ClusterSubAccount;
import com.cloudstar.dao.model.tenantmapping.ClusterUserMapping;
import com.cloudstar.service.grpc.AgentUserGrpc;
import com.cloudstar.service.grpcservice.facade.AgentSysProcessStepsService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;

/**
 * 关键步骤记录
 *
 * <AUTHOR>
 * @date 2022/11/11
 */
@RestController
@RequestMapping("/steps")
@RequiredArgsConstructor
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SysProcessStepsController {
    AgentSysProcessStepsService agentSysProcessStepsService;
    ClusterSubAccountMapper clusterSubAccountMapper;
    ClusterUserMappingMapper clusterUserMappingMapper;


    /**
     * 子用户删除
     */
    @PostMapping("/sub_user/delete/{userSid}")
    Rest<HashMap<Long, List<Integer>>> delSubUser(@PathVariable Long userSid) {
        HashMap<Long, List<Integer>> processStepsMap = new HashMap<>();

        List<ClusterSubAccount> clusterSubAccounts =
                clusterSubAccountMapper.selectList(Wrappers.lambdaQuery(ClusterSubAccount.class).eq(ClusterSubAccount::getUserSid, userSid));
        clusterSubAccountMapper.delete(Wrappers.lambdaUpdate(ClusterSubAccount.class).eq(ClusterSubAccount::getUserSid, userSid));
        clusterUserMappingMapper.delete(Wrappers.lambdaUpdate(ClusterUserMapping.class).eq(ClusterUserMapping::getUserSid, userSid));

        for (ClusterSubAccount clusterSubAccount : clusterSubAccounts) {
            log.info("executing  build targetServer by adapterUuid[{}]", clusterSubAccount.getAdapterUuid());
            agentSysProcessStepsService.build(clusterSubAccount.getAdapterUuid(), AgentUserGrpc.AgentUserBlockingStub.class);
            processStepsMap.put(clusterSubAccount.getId(), agentSysProcessStepsService.delSubUserByUserId(clusterSubAccount.getAccountUuid()));
        }
        return Rest.ok(processStepsMap);

    }

}
