package com.cloudstar.controller.message;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.model.SysMMessage;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.service.facade.message.SysMMessageService;
import com.cloudstar.service.pojo.dto.message.MessageInfoDto;
import com.cloudstar.service.pojo.vo.requestvo.message.DeleteMessageRequest;
import com.cloudstar.service.pojo.vo.requestvo.message.MessageRequest;
import com.cloudstar.service.pojo.vo.requestvo.message.UpdateMessageRequest;
import com.cloudstar.service.pojo.vo.responsevo.message.MessageInfoResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 *消息管理控制器
 *
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("message")
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MessageController {

    SysMMessageService messageService;

    /**
     * 获取用户的消息信息分页列表
     *
     * @param messageRequest 包含用户ID和已读状态的消息请求对象
     * @param pageForm 分页参数，包括页码、每页大小、排序字段、排序方向等
     * @return 包含消息信息的分页结果
     */
    @GetMapping("info/list")
    public Rest<PageResult<MessageInfoResponse>> getMessageInfoPage(MessageRequest messageRequest, PageForm pageForm) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Long userSid = authUser.getUserSid();
        MessageInfoDto messageInfoDto = new MessageInfoDto();
        messageInfoDto.setToUserId(userSid);
        messageInfoDto.setReadFlag(messageRequest.getReadFlag());
        messageInfoDto.setIsDeleted(false);
        pageForm.setSortDataField("createdDt");
        pageForm.setAsc(false);
        Page<SysMMessage> sysMMessagePage = messageService.getMessageInfo(messageInfoDto, pageForm);
        return Rest.ok(PageResult.of(sysMMessagePage, MessageInfoResponse.class));
    }


    /**
     * 更新指定消息的已读状态
     *
     * @param updateMessageRequest 包含消息ID和已读状态的请求对象
     * @return 表示更新是否成功的Rest对象
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.READ_MESSAGE)
    @PostMapping("/read")
    public Rest<Boolean> readMessage(@RequestBody UpdateMessageRequest updateMessageRequest) {

        Boolean readFlag = messageService.updateReadFlag(updateMessageRequest);

        return Rest.ok(readFlag);
    }

    /**
     * 将所有消息标记为已读状态
     *
     * @return 表示更新是否成功的Rest对象
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.IGNORE_ALL_MESSAGE)
    @PostMapping("/ignore/all")
    public Rest<Boolean> updateAllMessageReadFlag() {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Long userSid = authUser.getUserSid();
        Boolean result = messageService.updateAllMessageReadFlag(userSid);
        return Rest.ok(result);
    }

    /**
     * 根据消息ID删除消息
     *
     * @param deleteMessageRequest 包含消息ID的删除请求对象
     * @return 表示删除是否成功的Rest对象
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.DELETE_MESSAGE)
    @PostMapping("/delete")
    public Rest<Boolean> deleteMessage(@RequestBody DeleteMessageRequest deleteMessageRequest) {
        Boolean result = messageService.deleteMessage(deleteMessageRequest);
        return Rest.ok(result);
    }


}
