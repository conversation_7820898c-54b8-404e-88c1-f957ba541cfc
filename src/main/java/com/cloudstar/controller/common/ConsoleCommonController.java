package com.cloudstar.controller.common;

import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.dao.mapper.user.UserEntityMapper;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.service.validation.DoubleValidationService;
import com.cloudstar.service.validation.bean.dto.DoubleValidationDto;
import com.cloudstar.service.validation.bean.req.DoubleValidationReq;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import cn.hutool.core.bean.BeanUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 公共控制器
 *
 * <AUTHOR> Created on 2022/9/3
 * @date 2022/09/03
 */
@RestController
@RequestMapping("/common")
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ConsoleCommonController {

    DoubleValidationService doubleValidationService;

    UserEntityMapper userEntityMapper;

    ServerClient serverClient;

    /**
     * 二次验证密码
     */
    @GetMapping("/double_validation")
    public Rest doubleValidation(@RequestBody DoubleValidationReq request) {
        DoubleValidationDto dto = BeanUtil.copyProperties(request, DoubleValidationDto.class);
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        dto.setUserSid(authUser.getUserSid());
        dto.setUserType(authUser.getAccountType());
        UserEntity managerEntity = userEntityMapper.selectById(authUser.getUserSid());
        dto.setTargetPassword(managerEntity.getPassword());
        return Rest.ok(doubleValidationService.validation(dto));
    }

    /**
     * 获取ak，sk信息
     */
    @GetMapping("/ak_sk_info")
    public Rest getAkSk() {
        return serverClient.getAkSk();
    }
}
