package com.cloudstar.controller;


import com.cloudstar.ConfigService;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.component.elasticsearch.service.MirrorLogService;
import com.cloudstar.config.ObsAdminConfig;
import com.cloudstar.enums.VolcanoSchedulerEnum;
import com.cloudstar.integration.hcs.service.facade.HcsSwrService;
import com.cloudstar.integration.hcs.service.facade.HcsUserService;
import com.cloudstar.integration.nexus.enums.PrivilegeAction;
import com.cloudstar.integration.nexus.pojo.HttpRestResult;
import com.cloudstar.integration.nexus.pojo.privilege.Privilege;
import com.cloudstar.integration.nexus.pojo.role.Role;
import com.cloudstar.integration.nexus.pojo.role.UpdateRoleReq;
import com.cloudstar.integration.nexus.pojo.selector.CreateContentSelectorReq;
import com.cloudstar.integration.nexus.service.NexusApi;
import com.cloudstar.k8s.pojo.vo.requestvo.CreateImageSecretReq;
import com.cloudstar.k8s.pojo.vo.requestvo.CreatePvcReq;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.Job;
import com.cloudstar.k8s.service.facade.CrdService;
import com.cloudstar.k8s.service.facade.DeploymentService;
import com.cloudstar.k8s.service.facade.PersistentVolumeClaimService;
import com.cloudstar.k8s.service.facade.SecretService;
import com.cloudstar.k8s.service.facade.VolcanoJobResourceService;
import com.cloudstar.service.grpc.AgentTrainingJobProto;
import com.cloudstar.service.grpcservice.server.exector.hcs.HcsAgentUserExector;
import com.cloudstar.utils.MinioManager;
import com.cloudstar.utils.VolcanoJobCommandUtil;
import com.github.dockerjava.api.DockerClient;
import com.github.dockerjava.api.command.PullImageCmd;
import com.github.dockerjava.api.command.PullImageResultCallback;
import com.github.dockerjava.api.command.PushImageCmd;
import com.github.dockerjava.api.command.TagImageCmd;
import com.github.dockerjava.api.model.Image;
import com.github.dockerjava.api.model.PullResponseItem;
import com.github.dockerjava.core.DefaultDockerClientConfig;
import com.github.dockerjava.core.DockerClientBuilder;
import com.github.dockerjava.core.DockerClientConfig;
import com.github.dockerjava.core.command.PushImageResultCallback;

import org.springframework.core.io.ClassPathResource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;
import org.yaml.snakeyaml.Yaml;

import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.ResponseTransformer;
import software.amazon.awssdk.http.SdkHttpClient;
import software.amazon.awssdk.http.SdkHttpConfigurationOption;
import software.amazon.awssdk.http.apache.ApacheHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.S3Configuration;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.utils.AttributeMap;

/**
 * test
 *
 * <AUTHOR>
 */
@SuppressFBWarnings("HARD_CODE_PASSWORD")
@Slf4j
@RestController
public class TestController {


    @Resource
    CrdService crdService;

    @Resource
    KubernetesClient kubernetesClient;

    @Resource
    VolcanoJobResourceService volcanoJobResourceService;

    @Resource
    SecretService secretService;

    @Resource
    PersistentVolumeClaimService persistentVolumeClaimService;

    @Resource
    ConfigService configService;

    @Resource
    private NexusApi nexusApi;

    @Resource
    private DeploymentService deploymentService;

    @Resource
    private MirrorLogService mirrorLogService;

    @Resource
    private HcsUserService hcsUserService;

    @Resource
    private HcsSwrService hcsSwrService;

    @Resource
    private HcsAgentUserExector hcsAgentUserExector;

    public static final String EXPRESSION = "format == \"docker\" and (path =~ \"/v2/|^/v2/%s/.*\" )";
    public static final String CONTENT_SELECTOR = "ContentSelector";
    public static final String STRING = "_";
    public static final String HUB_DEFAULT = "ImageHubDefault";
    public static final String PERMISSION = "permission";
    public static final String VOLCANO_NAMESPACE = "volcano-system";
    public static final String NODE_SELECTOR_PREFIX = "--node-selector=";
    public static final String NODE_LABEL_KEY = "nodetype";

    /**
     * grpc 测试
     */
    @GetMapping("/grpc/client/test")
    public void test() {
        try {
            CreatePvcReq req = new CreatePvcReq();
            req.setName("my-pvc");
            req.setSize(2);
            final boolean pvc = persistentVolumeClaimService.createPvc("308e85afe37647dd8edc4816845c172f", req);
            System.out.println(pvc);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 创建job
     *
     * @throws IOException io
     */
    @GetMapping("/create/job/test")
    public void test1() throws IOException {
        final boolean existCrd = volcanoJobResourceService.isExistJob("bssadmin", "job-test");
        if (existCrd) {
            log.error("作业创建失败，作业名称已存在:{}", "job-test");
        }
        InputStream inputStream = null;
        org.springframework.core.io.Resource templateResource = new ClassPathResource("k8s/volcano-job-demo.yaml",
                                                                                      this.getClass().getClassLoader());
        inputStream = templateResource.getInputStream();
        Yaml yaml = new Yaml();
        final Job job = yaml.loadAs(inputStream, Job.class);
        job.getMetadata().setName("job-test");
        final String downloadScriptUrl = "https://************:32296/download-py";
        final String endpoint = "https://************:32296";
        final String ak = "et4AyzzgmstKkvsHSHlI";
        final String sk = "XglPajavcwudGWgm5bnQCfgT5GElgI2ZoeVM9oZW";
        final String bucketName = "bssadmin-sub";
        final String dataPrefixDir = "1404216341258240/training-data";
        final String codePrefixDir = "1404216341258240/training-code";
        final String uploadPrefixDir = "1404216341258240/training-model";
        final String envParams = "";
        final ArrayList<AgentTrainingJobProto.ParamsGroup> hyperParams = CollectionUtil.newArrayList();
        ArrayList<String> startCommandList = new ArrayList<>();
        startCommandList.add("/bin/bash");
        startCommandList.add("-c");
        StringBuilder startSb = new StringBuilder(VolcanoJobCommandUtil.initEnv(downloadScriptUrl, envParams));
        startSb.append(VolcanoJobCommandUtil.downloadData(endpoint, ak, sk, bucketName, dataPrefixDir));
        startSb.append(VolcanoJobCommandUtil.downloadAlgorithm(endpoint, ak, sk, bucketName, codePrefixDir, codePrefixDir));
        startSb.append(VolcanoJobCommandUtil.pythonRun("1404216341258240/training-code/train_mnist_tf2.1.py",
                                                       dataPrefixDir, VolcanoJobCommandUtil.createHyperParam(hyperParams)));
        startSb.append(VolcanoJobCommandUtil.uploadModel(endpoint, ak, sk, bucketName, uploadPrefixDir));
        startCommandList.add(startSb.toString());
        job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setName("job-test");
        job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setCommand(startCommandList);
        final boolean flag = volcanoJobResourceService.createJob("bssadmin", job);
        System.out.println(flag);
    }

    /**
     * secret 测试
     */
    @GetMapping("/create/secret/test")
    public void createSecret() {
        CreateImageSecretReq req = new CreateImageSecretReq();
        req.setRegistryUrl("cfn-nexus.rightcloud.com.cn");
        req.setUsername("308e85afe37647dd8edc4816845c172f");
        req.setPassword("e177c77077c34695a3c44fcb5e6daa9e");
        final boolean imageSecret = secretService.createImageSecret("308e85afe37647dd8edc4816845c172f", req);
        System.out.println(imageSecret);
    }


    /**
     * 模型集市公共桶复制测试
     */
    @GetMapping("/obs/copy/test")
    public void obsCopyTest() {
        String nodeLabel = "volcano.sh/nodetype:nvidia-a100-training";
        deleteDeployArgs(nodeLabel);
    }

    private void deleteDeployArgs(String nodeLabel) {
        if (ObjectUtil.isNotEmpty(nodeLabel) && nodeLabel.contains(NODE_LABEL_KEY)) {
            final String schedulerName = getScheduleName(nodeLabel);
            final List<String> deploymentArgs = deploymentService.getDeploymentArgs(VOLCANO_NAMESPACE, schedulerName);
            final List<String> collect = deploymentArgs.stream().filter(s -> !s.contains(nodeLabel)).collect(Collectors.toList());
            deploymentService.updateDeploymentArgs(VOLCANO_NAMESPACE, schedulerName, collect);
        }
    }

    private void addDeployArgs(String nodeLabel) {
        if (ObjectUtil.isNotEmpty(nodeLabel) && nodeLabel.contains(NODE_LABEL_KEY)) {
            final String schedulerName = getScheduleName(nodeLabel);
            final List<String> deploymentArgs = deploymentService.getDeploymentArgs(VOLCANO_NAMESPACE, schedulerName);
            deploymentArgs.add(NODE_SELECTOR_PREFIX + nodeLabel);
            deploymentService.updateDeploymentArgs(VOLCANO_NAMESPACE, schedulerName, deploymentArgs);
        }
    }

    private String getScheduleName(String nodeLabel) {
        final String[] split = nodeLabel.split("-");
        String usedType = split[split.length - 1];
        final String schedulerName = VolcanoSchedulerEnum.getSchedulerName(usedType);
        return schedulerName;
    }

    /**
     * 模型集市公共桶复制测试
     */
    @GetMapping("/deploy/test")
    public void updateDeployTest() {
        ObsAdminConfig config = configService.getConfig(ConfigType.OBS_ADMIN_CONFIG);
        String accessKey = config.getAccessKey().stringValue();
        String secretKey = config.getSecretKey().stringValue();
        String obsUrl = config.getObsUrl().stringValue();
        MinioManager minioManager = new MinioManager(obsUrl, accessKey, secretKey);

        //移除权限
        minioManager.addGetResourcePolicy("308e85afe37647dd8edc4816845c172f-policy", "arn:aws:s3:::aimarket-bucket/1412981704048640/*");
    }

    /**
     * 模型集市公共桶下载测试
     */
    @GetMapping("/obs/download/file/test")
    public void obsDownloadFileTest() {
        Region region = Region.US_EAST_1;
        S3ClientBuilder s3ClientBuilder = S3Client.builder()
                                                  .credentialsProvider(StaticCredentialsProvider.create(
                                                          AwsBasicCredentials.create("308e85afe37647dd8edc4816845c172f",
                                                                                     "acc60551156a4c8884aeee4ee7da90a0")))
                                                  .endpointOverride(URI.create("https://minio.cfn-20230109.com:8444"))
                                                  .region(region)
                                                  .serviceConfiguration(S3Configuration.builder()
                                                                                       .pathStyleAccessEnabled(true)
                                                                                       .chunkedEncodingEnabled(false)
                                                                                       .build());

        AttributeMap attributeMap = AttributeMap.builder()
                                                .put(SdkHttpConfigurationOption.TRUST_ALL_CERTIFICATES, true)
                                                .build();
        SdkHttpClient httpClient = ApacheHttpClient.builder()
                                                   .buildWithDefaults(attributeMap);
        s3ClientBuilder.httpClient(httpClient);
        final S3Client s3Client = s3ClientBuilder.build();
        GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                                                            .bucket("aimarket-bucket")
                                                            .key("1410563397541888/code/train_mnist_tf2.1.py")
                                                            .build();

        s3Client.getObject(getObjectRequest, ResponseTransformer.toFile(Paths.get("C:\\tmp\\train_mnist_tf2.1.py")));
    }

    /**
     * 模型集市公共桶下载测试
     */
    @GetMapping("/image/hub/test")
    public void imageHubTest() throws InterruptedException {
        // 创建 DockerClient 实例
        String username = "admin";
        String password = "1q2w3e4r%T";
        String registryAddress = "nexus.cfn-20230109.com:8444";
        // 配置 Docker 的端点
        DockerClientConfig config = DefaultDockerClientConfig.createDefaultConfigBuilder()
                                                             .withDockerHost("tcp://***********:2375")
                                                             .withRegistryUsername(username)
                                                             .withRegistryPassword(password)
                                                             .withRegistryUrl(registryAddress)
                                                             .build();
        DockerClient dockerClient = DockerClientBuilder.getInstance(config).build();
        final long startTime = System.currentTimeMillis();
        PullImageResultCallback pullExec = new PullImageResultCallback() {
            @Override
            public void onNext(PullResponseItem item) {
                System.out.println(item.getStatus());
            }
        };
        AtomicReference<String> imageId = new AtomicReference<>();
        final PullImageCmd pullImageCmd = dockerClient.pullImageCmd(
                "nexus.cfn-20230109.com:8444/308e85afe37647dd8edc4816845c172f/jupyter:with-ssh-3");
        pullImageCmd.exec(pullExec).awaitCompletion(30, TimeUnit.MINUTES);
        final List<Image> imageList = dockerClient.listImagesCmd().exec();
        if (CollectionUtil.isNotEmpty(imageList)) {
            imageList.stream().forEach(image -> {
                if (image.getRepoTags()[0].equals("nexus.cfn-20230109.com:8444/308e85afe37647dd8edc4816845c172f/jupyter:with-ssh-3")) {
                    imageId.set(image.getId());
                }
            });
        }
        if (ObjectUtil.isNotEmpty(imageId.get())) {
            String fullImageName = "nexus.cfn-20230109.com:8444" + "/aimarket/123456/jupyter";
            String fullImageNameWithTag = fullImageName + ":" + "v1.0.0";
            final TagImageCmd tagImageCmd = dockerClient.tagImageCmd(imageId.get(), fullImageName, "v1.0.0");
            tagImageCmd.exec();
            System.out.println(tagImageCmd);
            final PushImageCmd pushImageCmd = dockerClient.pushImageCmd(fullImageNameWithTag);
            System.out.println(pushImageCmd);
            pushImageCmd.exec(new PushImageResultCallback()).awaitCompletion();
        }
        final long endTime = System.currentTimeMillis();
        System.out.println("推镜像用时:" + (endTime - startTime));

        final String userId = "123456";
        //1.发布算法时 给该镜像创建选择器和权限
        //创建内容选择器
        String expression = String.format(EXPRESSION, "aimarket/" + userId);
        String contentSelectorName = userId + STRING + CONTENT_SELECTOR;
        CreateContentSelectorReq createContentSelectorReq = new CreateContentSelectorReq();
        createContentSelectorReq.setDescription(userId);
        createContentSelectorReq.setExpression(expression);
        createContentSelectorReq.setName(contentSelectorName);
        //没创建过 才去创建
        HttpRestResult<?> contentSelector = nexusApi.createContentSelector(createContentSelectorReq);
        if (!contentSelector.isStatus()) {
            throw new BizException("创建内容选择器失败:" + contentSelector.getMessage());
        }
        //创建权限
        String permissionName = userId + STRING + PERMISSION;
        Privilege privilege = new Privilege();
        privilege.setActions(List.of(PrivilegeAction.READ.toValue()));
        privilege.setContentSelector(contentSelectorName);
        privilege.setDescription(userId + STRING + "权限");
        privilege.setFormat("*");
        privilege.setName(permissionName);
        privilege.setRepository(HUB_DEFAULT);
        HttpRestResult<Void> createPermissionHttpRestResult = nexusApi.createRepositoryContentSelectorPrivilege(privilege);
        if (!createPermissionHttpRestResult.isStatus()) {
            throw new BizException("创建权限失败:" + createPermissionHttpRestResult.getMessage());
        }
        //2.订阅时，查询用户角色，给用户角色授权
        final HttpRestResult<Role> roleHttpRestResult = nexusApi.getRole("308e85afe37647dd8edc4816845c172f_Role");
        if (!roleHttpRestResult.isStatus()) {
            throw new BizException("获取角色失败:" + roleHttpRestResult.getMessage());
        }
        final Role role = roleHttpRestResult.getData();
        role.getPrivileges().add(permissionName);
        UpdateRoleReq updateRoleReq = BeanUtil.copyProperties(role, UpdateRoleReq.class);
        final HttpRestResult<Void> updateRole = nexusApi.updateRole(role.getId(), updateRoleReq);
        if (!updateRole.isStatus()) {
            throw new BizException("修改角色权限失败:" + updateRole.getMessage());
        }
        final long authTime = System.currentTimeMillis();
        System.out.println("镜像授权时间:" + (authTime - endTime));
    }

    /**
     * 测试
     */
    @GetMapping("/delete/hcs_user/{userId}")
    public Rest<Boolean> deleteHcsUser(@PathVariable("userId") String userId) {
        try {
            //删除租户
            hcsAgentUserExector.deleteHcsUser(userId);
            return Rest.ok(true);
        } catch (Exception e) {
            e.printStackTrace();
            return Rest.ok(false);
        }

    }

}
