package com.cloudstar.controller.cluster;

import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.sdk.server.pojo.ClusterFlavorReqDto;
import com.cloudstar.sdk.server.pojo.ClusterFlavorResDto;
import com.cloudstar.service.facade.cluster.ClusterFlavorService;
import com.cloudstar.service.pojo.dto.cluster.ClusterFlavorUpdateDto;
import com.cloudstar.service.pojo.vo.requestvo.cluster.ClusterFlavorUpdateReq;
import com.cloudstar.service.pojo.vo.requestvo.cluster.CreateClusterFlavorReq;
import com.cloudstar.service.pojo.vo.requestvo.cluster.QueryClusterFlavorReq;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterFlavorInfoResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterFlavorResp;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 集群资源规格表;(cluster_flavor)表控制层
 *
 * <AUTHOR> scx
 * @date : 2022-7-15
 */
@RestController
@RequestMapping("/cluster_flavor")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ClusterFlavorController {

    ClusterFlavorService clusterFlavorService;

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     *
     * @return 实例对象
     */
    @GetMapping("/{id}")
    public Rest<ClusterFlavorInfoResp> queryById(@PathVariable Long id) {
        return Rest.ok(clusterFlavorService.queryById(id));
    }

    /**
     * 分页查询
     *
     * @param req 筛选条件
     * @param pageForm 分页对象
     *
     * @return 查询结果
     */
    @GetMapping
    public Rest<PageResult<ClusterFlavorResp>> page(@Validated QueryClusterFlavorReq req,
                                                    PageForm pageForm) {
        return Rest.ok(clusterFlavorService.page(req, pageForm));
    }

    /**
     * 修改资源规格信息
     *
     * @param req 入参
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.CLUSTER_FLAVOR_UPDATE)
    @PostMapping("/update")
    public Rest<Boolean> update(@RequestBody @Validated ClusterFlavorUpdateReq req) {
        return Rest.ok(clusterFlavorService.update(BeanUtil.toBean(req, ClusterFlavorUpdateDto.class)));
    }

    /**
     * 查询res池类型
     *
     * @param resPoolType res池类型
     *
     * @return {@link Rest}<{@link List}<{@link ClusterFlavorInfoResp}>>
     */
    @GetMapping("/res_pool_type")
    public Rest<List<ClusterFlavorInfoResp>> queryByResPoolType(@RequestParam("resPoolType") String resPoolType,
                                                                @RequestParam("clusterId") Long clusterId) {
        return Rest.ok(clusterFlavorService.queryByresPoolType(resPoolType, clusterId));
    }


    /**
     * 获取算力资源类型
     *
     * @param req 要求事情
     *
     * @return {@link Rest}<{@link ClusterFlavorResDto}>
     */
    @GetMapping("/query_by_engine")
    public Rest<List<ClusterFlavorResDto>> getClusterFlavorByEngine(ClusterFlavorReqDto req) {
        return Rest.ok(clusterFlavorService.getClusterFlavor(req));
    }


    /**
     * 创建规格
     *
     * @param req 入参
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.CLUSTER_FLAVOR_CREATE)
    @PostMapping("/create")
    public Rest<Boolean> create(@RequestBody @Validated CreateClusterFlavorReq req) {
        return Rest.ok(clusterFlavorService.createClusterFlavor(req));
    }


    /**
     * 删除规格
     *
     * @param id 入参
     */
    @CustomerActionLog(Type = ActionLogTypeEnum.CLUSTER_FLAVOR_DELETE)
    @PostMapping("/delete/{id}")
    public Rest<Boolean> delete(@PathVariable Long id) {
        return Rest.ok(clusterFlavorService.deleteClusterFlavor(id));
    }

}
