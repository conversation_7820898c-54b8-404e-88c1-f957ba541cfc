package com.cloudstar.controller.tenant;

import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.service.facade.log.ActionLogService;
import com.cloudstar.service.pojo.vo.requestvo.tenant.UserActionLogRequest;
import com.cloudstar.service.pojo.vo.responsevo.tenant.UserActionLogResponse;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户操作日志
 *
 * <AUTHOR>
 * @description 用户操作日志控制层
 * @date 2022/10/31 15:27
 */
@Slf4j
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserActionLogController {

    ActionLogService actionLogService;
    /**
     * 获取用户操作日志列表
     *
     * @param userActionLogRequest 用户操作日志请求
     * @param pageForm             分页
     * @return 用户操作日志列表
     */
    @GetMapping("/action/logs")
    public Rest<PageResult<UserActionLogResponse>> getActionLogsList(UserActionLogRequest userActionLogRequest,
                                                                     PageForm pageForm) {
        return Rest.ok(actionLogService.getUserActionLogs(userActionLogRequest, pageForm));
    }
}
