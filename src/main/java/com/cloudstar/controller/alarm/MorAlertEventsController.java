package com.cloudstar.controller.alarm;


import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.sdk.monitor.result.MorAlertEventsListResult;
import com.cloudstar.sdk.monitor.form.MorAlertEventsPageForm;
import com.cloudstar.sdk.monitor.form.MorAlertEventsProcessForm;
import com.cloudstar.sdk.monitor.result.PageResult;
import com.cloudstar.service.facade.alarm.MorAlertEventsService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@RestController
@RequestMapping("/alert/events")
@AllArgsConstructor
public class MorAlertEventsController {

    private final MorAlertEventsService morAlertEventsService;


    /**
     * 详情
     */
    @GetMapping("/info/{hash}")
    public Rest<MorAlertEventsListResult> info(@PathVariable String hash) {
        return morAlertEventsService.info(hash);
    }

    /**
     * 分页列表
     */
    @GetMapping("/page")
    public Rest<PageResult<MorAlertEventsListResult>> page(MorAlertEventsPageForm form) {
        return morAlertEventsService.page(form);
    }

    /**
     * 导出
     */
    @GetMapping("/export")
    public Rest<Void> export(MorAlertEventsPageForm form) {
        return morAlertEventsService.export(form);
    }

    /**
     * 处理
     */
    @PutMapping("/process/{method}")
    public Rest<Boolean> process(@Valid @RequestBody MorAlertEventsProcessForm form,
                                 @PathVariable String method) {
        return morAlertEventsService.process(form, method);
    }

    /**
     * 清除
     */
    @DeleteMapping("/clear/{hashList}")
    public Rest<Boolean> clear(@PathVariable String hashList) {
        return morAlertEventsService.clear(hashList);
    }


}
