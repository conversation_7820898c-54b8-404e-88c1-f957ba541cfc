package com.cloudstar.controller.alarm;


import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.sdk.monitor.result.OpsAlarmNotifyGetResult;
import com.cloudstar.service.facade.alarm.OpsAlarmNotifyService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@AllArgsConstructor
@RequestMapping("/alarm/notification")
public class OpsAlarmNotifyController {

    private final OpsAlarmNotifyService alarmNotifyService;

    /**
     * 查询告警通知对象列表
     * @param notifyTargetType 通知对象类型
     * @return
     */
    @GetMapping("/list")
    public Rest<List<OpsAlarmNotifyGetResult>> getAlarmNotifyList(String notifyTargetType) {
        return alarmNotifyService.getAlarmNotifyList(notifyTargetType);
    }
}
