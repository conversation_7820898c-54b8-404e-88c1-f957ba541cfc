package com.cloudstar.controller.aimarket;


import com.cloudstar.aimarket.pojo.request.market.MarketShopTagTreeReq;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.service.facade.aimarket.MarketShopTagService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RequiredArgsConstructor
@RestController
@RequestMapping("/tag")
public class MarketShopTagController {


    MarketShopTagService marketShopTagService;

    /**
     * 租户端查询标签树
     */
    @GetMapping("/tree/console")
    public Rest getLabelTagTreeConsole(MarketShopTagTreeReq req) {

        return Rest.ok(marketShopTagService.getLabelTagTree(req));
    }
}
