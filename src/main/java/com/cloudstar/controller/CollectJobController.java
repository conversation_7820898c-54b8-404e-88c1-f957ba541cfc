package com.cloudstar.controller;

import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.service.facade.collect.CollectAllocateCpuService;
import com.cloudstar.service.facade.collect.CollectAllocateGpuService;
import com.cloudstar.service.facade.collect.CollectAllocateMemService;
import com.cloudstar.service.facade.collect.CollectAllocateNpuService;
import com.cloudstar.service.facade.collect.CollectJobsService;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 监控作业控制器
 *
 * <AUTHOR>
 * @date 2023/10/27 11:29
 */
@RestController
@RequestMapping("/collect/job")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class CollectJobController {

    CollectJobsService collectJobsService;

    CollectAllocateCpuService collectAllocateCpuService;

    CollectAllocateGpuService collectAllocateGpuService;

    CollectAllocateMemService collectAllocateMemService;

    CollectAllocateNpuService collectAllocateNpuService;

    /**
     * 清理数据
     */
    @GetMapping("/clean")
    public Rest<Boolean> queryTrainingJobLog() {
        log.info("清洗一周前CPU数据开始");
        collectAllocateCpuService.dataCleanFromWeekAgo();
        log.info("清洗一周前CPU数据结束");

        log.info("清洗一周前NPU数据开始");
        collectAllocateNpuService.dataCleanFromWeekAgo();
        log.info("清洗一周前NPU数据结束");

        log.info("清洗一周前GPU数据开始");
        collectAllocateGpuService.dataCleanFromWeekAgo();
        log.info("清洗一周前GPU数据结束");

        log.info("清洗一周前MEM数据开始");
        collectAllocateMemService.dataCleanFromWeekAgo();
        log.info("清洗一周前MEM数据结束");

        log.info("清洗一周前作业数据开始");
        collectJobsService.deleteJobLastMont();
        log.info("清洗一周前作业数据结束");

        log.info("清洗最近一周每天重复作业数据开始");
        collectJobsService.cleanRepeatJobGroupDay();
        log.info("清洗最近一周每天重复作业数据结束");
        return Rest.ok(true);
    }
}
