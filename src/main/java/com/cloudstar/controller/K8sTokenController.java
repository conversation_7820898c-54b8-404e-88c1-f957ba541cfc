package com.cloudstar.controller;


import cn.hutool.core.util.ObjectUtil;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.serviceaccount.ServiceAccountTokenMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.tenantmapping.ClusterSubAccount;
import com.cloudstar.sdk.iam.pojo.K8sTokenReq;
import com.cloudstar.service.grpc.AgentK8sTokenProto;
import com.cloudstar.service.grpcservice.facade.AgentK8sTokenService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * k8s token控制器
 */


@RestController
@RequestMapping("/k8s")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class K8sTokenController {

    ClusterEntityMapper clusterEntityMapper;

    ServiceAccountTokenMapper serviceAccountTokenMapper;

    /**
     * 创建k8s token
     */
    @PostMapping("/token")
    public Rest create(@RequestBody K8sTokenReq req) {

        final ClusterEntity clusterEntity = clusterEntityMapper.selectById(req.getClusterId());
        if (ObjectUtil.isEmpty(clusterEntity)) {
            throw new BizException("集群不存在");
        }
        try {
            // 处理K8sToken事件
            AgentK8sTokenService agentK8sTokenService = AgentK8sTokenService.build(clusterEntity.getAdapterUuid());
            String token = createK8sToken(agentK8sTokenService, req);
            return Rest.ok(token);
        } catch (Exception e) {
            log.error("处理K8sToken事件失败", e);
            throw new BizException("处理K8sToken事件失败");
        }
    }

    private String createK8sToken(AgentK8sTokenService agentK8sTokenService, K8sTokenReq k8sTokenReq) {
        AgentK8sTokenProto.GetK8sTokenReq req =
                AgentK8sTokenProto.GetK8sTokenReq.newBuilder()
                        .setName(k8sTokenReq.getName())
                        .setNamespace(k8sTokenReq.getNamespace())
                        .build();
        AgentK8sTokenProto.GetK8sTokenResp resp = agentK8sTokenService.getK8sToken(req);
        log.info("response: {}", resp);
        if (resp.getStatus()) {
            log.info("获取K8sToken成功: {}", resp.getToken());
            // 写入数据库
            Long id = serviceAccountTokenMapper.getIdByNameAndNameSpace(k8sTokenReq.getName(), k8sTokenReq.getNamespace());
            if (ObjectUtil.isEmpty(id)) {
                log.error("创建token失败, 未找到所需添加ID");
                throw new RuntimeException("创建token失败, 未找到所需添加ID");
            }
            ClusterSubAccount clusterSubAccount = serviceAccountTokenMapper.selectById(id);
            clusterSubAccount.setK8sToken(resp.getToken());
            serviceAccountTokenMapper.updateById(clusterSubAccount);
            log.info("token {} 在命名空间 {} 创建成功", resp.getToken(), k8sTokenReq.getNamespace());
            return resp.getToken();
        } else {
            log.error("获取K8sToken失败");
            throw new RuntimeException("获取K8sToken失败");
        }
    }


}
