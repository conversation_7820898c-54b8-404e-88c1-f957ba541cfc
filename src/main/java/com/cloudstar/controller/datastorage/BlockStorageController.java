package com.cloudstar.controller.datastorage;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.service.facade.datastorage.BlockStorageService;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.CreateBlockStorageReq;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.DilatationBlockStorageReq;
import com.cloudstar.service.pojo.vo.requestvo.datastorage.QueryBlockStorageReq;
import com.cloudstar.service.pojo.vo.responsevo.datastorage.BlockStoragePageResp;
import com.cloudstar.service.pojo.vo.responsevo.datastorage.BlockStorageResp;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 块存储
 *
 * <AUTHOR>
 * @date 2024/8/5 15:55
 */
@RestController
@RequestMapping("/block_storage")
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class BlockStorageController {


    BlockStorageService blockStorageService;

    /**
     * 创建块存储
     */
    @PostMapping("/create")
    public Rest create(@Validated @RequestBody CreateBlockStorageReq req) {
        return Rest.ok(blockStorageService.create(req));
    }

    /**
     * 查询块存储 page
     */
    @GetMapping("/page")
    public Rest<PageResult<BlockStoragePageResp>> page(QueryBlockStorageReq req, PageForm pageForm) {
        Page<BlockStoragePageResp> page = blockStorageService.page(req, pageForm);
        return Rest.ok(PageResult.of(page));
    }

    /**
     * 查询集群下可用的块存储
     */
    @GetMapping("/unused")
    public Rest<List<BlockStorageResp>> queryUnusedByCluster(@RequestParam Long clusterId) {
        return Rest.ok(blockStorageService.queryUnusedByCluster(clusterId));
    }


    /**
     * 删除块存储
     */
    @PostMapping("/delete")
    public Rest<Boolean> delete(@RequestParam Long id) {
        return Rest.ok(blockStorageService.delete(id));
    }

    /**
     * 扩容块存储
     */
    @PostMapping("/dilatation")
    public Rest dilatation(@Validated @RequestBody DilatationBlockStorageReq req) {
        return Rest.ok(blockStorageService.dilatation(req));
    }
}
