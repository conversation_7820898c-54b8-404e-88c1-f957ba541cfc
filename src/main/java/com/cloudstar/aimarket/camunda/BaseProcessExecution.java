package com.cloudstar.aimarket.camunda;


import cn.hutool.extra.spring.SpringUtil;
import com.cloudstar.aimarket.camunda.service.ExecutionProcessEngine;
import com.cloudstar.aimarket.enums.CamundaExecutionEnum;
import org.camunda.bpm.engine.delegate.DelegateExecution;

import java.util.HashMap;
import java.util.Map;

/**
 * base process execution
 *
 * <AUTHOR>
 * @date 2025/04/27
 */
public abstract class BaseProcessExecution {

    private Map<String, ExecutionProcessEngine> engines = new HashMap<>();

    protected void execution(DelegateExecution variable) {
        engines.values().forEach(e -> {
            if (e.currentTriggerExecution() == null || !e.currentTriggerExecution().equals(currentTriggerExecution())) {
                return;
            }
            e.execution(variable);
        });
    }

    public void setEngines() {
        this.engines = SpringUtil.getBeansOfType(ExecutionProcessEngine.class);
    }

    public BaseProcessExecution() {
        setEngines();
    }

    abstract CamundaExecutionEnum currentTriggerExecution();
}
