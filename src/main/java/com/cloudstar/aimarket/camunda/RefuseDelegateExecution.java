package com.cloudstar.aimarket.camunda;


import com.cloudstar.aimarket.enums.CamundaExecutionEnum;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

/**
 * refuse delegate execution
 *
 * <AUTHOR>
 * @date 2025/04/27
 */
@Slf4j
@Component("refuseDelegateExecution")
public class RefuseDelegateExecution extends BaseProcessExecution implements JavaDelegate {


    @Override
    public void execute(DelegateExecution execution) {
        log.info("businessKey:{},审批拒绝", execution.getBusinessKey());
        execution(execution);
    }

    @Override
    CamundaExecutionEnum currentTriggerExecution() {
        return CamundaExecutionEnum.AUDIT_REFUSE;
    }
}
