package com.cloudstar.aimarket.camunda;


import com.cloudstar.aimarket.enums.CamundaExecutionEnum;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.ExecutionListener;
import org.camunda.bpm.engine.task.Task;
import org.springframework.stereotype.Component;

/**
 * user task execution listener
 *
 * <AUTHOR>
 * @date 2025/04/27
 */
@Slf4j
@Component
public class UserTaskExecutionListener extends BaseProcessExecution implements ExecutionListener {

    @Override
    public void notify(DelegateExecution delegateExecution) {
        Task task = CamundaHelper.taskService.createTaskQuery()
                                             .executionId(delegateExecution.getId())
                                             .activityInstanceIdIn(delegateExecution.getActivityInstanceId())
                                             .singleResult();
        log.info("{}审批了节点任务:{},businessKey:{}", delegateExecution.getCurrentActivityName(), task.getAssignee(),
                 delegateExecution.getBusinessKey());
        execution(delegateExecution);
    }

    @Override
    CamundaExecutionEnum currentTriggerExecution() {
        return CamundaExecutionEnum.USER_TASK_1;
    }
}
