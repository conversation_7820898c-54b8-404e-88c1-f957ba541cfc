package com.cloudstar.aimarket.config;

import cn.hutool.extra.spring.SpringUtil;
import com.github.lianjiatech.retrofit.spring.boot.core.SourceOkHttpClientRegistrar;
import com.github.lianjiatech.retrofit.spring.boot.core.SourceOkHttpClientRegistry;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;

/**
 * 注册自定义okhttp
 *
 * <AUTHOR>
 * @date 2023/5/23 14:37
 */
@Slf4j
public class BssOkHttpClientRegistrar implements SourceOkHttpClientRegistrar {

    @Override
    public void register(SourceOkHttpClientRegistry registry) {

        // 添加bssOkHttpClient
        registry.register("okHttpClient", SpringUtil.getBean(OkHttpClient.class));
    }
}
