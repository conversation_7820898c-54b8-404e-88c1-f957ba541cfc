package com.cloudstar.aimarket.pojo.response.bigscreen;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * market subscribe user top resp
 *
 * <AUTHOR>
 * @description 大屏查询供应商成交订单排行
 * @date 2023/9/7 15:28
 */
@Data
public class MarketSubscribeUserTopResp implements Serializable {

    private static final long serialVersionUID = -8814419762950267229L;

    /**
     * 用户组织名称
     */
    private String orgName;

    /**
     * 成交次数
     */
    private BigDecimal userSubscribeAmount;

}
