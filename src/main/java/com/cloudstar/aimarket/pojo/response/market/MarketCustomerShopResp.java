package com.cloudstar.aimarket.pojo.response.market;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;


/**
 * market customer shop resp
 *
 * <AUTHOR>
 * @description 客户管理商品列表返回
 * @date 2023/8/11 17:28
 */
@Data
public class MarketCustomerShopResp implements Serializable {

    private static final long serialVersionUID = -8814419762950267229L;

    /**
     * 主键
     */
    private String shopId;


    /**
     * 商品logo
     */
    private String logoPath;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品状态：pending 待审核，refuse 审核拒绝，unpublished 未发布，online 已上架，offline 已下架
     */
    private String status;

    /**
     * 商品简介
     */
    private String introduce;

    /**
     * 商品类别：0 我的算法 ，1 其他 ， 2 开源项目
     */
    private Integer shopType;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 浏览次数
     */
    private Integer browseNum;

    /**
     * 订阅次数
     */
    private Integer subscribeNum;

    /**
     * 收藏次数
     */
    private Integer collectNum;


}
