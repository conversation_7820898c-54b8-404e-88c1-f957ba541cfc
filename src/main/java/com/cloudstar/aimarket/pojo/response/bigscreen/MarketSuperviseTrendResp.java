package com.cloudstar.aimarket.pojo.response.bigscreen;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * market supervise trend resp
 *
 * <AUTHOR>
 * @description 大屏查询资金监管服务趋势
 * @date 2023/9/8 15:28
 */
@Data
public class MarketSuperviseTrendResp implements Serializable {

    private static final long serialVersionUID = -8814419762950267229L;

    /**
     * 周期
     */
    private String period;

    /**
     * 总订单金额
     */
    private BigDecimal totalAmount;

    /**
     * 已完成金额
     */
    private BigDecimal completedAmount;
}
