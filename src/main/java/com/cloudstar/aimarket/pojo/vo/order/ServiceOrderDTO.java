/*
 * Copyright (c) 2018-2022. CloudStar.Co.Ltd. All rights reserved.
 */

package com.cloudstar.aimarket.pojo.vo.order;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * service order dto
 *
 * <AUTHOR>
 * @date 2025/04/27
 */
@Data
public class ServiceOrderDTO implements Serializable {

    /**
     * 申请单ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 申请单SN
     */
    private String orderSn;

    /**
     * 申请单名称
     */
    private String name;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 申请单类型
     */
    private String type;

    /**
     * 企业ID
     */
    private Long orgSid;

    /**
     * 服务类型id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private String serviceId;

    /**
     * 所有者ID
     */
    private Long ownerId;

    /**
     * 申请单配置
     */
    private String extraAttr;

    /**
     * 订单总金额
     */
    private BigDecimal totalAmount;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    /**
     * HPC资源原始状态
     */
    private String resourceOriginStatus;

    /**
     * 电话
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 类型名称
     */
    private String typeName;

    /**
     * 状态名称
     */
    private String statusName;

    /**
     * 组织名字
     */
    private String orgName;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 服务类别
     */
    private String serviceCategory;

    /**
     * 服务实例id
     */
    private Long serviceInstanceId;

    /**
     * 过程标识
     */
    private String processFlag;

    /**
     * 步骤名称
     */
    private String stepName;

    /**
     * 产品名称
     */
    private String productName;

    private String actionType;

    /**
     * 当前环节通知发送时间
     */
    private Date currentStepNoticeDt;
    /**
     * 当前环节开始时间
     */
    private Date currentStepCreatedDt;
    /**
     * 当前环节id
     */
    private Long currentStepId;

    /**
     * 流程结束时间
     */
    private Date endTime;
    /**
     * owner real name
     */
    private String ownerRealName;

    /**
     * 主干切过来的 sf_service_category里的数据
     */
    private String openType;

    /**
     * ops名称
     */
    private String opsName;

    /**
     * ops关键
     */
    private String opsKey;

    /**
     * 订单资源类型
     **/
    private String resourceType;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 原始成本
     */
    private BigDecimal originalCost;

    /**
     * 组织折扣
     */
    private BigDecimal orgDiscount;
    /**
     * 优惠券折扣
     */
    private BigDecimal couponDiscount;
    /**
     * 最终价
     */
    private BigDecimal finalCost;


    /**
     * 过程代码
     */
    private String processCode;

    /**
     * 商业计费账户id
     */
    private Long bizBillingAccountId;

    /**
     * 商业计费账户idstr
     */
    private String bizBillingAccountIdStr;

    /**
     * 当前扣除金额
     */
    private BigDecimal currAmount;

    /**
     * 底层平台订单id
     */
    private String platformOrderId;

    /**
     * 服务金额
     */
    private BigDecimal serviceAmount;

    /**
     * 折扣
     */
    private BigDecimal platformDiscount;

    /**
     * 优惠券Id
     */
    private Long couponSid;

    /**
     * 订单详情ID
     */
    private Long detailId;

    /**
     * 资源价格
     */
    private BigDecimal resourceAmount;

    /**
     * 费用类型
     */
    private String chargeType;

    /**
     * 持续时间
     */
    private Integer duration;

    /**
     * 结算类型：标准价、合同价
     */
    private String settlementType;

    /**
     * 合同名称
     */
    private String contractTitle;

    /**
     * 合同ID
     */
    private String contractId;

    /**
     * 合同路径
     */
    private String contractFile;

    /**
     * 合同文件名
     */
    private String contractFileName;

    /**
     * 代客下单，管理员ID
     */
    private Long behalfUserSid;

    /**
     * 集群ID
     */
    private String clusterUuid;

    /**
     * 信用额度支付金额
     */
    private BigDecimal creditAmount;

    /**
     * 充值现金券支付金额
     */
    private BigDecimal couponAmount;

    /**
     * 抵扣现金券优惠金额
     */
    private BigDecimal deductCouponDiscount;

    /**
     * 优惠总金额
     */
    private BigDecimal discountAmount;

    /**
     * 折扣金额负数形式
     */
    private BigDecimal orgDiscountReverse;

    /**
     * 优惠券抵扣金额负数形式
     */
    private BigDecimal couponDiscountReverse;

    /**
     * 现金支付金额
     */
    private BigDecimal cashAmount;

    public BigDecimal getServiceAmount() {
        return Objects.isNull(this.serviceAmount) ? BigDecimal.ZERO : this.serviceAmount;
    }

    public BigDecimal getPlatformDiscount() {
        return Objects.isNull(this.platformDiscount) ? BigDecimal.ONE : this.platformDiscount;
    }

    public BigDecimal getFinalCost() {
        return Objects.isNull(this.finalCost) ? BigDecimal.ZERO : this.finalCost;
    }



    /**
     * 客户名称
     */
    @JsonIgnore
    private String accountName;

    private String distributorName;

    private String errorInfo;

    @TableField(exist = false)
    private String statusInfo;

    private Long clusterId;

    private Long productResourceId;

    private Long entityId;

    private String entityName;

    /**
     * 补扣时间段
     */
    private Date expiredStartTime;
    private Date expiredEndTime;

}
