package com.cloudstar.aimarket.pojo.request.market;

import lombok.Data;

import java.io.Serializable;

/**
 * market shop tag tree req
 *
 * <AUTHOR>
 * @description: 商品标签树
 * @author: z<PERSON><PERSON>
 * @date: 2023/9/5 10:46
 * @date 2025/04/27
 */
@Data
public class MarketShopTagTreeReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品id
     */
    private String shopId;

    /**
     * 商品类别：0 我的算法 ，1 其他 ， 2 开源项目
     */
    private Integer shopType;

    /**
     * 标签类别 1：商品分类，2：交付方式
     */
    private String tagCategory;

}
