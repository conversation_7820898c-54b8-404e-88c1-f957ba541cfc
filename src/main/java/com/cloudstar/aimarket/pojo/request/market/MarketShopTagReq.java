package com.cloudstar.aimarket.pojo.request.market;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cloudstar.common.base.pojo.BaseRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品标签表
 * @TableName market_shop_tag
 */
@Data
public class MarketShopTagReq extends BaseRequest implements Serializable {
    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * tag名称
     */
    private String tagName;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


}
