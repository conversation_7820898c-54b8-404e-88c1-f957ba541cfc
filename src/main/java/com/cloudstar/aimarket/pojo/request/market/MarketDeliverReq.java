package com.cloudstar.aimarket.pojo.request.market;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 交付商品请求
 *
 * <AUTHOR>
 * @TableName market_shop_version
 * @date 2023/09/05
 */
@Data
public class MarketDeliverReq implements Serializable {
    /**
     * 服务结果描述不能为空
     */
    @NotBlank(message = "服务结果描述不能为空")
    private String remark;

    //private List<SysMFilePath> superviseFiles;

}
