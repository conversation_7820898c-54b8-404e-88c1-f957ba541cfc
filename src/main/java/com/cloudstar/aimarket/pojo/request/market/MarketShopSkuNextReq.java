package com.cloudstar.aimarket.pojo.request.market;


import com.cloudstar.aimarket.pojo.vo.market.StringVO;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 市场商店sku更新申请
 *
 * <AUTHOR>
 * @date 2023/08/15
 */
@Data
public class MarketShopSkuNextReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 属性id
     */
    @NotNull
    private Long id;
    /**
     * 属性类型
     */
    @NotBlank
    private String type;

    /**
     * 枚举值
     */
    @NotEmpty
    private List<StringVO> enumValues;

}
