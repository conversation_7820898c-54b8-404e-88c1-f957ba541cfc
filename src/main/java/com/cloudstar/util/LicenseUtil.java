package com.cloudstar.util;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cloudstar.ConfigService;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.pojo.LicenseVo;
import com.cloudstar.common.component.redis.util.RedisUtil;
import com.cloudstar.common.util.CrytoUtilSimple;
import com.cloudstar.config.LicenseConfig;
import com.cloudstar.dao.mapper.SysMLicenseMapper;
import com.cloudstar.dao.model.SysMLicense;
import com.cloudstar.enums.AccountTypeEnum;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.google.common.base.Strings;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 许可证工具类
 *
 * <AUTHOR>
 * @date 2022/12/23 10:48
 */
@Slf4j
public class LicenseUtil {

    private static final ConfigService CONFIG_SERVICE;
    private static final SysMLicenseMapper SYS_M_LICENSE_MAPPER;
    private static final boolean CHECK_SN = false;
    private static String productSn;
    private static final RedisUtil REDIS_UTIL;
    private static final String LICENSE_CATCH_KEY = "LICENSE_CATCH_KEY";

    static {
        productSn = System.getProperty("cloudstar.product.sn", "JZ7V-M3VJ-IYZR-NAUI");
        CONFIG_SERVICE = SpringUtil.getBean(ConfigService.class);
        SYS_M_LICENSE_MAPPER = SpringUtil.getBean(SysMLicenseMapper.class);
        REDIS_UTIL = SpringUtil.getBean(RedisUtil.class);
    }


    /**
     * 查询license信息 查询出来放在一个静态对象中 这样可以只需解密一次(适用于server项目)
     *
     * @return the license vo
     */
    public static LicenseVo queryLicenseInfoFromStatic() {
        LicenseVo licenseVo = null;
        final String cacheLicenseStr = REDIS_UTIL.get(LICENSE_CATCH_KEY);
        if (ObjectUtil.isEmpty(cacheLicenseStr)) {
            licenseVo = preAnalyzeLincenseVo();
        } else {
            licenseVo = JSONUtil.toBean(cacheLicenseStr, LicenseVo.class);
        }
        return licenseVo;
    }

    /**
     * license是否过期
     *
     * @return the boolean
     */
    public static boolean isExpireLicenseOnlyTime() {
        LicenseVo licenseVo = queryLicenseInfoFromStatic();
        if (licenseVo == null) {
            return true;
        }
        if (CHECK_SN && !Objects.equals(licenseVo.getProductSN(), getDbProductSn())) {
            log.info("许可证 特征码无法匹配，标记许可证过期!");
            return true;
        }
        Date expireDate = DateUtil.parse(licenseVo.getExpireDate() + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
        final Date now = new Date();
        return Objects.isNull(expireDate) || now.after(expireDate);
    }

    /**
     * license是否过期 （系统管理不会过期）
     *
     * @param authUser the auth user
     *
     * @return the boolean
     */
    public static boolean isExpireLicense(AuthUser authUser) {
        //系统内置admin账号不会过期
        if (AccountTypeEnum.MANAGER.getType().equals(authUser.getAccountType()) && "admin".equals(authUser.getAccount())) {
            return false;
        }
        return isExpireLicenseOnlyTime();
    }

    /**
     * 查询系统lincense即将过期的提示信息
     *
     * @return the string
     */
    public static Map queryLicenseExpireInfo() {
        LicenseVo lincenseVo = queryLicenseInfoFromStatic();
        if (lincenseVo != null) {
            if (CHECK_SN && !Objects.equals(lincenseVo.getProductSN(), getDbProductSn())) {
                Map result = new HashMap();
                result.put("status", Boolean.FALSE);
                result.put("msg", "尊敬的用户：许可证无效，请尽快联系管理员更新许可证。");
                return result;
            }
            // 提前7天提示license过期消息
            Date expireDate = DateUtil.parse(lincenseVo.getExpireDate() + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
            Date nowDate = new Date();
            long diffTime = expireDate.getTime() - nowDate.getTime();
            if (diffTime <= 7 * 24 * 60 * 60 * 1000) {
                String expireStr = licenseExpireDay(diffTime);
                StringBuilder massage = new StringBuilder("尊敬的用户：");
                if (diffTime < 0) {
                    massage.append("许可证已于").append(lincenseVo.getExpireDate()).append("过期");
                } else {
                    massage.append("许可证将在").append(lincenseVo.getExpireDate()).append("过期(").append(expireStr).append(")");
                }
                massage.append("，请尽快联系管理员更新许可证。");
                Map result = new HashMap();
                result.put("status", Boolean.TRUE);
                result.put("msg", massage.toString());
                return result;
            }
        } else {
            Map result = new HashMap();
            result.put("status", Boolean.FALSE);
            result.put("msg", "尊敬的用户：许可证还未配置成功，请尽快联系管理员更新许可证。");
            return result;
        }
        return null;
    }

    private static String getLicenseStrFromDb() {
        List<SysMLicense> sysMLicenses = SYS_M_LICENSE_MAPPER.selectList(new QueryWrapper<>());
        if (CollectionUtil.isNotEmpty(sysMLicenses)) {
            return sysMLicenses.get(0).getLicenseBody();
        }
        return null;
    }

    /**
     * 解析许可证信息
     **/
    public static LicenseVo analyzeLincenseVo(String licenseStr) {
        try {
            if (ObjectUtil.isEmpty(licenseStr)) {
                log.warn("licenseStr is null");
                return null;
            }
            // 修改许可证 前 6 位是掩码
            String licenseReal = licenseStr.substring(6);
            String decryptResult = CrytoUtilSimple.decrypt(licenseReal, true);
            Map<String, Object> map = new HashMap<>();
            String[] splitArr = decryptResult.split("&");
            for (String str : splitArr) {
                String[] kvArr = str.split("=");
                map.put(kvArr[0], kvArr.length >= 2 ? kvArr[1] : null);
            }
            LicenseVo resultVo = BeanUtil.mapToBean(map, LicenseVo.class, false, new CopyOptions());
            REDIS_UTIL.set(LICENSE_CATCH_KEY, JSONUtil.toJsonStr(resultVo));
            return resultVo;
        } catch (Exception e) {
            log.error("analyze license error return null, error：" + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 清除缓存
     */
    public static void clearCache() {
        //清除缓存
        REDIS_UTIL.delete(LICENSE_CATCH_KEY);
    }

    /**
     * 读取config表中sn
     */
    public static String getDbProductSn() {
        try {
            LicenseConfig config = CONFIG_SERVICE.getConfig(ConfigType.LICENSE_CONFIG);
            return config.getProductSn().stringValue();
        } catch (Exception e) {
            log.error("【特征码】无法获取，使用默认.");
        }
        return productSn;
    }


    /**
     * 校验特征码, 解析许可证信息
     */
    public static LicenseVo preAnalyzeLincenseVo() {
        if (Strings.isNullOrEmpty(getDbProductSn())) {
            log.error("【特征码】无法获取，标记许可证过期!");
            return null;
        }
        return analyzeLincenseVo(getLicenseStrFromDb());
    }

    /**
     * 许可证过期提示
     *
     * @param diffTime 时间
     */
    public static String licenseExpireDay(long diffTime) {
        if (diffTime < 0) {
            return "已经过期";
        }
        long nd = 1000 * 24 * 60 * 60;
        long day = diffTime / nd;
        if (day > 0) {
            return "还剩" + day + "天";
        } else {
            return "还剩" + "不到一天";
        }
    }

}
