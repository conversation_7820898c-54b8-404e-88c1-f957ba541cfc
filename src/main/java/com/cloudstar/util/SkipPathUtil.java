package com.cloudstar.util;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 跳过路径跑龙套
 *
 * <AUTHOR>
 * @description: TODO
 * @date 2022/7/19 17:21
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "filter-chain")
public class SkipPathUtil {

    public List<String> definitions;

    private static final List<String> REGEX_LIST = new CopyOnWriteArrayList<>();

    /**
     * 跳过过滤器
     *
     * @param path 路径
     *
     * @return boolean
     */
    public boolean skipFilter(String path) {
        if (CollectionUtils.isEmpty(definitions)) {
            log.warn("没有定义忽略接口白名单,所有接口都会被校验权限!");
            return false;
        }
        createRegexList();
        for (String definition : REGEX_LIST) {
            // 全走正则表达式匹配
            if (definition.endsWith("$")) {
                if (Pattern.compile(definition).matcher(path).find()) {
                    log.debug("{} 匹配 {}, 比较正则:{} ", path, definition);
                    return true;
                }
            } else {
                // 全匹配
                if (Objects.equals(definition, path)) {
                    log.debug("{} 全匹配 {}", path, definition);
                    return true;
                }
            }
        }
        return false;
    }

    private void createRegexList() {
        List<String> collect = definitions.stream().map(item -> {
            if (item.endsWith("**")) {
                // 后面匹配多个
                item = item.substring(0, item.length() - 2) + "[\\w/.\\W]*$";
            } else if (item.endsWith("*")) {
                // 匹配一个
                item = item.substring(0, item.length() - 1) + "(/)?[\\w]*(/)?$";
            } else {
                if (item.endsWith("/")) {
                    item = item.substring(0, item.length() - 1);
                }
            }
            return item;
        }).collect(Collectors.toList());
        REGEX_LIST.addAll(collect);
    }
}
