package com.cloudstar.util;

import com.cloudstar.common.desensitized.annotation.DesensitizationField;
import com.cloudstar.common.desensitized.enums.DesensitizedType;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.ClassUtils;
import org.springframework.util.ObjectUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;

import lombok.extern.slf4j.Slf4j;

/**
 * 加密工具类
 *
 * <AUTHOR>
 * @date 2022.09.02
 */
@Slf4j
public class DesensitizationUtil {

    /**
     *      * 脱敏      * @param objList      * @return
     */
    public static <T> T desensitization(T objList) {
        if (objList instanceof ArrayList) {
            ArrayList resultList = (ArrayList) objList;
            if (CollectionUtils.isNotEmpty(resultList)) {
                for (int i = 0; i < resultList.size(); i++) {
                    doDesensitization(resultList.get(i));
                }
            }
        } else {
            doDesensitization(objList);
        }
        return objList;
    }

    /**
     * 脱敏
     */

    public static <T> T doDesensitization(T obj) {
        try {
            Class<? extends Object> clazz = ClassUtils.getUserClass(obj);
            //获取所有自己声明的属性 
            Field[] fields = clazz.getDeclaredFields();
            //遍历属性 
            for (Field field : fields) {
                //通过注解中的值来获取属性名（注解中的值应该与属性名相等） 
                DesensitizationField columnField = field.getAnnotation(DesensitizationField.class);
                //如果是没有注解的属性忽略掉
                if (null == columnField) {
                    continue;
                }
                String type = field.getGenericType().toString(); //获取属性的类型 
                PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
                Method rM = pd.getReadMethod();
                Object oldValue = (Object) rM.invoke(obj);
                Object newValue = oldValue;
                if (null != oldValue) {
                    Method wM = pd.getWriteMethod();
                    // 如果有type，则走新的脱敏
                    DesensitizedType desType = columnField.type();
                    if (!ObjectUtils.isEmpty(desType) && oldValue instanceof String) {
                        //脱敏
                        String val = desensitizedByStr((String) oldValue, desType);
                        wM.invoke(obj, val);
                        continue;
                    }
                    String doMethodName = columnField.toString();
                    if ("mobile".equalsIgnoreCase(field.getName())) {
                        doMethodName = "doMobilephone";
                    } else if ("email".equalsIgnoreCase(field.getName())) {
                        doMethodName = "doEmail";
                    } else if (field.getName().toLowerCase().contains("password")) {
                        doMethodName = "doPassword";
                    } else if ("realName".equalsIgnoreCase(field.getName())) {
                        doMethodName = "doChinesename";
                    }
                    Method getMethod = DesensitizationUtil.class.getDeclaredMethod(doMethodName, String.class,
                                                                                   Object.class);
                    newValue = getMethod.invoke(DesensitizationUtil.class, type, oldValue);
                    wM.invoke(obj, newValue);
                }
            }
        } catch (Exception e) {
            log.error("脱敏处理失败{}", e);
        }
        return obj;

    }


    /**
     * 新的脱敏工具方法
     *
     * <AUTHOR>
     */
    public static String desensitizedByStr(String str, DesensitizedType type) {
        if (ObjectUtils.isEmpty(str)) {
            return str;
        }
        switch (type) {
            case NAME:
                return str.charAt(0) + "****";
            case ID_CARD:
                return str.length() == 15 ? str.replaceAll("(\\w{6})\\w*(\\w{3})", "$1******$2")
                        : str.replaceAll("(\\w{6})\\w*(\\w{3})", "$1*********$2");
            case MOBILE:
                return str.replaceAll("(\\w{3})\\w*(\\w{4})", "$1****$2");
            case EMAIL:
                return str.replaceAll("(^\\w{0})[^@]*(@.*$)", "$1****$2");
            default:
                return "********";
        }
    }
}
