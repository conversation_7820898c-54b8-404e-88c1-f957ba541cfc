package com.cloudstar.config;

import java.util.Date;
import java.util.UUID;

/**
 * compressed id generator, result id not great than 53bits before 2318-06-04.
 */
public class IdGenerator {

    private static IdGenerator instance = new IdGenerator(0);

    public  static long generateId() {
        return instance.nextId();
    }

    private final long machineBit = 5;
    private final long sequenceBit = 8;

    /**
     * mask/max value
     */
    private final long maxMachineNum = -1L ^ (-1L << machineBit);
    private final long maxSequence = -1L ^ (-1L << sequenceBit);

    private final long machineLeft = sequenceBit;
    private final long timesatmpLeft = machineBit + sequenceBit;

    private long machineId;
    private long sequence = 0L;
    private long lastStmp = -1L;

    private IdGenerator(long machineId) {
        if (machineId > maxMachineNum || machineId < 0) {
            throw new IllegalArgumentException(
                    "machineId can't be greater than " + maxMachineNum + " or less than 0");
        }
        this.machineId = machineId;
    }

    /**
     * generate new ID
     *
     * @return
     */
    public synchronized long nextId() {
        long currStmp = getTimestamp();
        if (currStmp < lastStmp) {
            throw new RuntimeException("Clock moved backwards.  Refusing to generate id");
        }

        if (currStmp == lastStmp) {
            sequence = (sequence + 1) & maxSequence;
            if (sequence == 0L) {
                currStmp = getNextTimestamp();
            }
        } else {
            sequence = 0L;
        }

        lastStmp = currStmp;

        return currStmp << timesatmpLeft
                | machineId << machineLeft
                | sequence;
    }

    private long getNextTimestamp() {
        long mill = getTimestamp();
        while (mill <= lastStmp) {
            mill = getTimestamp();
        }
        return mill;
    }

    private long getTimestamp() {
        // per 10msK
        return System.currentTimeMillis() / 10;
    }

    public  Date parseIdTimestamp(long id) {
        return new Date((id >>> timesatmpLeft) * 10);
    }

    public static String uuid() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }



}