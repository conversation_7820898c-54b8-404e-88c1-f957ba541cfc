package com.cloudstar.service.grpcservice;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloudstar.dao.mapper.cluster.ClusterResourcePoolMapper;
import com.cloudstar.dao.model.cluster.ClusterNode;
import com.cloudstar.dao.model.cluster.ClusterResourcePool;
import com.cloudstar.sdk.schedule.pojo.FlavorsResourceRep;
import com.cloudstar.service.facade.cluster.ClusterNodeService;
import com.cloudstar.service.grpc.agentMonitorCollectProto.syncResourcePoolBms;
import com.cloudstar.service.grpc.agentMonitorCollectProto.syncResourcePoolBmsResponse;
import com.cloudstar.service.grpcservice.facade.AgentMonitorService;
import com.cloudstar.service.pojo.dto.resourcepool.ResourcePoolAllocateV1;
import com.cloudstar.service.utils.ListUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AgentMonitorServiceImpl implements AgentMonitorService {

    ClusterResourcePoolMapper clusterResourcePoolMapper;

    ClusterNodeService clusterNodeService;

    static List<String> computerCardModelKey = Arrays.asList("huawei.com/ascend-1980", "huawei-ascend-d910");
    static List<String> npuKey = Arrays.asList("huawei.com/ascend-1980", "huawei-ascend-d910", "npu");
    static List<String> memoryKey = Arrays.asList("memory", "mem");
    static List<String> gpuKey = Arrays.asList("nvidia-t4", "gpu");
    static List<String> cpuKey = Arrays.asList("cpu");


    @Override
    public void syncResourcePools(Long clusterId, FlavorsResourceRep flavorsResourceRep) {
        List<ClusterResourcePool> clusterResourcePools = clusterResourcePoolMapper.selectList(
                Wrappers.lambdaQuery(ClusterResourcePool.class).eq(ClusterResourcePool::getClusterId, clusterId));
        List<String> clusterPoolIds = clusterResourcePools.stream()
                                                          .map(ClusterResourcePool::getPoolId)
                                                          .collect(Collectors.toList());

        List<FlavorsResourceRep.Item> items = flavorsResourceRep.getItems()
                                                                .stream()
                                                                .filter(distinctByKey(
                                                                        item -> item.getMetadata().getName()))
                                                                .collect(Collectors.toList());
        //同步v1版本集群信息
        List<ClusterNode> clusterNodes = new ArrayList<>();
        items.stream().filter(flavorsResource -> clusterPoolIds.contains(flavorsResource.getMetadata().getName())).distinct().forEach(resource -> {
            for (ClusterResourcePool clusterResourcePool : clusterResourcePools) {
                try {
                    if (clusterResourcePool.getPoolId().equals(resource.getMetadata().getName())) {
                        ClusterNode build = buildClusterNode(resource, clusterResourcePool);
                        clusterNodes.add(build);
                        break;
                    }
                } catch (Exception e) {
                    log.error("错误参数:[{}]", resource);
                }
            }
        });

        //与之前同步的数据作比较，存在更新不存在增加
        List<String> nodeIds = clusterNodes.stream().map(ClusterNode::getNodeId).collect(Collectors.toList());
        if (nodeIds.isEmpty()) {
            return;
        }
        List<ClusterNode> saveDClusterNodes = clusterNodeService.list(
                Wrappers.lambdaQuery(ClusterNode.class).in(ClusterNode::getNodeId, nodeIds));
        List<ClusterNode> latestClusterNodes = new ArrayList<>();
        boolean save;
        for (ClusterNode clusterNode : clusterNodes) {
            save = true;
            for (ClusterNode saveDClusterNode : saveDClusterNodes) {
                if (clusterNode.getNodeId().equals(saveDClusterNode.getNodeId())) {
                    ClusterNode latestClusterNode = replaceLatestClusterNode(clusterNode, saveDClusterNode);
                    latestClusterNodes.add(latestClusterNode);
                    save = false;
                    break;
                }
            }
            if (save) {
                latestClusterNodes.add(clusterNode);
            }
        }
        if (!latestClusterNodes.isEmpty()) {
            clusterNodeService.saveOrUpdateBatch(latestClusterNodes);
        }

    }

    /**
     * 逻辑跟上面一样的 只是数据结构不一样
     *
     * @param clusterId 。
     * @param allocateV1 v1版本的返回结果
     */
    @Override
    public void syncResourcePoolsV1(Long clusterId, ResourcePoolAllocateV1 allocateV1) {
        List<ClusterResourcePool> clusterResourcePools = clusterResourcePoolMapper.selectList(
                Wrappers.lambdaQuery(ClusterResourcePool.class).eq(ClusterResourcePool::getClusterId, clusterId));
        List<String> clusterPoolIds = clusterResourcePools.stream()
                                                          .map(ClusterResourcePool::getPoolId)
                                                          .collect(Collectors.toList());

        List<ResourcePoolAllocateV1.Items> items = allocateV1.getItems()
                                                             .stream()
                                                             .filter(distinctByKey(item -> item.getMetadata().getUid()))
                                                             .collect(Collectors.toList());
        //同步v1版本集群信息
        List<ClusterNode> clusterNodes = new ArrayList<>();
        items.stream().filter(flavorsResource -> clusterPoolIds.contains(flavorsResource.getMetadata().getUid())).distinct().forEach(resource -> {
            for (ClusterResourcePool clusterResourcePool : clusterResourcePools) {
                try {
                    if (clusterResourcePool.getPoolId().equals(resource.getMetadata().getUid())) {
                        ClusterNode build = buildClusterNode(resource, clusterResourcePool);
                        clusterNodes.add(build);
                        break;
                    }
                } catch (Exception e) {
                    log.error("错误参数:[{}]", resource);
                }
            }
        });

        //与之前同步的数据作比较，存在更新不存在增加
        List<String> nodeIds = clusterNodes.stream().map(ClusterNode::getNodeId).collect(Collectors.toList());
        if (nodeIds.isEmpty()) {
            return;
        }
        List<ClusterNode> saveDClusterNodes = clusterNodeService.list(
                Wrappers.lambdaQuery(ClusterNode.class).in(ClusterNode::getNodeId, nodeIds));
        List<ClusterNode> latestClusterNodes = new ArrayList<>();
        boolean save;
        for (ClusterNode clusterNode : clusterNodes) {
            save = true;
            for (ClusterNode saveDClusterNode : saveDClusterNodes) {
                if (clusterNode.getNodeId().equals(saveDClusterNode.getNodeId())) {
                    ClusterNode latestClusterNode = replaceLatestClusterNode(clusterNode, saveDClusterNode);
                    latestClusterNodes.add(latestClusterNode);
                    save = false;
                    break;
                }
            }
            if (save) {
                latestClusterNodes.add(clusterNode);
            }
        }
        if (!latestClusterNodes.isEmpty()) {
            clusterNodeService.saveOrUpdateBatch(latestClusterNodes);
        }
    }

    @Override
    public void syncResourcePoolsBms(Long clusterId, syncResourcePoolBmsResponse resp) {
        List<syncResourcePoolBms> list = resp.getListList();
        if (CollectionUtil.isNotEmpty(list)) {
            List<ClusterNode> sourceList = new ArrayList<>();
            list.stream().distinct().forEach(resource -> {
                ClusterNode build = new ClusterNode();
                build.setNodeId(resource.getNodeId());
                build.setNodeName(resource.getNodeName());
                build.setArchive(resource.getArchive());
                build.setOs(resource.getOs());
                build.setComputeProductName(resource.getComputeProductName());
                build.setCpu(resource.getCpu());
                build.setCpuAvailable(resource.getCpuAvailable());
                build.setMemory(resource.getMemory());
                build.setMemoryAvailable(resource.getMemoryAvailable());
                build.setGpu(resource.getGpu());
                build.setGpuAvailable(resource.getGpuAvailable());
                build.setNpu(resource.getNpu());
                build.setNpuAvailable(resource.getNpuAvailable());
                build.setDisk(resource.getDisk());
                build.setDiskAvailable(resource.getDiskAvailable());
                build.setLabels(resource.getLabels());
                build.setIsSync(true);
                build.setStatus(resource.getStatus());
                build.setComputeProductName(resource.getComputeProductName());
                build.setNodeAddress(resource.getNodeAddress());
                sourceList.add(build);
            });

            List<ClusterNode> dbList = clusterNodeService.list(new QueryWrapper<ClusterNode>().lambda().eq(ClusterNode::getClusterId, clusterId));
            // 判断条件
            BiPredicate<ClusterNode, ClusterNode> matchPredicate = (res1, res2) -> Objects.equals(
                    res1.getNodeId(), res2.getNodeId());
            // 需要更新的数据
            List<ClusterNode> requireUpdateList = ListUtil.intersection(sourceList, dbList, matchPredicate);
            requireUpdateList.stream().forEach(clusterNode -> {
                //修改时间
                clusterNode.setUpdatedDt(new Date());
                clusterNodeService.update(clusterNode, new QueryWrapper<ClusterNode>().eq("node_id", clusterNode.getNodeId()));
            });
            // 需要插入的数据
            List<ClusterNode> requireInsertList = ListUtil.subtract(sourceList, dbList, matchPredicate);
            requireInsertList.stream().forEach(clusterNode -> {
                clusterNode.setClusterId(clusterId);
                clusterNode.setUpdatedDt(new Date());
                clusterNode.setCreatedDt(new Date());
                clusterNodeService.save(clusterNode);
            });
            // 需要删除的数据
            List<ClusterNode> requireDeleteList = ListUtil.subtract(dbList, sourceList, matchPredicate);
            requireDeleteList.stream().forEach(clusterNode -> {
                clusterNodeService.deleteNode(clusterNode.getId());
            });
            log.info("集群[{}]资源池节点同步信息, 总数:[{}],新增:[{}],更新:[{}],删除:[{}]",
                     clusterId, sourceList.size(), requireInsertList.size(),
                     requireUpdateList.size(), requireDeleteList.size());
        }
    }

    /**
     * buildClusterNode
     */
    public ClusterNode buildClusterNode(ResourcePoolAllocateV1.Items resource,
                                        ClusterResourcePool clusterResourcePool) {
        ClusterNode build = ClusterNode.builder()
                                       .clusterId(clusterResourcePool.getClusterId())
                                       .nodeId(resource.getMetadata().getUid() + "-node-1")
                                       .nodeName(UUID.fastUUID().toString())
                                       .poolId(clusterResourcePool.getId())
                                       .disk(0L)
                                       .diskAvailable(0L)
                                       .isSync(true)
                                       .build();

        resource.getCapacity().forEach((k, v) -> {
            if (computerCardModelKey.contains(k)) {
                build.setComputeProductName(k);
            }
            if (memoryKey.contains(k)) {
                build.setMemory(convertStringToLong(v));
            }
            if (cpuKey.contains(k)) {
                build.setCpu(convertStringToLong(v));
            }
            if (gpuKey.contains(k)) {
                build.setGpu(convertStringToLong(v));
            }
            if (npuKey.contains(k)) {
                build.setNpu(convertStringToLong(v));
            }
        });

        resource.getAllocated().forEach((k, v) -> {
            if (computerCardModelKey.contains(k)) {
                build.setComputeProductName(k);
            }
            if (memoryKey.contains(k)) {
                build.setMemoryAvailable(build.getMemory() - convertStringToLong(v));
            }
            if (cpuKey.contains(k)) {
                build.setCpuAvailable(build.getCpu() - convertStringToLong(v));
            }
            if (gpuKey.contains(k)) {
                build.setGpuAvailable(build.getGpu() - convertStringToLong(v));
            }
            if (npuKey.contains(k)) {
                build.setNpuAvailable(build.getNpu() - convertStringToLong(v));
            }
        });
        return build;
    }

    /**
     * xxx
     *
     * @param resource 。
     * @param clusterResourcePool 。
     *
     * @return 。
     */
    public ClusterNode buildClusterNode(FlavorsResourceRep.Item resource, ClusterResourcePool clusterResourcePool) {
        ClusterNode build = ClusterNode.builder()
                                       .clusterId(clusterResourcePool.getClusterId())
                                       .nodeId(resource.getMetadata().getName() + "-node-1")
                                       .nodeName(UUID.fastUUID().toString())
                                       .poolId(clusterResourcePool.getId())
                                       .disk(0L)
                                       .diskAvailable(0L)
                                       .isSync(true)
                                       .build();

        resource.getTable().getCapacity().getValue().forEach((k, v) -> {
            if (computerCardModelKey.contains(k)) {
                build.setComputeProductName(k);
            }
            if (memoryKey.contains(k)) {
                build.setMemory(convertStringToLong(v));
            }
            if (cpuKey.contains(k)) {
                build.setCpu(convertStringToLong(v));
            }
            if (gpuKey.contains(k)) {
                build.setGpu(convertStringToLong(v));
            }
            if (npuKey.contains(k)) {
                build.setNpu(convertStringToLong(v));
            }
        });

        resource.getTable().getAllocated().getValue().forEach((k, v) -> {
            if (computerCardModelKey.contains(k)) {
                build.setComputeProductName(k);
            }
            if (memoryKey.contains(k)) {
                build.setMemoryAvailable(build.getMemory() - convertStringToLong(v));
            }
            if (cpuKey.contains(k)) {
                build.setCpuAvailable(build.getCpu() - convertStringToLong(v));
            }
            if (gpuKey.contains(k)) {
                build.setGpuAvailable(build.getGpu() - convertStringToLong(v));
            }
            if (npuKey.contains(k)) {
                build.setNpuAvailable(build.getNpu() - convertStringToLong(v));
            }
        });
        return build;
    }

    /**
     * replaceLatestClusterNode
     */
    public ClusterNode replaceLatestClusterNode(ClusterNode clusterNode, ClusterNode saveDClusterNode) {
        saveDClusterNode.setComputeProductName(clusterNode.getComputeProductName());
        saveDClusterNode.setCpu(clusterNode.getCpu());
        saveDClusterNode.setCpuAvailable(clusterNode.getCpuAvailable());
        saveDClusterNode.setMemory(clusterNode.getMemory());
        saveDClusterNode.setMemoryAvailable(clusterNode.getMemoryAvailable());
        saveDClusterNode.setNpu(clusterNode.getNpu());
        saveDClusterNode.setNpuAvailable(clusterNode.getNpuAvailable());
        saveDClusterNode.setGpu(clusterNode.getGpu());
        saveDClusterNode.setGpuAvailable(clusterNode.getGpuAvailable());
        saveDClusterNode.setIsSync(true);
        return saveDClusterNode;
    }


    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Set<Object> seen = ConcurrentHashMap.newKeySet();
        return t -> seen.add(keyExtractor.apply(t));
    }

    private Long convertStringToLong(Object str) {
        try {
            String gi = str.toString().replaceAll("Gi", "").replaceAll("m", "");
            return Long.parseLong(gi);
        } catch (Exception e) {
            return 0L;
        }
    }
}




