package com.cloudstar.service.grpcservice.impl;

import com.cloudstar.service.grpc.HeartbeatCheckProto.CheckHeartbeatReq;
import com.cloudstar.service.grpc.HeartbeatCheckProto.CheckHeartbeatResp;
import com.cloudstar.service.grpc.HeartbeatCheckServiceGrpc;
import com.cloudstar.service.grpcservice.facade.AgentHeartbeatCheckService;
import com.cloudstar.utils.ProtoJsonUtils;

import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import io.grpc.ManagedChannel;
import lombok.extern.slf4j.Slf4j;

/**
 * 提供调用适配器服务的具体实现类
 */
@Service
@Slf4j
public class AgentHeartbeatCheckServiceImpl implements AgentHeartbeatCheckService {

    private HeartbeatCheckServiceGrpc.HeartbeatCheckServiceBlockingStub blockingStub;

    @Resource
    private ManagedChannel managedChannel;

    @PostConstruct
    public void init() {
        blockingStub = HeartbeatCheckServiceGrpc.newBlockingStub(managedChannel);
    }


    @Override
    public boolean heartbeatCheck() {
        CheckHeartbeatReq clusterSubRequest = CheckHeartbeatReq.newBuilder().build();
        final CheckHeartbeatResp checkHeartbeatResp = blockingStub.checkHeartbeat(clusterSubRequest);
        log.info("心跳检测返回值:{}", ProtoJsonUtils.toJson(checkHeartbeatResp));
        return true;
    }

}
