package com.cloudstar.service.grpcservice.impl;

import com.cloudstar.common.base.pojo.BillCollectRedisDto;
import com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq;
import com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp;
import com.cloudstar.service.grpc.GetAgentBillGRPCServiceGrpc;
import com.cloudstar.service.grpcservice.facade.AgentBillCollectService;
import com.cloudstar.utils.ProtoJsonUtils;

import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import io.grpc.ManagedChannel;
import lombok.extern.slf4j.Slf4j;

/**
 * 采集话单
 *
 * <AUTHOR>
 * @date 2024/7/8 13:52
 */
@Service
@Slf4j
public class AgentBillCollectServiceImpl implements AgentBillCollectService {

    private GetAgentBillGRPCServiceGrpc.GetAgentBillGRPCServiceBlockingStub blockingStub;

    @Resource
    private ManagedChannel managedChannel;

    @PostConstruct
    public void init() {
        blockingStub = GetAgentBillGRPCServiceGrpc.newBlockingStub(managedChannel);
    }


    @Override
    public boolean bmsBillCollect(BillCollectRedisDto dto) {
        BmsBillCollectReq.Builder builder = BmsBillCollectReq.newBuilder();
        builder.setNamespace(dto.getNamespace());
        builder.setName(dto.getName());
        builder.setType(dto.getType());
        if (ObjectUtil.isNotEmpty(dto.getId())) {
            builder.setId(dto.getId());
        }
        if (ObjectUtil.isNotEmpty(dto.getCreateTime())) {
            builder.setCreateTime(dto.getCreateTime().getTime());
        }
        if (ObjectUtil.isNotEmpty(dto.getWaitTime())) {
            builder.setWaitTime(dto.getWaitTime().getTime());
        }
        if (ObjectUtil.isNotEmpty(dto.getStartTime())) {
            builder.setStartTime(dto.getStartTime().getTime());
        }
        if (ObjectUtil.isNotEmpty(dto.getCompleteTime())) {
            builder.setCompleteTime(dto.getCompleteTime().getTime());
        }
        log.info("推送话单采集请求返回值:{}", ProtoJsonUtils.toJson(builder.build()));
        BmsBillCollectResp bmsBillCollectResp = blockingStub.bmsBillCollect(builder.build());
        log.info("推送话单采集请求返回值:{}", ProtoJsonUtils.toJson(bmsBillCollectResp));
        return true;
    }

}
