package com.cloudstar.service.grpcservice;

import com.cloudstar.service.grpc.GrpcManage;
import com.cloudstar.service.grpc.ServiceAccountGrpc;
import com.cloudstar.service.grpc.ServiceAccountProto.AddServiceAccountRequest;
import com.cloudstar.service.grpc.ServiceAccountProto.AddServiceAccountResponse;
import com.cloudstar.service.grpc.ServiceAccountProto.DeleteServiceAccountRequest;
import com.cloudstar.service.grpc.ServiceAccountProto.DeleteServiceAccountResponse;
import com.cloudstar.service.grpc.ServiceAccountProto.EditServiceAccountRequest;
import com.cloudstar.service.grpc.ServiceAccountProto.EditServiceAccountResponse;
import com.cloudstar.service.grpcservice.facade.ServiceAccount;

import org.springframework.stereotype.Service;

import io.grpc.stub.AbstractStub;
import lombok.extern.slf4j.Slf4j;

/**
 * 客户端实现
 */
@Slf4j
@Service
public class ServiceAccountImpl implements ServiceAccount {

    private ServiceAccountGrpc.ServiceAccountBlockingStub blockingStub;

    @Override
    public void build(String targetServer, Class<? extends AbstractStub<?>> clazz) {
        blockingStub = ServiceAccountGrpc.newBlockingStub(GrpcManage.getChannel(targetServer));
    }

    @Override
    public AddServiceAccountResponse addServiceAccount(AddServiceAccountRequest request) {
        try {
            return blockingStub.addServiceAccount(request);
        } catch (Exception e) {
            log.error("Error adding service account: {}", e.getMessage());
            return AddServiceAccountResponse.newBuilder()
                                            .setSuccess(false)
                                            .setMessage("Failed to create service account")
                                            .build();
        }
    }

    @Override
    public DeleteServiceAccountResponse deleteServiceAccount(DeleteServiceAccountRequest request) {
        try {
            return blockingStub.deleteServiceAccount(request);
        } catch (Exception e) {
            log.error("Error deleting service account: {}", e.getMessage());
            return DeleteServiceAccountResponse.newBuilder()
                                               .setSuccess(false)
                                               .setMessage("Failed to delete service account")
                                               .build();
        }
    }

    @Override
    public EditServiceAccountResponse editServiceAccount(EditServiceAccountRequest request) {
        try {
            return blockingStub.editServiceAccount(request);
        } catch (Exception e) {
            log.error("Error editing service account: {}", e.getMessage());
            return EditServiceAccountResponse.newBuilder()
                                             .setSuccess(false)
                                             .setMessage("Failed to edit service account")
                                             .build();
        }
    }
}