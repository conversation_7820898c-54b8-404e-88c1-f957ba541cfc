package com.cloudstar.service.grpcservice.facade;

import com.cloudstar.common.base.pojo.mq.PrivateModelEventMessage;
import com.cloudstar.dao.model.res.ResPrivateModel;
import com.cloudstar.service.grpc.AgentPrivateModelGrpc.AgentPrivateModelBlockingStub;
import com.cloudstar.service.grpcservice.AgentPrivateModelServiceImpl;


/**
 * 私有模型服务
 *
 * <AUTHOR>
 * @date 2024/07/03
 */
public interface AgentPrivateModelService extends GrpcServiceBase {

    /**
     * 构建一个调用目标服务的对象
     *
     * @param targetServer 服务地址
     *
     * @return 调用目标服务的对象
     */
    static AgentPrivateModelService build(String targetServer) {
        AgentPrivateModelService service = new AgentPrivateModelServiceImpl();
        service.build(targetServer, AgentPrivateModelBlockingStub.class);
        return service;
    }


    /**
     * 创造
     *
     * @param model 消息
     */
    void create(ResPrivateModel model);

    /**
     * 操作
     *
     * @param model 消息
     */
    void operate(ResPrivateModel model, PrivateModelEventMessage message);


}
