package com.cloudstar.service.grpcservice.facade;

import com.cloudstar.dao.model.notebook.NotebookEntity;
import com.cloudstar.sdk.schedule.pojo.NotebookPortReq;
import com.cloudstar.service.grpc.AgentNotebookGrpc;
import com.cloudstar.service.grpcservice.AgentNotebookServiceImpl;

/**
 * notebook
 *
 * <AUTHOR>
 * @date 2024/7/2 16:50
 */
public interface AgentNotebookService extends GrpcServiceBase {

    void createNotebook(NotebookEntity notebook);

    void stopNotebook(NotebookEntity notebook);

    void deleteNotebook(NotebookEntity notebook);

    void saveImage(NotebookEntity notebook, String imageName, String imageTag);

    Boolean createPort(NotebookPortReq req);

    Boolean deletePort(NotebookPortReq req);

    String getNotebookEndpoint();


    /**
     * 构建一个调用目标服务的对象
     *
     * @param targetServer 服务地址
     *
     * @return 调用目标服务的对象
     */
    static AgentNotebookService build(String targetServer) {
        AgentNotebookServiceImpl service = new AgentNotebookServiceImpl();
        service.build(targetServer, AgentNotebookGrpc.AgentNotebookBlockingStub.class);
        return service;
    }
}
