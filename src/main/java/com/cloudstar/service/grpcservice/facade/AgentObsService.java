package com.cloudstar.service.grpcservice.facade;

import com.cloudstar.common.base.pojo.mq.DataStorageResourceMessage;
import com.cloudstar.sdk.schedule.pojo.PublishDataStorageReq;
import com.cloudstar.service.grpc.AgentObsGrpc;
import com.cloudstar.service.grpc.AgentObsProto.BucketPolicyResponse;
import com.cloudstar.service.grpc.AgentObsProto.SyncDataStorageProtoResponse;
import com.cloudstar.service.grpc.AgentObsProto.existsFileOrFileDirResponse;
import com.cloudstar.service.grpcservice.AgentObsServiceImpl;

/**
 * OBS桶操作
 *
 * <AUTHOR>
 * @date 2022-08-26 10:39
 */
public interface AgentObsService extends GrpcServiceBase {


    /**
     * 构建
     *
     * @param targetServer 目标服务器
     *
     * @return {@link AgentPermissionService}
     */
    static AgentObsService build(String targetServer) {
        AgentObsServiceImpl service = new AgentObsServiceImpl();
        service.build(targetServer, AgentObsGrpc.AgentObsBlockingStub.class);
        return service;
    }

    /**
     * 创建文件dir请求
     *
     * @param message 消息
     */
    void createFileDirRequest(DataStorageResourceMessage message);

    /**
     * 删除对象
     *
     * @param message 消息
     */
    void deleteObject(DataStorageResourceMessage message);

    /**
     * 同步数据存储
     *
     * @param userId 用户id
     * @param filePath 文件路径
     * @param excludeFileDir 排除文件夹
     *
     * @return {@link SyncDataStorageProtoResponse}
     */
    SyncDataStorageProtoResponse syncDataStorage(String userId, String filePath, boolean excludeFileDir);

    /**
     * 存在文件或文件dir
     *
     * @param userId 用户id
     * @param filePath 文件路径
     *
     * @return {@link existsFileOrFileDirResponse}
     */
    existsFileOrFileDirResponse existsFileOrFileDir(String userId, String filePath);


    /**
     * 创建桶策略
     *
     * @param bucketName 桶名称
     * @param fileDir 文件目录
     * @param accountUuid 账户UUID
     */
    BucketPolicyResponse createPolicy(String bucketName, String fileDir, String accountUuid);

    /**
     * 删除桶策略
     *
     * @param bucketName 桶名称
     * @param fileDir 文件目录
     * @param accountUuid 账户UUID
     */
    BucketPolicyResponse deletePolicy(String bucketName, String fileDir, String accountUuid);

    BucketPolicyResponse queryPolicy(String fileDir);

    boolean publish(PublishDataStorageReq req);

    boolean subscribe(PublishDataStorageReq req);

    boolean unsubscribe(PublishDataStorageReq req);


}
