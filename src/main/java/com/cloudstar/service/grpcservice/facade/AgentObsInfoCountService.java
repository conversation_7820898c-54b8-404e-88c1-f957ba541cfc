package com.cloudstar.service.grpcservice.facade;

import cn.hutool.core.util.StrUtil;
import com.cloudstar.service.grpc.AgentObsForBmsGrpc;
import com.cloudstar.service.grpc.AgentObsForBmsProto;
import com.cloudstar.service.grpcservice.AgentObsInfoCountServiceImpl;
import io.grpc.stub.AbstractStub;
import io.grpc.stub.StreamObserver;

/**
 * Grpc Obs信息统计
 *
 * <AUTHOR>
 * @date 2022-08-17 14:20
 */
public interface AgentObsInfoCountService extends GrpcServiceBase {


    /**
     * 构建
     *
     * @param targetServer 目标服务器
     * @param type 类型
     *
     * @return {@link AgentObsInfoCountService}
     */
    static AgentObsInfoCountService build(String targetServer, String type) {
        AgentObsInfoCountServiceImpl service = new AgentObsInfoCountServiceImpl();
        if (StrUtil.equals("add", type)) {
            service.build(targetServer, AgentObsForBmsGrpc.AgentObsForBmsStub.class, type);
        } else {
            service.build(targetServer, AgentObsForBmsGrpc.AgentObsForBmsBlockingStub.class, type);
        }
        return service;
    }

    void build(String targetServer, Class<? extends AbstractStub<?>> clazz, String type);

    void countObs(AgentObsForBmsProto.SyncDataObsProtoRequest syncDataObsProtoRequest,
                  StreamObserver<AgentObsForBmsProto.SyncDataObsProtoResponse> response);
}
