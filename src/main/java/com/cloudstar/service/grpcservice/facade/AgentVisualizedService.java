package com.cloudstar.service.grpcservice.facade;

import com.cloudstar.common.base.pojo.mq.VisualizedEventMessage;
import com.cloudstar.service.grpc.AgentVisualizedJobServiceGrpc;
import com.cloudstar.service.grpcservice.AgentVisualizedServiceImpl;


/**
 * 代理镜像服务
 *
 * <AUTHOR>
 * @date 2024/07/03
 */
public interface AgentVisualizedService extends GrpcServiceBase {

    /**
     * 构建一个调用目标服务的对象
     *
     * @param targetServer 服务地址
     * @return 调用目标服务的对象
     */
    static AgentVisualizedService build(String targetServer) {
        AgentVisualizedService service = new AgentVisualizedServiceImpl();
        service.build(targetServer, AgentVisualizedJobServiceGrpc.AgentVisualizedJobServiceStub.class);
        return service;
    }


    /**
     * 创造
     *
     * @param message 消息
     */
    void create(VisualizedEventMessage message);

    /**
     * 开始
     *
     * @param message 消息
     */
    void start(VisualizedEventMessage message);

    /**
     * 停止
     *
     * @param message 消息
     */
    void stop(VisualizedEventMessage message);


    /**
     * 删除
     *
     * @param message 消息
     */
    void delete(VisualizedEventMessage message);
}
