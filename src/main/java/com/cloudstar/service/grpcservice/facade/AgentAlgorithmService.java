package com.cloudstar.service.grpcservice.facade;

import com.cloudstar.sdk.schedule.pojo.PublishAlgorithmReq;
import com.cloudstar.service.grpc.AgentAlgorithmServiceGrpc;
import com.cloudstar.service.grpcservice.AgentAlgorithmServiceImpl;

/**
 * 算法
 *
 * <AUTHOR>
 * @date 2024/8/20 16:51
 */
public interface AgentAlgorithmService extends GrpcServiceBase {


    /**
     * 构建
     *
     * @param targetServer 目标服务器
     *
     * @return {@link AgentPermissionService}
     */
    static AgentAlgorithmService build(String targetServer) {
        AgentAlgorithmServiceImpl service = new AgentAlgorithmServiceImpl();
        service.build(targetServer, AgentAlgorithmServiceGrpc.AgentAlgorithmServiceStub.class);
        return service;
    }

    boolean publish(PublishAlgorithmReq req);

    boolean subscribe(PublishAlgorithmReq req);

    boolean unsubscribe(PublishAlgorithmReq req);


}
