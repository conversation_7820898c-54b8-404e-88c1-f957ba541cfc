package com.cloudstar.service.grpcservice.server.exector;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.ConfigService;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.enums.NodeLabelKeyEnum;
import com.cloudstar.common.base.enums.NodePurposeEnum;
import com.cloudstar.config.NexusAdminConfig;
import com.cloudstar.dao.mapper.cluster.AgentFlavorMapper;
import com.cloudstar.dao.mapper.user.AgentUsersMapper;
import com.cloudstar.dao.mapper.user.ResImageHubUserMapper;
import com.cloudstar.dao.model.cluster.AgentFlavor;
import com.cloudstar.dao.model.user.AgentUsers;
import com.cloudstar.dao.model.user.ResImageHubUser;
import com.cloudstar.enums.VolcanoSchedulerEnum;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.Notebook;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.Affinity;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.ImagePullSecrets;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.Volumes;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.affinity.NodeAffinity;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.affinity.nodeaffinity.RequiredDuringSchedulingIgnoredDuringExecution;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.affinity.nodeaffinity.requiredduringschedulingignoredduringexecution.NodeSelectorTerms;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.affinity.nodeaffinity.requiredduringschedulingignoredduringexecution.nodeselectorterms.MatchExpressions;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.containers.LivenessProbe;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.containers.ReadinessProbe;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.containers.SecurityContext;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.containers.VolumeMounts;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.containers.livenessprobe.HttpGet;
import com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.volumes.PersistentVolumeClaim;
import com.cloudstar.k8s.service.facade.NotebookService;
import com.cloudstar.k8s.service.facade.ServiceService;
import com.cloudstar.service.grpc.AgentNotebookProto;
import com.cloudstar.service.grpc.AgentNotebookProto.createNotebookPortReq;
import com.cloudstar.service.grpc.AgentNotebookProto.createNotebookPortResp;
import com.cloudstar.service.grpc.AgentNotebookProto.deleteNotebookPortReq;
import com.cloudstar.service.grpc.AgentNotebookProto.deleteNotebookPortResp;
import com.cloudstar.service.grpc.AgentNotebookProto.notebookEndpointResp;
import com.cloudstar.utils.K8sResourceUtil;
import com.cloudstar.utils.NotebookCommandUtil;

import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import io.fabric8.kubernetes.api.model.EnvVar;
import io.fabric8.kubernetes.api.model.IntOrString;
import io.fabric8.kubernetes.api.model.Pod;
import io.fabric8.kubernetes.api.model.Service;
import io.fabric8.kubernetes.api.model.ServicePort;
import io.fabric8.kubernetes.api.model.batch.v1.Job;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.extern.slf4j.Slf4j;


/**
 * agent noteBook exector
 */
@Slf4j
@Component
public abstract class AgentNoteBookExector {

    @Resource
    AgentFlavorMapper agentFlavorMapper;

    @Resource
    NotebookService notebookService;

    @Resource
    AgentUsersMapper agentUsersMapper;

    @Resource
    ServiceService serviceService;

    @Resource
    KubernetesClient kubernetesClient;

    @Resource
    ResImageHubUserMapper resImageHubUserMapper;

    @Resource
    private ConfigService configService;

    private static final String QUEUE_NAME = "volcano.sh/queue-name";


    /**
     * <pre>
     * 创建notebook
     * </pre>
     */
    public void createNotebook(AgentNotebookProto.createNotebookReq request,
                               io.grpc.stub.StreamObserver<AgentNotebookProto.createNotebookResp> responseObserver) {
        log.info("createNotebook:{}", request);
        Boolean status = true;
        try {
            final AgentUsers agentUsers = agentUsersMapper.selectOne(
                    new LambdaQueryWrapper<AgentUsers>().eq(AgentUsers::getUserId, request.getUserId()));
            if (ObjectUtil.isEmpty(agentUsers)) {
                status = false;
                log.error("开发环境创建失败,底层账号不存在:{},{}", request.getName(), request.getUserId());
                return;
            }
            final boolean existCrd = notebookService.isExistNoteBook(request.getNamespace(), request.getName());
            if (existCrd) {
                status = false;
                log.error("开发环境创建失败,开发环境已存在:{}", request.getName());
                return;
            }
            final AgentFlavor flavor = agentFlavorMapper.selectById(request.getFlavorId());
            if (ObjectUtil.isEmpty(flavor)) {
                status = false;
                log.error("开发环境创建失败,未找到对应的规格:{}", request.getName());
                return;
            }
            //设置队列名称
            if (ObjectUtil.isEmpty(request.getPoolId())) {
                status = false;
                log.error("开发环境创建失败,未找到对应资源池:{}", request.getName());
                return;
            }
            InputStream inputStream = null;
            log.info("templateResource，读取notebook-demo.yaml");
            org.springframework.core.io.Resource templateResource = new ClassPathResource("k8s/notebook-demo.yaml",
                                                                                          this.getClass().getClassLoader());
            inputStream = templateResource.getInputStream();
            Yaml yaml = new Yaml();
            log.info("yaml.loadAs");
            final Notebook notebook = yaml.loadAs(inputStream, Notebook.class);
            //设置metadata
            notebook.getMetadata().setName(request.getName());
            HashMap labels = new HashMap();
            labels.put("app", request.getName());
            labels.put(QUEUE_NAME, request.getPoolId());
            HashMap annotations = new HashMap();
            annotations.put(QUEUE_NAME, request.getPoolId());
            notebook.getMetadata().setAnnotations(annotations);
            notebook.getMetadata().setLabels(labels);
            notebook.getMetadata().setNamespace(request.getNamespace());
            //设置容器规格
            notebook.getSpec().getTemplate().getSpec().getContainers().get(0).setImage(request.getImage());
            notebook.getSpec().getTemplate().getSpec().getContainers().get(0).setName(request.getName());
            HashMap resourceLimit = K8sResourceUtil.getResourceLimit(request.getIsVirtualized(), flavor);
            notebook.getSpec().getTemplate().getSpec().getContainers().get(0).getResources().setLimits(resourceLimit);
            notebook.getSpec().getTemplate().getSpec().getContainers().get(0).getResources().setRequests(resourceLimit);
            // 是否特权模式
            SecurityContext securityContext = new SecurityContext();
            securityContext.setPrivileged(request.getPrivilege());
            notebook.getSpec().getTemplate().getSpec().getContainers().get(0).setSecurityContext(securityContext);
            notebook.getSpec().getTemplate().getSpec().getContainers().get(0).setImagePullPolicy("IfNotPresent");
            if (request.getPrivilege()) {
                // 特权模式
                String className = System.getenv("PRIVILEGE_RUNTIME_CLASSNAME");
                notebook.getSpec().getTemplate().getSpec().setRuntimeClassName(className);
            }
            //配置健康检查 存活检查
            LivenessProbe livenessProbe = new LivenessProbe();
            HttpGet httpGet = new HttpGet();
            httpGet.setPort(new IntOrString(80));
            httpGet.setPath("/");
            livenessProbe.setInitialDelaySeconds(120);
            livenessProbe.setPeriodSeconds(10);
            livenessProbe.setTimeoutSeconds(5);
            livenessProbe.setSuccessThreshold(1);
            livenessProbe.setFailureThreshold(3);
            livenessProbe.setHttpGet(httpGet);
            //notebook.getSpec().getTemplate().getSpec().getContainers().get(0).setLivenessProbe(livenessProbe);

            //配置健康检查 就绪检查
            ReadinessProbe readinessProbe = new ReadinessProbe();
            com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.containers.readinessprobe.HttpGet readinessProbeHttp =
                    new com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.spec.containers.readinessprobe.HttpGet();
            readinessProbeHttp.setPort(new IntOrString(80));
            readinessProbeHttp.setPath("/");
            readinessProbe.setInitialDelaySeconds(120);
            readinessProbe.setPeriodSeconds(10);
            readinessProbe.setTimeoutSeconds(5);
            readinessProbe.setSuccessThreshold(1);
            readinessProbe.setFailureThreshold(3);
            readinessProbe.setHttpGet(readinessProbeHttp);
            //notebook.getSpec().getTemplate().getSpec().getContainers().get(0).setReadinessProbe(readinessProbe);
            notebook.getSpec().getTemplate().getSpec().setSchedulerName(VolcanoSchedulerEnum.DEVELOP_SCHEDULER.getSchedulerName());
            //设置节点选择器
            if (ObjectUtil.isEmpty(request.getComputeProductName())
                    || ObjectUtil.isEmpty(request.getNodePurpose())) {
                status = false;
                log.error("开发环境创建失败,未找到调度节点:{}", request.getName());
                return;
            }
            //设置计算卡型号
            Map<String, String> nodeMap = new HashMap<>();
            nodeMap.put(NodeLabelKeyEnum.COMPUTE.getCode(), request.getComputeProductName());
            //是否虚拟化
            if (request.getIsVirtualized()) {
                nodeMap.put(NodeLabelKeyEnum.VIRTUALIZED.getCode(), "true");
            }
            //TODO 环境变量控制 判断是否强制区分节点虚拟化，强制区分，虚拟化规格只能往虚拟化节点调用，非虚拟化规格只能往非虚拟化节点调用
            if (false) {
                nodeMap.put(NodeLabelKeyEnum.VIRTUALIZED.getCode(), request.getIsVirtualized() ? "true" : "false");
            }
            notebook.getSpec().getTemplate().getSpec().setNodeSelector(nodeMap);
            //设置节点用途
            final Affinity affinity = new Affinity();
            final NodeAffinity nodeAffinity = new NodeAffinity();
            final RequiredDuringSchedulingIgnoredDuringExecution requireExecution = new RequiredDuringSchedulingIgnoredDuringExecution();
            final List<NodeSelectorTerms> nodeSelectorTermsList = new ArrayList<>();
            final NodeSelectorTerms nodeSelectorTerms = new NodeSelectorTerms();
            final List<MatchExpressions> matchExpressionsList = new ArrayList<>();
            MatchExpressions matchExpressions = new MatchExpressions();
            matchExpressions.setKey(NodeLabelKeyEnum.NODEPURPOSE.getCode());
            matchExpressions.setOperator("In");
            matchExpressions.setValues(Arrays.asList(NodePurposeEnum.DEVELOP.getCode(), NodePurposeEnum.ALL.getCode()));
            matchExpressionsList.add(matchExpressions);
            nodeSelectorTerms.setMatchExpressions(matchExpressionsList);
            nodeSelectorTermsList.add(nodeSelectorTerms);
            requireExecution.setNodeSelectorTerms(nodeSelectorTermsList);
            nodeAffinity.setRequiredDuringSchedulingIgnoredDuringExecution(requireExecution);
            affinity.setNodeAffinity(nodeAffinity);
            notebook.getSpec().getTemplate().getSpec().setAffinity(affinity);
            ImagePullSecrets secret = new ImagePullSecrets();
            secret.setName(request.getNamespace() + "-image-secret");
            ArrayList<ImagePullSecrets> imagePullSecrets = new ArrayList<>();
            imagePullSecrets.add(secret);
            notebook.getSpec().getTemplate().getSpec().setImagePullSecrets(imagePullSecrets);
            //创建存储挂载
            log.info("创建存储挂载");
            final List<AgentNotebookProto.blockStorageInfo> storageInfoList = request.getStorageInfoList();
            if (CollectionUtil.isNotEmpty(storageInfoList)) {
                List<Volumes> volumesList = new ArrayList<>();
                List<VolumeMounts> volumeMountsList = new ArrayList<>();
                storageInfoList.stream().forEach(blockStorageInfo -> {
                    Volumes volumes = new Volumes();
                    volumes.setName(blockStorageInfo.getName());
                    PersistentVolumeClaim persistentVolumeClaim = new PersistentVolumeClaim();
                    persistentVolumeClaim.setClaimName(blockStorageInfo.getName());
                    volumes.setPersistentVolumeClaim(persistentVolumeClaim);
                    volumesList.add(volumes);
                    VolumeMounts volumeMounts = new VolumeMounts();
                    volumeMounts.setName(blockStorageInfo.getName());
                    volumeMounts.setMountPath(blockStorageInfo.getMountPath());
                    volumeMountsList.add(volumeMounts);
                });
                notebook.getSpec().getTemplate().getSpec().setVolumes(volumesList);
                notebook.getSpec().getTemplate().getSpec().getContainers().get(0).setVolumeMounts(volumeMountsList);
            }
            log.info("新增ssh相关配置");
            //新增ssh相关配置
            if (ObjectUtil.isNotEmpty(request.getSsh())
                    && ObjectUtil.isNotEmpty(request.getSsh().getAk())
                    && ObjectUtil.isNotEmpty(request.getSsh().getSk())) {
                //设置ssh信息
                final AgentNotebookProto.sshInfo ssh = request.getSsh();
                ArrayList<String> command = new ArrayList<>();
                command.add("/bin/bash");
                command.add("-c");
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(NotebookCommandUtil.createSK(ssh.getAk(), ssh.getSk()));
                if (ObjectUtil.isNotEmpty(ssh.getWhiteIps())) {
                    stringBuilder.append(NotebookCommandUtil.createWhiteIp(ssh.getWhiteIps()));
                }
                stringBuilder.append(NotebookCommandUtil.runSsh());
                stringBuilder.append(NotebookCommandUtil.runJupyterLab(request.getNamespace(), request.getName()));
                command.add(stringBuilder.toString());
                notebook.getSpec().getTemplate().getSpec().getContainers().get(0).setCommand(command);
            } else {
                ArrayList<String> command = new ArrayList<>();
                command.add("/bin/bash");
                command.add("-c");
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append(NotebookCommandUtil.runJupyterLab(request.getNamespace(), request.getName()).replace("&&", ""));
                command.add(stringBuilder.toString());
                notebook.getSpec().getTemplate().getSpec().getContainers().get(0).setCommand(command);
            }
            log.info("notebookService.createNoteBook start");
            notebookService.createNoteBook(request.getNamespace(), notebook);
            log.info("notebookService.createNoteBook end");
        } catch (Exception e) {
            status = false;
            log.error("创建开发环境失败:{}", e.getMessage());
            e.printStackTrace();
        } finally {
            AgentNotebookProto.createNotebookResp response = AgentNotebookProto.createNotebookResp.newBuilder().setStatus(status).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }

    }


    /**
     * <pre>
     * 删除notebook
     * </pre>
     */
    public void deleteNotebook(AgentNotebookProto.deleteNotebookReq request,
                               io.grpc.stub.StreamObserver<AgentNotebookProto.deleteNotebookResp> responseObserver) {
        Boolean status = true;
        try {
            final AgentUsers agentUsers = agentUsersMapper.selectOne(
                    new LambdaQueryWrapper<AgentUsers>().eq(AgentUsers::getUserId, request.getUserId()));
            if (ObjectUtil.isEmpty(agentUsers)) {
                status = false;
                log.error("开发环删除失败,底层账号不存在:{},{}", request.getName(), request.getUserId());
                return;
            }
            notebookService.deleteNoteBook(request.getNamespace(), request.getName());
            if (CollectionUtil.isNotEmpty(request.getSvcListList())) {
                try {
                    request.getSvcListList().forEach(svcName -> {
                        serviceService.deleteService(request.getNamespace(), svcName);
                    });
                } catch (Exception e) {
                    log.error("notebook删除端口失败", e);
                }
            }
        } catch (Exception e) {
            status = false;
            log.error("删除开发环境失败:{}", e.getMessage());
        } finally {
            AgentNotebookProto.deleteNotebookResp response = AgentNotebookProto.deleteNotebookResp.newBuilder().setStatus(status).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    /**
     * <pre>
     * 创建端口
     * </pre>
     */
    public void createNotebookPort(createNotebookPortReq request,
                                   io.grpc.stub.StreamObserver<createNotebookPortResp> responseObserver) {
        boolean status = true;
        Integer nodePort = null;
        String uid = null;
        try {
            InputStream inputStream = null;
            org.springframework.core.io.Resource templateResource = new ClassPathResource("k8s/deepseek-svc-demo.yaml",
                                                                                          this.getClass().getClassLoader());
            inputStream = templateResource.getInputStream();
            Yaml yaml = new Yaml();
            final Service service = yaml.loadAs(inputStream, Service.class);
            service.getMetadata().setName(request.getSvcName());
            service.getMetadata().setNamespace(request.getNamespace());
            final List<ServicePort> ports = new ArrayList<>();
            ServicePort servicePort = new ServicePort();
            servicePort.setName(request.getSvcName());
            servicePort.setPort(Integer.parseInt(request.getInsidePort() + ""));
            servicePort.setTargetPort(new IntOrString(Integer.parseInt(request.getInsidePort() + "")));
            ports.add(servicePort);
            service.getSpec().setPorts(ports);
            HashMap labels = new HashMap();
            labels.put("statefulset", request.getNotebookId());
            service.getSpec().setSelector(labels);
            service.getSpec().setType("NodePort");
            final boolean flag = serviceService.createService(request.getNamespace(), service);
            if (flag) {
                final Service createService = serviceService.getService(request.getNamespace(), request.getSvcName());
                nodePort = createService.getSpec().getPorts().get(0).getNodePort();
                uid = createService.getMetadata().getUid();

            } else {
                status = false;
            }
        } catch (Exception e) {
            status = false;
            log.error("notebook创建端口失败:", e);
        } finally {
            createNotebookPortResp.Builder builder = createNotebookPortResp.newBuilder();
            builder.setStatus(status);
            builder.setSvcName(request.getSvcName());
            if (ObjectUtil.isNotEmpty(nodePort)) {
                builder.setExternalPort(Long.parseLong(nodePort + ""));
            }
            if (ObjectUtil.isNotEmpty(uid)) {
                builder.setSvcId(uid);
            }
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * <pre>
     * 删除端口
     * </pre>
     */
    public void deleteNotebookPort(deleteNotebookPortReq request,
                                   io.grpc.stub.StreamObserver<deleteNotebookPortResp> responseObserver) {
        boolean status = true;
        try {
            status = serviceService.deleteService(request.getNamespace(), request.getSvcName());
        } catch (Exception e) {
            status = false;
            log.error("notebook删除端口失败:", e);
        } finally {
            deleteNotebookPortResp.Builder builder = deleteNotebookPortResp.newBuilder();
            builder.setStatus(status);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }

    }

    /**
     * <pre>
     * 查询端点
     * </pre>
     */
    public void getNotebookEndpoint(AgentNotebookProto.notebookEndpointReq request,
                                    io.grpc.stub.StreamObserver<AgentNotebookProto.notebookEndpointResp> responseObserver) {
        boolean status = true;
        String endpoint = "";
        try {
            String masterUrl = kubernetesClient.getConfiguration().getMasterUrl();
            if (ObjectUtil.isNotEmpty(masterUrl)) {
                URL url = new URL(masterUrl);
                endpoint = url.getHost();
            }
        } catch (Exception e) {
            status = false;
            log.error("notebook端点查询失败:", e);
        } finally {
            notebookEndpointResp.Builder builder = notebookEndpointResp.newBuilder();
            builder.setStatus(status);
            builder.setEndpoint(endpoint);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    /**
     * <pre>
     * 查询端点
     * </pre>
     */
    public void saveImage(AgentNotebookProto.saveImageReq request,
                          io.grpc.stub.StreamObserver<AgentNotebookProto.saveImageResp> responseObserver) {
        log.info("saveImage:{}", request);
        Boolean status = true;
        try {
            final AgentUsers agentUsers = agentUsersMapper.selectOne(
                    new LambdaQueryWrapper<AgentUsers>().eq(AgentUsers::getUserId, request.getUserId()));
            if (ObjectUtil.isEmpty(agentUsers)) {
                status = false;
                log.error("开发环境保存镜像失败,底层账号不存在:{},{}", request.getNotebookName(), request.getUserId());
                return;
            }
            // 多种情况兼容
            final ResImageHubUser resImageHubUser = resImageHubUserMapper.selectOne(
                    new LambdaQueryWrapper<ResImageHubUser>()
                            .eq(ResImageHubUser::getUsername, request.getUserId())
                            .or().eq(ResImageHubUser::getUsername, agentUsers.getUserName()));
            if (ObjectUtil.isEmpty(resImageHubUser)) {
                status = false;
                log.error("开发环境保存镜像失败,底层镜像账号不存在:{},{}", request.getNotebookName(), request.getUserId());
                return;
            }
            NexusAdminConfig config = configService.getConfig(ConfigType.NEXUS_ADMIN_CONFIG);
            if (ObjectUtil.isEmpty(config) || ObjectUtil.isEmpty(config.getRegistryAddress())) {
                status = false;
                log.error("开发环境保存镜像失败,镜像仓库地址不存在:{}", request.getNotebookName());
                return;
            }
            final String registryAddress = config.getRegistryAddress().stringValue();

            final Pod notebookPod = notebookService.getNotebookPod(request.getNamespace(), request.getNotebookName());
            if (ObjectUtil.isEmpty(notebookPod)) {
                status = false;
                log.error("开发环境保存镜像失败,容器不存在:{}", request.getNotebookName());
                return;
            }
            // 获取调度到的节点名
            String nodeName = notebookPod.getSpec().getNodeName();
            final Map<String, String> nodeSelector = notebookPod.getSpec().getNodeSelector();
            if (ObjectUtil.isNotEmpty(nodeName)) {
                nodeSelector.put("kubernetes.io/hostname", nodeName);
            }
            String containerId = notebookPod.getStatus().getContainerStatuses().get(0).getContainerID();
            if (ObjectUtil.isNotEmpty(containerId)) {
                String[] parts = containerId.split("//");
                if (parts.length > 1) {
                    containerId = parts[1];
                }
            }
            InputStream inputStream = null;
            log.info("templateResource，读取saveImageJob.yaml");
            final String isUsedContainerd = System.getenv("IS_USED_CONTAINERD");
            String path = "k8s/saveImageDocker.yaml";
            if ("true".equals(isUsedContainerd)) {
                path = "k8s/saveImageContainerd.yaml";
            }
            org.springframework.core.io.Resource templateResource = new ClassPathResource(path,
                                                                                          this.getClass().getClassLoader());
            inputStream = templateResource.getInputStream();
            Yaml yaml = new Yaml();
            log.info("yaml.loadAs");
            final Job job = yaml.loadAs(inputStream, Job.class);
            final Map<String, String> annotation = new HashMap<>();
            annotation.put("notebookName", request.getNotebookName());
            job.getMetadata().setName(request.getNotebookName() + "-save-image");
            job.getMetadata().setNamespace(request.getNamespace());
            job.getMetadata().setAnnotations(annotation);
            job.getSpec().getTemplate().getSpec().setNodeSelector(nodeSelector);
            List<EnvVar> envList = new ArrayList<>();
            EnvVar containerIdEnv = new EnvVar();
            containerIdEnv.setName("CONTAINER_ID");
            containerIdEnv.setValue(containerId);
            envList.add(containerIdEnv);

            EnvVar imageNameEnv = new EnvVar();
            imageNameEnv.setName("IMAGE_NAME");
            imageNameEnv.setValue(request.getImageName());
            envList.add(imageNameEnv);

            EnvVar imageNameSpaceEnv = new EnvVar();
            imageNameSpaceEnv.setName("IMAGE_NAMESPACE");
            imageNameSpaceEnv.setValue(request.getNamespace());
            envList.add(imageNameSpaceEnv);

            EnvVar imageTagEnv = new EnvVar();
            imageTagEnv.setName("IMAGE_TAG");
            imageTagEnv.setValue(request.getImageTag());
            envList.add(imageTagEnv);

            EnvVar nexusUrlEnv = new EnvVar();
            nexusUrlEnv.setName("REGISTRY_URL");
            nexusUrlEnv.setValue(registryAddress);
            envList.add(nexusUrlEnv);

            EnvVar nexusUserEnv = new EnvVar();
            nexusUserEnv.setName("DOCKER_USER");
            nexusUserEnv.setValue(resImageHubUser.getUsername());
            envList.add(nexusUserEnv);

            EnvVar nexusPasswordEnv = new EnvVar();
            nexusPasswordEnv.setName("DOCKER_PASS");
            nexusPasswordEnv.setValue(resImageHubUser.getPassword());
            envList.add(nexusPasswordEnv);
            job.getSpec().getTemplate().getSpec().getContainers().get(0).setEnv(envList);
            final Job createJob = kubernetesClient.batch().v1().jobs().inNamespace(request.getNamespace()).create(job);
        } catch (Exception e) {
            status = false;
            log.error("保存notebook镜像失败:{}", e.getMessage());
            e.printStackTrace();
        } finally {
            AgentNotebookProto.saveImageResp response = AgentNotebookProto.saveImageResp.newBuilder().setStatus(status).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }
}
