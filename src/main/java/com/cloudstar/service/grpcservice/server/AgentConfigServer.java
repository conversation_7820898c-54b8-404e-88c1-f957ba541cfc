package com.cloudstar.service.grpcservice.server;

import com.cloudstar.service.grpc.AgentConfigProto;
import com.cloudstar.service.grpc.AgentConfigWatchGrpc;
import com.cloudstar.service.grpcservice.server.exector.AgentConfigWatchExector;
import com.cloudstar.service.grpcservice.server.exector.GrpcExectorFactory;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@GrpcService
@Slf4j
public class AgentConfigServer extends AgentConfigWatchGrpc.AgentConfigWatchImplBase {
    @Resource
    GrpcExectorFactory<AgentConfigWatchExector> factory;

    @PostConstruct
    public void init() {
        factory.getExector(AgentConfigWatchExector.class).init();
    }

    /**
     * <pre>
     * 创建块存储
     * </pre>
     */
    public void agentConfigSend(AgentConfigProto.AgentConfigRequest request,
                                StreamObserver<AgentConfigProto.AgentConfigResponse> responseObserver) {
        factory.getExector(AgentConfigWatchExector.class).agentConfigSend(request, responseObserver);
    }

}
