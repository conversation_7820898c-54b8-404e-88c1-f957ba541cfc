package com.cloudstar.service.grpcservice.server.exector;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.cloudstar.ConfigService;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.enums.MirrorStatusEnum;
import com.cloudstar.common.base.pojo.mq.MirrorBuildMessage;
import com.cloudstar.common.component.elasticsearch.dao.model.MirrorLog;
import com.cloudstar.common.component.elasticsearch.service.MirrorLogService;
import com.cloudstar.common.component.redis.util.RedisUtil;
import com.cloudstar.config.NexusAdminConfig;
import com.cloudstar.service.grpc.MirrorBuildProto;
import com.cloudstar.utils.DockerUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardOpenOption;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 *MirrorBuild Exector
 *
 */
@Slf4j
@Component
public abstract class MirrorBuildExector {
    private  RedisUtil redisUtil;

    // 用于对象转换
    private  ObjectMapper objectMapper;

    private static DockerUtil dockerUtil;

    @Resource
    private  ConfigService configService;

    // docker模板文件
    private static final String TEMPLATE = "Dockerfile.template";
    private static final String DOCKERNAME = "Dockerfile";

    // 添加成员变量存储当前临时目录路径
    private Path currentTempDir;

    // 推送仓库地址
    private static final String NEXUS = "cfn-nexus.rightcloud.com.cn/";

    // 用于Redis做key
    private static final String REDIS_KEY_PREFIX = "mirrorBuildQueue:";
    private String redisKey;

    // 用户保存Dockerfile内容并响应到客户端
    private String dockerfileContent;

    @Resource
    private MirrorLogService mirrorLogService;

    /**
     * Dcoker初始化
     */
    @PostConstruct
    public void init() {
        try {
            NexusAdminConfig nexusAdminConfig = configService.getConfig(ConfigType.NEXUS_ADMIN_CONFIG);
            dockerUtil = new DockerUtil(nexusAdminConfig);
        } catch (Exception e) {
            log.error("初始化Docker配置失败:{}", e.getMessage());
        }
    }

    /**
     * 服务端收到指令，执行构建
     *
     * @param request 客户端发送的req
     * @param responseObserver 响应状态
     */
    public void executeBuild(MirrorBuildProto.MirrorBuildMessage request, StreamObserver<MirrorBuildProto.MirrorBuildResponse> responseObserver) {
        log.info("服务端开始执行构建");
        // 检查请求是否为空
        if (request == null) {
            log.error("Received null request.");
            responseObserver.onNext(buildResponse(false, MirrorStatusEnum.BUILDFAILED, "Received null request.", "null"));
            responseObserver.onCompleted();
            return;
        }
        try {
            // 定义一个布尔值用于记录结果
            boolean buildResult = true;
            /// 执行 Dockerfile 生成(暂时跳过)
            // boolean result = buildByTemplate(mirrorBuildMessage);
            // log.info("已执行Dockerfile文件生成");
            // if (!result) {
            //     log.error("生成 Dockerfile 失败");
            //     buildResult = false;
            //     responseObserver.onNext(buildResponse(buildResult, MirrorStatusEnum.BUILDFAILED, "生成 Dockerfile 失败", "null"));
            //     responseObserver.onCompleted();
            //    return;
            // }
            // log.info("Dockerfile文件已生成");
            // responseObserver.onNext(buildResponse(buildResult, MirrorStatusEnum.BUILDING, "构建中", dockerfileContent));
            // 构建并推送至仓库
            boolean pullResult = pullToRepository(request.getMirrorName());
            log.info("将 Dockerfile 构建成镜像并推送至仓库已执行");
            if (!pullResult) {
                log.error("推送失败");
                buildResult = false;
                // TODO 此处后续需修改content内容
                responseObserver.onNext(buildResponse(buildResult, MirrorStatusEnum.BUILDFAILED, "推送失败", "null"));
                responseObserver.onCompleted();
                return;
            }
            // 根据是否成功推送创建响应
            responseObserver.onNext(buildResponse(buildResult, MirrorStatusEnum.FINISHED, "构建完成", "null"));
            responseObserver.onCompleted();
            return;
        } catch (Exception e) {
            log.error("Error processing build request: {}", e.getMessage(), e);

            // 如果发生异常，返回失败响应
            MirrorBuildProto.MirrorBuildResponse errorResponse = MirrorBuildProto.MirrorBuildResponse.newBuilder()
                    .setSuccess(false)
                    .setStatus(
                            MirrorStatusEnum.BUILDFAILED.getCode())
                    .setMessage("Error processing build request: "
                            + e.getMessage())
                    .build();

            responseObserver.onNext(errorResponse);
            responseObserver.onCompleted();
            return;
        }
    }

    /**
     * 构建响应信息
     *
     * @param success 布尔值，返回是否成功
     * @param status 构架状态
     * @param message 响应信息
     *
     * @return 响应体
     */
    private MirrorBuildProto.MirrorBuildResponse buildResponse(boolean success, MirrorStatusEnum status, String message, String content) {
        return MirrorBuildProto.MirrorBuildResponse.newBuilder()
                .setSuccess(success)
                .setStatus(status.name())
                .setContent(content)
                .setMessage(message)
                .build();
    }

    /**
     * 将构建请求放入 Redis 队列
     *
     * @param request MirrorBuildMessage 构建请求
     *
     * @return boolean 如果成功放入队列则返回 true，否则返回 false
     */
    private boolean enqueueBuildRequestToRedis(MirrorBuildProto.MirrorBuildMessage request) {
        try {
            // 创建 MirrorBuildMessage 实例
            MirrorBuildMessage mirrorBuildMessage = new MirrorBuildMessage();
            BeanUtils.copyProperties(request, mirrorBuildMessage);

            // 将 mirrorBuildMessage 转换为 JSON 字符串
            String message = objectMapper.writeValueAsString(mirrorBuildMessage);

            // 将 JSON 字符串放入 Redis 队列
            log.info("执行了一次入队操作");
            redisUtil.lRightPush(redisKey, message); // 替换为实际的 Redis 键

            return true; // 操作成功
        } catch (Exception e) {
            log.error(e.getMessage());
            return false;
        }
    }

    /**
     * 执行出队操作
     */
    private MirrorBuildMessage outqueueBuildRequestToRedis() {
        try {
            String message = redisUtil.lBLeftPop(redisKey, 5, TimeUnit.SECONDS);
            if (message != null) {
                return objectMapper.readValue(message, MirrorBuildMessage.class);
            } else {
                log.info("队列为空");
                return null;
            }
        } catch (JsonProcessingException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    /**
     * 根据模板生成docker文件
     *
     * @param message 模板所需具体版本参数
     *
     * @return 返回一个布尔类型判断是否执行成功
     */
    private boolean buildByTemplate(MirrorBuildMessage message) {
        //传参，便于后续替换
        Map<String, String> parameters = Map.of(
                "BASE_IMAGE_OS", message.getOperatingSystem(),
                "BASE_IMAGE_ARCH", message.getPlatformArchitecture(),
                "BASE_IMAGE_FRAMEWORK", message.getTrainingFramework(),
                "BASE_IMAGE_CUDA", message.getTrainingDriver()
        );

        try {
            // 从resources读取模板文件
            String dockerfileContent;
            try (InputStream inputStream = getClass().getClassLoader()
                    .getResourceAsStream(TEMPLATE)) {

                if (inputStream == null) {
                    log.error("模板文件不存在: {}", TEMPLATE);
                    return false;
                }

                dockerfileContent = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
            }

            // 读取模板 Dockerfile,替换占位符
            for (Map.Entry<String, String> entry : parameters.entrySet()) {
                dockerfileContent = dockerfileContent.replace("${" + entry.getKey() + "}", entry.getValue());
            }

            // 处理自定义内容
            String customContent = message.getCustomContent();
            if (StrUtil.isNotBlank(customContent)) {
                // 格式化自定义内容
                String formattedCustomContent = String.format("\n\n# 自定义配置开始\n%s\n# 自定义配置结束\n", customContent);

                // 追加自定义内容到dockerfileContent
                dockerfileContent += formattedCustomContent;
                log.info("已添加自定义配置到Dockerfile");
            }

            currentTempDir = Files.createTempDirectory("docker_");
            Path outputPath = currentTempDir.resolve(DOCKERNAME);

            // 将生成的 Dockerfile 写入临时文件
            Files.writeString(outputPath, dockerfileContent, StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            log.info("Dockerfile 已生成并保存到: {}", outputPath);
            return true;
        } catch (IOException e) {
            log.error("生成Dockerfile失败:" + e.getMessage());
            return false;
        }
    }

    /**
     * 根据Docker文件构建镜像并推送至仓库
     *
     * @return 返回一个布尔值判断是否成功
     */
    private boolean pullToRepository(String mirrorName) {
        // 用于记录日志的taskId
        String taskId = UUID.randomUUID().toString();
        dockerUtil.setTaskId(taskId);
        String imageId = null;

        // 镜像版本
        String version;
        String finalMirrorName;
        List<MirrorLog> mirrorLogList = new ArrayList<>();
        try {
            // 使用已存在的临时目录,由于基础镜像暂未准备就绪，先使用其他模板
            // if (currentTempDir == null) {
            //    log.error("未找到生成的Dockerfile目录");
            //     return false;
            // }
            // Path dockerfilePath = currentTempDir.resolve(DOCKERNAME);

            // TODO 待基础镜像就绪删除本行，解除上面代码注释
            InputStream inputStream = null;
            org.springframework.core.io.Resource templateResource = new ClassPathResource(DOCKERNAME,
                    this.getClass().getClassLoader());
            inputStream = templateResource.getInputStream();

            // 分割名称和版本
            String[] parts = mirrorName.split(":");
            finalMirrorName = parts[0];
            version = parts[1];

            String tagName = NEXUS + finalMirrorName;
            imageId = dockerUtil.buildImage(inputStream, tagName, version);
            log.info("镜像id:{}", imageId);

            // 推送镜像
            dockerUtil.pushImage(tagName + ":" + version);

            // 获取所有日志并写入ES
            final String logName = finalMirrorName + version;
            Map<Long, String> sortedLogs = dockerUtil.getSortedLogs(taskId);
            sortedLogs.forEach((key, value) -> {
                MirrorLog mirrorLog = new MirrorLog();
                mirrorLog.setMirrorName(logName);
                mirrorLog.setLogContent(value);
                mirrorLog.setSortedCondition(key);
                mirrorLog.setTimestamp(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());
                mirrorLogList.add(mirrorLog);
            });
            return true;
        } catch (Exception e) {
            String[] errParts = mirrorName.split(":");
            finalMirrorName = errParts[0];
            version = errParts[1];
            log.error("构建镜像失败", e);
            // 即使发生异常也要保存已收集的日志
            final String errorLogName = finalMirrorName + version;

            Map<Long, String> errLogs = dockerUtil.getSortedLogs(taskId);
            errLogs.forEach((key, value) -> {
                MirrorLog mirrorLog = new MirrorLog();
                mirrorLog.setMirrorName(errorLogName);
                mirrorLog.setLogContent(value);
                mirrorLog.setSortedCondition(key);
                mirrorLog.setTimestamp(LocalDateTime.now().toInstant(ZoneOffset.UTC).toEpochMilli());
                mirrorLogList.add(mirrorLog);
            });
            // 清理镜像
            if (imageId != null) {
                try {
                    dockerUtil.removeImage(imageId);
                    log.info("已删除镜像: {}", imageId);
                } catch (Exception removeEx) {
                    log.error("清理镜像失败: {}", removeEx.getMessage(), removeEx);
                }
            }
            log.error(e.getMessage());
            return false;
        } finally {
            // 清理日志
            dockerUtil.clearTaskLogs(taskId);
            if (CollectionUtil.isNotEmpty(mirrorLogList)) {
                mirrorLogService.saveAll(mirrorLogList);
            }
        }
    }
}
