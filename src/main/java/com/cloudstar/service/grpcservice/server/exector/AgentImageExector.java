package com.cloudstar.service.grpcservice.server.exector;

import com.cloudstar.service.facade.res.ResImageHubUserService;
import com.cloudstar.service.grpc.AgentImageProto;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * agent image exector
 *
 * <AUTHOR>
 * @date 2025/03/09
 */
@Slf4j
@Component
public abstract class AgentImageExector {

    @Resource
    public ResImageHubUserService resImageHubUserService;

    /**
     * <pre>
     *
     *
     * Synchronize mirror list
     *
     *
     * </pre>
     *
     * @param request 请求对象
     * @param responseObserver 响应观测器
     */
    public abstract void synchronizeMirrorList(AgentImageProto.SyncImageDataProtoRequest request,
                                      StreamObserver<AgentImageProto.SyncImageDataProtoResponse> responseObserver);

    /**
     * <pre>
     * 删除镜像
     * </pre>
     *
     * @param request 请求对象
     * @param responseObserver 响应观测器
     */
    public abstract void deleteImage(AgentImageProto.DeleteImageRequest request,
                                     StreamObserver<AgentImageProto.DeleteImageResponse> responseObserver);

    public void init() {
        log.info("init AgentImageExector");
    }
}
