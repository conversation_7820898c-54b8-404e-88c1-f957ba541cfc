package com.cloudstar.service.grpcservice.server;

import com.cloudstar.service.grpc.AgentAlgorithmProto.PublishRequest;
import com.cloudstar.service.grpc.AgentAlgorithmProto.PublishResponse;
import com.cloudstar.service.grpc.AgentAlgorithmProto.SubscribeRequest;
import com.cloudstar.service.grpc.AgentAlgorithmProto.SubscribeResponse;
import com.cloudstar.service.grpc.AgentAlgorithmProto.UnsubscribeRequest;
import com.cloudstar.service.grpc.AgentAlgorithmProto.UnsubscribeResponse;
import com.cloudstar.service.grpc.AgentAlgorithmServiceGrpc;
import com.cloudstar.service.grpcservice.server.exector.AgentAlgorithmExector;
import com.cloudstar.service.grpcservice.server.exector.GrpcExectorFactory;

import net.devh.boot.grpc.server.service.GrpcService;

import javax.annotation.Resource;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 算法服务端
 *
 * <AUTHOR>
 * @date 2024/8/20 14:41
 */
@GrpcService
@RequiredArgsConstructor
@Slf4j
public class AgentAlgorithmServer extends AgentAlgorithmServiceGrpc.AgentAlgorithmServiceImplBase {

    @Resource
    GrpcExectorFactory<AgentAlgorithmExector> factory;

    /**
     * <pre>
     * 发布算法
     * </pre>
     */
    public void publish(PublishRequest request, io.grpc.stub.StreamObserver<PublishResponse> responseObserver) {
        AgentAlgorithmExector exector = factory.getExector(AgentAlgorithmExector.class);
        exector.publish(request, responseObserver);
    }

    /**
     * <pre>
     * 订阅算法
     * </pre>
     */
    public void subscribe(SubscribeRequest request, io.grpc.stub.StreamObserver<SubscribeResponse> responseObserver) {
        AgentAlgorithmExector exector = factory.getExector(AgentAlgorithmExector.class);
        exector.subscribe(request, responseObserver);
    }

    /**
     * 退订算法
     */
    public void unsubscribe(UnsubscribeRequest request, io.grpc.stub.StreamObserver<UnsubscribeResponse> responseObserver) {
        AgentAlgorithmExector exector = factory.getExector(AgentAlgorithmExector.class);
        exector.unsubscribe(request, responseObserver);
    }
}
