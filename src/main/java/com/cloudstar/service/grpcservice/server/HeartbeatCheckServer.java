package com.cloudstar.service.grpcservice.server;

import com.cloudstar.service.grpc.HeartbeatCheckProto.CheckHeartbeatResp;
import com.cloudstar.service.grpc.HeartbeatCheckServiceGrpc;

import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.service.GrpcService;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;


/**
 * grpc  心跳检查服务端
 *
 * <AUTHOR>
 * @date 2022/9/2 15:30
 */
@GrpcService
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class HeartbeatCheckServer extends HeartbeatCheckServiceGrpc.HeartbeatCheckServiceImplBase {


    @Override
    public void checkHeartbeat(com.cloudstar.service.grpc.HeartbeatCheckProto.CheckHeartbeatReq request,
                               io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.HeartbeatCheckProto.CheckHeartbeatResp> responseObserver) {
        CheckHeartbeatResp response = CheckHeartbeatResp.newBuilder().setStatus(true).build();
        responseObserver.onNext(response);
        log.info("接收到心跳包");
        responseObserver.onCompleted();
    }
}
