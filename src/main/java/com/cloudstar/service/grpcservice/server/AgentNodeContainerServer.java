package com.cloudstar.service.grpcservice.server;



import com.cloudstar.service.grpc.AgentNodeContainerGrpc;
import com.cloudstar.service.grpc.AgentNodeContainerProto;
import com.cloudstar.service.grpcservice.server.exector.AgentNodeContainerExector;
import com.cloudstar.service.grpcservice.server.exector.GrpcExectorFactory;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import net.devh.boot.grpc.server.service.GrpcService;

import javax.annotation.Resource;

@GrpcService
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AgentNodeContainerServer extends AgentNodeContainerGrpc.AgentNodeContainerImplBase {

    @Resource
    GrpcExectorFactory<AgentNodeContainerExector> factory;

    /**
     * 获取节点容器列表
     */
    public void queryNodeContainer(AgentNodeContainerProto.QueryNodeContainerReq req,
                                   io.grpc.stub.StreamObserver<AgentNodeContainerProto.QueryNodeContainerResp> responseObserver) {
        factory.getExector(AgentNodeContainerExector.class).queryNodeContainer(req, responseObserver);
    }

}
