package com.cloudstar.service.grpcservice.server.exector;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.ConfigService;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.enums.NodeLabelKeyEnum;
import com.cloudstar.common.base.enums.NodePurposeEnum;
import com.cloudstar.common.base.enums.TrainingJobStatusEnum;
import com.cloudstar.config.AgentSysEnvConfig;
import com.cloudstar.dao.mapper.cluster.AgentEngineMapper;
import com.cloudstar.dao.mapper.cluster.AgentFlavorMapper;
import com.cloudstar.dao.mapper.training.AgentJobsMapper;
import com.cloudstar.dao.model.cluster.AgentEngine;
import com.cloudstar.dao.model.cluster.AgentFlavor;
import com.cloudstar.dao.model.training.AgentJobs;
import com.cloudstar.enums.VolcanoSchedulerEnum;
import com.cloudstar.k8s.pojo.vo.requestvo.CreatePvcReq;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.Job;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.Policies;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.Affinity;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.ImagePullSecrets;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.Volumes;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.affinity.NodeAffinity;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.affinity.nodeaffinity.RequiredDuringSchedulingIgnoredDuringExecution;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.affinity.nodeaffinity.requiredduringschedulingignoredduringexecution.NodeSelectorTerms;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.affinity.nodeaffinity.requiredduringschedulingignoredduringexecution.nodeselectorterms.MatchExpressions;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.containers.Env;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.containers.Resources;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.containers.SecurityContext;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.containers.VolumeMounts;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.containers.env.ValueFrom;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.containers.env.valuefrom.FieldRef;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobspec.tasks.template.spec.volumes.PersistentVolumeClaim;
import com.cloudstar.k8s.service.facade.PersistentVolumeClaimService;
import com.cloudstar.k8s.service.facade.VolcanoJobResourceService;
import com.cloudstar.service.grpc.AgentTrainingJobCallBackProto;
import com.cloudstar.service.grpc.AgentTrainingJobProto;
import com.cloudstar.service.grpcservice.facade.AgentTrainingJobCallBackService;
import com.cloudstar.utils.K8sResourceUtil;
import com.cloudstar.utils.VolcanoJobCommandUtil;

import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.extern.slf4j.Slf4j;

/**
 * agent training job exector
 */
@Component
@Slf4j
public abstract class AgentTrainingJobExector {

    @Resource
    private VolcanoJobResourceService volcanoJobResourceService;

    @Resource
    private AgentJobsMapper agentJobsMapper;

    @Resource
    private AgentFlavorMapper agentFlavorMapper;

    @Resource
    private AgentEngineMapper agentEngineMapper;

    @Resource
    private AgentTrainingJobCallBackService agentTrainingJobCallBackService;

    @Resource
    private ConfigService configService;

    @Resource
    private PersistentVolumeClaimService persistentVolumeClaimService;

    private static final String AI_MARKET_BUCKET = "aimarket-bucket";

    /**
     * 创建训练作业
     *
     * @param request req
     * @param responseObserver resp
     */
    public void addTrainingJob(AgentTrainingJobProto.AddTrainingJobRequest request,
                               io.grpc.stub.StreamObserver<AgentTrainingJobProto.AddTrainingJobResponse> responseObserver) {
        log.info("k8s-agent add trainingJob :{}", JSONUtil.toJsonStr(request));
        String status = "true";
        AgentTrainingJobCallBackProto.TrainingJobCallBackRequest.Builder builder
                = AgentTrainingJobCallBackProto.TrainingJobCallBackRequest.newBuilder();
        AgentJobs jobs = new AgentJobs();
        boolean isCreatePvc = false;
        try {
            final boolean existCrd = volcanoJobResourceService.isExistJob(request.getUserId(), request.getMetadata().getName());
            if (existCrd) {
                log.error("作业创建失败，作业名称已存在:{}", request.getMetadata().getName());
                status = "false";
                AgentTrainingJobProto.AddTrainingJobResponse response
                        = AgentTrainingJobProto.AddTrainingJobResponse.newBuilder().setStatus(status).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            InputStream inputStream = null;
            org.springframework.core.io.Resource templateResource = new ClassPathResource("k8s/volcano-job-demo.yaml",
                                                                                          this.getClass().getClassLoader());
            inputStream = templateResource.getInputStream();
            Yaml yaml = new Yaml();
            final Job job = yaml.loadAs(inputStream, Job.class);
            job.getMetadata().setName(request.getMetadata().getName());

            //预置框架
            if (ObjectUtil.isNotEmpty(request.getEngine())
                    && ObjectUtil.isNotEmpty(request.getEngine().getEngineName())) {
                String engine = request.getEngine().getEngineName();
                final AgentEngine agentEngine = agentEngineMapper.selectOne(
                        new LambdaQueryWrapper<AgentEngine>().eq(AgentEngine::getEngineId, engine));
                if (ObjectUtil.isEmpty(agentEngine)) {
                    status = "false";
                    log.error("创建作业失败，未找到对应引擎");
                    AgentTrainingJobProto.AddTrainingJobResponse response
                            = AgentTrainingJobProto.AddTrainingJobResponse.newBuilder().setStatus(status).build();
                    responseObserver.onNext(response);
                    responseObserver.onCompleted();
                    return;
                }
                job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setImage(agentEngine.getEngineId());
            } else {
                final AgentTrainingJobProto.dataResource inputImage = request.getInputImage();
                if (ObjectUtil.isNotEmpty(inputImage)) {
                    job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setImage(inputImage.getName());
                } else {
                    status = "false";
                    log.error("创建作业失败，自定义镜像未找到");
                    AgentTrainingJobProto.AddTrainingJobResponse response
                            = AgentTrainingJobProto.AddTrainingJobResponse.newBuilder().setStatus(status).build();
                    responseObserver.onNext(response);
                    responseObserver.onCompleted();
                    return;
                }
            }
            ImagePullSecrets secret = new ImagePullSecrets();
            secret.setName(request.getUserId() + "-image-secret");
            ArrayList<ImagePullSecrets> imagePullSecrets = new ArrayList<>();
            imagePullSecrets.add(secret);
            job.getSpec().getTasks().get(0).getTemplate().getSpec().setImagePullSecrets(imagePullSecrets);
            //设置队列名称
            if (ObjectUtil.isEmpty(request.getSpec().getPoolId())) {
                status = "false";
                log.error("创建作业失败，未找到对应的资源池");
                AgentTrainingJobProto.AddTrainingJobResponse response
                        = AgentTrainingJobProto.AddTrainingJobResponse.newBuilder().setStatus(status).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            job.getSpec().setQueue(request.getSpec().getPoolId());
            job.getSpec().setSchedulerName(VolcanoSchedulerEnum.TRAINING_SCHEDULER.getSchedulerName());
            final AgentTrainingJobProto.BmsExtraParam bmsParam = request.getSpec().getBmsParam();
            //设置节点选择器
            if (ObjectUtil.isEmpty(bmsParam) || ObjectUtil.isEmpty(bmsParam.getComputeProductName())
                    || ObjectUtil.isEmpty(bmsParam.getNodePurpose())) {
                status = "false";
                log.error("创建作业失败，未找到调度节点");
                AgentTrainingJobProto.AddTrainingJobResponse response
                        = AgentTrainingJobProto.AddTrainingJobResponse.newBuilder().setStatus(status).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            //设置计算卡型号
            Map<String, String> nodeMap = new HashMap<>();
            nodeMap.put(NodeLabelKeyEnum.COMPUTE.getCode(), bmsParam.getComputeProductName());
            //是否虚拟化
            if (bmsParam.getIsVirtualized()) {
                nodeMap.put(NodeLabelKeyEnum.VIRTUALIZED.getCode(), "true");
            }
            //TODO 环境变量控制 判断是否强制区分节点虚拟化，强制区分，虚拟化规格只能往虚拟化节点调用，非虚拟化规格只能往非虚拟化节点调用
            if (false) {
                nodeMap.put(NodeLabelKeyEnum.VIRTUALIZED.getCode(), bmsParam.getIsVirtualized() ? "true" : "false");
            }
            job.getSpec().getTasks().get(0).getTemplate().getSpec().setNodeSelector(nodeMap);
            //设置节点用途
            final Affinity affinity = new Affinity();
            final NodeAffinity nodeAffinity = new NodeAffinity();
            final RequiredDuringSchedulingIgnoredDuringExecution requireExecution = new RequiredDuringSchedulingIgnoredDuringExecution();
            final List<NodeSelectorTerms> nodeSelectorTermsList = new ArrayList<>();
            final NodeSelectorTerms nodeSelectorTerms = new NodeSelectorTerms();
            final List<MatchExpressions> matchExpressionsList = new ArrayList<>();
            MatchExpressions matchExpressions = new MatchExpressions();
            matchExpressions.setKey(NodeLabelKeyEnum.NODEPURPOSE.getCode());
            matchExpressions.setOperator("In");
            matchExpressions.setValues(Arrays.asList(NodePurposeEnum.TRAINING.getCode(), NodePurposeEnum.ALL.getCode()));
            matchExpressionsList.add(matchExpressions);
            nodeSelectorTerms.setMatchExpressions(matchExpressionsList);
            nodeSelectorTermsList.add(nodeSelectorTerms);
            requireExecution.setNodeSelectorTerms(nodeSelectorTermsList);
            nodeAffinity.setRequiredDuringSchedulingIgnoredDuringExecution(requireExecution);
            affinity.setNodeAffinity(nodeAffinity);
            job.getSpec().getTasks().get(0).getTemplate().getSpec().setAffinity(affinity);
            String flavor = request.getSpec().getFlavorId();
            final AgentFlavor agentFlavor =
                    agentFlavorMapper.selectOne(new LambdaQueryWrapper<AgentFlavor>().eq(AgentFlavor::getFlavorId, flavor));
            if (ObjectUtil.isEmpty(agentFlavor)) {
                status = "false";
                log.error("创建作业失败，未找到对应规格");
                AgentTrainingJobProto.AddTrainingJobResponse response
                        = AgentTrainingJobProto.AddTrainingJobResponse.newBuilder().setStatus(status).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            final Resources resources = new Resources();
            HashMap spec = K8sResourceUtil.getResourceLimit(bmsParam.getIsVirtualized(), agentFlavor);
            resources.setLimits(spec);
            resources.setRequests(spec);
            job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setResources(resources);
            final String logPath = request.getSpec().getLogExportPathObj();
            final List<AgentTrainingJobProto.ParamsGroup> paramsGroupList = request.getParamsGroupList();
            ArrayList<AgentTrainingJobProto.ParamsGroup> envParams = new ArrayList<>();
            ArrayList<AgentTrainingJobProto.ParamsGroup> hyperParams = new ArrayList<>();
            paramsGroupList.stream().forEach(paramsGroup -> {
                if (paramsGroup.getType().equals("env_params")) {
                    envParams.add(paramsGroup);
                }
                if (paramsGroup.getType().equals("hyper_params")) {
                    hyperParams.add(paramsGroup);
                }
            });
            final String ak = request.getSubAccount().getAccessKey();
            final String sk = request.getSubAccount().getSecretKey();
            final String bucketName = request.getSubAccount().getBucket();
            AtomicReference<String> dataPrefixDir = new AtomicReference<>();
            request.getInputsList().stream().forEach(dataResource -> {
                if (dataResource.getName().equals("data_url")) {
                    // TODO 先注释排查一下，如果dataprefix 没有/后缀，下载不成功了
                    dataPrefixDir.set(dataResource.getObsUrl().replaceAll("^/+|/+$", ""));
                }
            });
            AtomicReference<String> uploadPrefixDir = new AtomicReference<>();
            request.getOutputsList().stream().forEach(dataResource -> {
                if (dataResource.getName().equals("train_url")) {
                    uploadPrefixDir.set(dataResource.getObsUrl().replaceAll("^/+|/+$", ""));
                }
            });
            AgentSysEnvConfig config = configService.getConfig(ConfigType.AGENT_SYS_ENV_CONFIG);
            if (ObjectUtil.isEmpty(config) || ObjectUtil.isEmpty(config.getDownloadScriptUrl())) {
                status = "false";
                log.error("创建作业失败，系统未配置下载脚本");
                AgentTrainingJobProto.AddTrainingJobResponse response
                        = AgentTrainingJobProto.AddTrainingJobResponse.newBuilder().setStatus(status).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            final String downloadScriptUrl = config.getDownloadScriptUrl().stringValue();
            final String obsEndpoint = config.getObsUrl().stringValue();
            if (ObjectUtil.isEmpty(obsEndpoint)) {
                status = "false";
                log.error("创建作业失败，系统未配置对象存储地址");
                AgentTrainingJobProto.AddTrainingJobResponse response
                        = AgentTrainingJobProto.AddTrainingJobResponse.newBuilder().setStatus(status).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
            }
            ArrayList<String> startCommandList = new ArrayList<>();
            startCommandList.add("/bin/bash");
            startCommandList.add("-c");
            StringBuilder startSb = new StringBuilder(
                    VolcanoJobCommandUtil.initEnv(downloadScriptUrl, VolcanoJobCommandUtil.createEnvParam(envParams)));
            startSb.append(VolcanoJobCommandUtil.downloadData(obsEndpoint, ak, sk, bucketName, dataPrefixDir.get()));
            //如果用户用的自定义镜像 并且自定义镜像包含了算法 就不下载算法 选择的算法为空 就不下载
            //算法 算法不为空
            String codePrefixDir = request.getAlgorithm().getCodeDir().replaceAll("^/+|/+$", "");
            if (ObjectUtil.isNotEmpty(request.getAlgorithm()) && StrUtil.isNotEmpty(request.getAlgorithm().getCodeDir())) {
                if (request.getAlgorithm().getIsSubscribe()) {
                    startSb.append(VolcanoJobCommandUtil.downloadAlgorithm(obsEndpoint, ak, sk, AI_MARKET_BUCKET, codePrefixDir,
                                                                           codePrefixDir + "/"));
                } else {
                    startSb.append(VolcanoJobCommandUtil.downloadAlgorithm(obsEndpoint, ak, sk, bucketName, codePrefixDir,
                                                                           codePrefixDir + "/"));
                }
            }
            //判断节点数是否大于1，大于1是多机多卡训练 需加上特别的参数
            final int nodeCount = request.getSpec().getNodeCount();
            job.getSpec().getTasks().get(0).setReplicas(nodeCount);
            //设置镜像启动用户为root 用户
            SecurityContext securityContext = new SecurityContext();
            securityContext.setRunAsUser(0L);
            job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setSecurityContext(securityContext);
            if (nodeCount > 1) {
                setDistributedEnv(job, nodeCount);
                startSb.append(VolcanoJobCommandUtil.distributedParam());
            }
            //如果使用的是npu训练作业 设置npu参数
            if (!"0".equals(agentFlavor.getNpuUnitNum())) {
                startSb.append(VolcanoJobCommandUtil.setNpuEnv());
            }
            log.info("deal command:{}", request.getCommand());
            if (ObjectUtil.isNotEmpty(request.getCommand())) {
                log.info("command mod : {} - {} - {}", request.getCommand(), dataPrefixDir.get(), hyperParams);
                startSb.append(VolcanoJobCommandUtil.pythonRunCmd(request.getCommand(), dataPrefixDir.get(),
                                                                  VolcanoJobCommandUtil.createHyperParam(hyperParams)));
            } else {
                startSb.append(VolcanoJobCommandUtil.pythonRun(request.getAlgorithm().getBootFile().replaceAll("^/+|/+$", ""), dataPrefixDir.get(),
                                                               VolcanoJobCommandUtil.createHyperParam(hyperParams)));
            }
            startSb.append(VolcanoJobCommandUtil.uploadModel(obsEndpoint, ak, sk, bucketName, uploadPrefixDir.get()));
            startSb.append(VolcanoJobCommandUtil.uploadLog(obsEndpoint, ak, sk, bucketName, logPath));
            //处理启动命令的文件地址格式
            startCommandList.add(startSb.toString());
            job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setName(request.getMetadata().getName());
            job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setCommand(startCommandList);
            job.getSpec().getTasks().get(0).getTemplate().getSpec().setRestartPolicy("Never");
            //创建存储 作业数据持久化，后续实现断点续训
            CreatePvcReq createPvcReq = new CreatePvcReq();
            createPvcReq.setSize(40);
            createPvcReq.setName(request.getMetadata().getName() + "-pvc");
            String accessMode = "ReadWriteMany";
            if (StrUtil.isNotEmpty(System.getenv("JOB_PVC_ACCESSMODE"))) {
                accessMode = System.getenv("JOB_PVC_ACCESSMODE");
            }
            createPvcReq.setAccessMode(accessMode);
            isCreatePvc = persistentVolumeClaimService.createPvc(request.getUserId(), createPvcReq);
            if (!isCreatePvc) {
                status = "false";
                log.error("创建作业失败，作业数据持久化失败");
                AgentTrainingJobProto.AddTrainingJobResponse response
                        = AgentTrainingJobProto.AddTrainingJobResponse.newBuilder().setStatus(status).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            Env podName = new Env();
            podName.setName("POD_NAME");
            ValueFrom valueFrom = new ValueFrom();
            FieldRef fieldRef = new FieldRef();
            fieldRef.setFieldPath("metadata.name");
            valueFrom.setFieldRef(fieldRef);
            podName.setValueFrom(valueFrom);
            final List<Env> env = job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).getEnv();
            if (CollectionUtil.isNotEmpty(env)) {
                env.add(podName);
                job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setEnv(env);
            } else {
                job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setEnv(Arrays.asList(podName));
            }
            Volumes volumes = new Volumes();
            volumes.setName("data-volume");
            PersistentVolumeClaim persistentVolumeClaim = new PersistentVolumeClaim();
            persistentVolumeClaim.setClaimName(request.getMetadata().getName() + "-pvc");
            volumes.setPersistentVolumeClaim(persistentVolumeClaim);
            final List<Volumes> oldVolumes = job.getSpec().getTasks().get(0).getTemplate().getSpec().getVolumes();
            if (CollectionUtil.isNotEmpty(oldVolumes)) {
                job.getSpec().getTasks().get(0).getTemplate().getSpec().getVolumes().add(volumes);
            } else {
                job.getSpec().getTasks().get(0).getTemplate().getSpec().setVolumes(Arrays.asList(volumes));
            }
            VolumeMounts volumeMount = new VolumeMounts();
            volumeMount.setMountPath("/home/<USER>");
            volumeMount.setName("data-volume");
            final List<VolumeMounts> volumeMountsList = job.getSpec()
                                                           .getTasks()
                                                           .get(0)
                                                           .getTemplate()
                                                           .getSpec()
                                                           .getContainers()
                                                           .get(0)
                                                           .getVolumeMounts();
            if (CollectionUtil.isNotEmpty(volumeMountsList)) {
                job.getSpec()
                   .getTasks()
                   .get(0)
                   .getTemplate()
                   .getSpec()
                   .getContainers()
                   .get(0)
                   .getVolumeMounts().add(volumeMount);
            } else {
                job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setVolumeMounts(Arrays.asList(volumeMount));
            }
            if (Objects.nonNull(request.getMetadata())
                    && CollectionUtil.isNotEmpty(request.getMetadata().getAnnotationsList())) {
                // 设置annotation
                for (AgentTrainingJobProto.annotationsOption option : request.getMetadata().getAnnotationsList()) {
                    job.getMetadata().setAdditionalProperty(option.getKey(), option.getValue());
                }

            }
            final boolean flag = volcanoJobResourceService.createJob(request.getUserId(), job);

            jobs.setCfnJobId(request.getCfnJobId());
            jobs.setClusterType(request.getFlavorInfo().getType());
            jobs.setJobType(request.getFlavorInfo().getType());
            jobs.setJobDescribe(request.getMetadata().getDescription());
            jobs.setJobName(request.getMetadata().getName());
            jobs.setCreatedAt(new Date());
            jobs.setUserId(request.getUserId());
            jobs.setUserName(request.getUserId());
            jobs.setJobPhase(TrainingJobStatusEnum.CREATING.getType());
            jobs.setJobSecPhase(TrainingJobStatusEnum.CREATING.getType());
            agentJobsMapper.insert(jobs);
        } catch (Exception e) {
            e.printStackTrace();
            status = "false";
            //创建作业失败 回写状态
            if (ObjectUtil.isNotEmpty(e.getMessage())) {
                builder.setMessage(e.getMessage());
            }
            builder.setCfnJobId(request.getCfnJobId());
            builder.setStatus(TrainingJobStatusEnum.FAILED.getType());
            builder.setCreateTime(jobs.getCreatedAt().getTime());
            //如果作业创建保存数据库失败 就删除已创建的k8s作业
            persistentVolumeClaimService.deletePvc(request.getUserId(), request.getMetadata().getName() + "-pvc");
            volcanoJobResourceService.deleteJob(request.getUserId(), request.getMetadata().getName());
            agentJobsMapper.delete(new LambdaQueryWrapper<AgentJobs>().eq(AgentJobs::getJobName, request.getMetadata().getName())
                                                                      .eq(AgentJobs::getUserId, request.getUserId()));
            agentTrainingJobCallBackService.syncJob(builder.build());
            log.error("作业创建失败，失败原因:{}", e);
        } finally {
            AgentTrainingJobProto.AddTrainingJobResponse response
                    = AgentTrainingJobProto.AddTrainingJobResponse.newBuilder().setStatus(status).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }

    }

    /**
     * 删除作业
     *
     * @param request req
     * @param responseObserver resp
     */
    public void deleteTrainingJob(AgentTrainingJobProto.JobOperateRequest request,
                                  io.grpc.stub.StreamObserver<AgentTrainingJobProto.JobOperateResponse> responseObserver) {

        Boolean status = true;
        try {
            final AgentJobs agentJobs = agentJobsMapper.selectOne(new LambdaQueryWrapper<AgentJobs>().eq(AgentJobs::getJobId, request.getJobId()));
            if (ObjectUtil.isEmpty(agentJobs)) {
                status = false;
                log.error("未找到对应作业：{}", request.getJobId());
                AgentTrainingJobProto.JobOperateResponse response = AgentTrainingJobProto.JobOperateResponse.newBuilder().setStatus(status).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            final boolean existJob = volcanoJobResourceService.isExistJob(agentJobs.getUserId(), agentJobs.getJobName());
            if (!existJob) {
                log.info("作业已删除，无需重复删除:{}", agentJobs.getJobName());
                AgentTrainingJobProto.JobOperateResponse response = AgentTrainingJobProto.JobOperateResponse.newBuilder().setStatus(status).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            persistentVolumeClaimService.deletePvc(agentJobs.getUserId(), agentJobs.getJobName() + "-pvc");
            status = volcanoJobResourceService.deleteJob(agentJobs.getUserId(), agentJobs.getJobName());
            if (status) {
                agentJobs.setJobPhase(TrainingJobStatusEnum.DELETED.getType());
                agentJobs.setJobSecPhase(TrainingJobStatusEnum.DELETED.getType());
                agentJobsMapper.updateById(agentJobs);
            }
        } catch (Exception e) {
            status = false;
            log.error("删除作业失败，失败原因：{}", e);
        } finally {
            AgentTrainingJobProto.JobOperateResponse response = AgentTrainingJobProto.JobOperateResponse.newBuilder().setStatus(status).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    /**
     * 停止作业
     *
     * @param request req
     * @param responseObserver resp
     */
    public void stopTrainingJob(AgentTrainingJobProto.JobOperateRequest request,
                                io.grpc.stub.StreamObserver<AgentTrainingJobProto.JobOperateResponse> responseObserver) {
        Boolean status = true;
        try {
            final AgentJobs agentJobs = agentJobsMapper.selectOne(new LambdaQueryWrapper<AgentJobs>().eq(AgentJobs::getJobId, request.getJobId()));
            if (ObjectUtil.isEmpty(agentJobs)) {
                status = false;
                log.error("未找到对应作业：{}", request.getJobId());
                AgentTrainingJobProto.JobOperateResponse response = AgentTrainingJobProto.JobOperateResponse.newBuilder().setStatus(status).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            final boolean existJob = volcanoJobResourceService.isExistJob(agentJobs.getUserId(), agentJobs.getJobName());
            if (!existJob) {
                log.info("作业已停止，无需重复停止:{}", agentJobs.getJobName());
                AgentTrainingJobProto.JobOperateResponse response = AgentTrainingJobProto.JobOperateResponse.newBuilder().setStatus(status).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            status = volcanoJobResourceService.deleteJob(agentJobs.getUserId(), agentJobs.getJobName());
        } catch (Exception e) {
            status = false;
            log.error("停止作业失败，失败原因：{}", e);
        } finally {
            AgentTrainingJobProto.JobOperateResponse response = AgentTrainingJobProto.JobOperateResponse.newBuilder().setStatus(status).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    /**
     * 查询作业日志
     *
     * @param request req
     * @param responseObserver resp
     */
    public void showTrainingJobLogsPreview(AgentTrainingJobProto.JobOperateRequest request,
                                           io.grpc.stub.StreamObserver<AgentTrainingJobProto.ShowTrainingJobLogsPreviewResp> responseObserver) {
        String content = "未采集到作业日志";
        try {
            final AgentJobs agentJobs =
                    agentJobsMapper.selectOne(new LambdaQueryWrapper<AgentJobs>().eq(AgentJobs::getJobId, request.getJobId()));
            if (ObjectUtil.isEmpty(agentJobs)) {
                log.error("未找到对应作业：{}", request.getJobId());
                content = "底层未找到对应作业";
                AgentTrainingJobProto.ShowTrainingJobLogsPreviewResp response =
                        AgentTrainingJobProto.ShowTrainingJobLogsPreviewResp.newBuilder().setContent(content).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            content = volcanoJobResourceService.getJobLog(agentJobs.getUserId(), agentJobs.getJobName(), request.getPodName());
        } catch (Exception e) {
            content = "未采集到作业日志";
            log.error("作业日志查询失败，失败原因:{}", e);
        } finally {
            AgentTrainingJobProto.ShowTrainingJobLogsPreviewResp response
                    = AgentTrainingJobProto.ShowTrainingJobLogsPreviewResp.newBuilder().setContent(content).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    /**
     * 获取对象存储端口
     *
     * @param url url
     */
    @SuppressFBWarnings("REDOS")
    private String getObsEndpoint(String url) {
        // 正则表达式匹配HTTP地址到端口位置
        String regex = "^(https?:\\/\\/[\\w.-]+)(:[0-9]+)?";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            String extractedPart = matcher.group(0);
            log.info("作业对象存储地址:{}", extractedPart);
            return extractedPart;
        }
        return null;
    }
    
    /**
     * 设置分布式作业环境变量
     */
    private void setDistributedEnv(Job job, Integer nodeCount) {
        final List<Env> envs = new ArrayList<>();
        Env masterPort = new Env();
        masterPort.setName("MASTER_PORT");
        masterPort.setValue("29500");
        Env worldSize = new Env();
        worldSize.setName("WORLD_SIZE");
        worldSize.setValue(nodeCount.toString());
        Env rank = new Env();
        rank.setName("RANK");
        ValueFrom valueFrom = new ValueFrom();
        FieldRef fieldRef = new FieldRef();
        fieldRef.setFieldPath("metadata.annotations['volcano.sh/task-index']");
        valueFrom.setFieldRef(fieldRef);
        rank.setValueFrom(valueFrom);
        envs.add(masterPort);
        envs.add(worldSize);
        envs.add(rank);
        final List<Env> oldEnvs = job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).getEnv();
        if (CollectionUtil.isNotEmpty(oldEnvs)) {
            oldEnvs.addAll(envs);
            job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setEnv(oldEnvs);
        } else {
            job.getSpec().getTasks().get(0).getTemplate().getSpec().getContainers().get(0).setEnv(envs);
        }
        List<Policies> policiesList = new ArrayList<>();
        Policies policies = new Policies();
        policies.setEvent("TaskCompleted");
        policies.setAction("CompleteJob");
        policiesList.add(policies);
        job.getSpec().getTasks().get(0).setPolicies(policiesList);
    }

}
