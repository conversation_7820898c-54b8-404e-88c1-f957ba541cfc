package com.cloudstar.service.grpcservice.server;

import com.cloudstar.service.grpc.AgentObsGrpc;
import com.cloudstar.service.grpc.AgentObsProto;
import com.cloudstar.service.grpc.AgentObsProto.PublishDataStorageRequest;
import com.cloudstar.service.grpc.AgentObsProto.PublishDataStorageResponse;
import com.cloudstar.service.grpc.AgentObsProto.SubscribeDataStorageRequest;
import com.cloudstar.service.grpc.AgentObsProto.SubscribeDataStorageResponse;
import com.cloudstar.service.grpc.AgentObsProto.UnsubscribeDataStorageRequest;
import com.cloudstar.service.grpc.AgentObsProto.UnsubscribeDataStorageResponse;
import com.cloudstar.service.grpcservice.server.exector.AgentObsExector;
import com.cloudstar.service.grpcservice.server.exector.GrpcExectorFactory;

import net.devh.boot.grpc.server.service.GrpcService;

import javax.annotation.Resource;

import io.grpc.stub.StreamObserver;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;


@GrpcService
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
@RequiredArgsConstructor
public class AgentObsServer extends AgentObsGrpc.AgentObsImplBase {

    @Resource
    GrpcExectorFactory<AgentObsExector> factory;

    /**
     * 判断文件是否存在
     *
     * @param request 请求参数
     * @param responseObserver 布尔值表示是否存在
     */
    @Override
    public void existsFileOrFileDir(AgentObsProto.existsFileOrFileDirRequest request,
                                    StreamObserver<AgentObsProto.existsFileOrFileDirResponse> responseObserver) {
        factory.getExector(AgentObsExector.class).existsFileOrFileDir(request, responseObserver);
    }

    /**
     * 同步数据存储
     *
     * @param request 请求参数
     * @param responseObserver 文件大小以及文件数量
     */
    public void syncDataStorage(AgentObsProto.SyncDataStorageProtoRequest request,
                                StreamObserver<AgentObsProto.SyncDataStorageProtoResponse> responseObserver) {
        factory.getExector(AgentObsExector.class).syncDataStorage(request, responseObserver);
    }


    /**
     * 创建存储桶策略。
     */
    public void createPolicy(AgentObsProto.BucketPolicyRequest request,
                             StreamObserver<AgentObsProto.BucketPolicyResponse> responseObserver) {
        factory.getExector(AgentObsExector.class).createPolicy(request, responseObserver);
    }

    /**
     * 删除存储桶策略。
     */
    public void deletePolicy(AgentObsProto.BucketPolicyRequest request,
                             StreamObserver<AgentObsProto.BucketPolicyResponse> responseObserver) {
        factory.getExector(AgentObsExector.class).deletePolicy(request, responseObserver);
    }

    /**
     * 查询存储桶策略。
     */
    public void queryPolicy(AgentObsProto.BucketPolicyRequest request,
                            StreamObserver<AgentObsProto.BucketPolicyResponse> responseObserver) {
        factory.getExector(AgentObsExector.class).queryPolicy(request, responseObserver);
    }

    /**
     * <pre>
     * 发布数据集
     * </pre>
     */
    public void publishDataStorage(PublishDataStorageRequest request,
                                   io.grpc.stub.StreamObserver<PublishDataStorageResponse> responseObserver) {
        factory.getExector(AgentObsExector.class).publishDataStorage(request, responseObserver);
    }

    /**
     * <pre>
     * 订阅数据集
     * </pre>
     */
    public void subscribeDataStorage(SubscribeDataStorageRequest request,
                                     io.grpc.stub.StreamObserver<SubscribeDataStorageResponse> responseObserver) {
        factory.getExector(AgentObsExector.class).subscribeDataStorage(request, responseObserver);
    }

    /**
     * 退订
     */
    public void unsubscribeDataStorage(UnsubscribeDataStorageRequest request,
                                       io.grpc.stub.StreamObserver<UnsubscribeDataStorageResponse> responseObserver) {
        factory.getExector(AgentObsExector.class).unsubscribeDataStorage(request, responseObserver);
    }


}