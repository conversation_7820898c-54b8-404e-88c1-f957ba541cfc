package com.cloudstar.service.grpcservice.server.exector;

import com.cloudstar.k8s.service.crd.sh.volcano.scheduling.v1beta1.Queue;
import com.cloudstar.k8s.service.facade.VolcanoQueueService;
import com.cloudstar.service.grpc.AgentQueueProto;

import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.util.Arrays;
import java.util.HashMap;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * agent Queue exector
 */
@Component
@Slf4j
public abstract class AgentQueueExector {

    @Resource
    private VolcanoQueueService volcanoQueueService;

    /**
     * <pre>
     * 创建queue
     * </pre>
     */
    public void createQueue(AgentQueueProto.QueueMessageRequest request,
                            io.grpc.stub.StreamObserver<AgentQueueProto.QueueMessageResponse> responseObserver) {
        try {
            final boolean existQueue = volcanoQueueService.isExistQueue(request.getQueueName());
            if (existQueue) {
                log.error("queue已存在:{}", request.getQueueName());
                AgentQueueProto.QueueMessageResponse response = AgentQueueProto.QueueMessageResponse.newBuilder().setStatus(false).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            InputStream inputStream = null;
            org.springframework.core.io.Resource templateResource = new ClassPathResource("k8s/volcano-queue-demo.yaml",
                                                                                          this.getClass().getClassLoader());
            inputStream = templateResource.getInputStream();
            Yaml yaml = new Yaml();
            final Queue queue = yaml.loadAs(inputStream, Queue.class);
            HashMap labels = new HashMap();
            labels.put("queueType", request.getQueueType());
            HashMap annotations = new HashMap();
            annotations.put("queueType", request.getQueueType());
            queue.getMetadata().setAnnotations(annotations);
            queue.getMetadata().setLabels(labels);
            queue.getMetadata().setName(request.getQueueName());
            queue.getSpec().getAffinity()
                 .getNodeGroupAffinity()
                 .setRequiredDuringSchedulingIgnoredDuringExecution(Arrays.asList(request.getQueueName()));
            final boolean flag = volcanoQueueService.createOrUpdateQueue(queue);
            AgentQueueProto.QueueMessageResponse response = AgentQueueProto.QueueMessageResponse.newBuilder().setStatus(flag).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("创建队列失败:{}", e);
            AgentQueueProto.QueueMessageResponse response = AgentQueueProto.QueueMessageResponse.newBuilder().setStatus(false).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    /**
     * <pre>
     * 修改queue
     * </pre>
     */
    public void updateQueue(AgentQueueProto.UpdateQueueRequest request,
                            io.grpc.stub.StreamObserver<AgentQueueProto.QueueMessageResponse> responseObserver) {
        try {
            final Queue queue = volcanoQueueService.getQueueByName(request.getOldQueueName());
            if (ObjectUtil.isEmpty(queue)) {
                log.error("queue不存在:{}", request.getOldQueueName());
                AgentQueueProto.QueueMessageResponse response = AgentQueueProto.QueueMessageResponse.newBuilder().setStatus(false).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
            }
            //先删除 在创建新的
            volcanoQueueService.deleteQueue(request.getOldQueueName());
            HashMap labels = new HashMap();
            labels.put("queueType", request.getQueueType());
            HashMap annotations = new HashMap();
            annotations.put("queueType", request.getQueueType());
            queue.getMetadata().setAnnotations(annotations);
            queue.getMetadata().setLabels(labels);
            queue.getMetadata().setName(request.getNewQueueName());
            queue.getSpec().getAffinity().getNodeGroupAffinity()
                 .setRequiredDuringSchedulingIgnoredDuringExecution(Arrays.asList(request.getNewQueueName()));
            final boolean flag = volcanoQueueService.createOrUpdateQueue(queue);
            AgentQueueProto.QueueMessageResponse response = AgentQueueProto.QueueMessageResponse.newBuilder().setStatus(flag).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("修改queue失败:{}", e);
            AgentQueueProto.QueueMessageResponse response = AgentQueueProto.QueueMessageResponse.newBuilder().setStatus(false).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }

    /**
     * <pre>
     * 删除queue
     * </pre>
     */
    public void deleteQueue(AgentQueueProto.QueueMessageRequest request,
                            io.grpc.stub.StreamObserver<AgentQueueProto.QueueMessageResponse> responseObserver) {
        try {
            final boolean existQueue = volcanoQueueService.isExistQueue(request.getQueueName());
            if (!existQueue) {
                log.error("queue不存在:{}", request.getQueueName());
                AgentQueueProto.QueueMessageResponse response = AgentQueueProto.QueueMessageResponse.newBuilder().setStatus(false).build();
                responseObserver.onNext(response);
                responseObserver.onCompleted();
                return;
            }
            final boolean flag = volcanoQueueService.deleteQueue(request.getQueueName());
            AgentQueueProto.QueueMessageResponse response = AgentQueueProto.QueueMessageResponse.newBuilder().setStatus(flag).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("创建队列失败:{}", e);
            AgentQueueProto.QueueMessageResponse response = AgentQueueProto.QueueMessageResponse.newBuilder().setStatus(false).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        }
    }
}
