package com.cloudstar.service.grpcservice.server.exector.bms;


import com.cloudstar.service.grpc.AgentImageProto;
import com.cloudstar.service.grpcservice.server.exector.AgentImageExector;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * bms agent image exector
 *
 * <AUTHOR>
 * @date 2025/03/09
 */
@Component
@Slf4j
public class BmsAgentImageExector extends AgentImageExector {
    @Override
    public void synchronizeMirrorList(AgentImageProto.SyncImageDataProtoRequest request,
                                      StreamObserver<AgentImageProto.SyncImageDataProtoResponse> responseObserver) {
        log.info("receive synchronizeMirrorList request");
        List<AgentImageProto.ImageDate> imageDates = resImageHubUserService.queryUserImage(request.getMirrorUsername());
        AgentImageProto.SyncImageDataProtoResponse.Builder response = AgentImageProto.SyncImageDataProtoResponse.newBuilder();
        response.setSuccess(true);
        response.setMessage("success");
        response.addAllDataStorageList(imageDates);
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
        log.info("send synchronizeMirrorList response");
    }

    @Override
    public void deleteImage(AgentImageProto.DeleteImageRequest request,
                            StreamObserver<AgentImageProto.DeleteImageResponse> responseObserver) {
        log.info("receive deleteImage request");
        resImageHubUserService.deleteImage(request.getImageId());
        responseObserver.onNext(AgentImageProto.DeleteImageResponse.newBuilder().setSuccess(true).setMessage("success").build());
        responseObserver.onCompleted();
        log.info("send deleteImage response");
    }
}
