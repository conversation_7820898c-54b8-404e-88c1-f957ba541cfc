package com.cloudstar.service.grpcservice.server.exector.bms;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.enums.ClusterTypeEnum;
import com.cloudstar.common.base.util.UuidUtil;
import com.cloudstar.config.ObsAdminConfig;
import com.cloudstar.dao.model.user.AgentUsers;
import com.cloudstar.dao.model.user.ResImageHubUser;
import com.cloudstar.dao.model.user.ResObsUser;
import com.cloudstar.enums.AccountValidationType;
import com.cloudstar.enums.AgentUserStatus;
import com.cloudstar.service.grpc.AgentUserProto;
import com.cloudstar.service.grpcservice.server.exector.AgentUserExector;

import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

import cn.hutool.core.util.ObjectUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;

/**
 * Bms Agent User Exector
 */
@Component
@Slf4j
public class BmsAgentUserExector extends AgentUserExector {


    /**
     * <pre>
     * 获取一个未绑定的子账户
     * </pre>
     */
    public void getIdleUser(AgentUserProto.GetIdleUserRequest request, StreamObserver<AgentUserProto.GetIdleUserResponse> responseObserver) {
        AgentUserProto.GetIdleUserResponse.Builder response = AgentUserProto.GetIdleUserResponse.newBuilder();
        AgentUsers user = getUnboundUsers(request);
        AgentUserProto.UserNameSpaceRes nameSpaceRes = null;
        AgentUserProto.UserImageRes imageRes = null;
        AgentUserProto.UserObsRes obs = null;
        //未找到未绑定的用户,则根据用户id 创建一个未绑定的子账户
        String userId = request.getUserId();
        try {
            if (Objects.nonNull(user) || request.getIsSubAccount()) {
                userId = Objects.nonNull(user) ? user.getUserId() : request.getUserId();
                fillInMirrorInformation(userId, response);
                fillInObsInformation(userId, response);
                fillInNamespaceInformation(request, response);
                createImageSecret(request, response.getImage());
                // 子用户不创建底层账号，使用主账号的信息
                String subUserId = request.getIsSubAccount() ? UuidUtil.getShortUuid() : userId;
                AgentUserProto.UserResponse userResponse = AgentUserProto.UserResponse.newBuilder()
                                                                                      .setUserId(subUserId)
                                                                                      .setUserAk(userId)
                                                                                      .setUserSk(response.getObs().getSecret())
                                                                                      .setUserType(ClusterTypeEnum.BMS.getType())
                                                                                      .setUserName(subUserId)
                                                                                      .setProjectUrl(response.getObs().getPathName())
                                                                                      .build();
                response.setUser(userResponse);
                response.setStep(AgentUserProto.GetIdleUserResponse.CreateUserSteps.GET_SUCCESS);
                responseObserver.onNext(response.build());
                responseObserver.onCompleted();
                return;
            }
            response.setUserId(userId);
            // 创建namespace
            nameSpaceRes = resNameSpaceUserService.createNameSpace(userId);
            response.setNamespace(nameSpaceRes);
            //创建仓库账号
            imageRes = resImageHubUserService.createRepositoryUser(userId);
            response.setImage(imageRes);
            //创建minio账号
            obs = resObsUserService.createMinioUser(userId);
            response.setObs(obs);

            AgentUserProto.UserResponse userResponse = AgentUserProto.UserResponse.newBuilder()
                                                                                  .setUserId(userId)
                                                                                  .setUserAk(userId)
                                                                                  .setUserSk(obs.getSecret())
                                                                                  .setUserType(ClusterTypeEnum.BMS.getType())
                                                                                  .setUserName(obs.getBucketName())
                                                                                  .setProjectUrl(obs.getPathName())
                                                                                  .build();
            response.setUser(userResponse);
            //保存可以绑定的用户
            saveUserToBeBound(userId, obs);
            createImageSecret(request, imageRes);
            response.setStep(AgentUserProto.GetIdleUserResponse.CreateUserSteps.GET_SUCCESS);
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            //不为空证明创建成功了账号 需要删除账号
            if (ObjectUtil.isNotEmpty(nameSpaceRes)) {
                resNameSpaceUserService.deleteNameSpace(nameSpaceRes.getNamespace());
            }
            if (ObjectUtil.isNotEmpty(imageRes)) {
                resImageHubUserService.deleteRepositoryUser(userId);
            }
            if (ObjectUtil.isNotEmpty(obs)) {
                resObsUserService.deleteUser(userId);
            }
            response.setStep(AgentUserProto.GetIdleUserResponse.CreateUserSteps.GET_IDLE_USER);
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
            return;
        }
        response.setStep(AgentUserProto.GetIdleUserResponse.CreateUserSteps.GET_SUCCESS);
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    /**
     * 填写镜像信息
     *
     * @param userId 用户id
     * @param response 回答
     */
    private void fillInMirrorInformation(String userId, AgentUserProto.GetIdleUserResponse.Builder response) {
        ResImageHubUser resImageHubUser = resImageHubUserService.getBaseMapper().selectOne(new LambdaQueryWrapper<ResImageHubUser>()
                                                                                                   .eq(ResImageHubUser::getUsername, userId));
        if (Objects.nonNull(resImageHubUser)) {
            AgentUserProto.UserImageRes imageRes = AgentUserProto.UserImageRes.newBuilder()
                                                                              .setAccount(userId)
                                                                              .setSecret(resImageHubUser.getPassword())
                                                                              .build();
            response.setImage(imageRes);
        } else {
            AgentUserProto.UserImageRes imageRes = resImageHubUserService.createRepositoryUser(userId);
            response.setImage(imageRes);
        }
    }

    /**
     * 填写obs信息
     *
     * @param userId 用户id
     * @param response 回答
     */
    private void fillInObsInformation(String userId, AgentUserProto.GetIdleUserResponse.Builder response) {
        ResObsUser resObsUser = resObsUserService.getBaseMapper().selectOne(new LambdaQueryWrapper<ResObsUser>()
                                                                                    .eq(ResObsUser::getUsername, userId));
        if (Objects.nonNull(resObsUser)) {
            ObsAdminConfig config = configService.getConfig(ConfigType.OBS_ADMIN_CONFIG);
            AgentUserProto.UserObsRes obsRes = AgentUserProto.UserObsRes.newBuilder()
                                                                        .setAccount(userId)
                                                                        .setSecret(resObsUser.getPassword())
                                                                        .setBucketName(userId)
                                                                        .setPathName(config.getObsUrl().stringValue()).build();
            response.setObs(obsRes);
        } else {
            AgentUserProto.UserObsRes obs = resObsUserService.createMinioUser(userId);
            response.setObs(obs);
        }
    }

    /**
     * 保存可以绑定的用户
     *
     * @param userId 用户id
     * @param obs obs
     */
    private void saveUserToBeBound(String userId, AgentUserProto.UserObsRes obs) {
        AgentUsers agentUsers = new AgentUsers();
        agentUsers.setUserType(ClusterTypeEnum.BMS.getType());
        agentUsers.setUserId(userId);
        agentUsers.setUserName(userId);
        agentUsers.setObsId(obs.getBucketName());
        agentUsers.setValidType(AccountValidationType.AKSK.getValue());
        agentUsers.setUserPassword(obs.getSecret());
        agentUsers.setUserAk(userId);
        agentUsers.setUserSk(obs.getSecret());
        agentUsers.setStatus(AgentUserStatus.UNBOUND.getCodeEn());
        Date updatedDt = new Date();
        agentUsers.setCreatedDt(updatedDt);
        agentUsers.setUpdatedDt(updatedDt);
        agentUsers.setDomainId(userId);
        agentUsersMapper.insert(agentUsers);
    }
}
