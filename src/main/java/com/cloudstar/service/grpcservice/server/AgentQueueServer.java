package com.cloudstar.service.grpcservice.server;

import com.cloudstar.service.grpc.AgentQueueGrpc;
import com.cloudstar.service.grpc.AgentQueueProto.QueueMessageRequest;
import com.cloudstar.service.grpc.AgentQueueProto.QueueMessageResponse;
import com.cloudstar.service.grpc.AgentQueueProto.UpdateQueueRequest;
import com.cloudstar.service.grpcservice.server.exector.AgentQueueExector;
import com.cloudstar.service.grpcservice.server.exector.GrpcExectorFactory;

import net.devh.boot.grpc.server.service.GrpcService;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

/**
 * queue server
 *
 * <AUTHOR>
 * @date 2024/12/12 15:15
 */
@GrpcService
@Slf4j
public class AgentQueueServer extends AgentQueueGrpc.AgentQueueImplBase {

    @Resource
    GrpcExectorFactory<AgentQueueExector> factory;

    /**
     * <pre>
     * 创建queue
     * </pre>
     */
    public void createQueue(QueueMessageRequest request,
                            io.grpc.stub.StreamObserver<QueueMessageResponse> responseObserver) {
        factory.getExector(AgentQueueExector.class).createQueue(request, responseObserver);
    }

    /**
     * <pre>
     * 修改queue
     * </pre>
     */
    public void updateQueue(UpdateQueueRequest request,
                            io.grpc.stub.StreamObserver<QueueMessageResponse> responseObserver) {
        factory.getExector(AgentQueueExector.class).updateQueue(request, responseObserver);
    }

    /**
     * <pre>
     * 删除queue
     * </pre>
     */
    public void deleteQueue(QueueMessageRequest request,
                            io.grpc.stub.StreamObserver<QueueMessageResponse> responseObserver) {
        factory.getExector(AgentQueueExector.class).deleteQueue(request, responseObserver);
    }
}
