package com.cloudstar.service.grpcservice.server.exector;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.dao.mapper.user.AgentUsersMapper;
import com.cloudstar.dao.model.user.AgentUsers;
import com.cloudstar.k8s.pojo.resource.BizContainerStatus;
import com.cloudstar.k8s.pojo.resource.BizPod;
import com.cloudstar.k8s.service.facade.PodService;
import com.cloudstar.service.grpc.AgentNodeContainerProto;
import io.fabric8.kubernetes.api.model.PodList;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public abstract class AgentNodeContainerExector {

    @Resource
    public PodService podService;

    @Resource
    private KubernetesClient kubernetesClient;

    @Resource
    private AgentUsersMapper agentUsersMapper;


    /**
     * 查询节点容器信息
     *
     * @param req              查询请求参数
     * @param responseObserver 响应观察者
     */
    public void queryNodeContainer(AgentNodeContainerProto.QueryNodeContainerReq req,
                                   StreamObserver<AgentNodeContainerProto.QueryNodeContainerResp> responseObserver) {
        log.info("开始查询节点容器信息,请求参数:{}", req);
        String nodeName = req.getNodeName();
        Map<String, List<BizPod>> stringListMap = podService.listAllRuningPodGroupByNodeName();
        // 只获取指定节点的容器信息
        List<BizPod> bizPods = stringListMap.get(nodeName);
        log.info("节点容器信息：{}", bizPods);
        if (ObjectUtil.isEmpty(bizPods)) {
            log.warn("未查询到节点容器信息,节点名称:{}", nodeName);
            AgentNodeContainerProto.QueryNodeContainerResp response = AgentNodeContainerProto.QueryNodeContainerResp
                    .newBuilder()
                    .build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
            return;
        }
        // 获取过滤掉系统组件的容器信息
        PodList podList = getRunningPodListOnNode(nodeName);
        log.info("查询到的PodList:{}", podList);
        // 得到nameSpace为key user_name为value的map
        Map<String, String> nameSpaceUserMap = podList.getItems().stream()
                .collect(Collectors.toMap(
                        pod -> pod.getMetadata().getNamespace(),
                        pod -> {
                            LambdaQueryWrapper<AgentUsers> queryWrapper = new LambdaQueryWrapper<>();
                            queryWrapper.eq(AgentUsers::getUserId, pod.getMetadata().getNamespace());
                            AgentUsers agentUsers = agentUsersMapper.selectOne(queryWrapper);
                            return ObjectUtil.isNotEmpty(agentUsers) ? agentUsers.getUserName() : "unknown";
                        },
                        (existing, replacement) -> existing));
        log.info("nameSpaceUserMap:{}", nameSpaceUserMap);
        List<AgentNodeContainerProto.QueryNodeContainerList.Builder> list = bizPods.stream().map(pod -> {
            AgentNodeContainerProto.QueryNodeContainerList.Builder builder = AgentNodeContainerProto
                    .QueryNodeContainerList
                    .newBuilder();

            String account = nameSpaceUserMap.getOrDefault(pod.getNamespace(), "unknown");
            builder.setUid(getContainerId(pod.getContainerStatuses()))
                    .setName(pod.getName())
                    .setNameSpace(pod.getNamespace())
                    .setRestartCount(getRestartCount(pod.getContainerStatuses()))
                    .setAge(getAge(pod))
                    .setAccount(account)
                    .setPodIp(pod.getPodIp());
            return builder;
        }).collect(Collectors.toList());
        log.info("list信息:{}", list);
        log.info("查询节点容器信息成功,节点名称:{},容器数量:{}", nodeName, list.size());
        List<AgentNodeContainerProto.QueryNodeContainerList> builtList = list.stream()
                .map(AgentNodeContainerProto.QueryNodeContainerList.Builder::build)
                .collect(Collectors.toList());
        log.info("构建的节点容器信息列表:{}", builtList);
        AgentNodeContainerProto.QueryNodeContainerResp response = AgentNodeContainerProto.QueryNodeContainerResp
                .newBuilder()
                .addAllList(builtList)
                .build();
        log.info("查询节点容器信息响应:{}", response);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
        log.info("结束查询节点容器信息");
    }

    public String getContainerId(List<BizContainerStatus> containerStatuses) {
        if (containerStatuses != null && !containerStatuses.isEmpty()) {
            String containerId = containerStatuses.get(0).getContainerID();
            if (containerId != null && containerId.startsWith("containerd://")) {
                return containerId.substring("containerd://".length());
            }
            return containerId;
        }
        return null;
    }

    public String getRestartCount(List<BizContainerStatus> containerStatuses) {
        if (containerStatuses != null && !containerStatuses.isEmpty()) {
            return String.valueOf(containerStatuses.get(0).getRestartCount());
        }
        return null;
    }

    private String getAge(BizPod pod) {
        String creationTimestamp = pod.getCreationTimestamp();
        Instant creation = Instant.parse(creationTimestamp);
        Instant now = Instant.now();
        long days = ChronoUnit.DAYS.between(creation, now);
        return days + "d";
    }

    private PodList getRunningPodListOnNode(String nodeName) {
        PodList podList = kubernetesClient.pods()
                .inAnyNamespace() // 跨命名空间查询
                .withNewFilter()
                .withField("spec.nodeName", nodeName)
                .withField("status.phase", "Running")
                .withoutField("metadata.namespace", "kube-system")
                .withoutField("metadata.namespace", "cadvisor")
                .withoutField("metadata.namespace", "calico-system")
                .withoutField("metadata.namespace", "calico-apiserver")
                .withoutField("metadata.namespace", "cert-manager")
                .withoutField("metadata.namespace", "higress-system")
                .withoutField("metadata.namespace", "istio-ingress")
                .withoutField("metadata.namespace", "istio-system")
                .withoutField("metadata.namespace", "kube-public")
                .withoutField("metadata.namespace", "kubernetes-dashboard")
                .withoutField("metadata.namespace", "local-path-storage")
                .withoutField("metadata.namespace", "notebook-controller-system")
                .withoutField("metadata.namespace", "tensorboard-controller-system")
                .withoutField("metadata.namespace", "volcano-monitoring")
                .withoutField("metadata.namespace", "volcano-system")
                .withoutField("metadata.namespace", "rook-ceph")
                .withoutField("metadata.namespace", "default")
                .withoutField("metadata.namespace", "inference-controller-system")
                .endFilter()
                .list();
        return podList;
    }
}
