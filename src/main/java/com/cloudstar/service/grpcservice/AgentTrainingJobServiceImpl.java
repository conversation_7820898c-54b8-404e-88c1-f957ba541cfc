package com.cloudstar.service.grpcservice;

import com.cloudstar.common.base.constant.RedisCacheKeyEnum;
import com.cloudstar.common.base.enums.ClusterTypeEnum;
import com.cloudstar.common.base.enums.NoteBookStatusEnum;
import com.cloudstar.common.base.enums.TrainingJobStatusEnum;
import com.cloudstar.common.base.enums.TrainingJobTypeEnum;
import com.cloudstar.common.component.redis.util.RedisUtil;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.training.TrainingJobEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.training.TrainingJobEntity;
import com.cloudstar.module.job.executor.JobExecutor;
import com.cloudstar.module.job.executor.factory.JobExecutorFactory;
import com.cloudstar.sdk.schedule.pojo.TrainingJobLogResp;
import com.cloudstar.service.grpc.AgentTrainingJobGrpc;
import com.cloudstar.service.grpc.AgentTrainingJobProto.AddTrainingJobRequest.Builder;
import com.cloudstar.service.grpc.AgentTrainingJobProto.AddTrainingJobResponse;
import com.cloudstar.service.grpc.AgentTrainingJobProto.JobOperateRequest;
import com.cloudstar.service.grpc.AgentTrainingJobProto.JobOperateResponse;
import com.cloudstar.service.grpc.AgentTrainingJobProto.ShowTrainingJobLogsPreviewResp;
import com.cloudstar.service.grpc.GrpcManage;
import com.cloudstar.service.grpcservice.facade.AgentTrainingJobService;
import com.cloudstar.service.pojo.dto.trainingjob.ExecuteJobEntity;
import com.cloudstar.service.pojo.dto.trainingjob.TrainingJobMsgDto;
import com.cloudstar.service.utils.TrainingJobMsgUtil;

import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import io.grpc.stub.AbstractStub;
import lombok.extern.slf4j.Slf4j;

/**
 * 代理训练作业服务impl
 *
 * <AUTHOR> Created on 2022/8/30
 * @date 2022/08/31
 */
@Service
@Slf4j
public class AgentTrainingJobServiceImpl implements AgentTrainingJobService {

    private AgentTrainingJobGrpc.AgentTrainingJobBlockingStub blockingStub;

    private TrainingJobEntityMapper trainingJobEntityMapper;

    private ClusterEntityMapper clusterEntityMapper;

    private RedisUtil redisUtil;
    private Executor taskExecutor;

    private TrainingJobMsgUtil trainingJobMsgUtil;

    private JobExecutorFactory jobExecutorFactory;

    @Override
    public void build(String targetServer, Class<? extends AbstractStub<?>> clazz) {
        blockingStub = (AgentTrainingJobGrpc.AgentTrainingJobBlockingStub) GrpcManage.getStub(targetServer, clazz);
        trainingJobEntityMapper = SpringUtil.getBean(TrainingJobEntityMapper.class);
        clusterEntityMapper = SpringUtil.getBean(ClusterEntityMapper.class);
        redisUtil = SpringUtil.getBean(RedisUtil.class);
        trainingJobMsgUtil = SpringUtil.getBean(TrainingJobMsgUtil.class);
        taskExecutor = SpringUtil.getBean("threadPool", Executor.class);
        jobExecutorFactory = SpringUtil.getBean(JobExecutorFactory.class);
    }

    @Override
    public void addTrainingJob(TrainingJobEntity trainingJob) {

        ExecuteJobEntity executeJob = before(trainingJob);
        JobExecutor jobExecutor = jobExecutorFactory.getJobExecutor(executeJob.getClusterEntity().getClusterType());
        Builder builder = jobExecutor.execute(executeJob);

        if (builder == null) {
            return;
        }
        try {
            CompletableFuture.supplyAsync(() -> {
                log.info("异步调度agent开始。。。");
                log.info("addTrainingJob-创建作业,下发grpc作业参数【{}】", StrUtil.toString(builder));
                AddTrainingJobResponse addTrainingJobResponse = blockingStub.addTrainingJob(
                        builder.build());
                if (Objects.isNull(addTrainingJobResponse) || ObjectUtil.equals(addTrainingJobResponse.getStatus(), false)) {
                    trainingJob.setStatus(NoteBookStatusEnum.FAILED.getType());
                    trainingJob.setUpdatedDt(new Date());
                    trainingJobEntityMapper.updateById(trainingJob);
                }
                return null;
            }, taskExecutor);
        } catch (Exception e) {
            jobExecutor.updateErrorJob(trainingJob, "适配器通信异常，作业调度失败！");
            log.error("适配器通信异常，作业调度失败:{}", e.getMessage());
        }
    }

    @Override
    public void stopTrainJob(TrainingJobEntity trainingJob) {
        StringBuilder cacheKey = new StringBuilder(RedisCacheKeyEnum.TRAINING_JOB_STATUS.getKey());
        cacheKey.append("::").append(trainingJob.getId());
        String msg = null;
        try {
            ClusterEntity clusterEntity = clusterEntityMapper.selectById(trainingJob.getClusterId());
            JobOperateRequest.Builder requestBuilder = JobOperateRequest.newBuilder().setJobId(trainingJob.getJobId());
            // slurm 集群的作业，需要增加参数才能停止
            if (ClusterTypeEnum.SLURM.getType().equals(clusterEntity.getClusterType())) {
                requestBuilder.setExecutionType("Suspend");
            }
            JobOperateResponse response = blockingStub.stopTrainingJob(requestBuilder.build());

            log.info("作业[{}]停止操作响应值[{}]", trainingJob.getId(), response.getStatus());
            //停止成功
            if (response.getStatus()) {
                log.info("作业[{}]停止成功", trainingJob.getId());
                msg = "停止成功";
                trainingJob.setErrorMsg("");
                trainingJob.setStatus(TrainingJobStatusEnum.TERMINATED.getType());
            } else {
                //获取老的状态
                String status = redisUtil.get(cacheKey.toString());
                trainingJob.setStatus(status);
                trainingJob.setErrorMsg("底层算力中心停止失败！");
                msg = "停止失败";
                log.info("作业[{}]停止失败", trainingJob.getId());
            }
        } catch (Exception e) {
            //获取老的状态
            String status = redisUtil.get(cacheKey.toString());
            trainingJob.setStatus(status);
            msg = "停止失败";
            trainingJob.setErrorMsg("适配器通信异常，停止失败！");
            log.error("作业[{}]停止失败，失败原因：【{}】", trainingJob.getId(), e.getMessage());
        }
        trainingJobEntityMapper.updateById(trainingJob);
        redisUtil.delete(cacheKey.toString());
        //协同作业无需发送消息
        if (TrainingJobTypeEnum.COORDINATION.getType().equals(trainingJob.getJobType())) {
            return;
        }
        TrainingJobMsgDto dto = new TrainingJobMsgDto();
        dto.setJobId(trainingJob.getId().toString());
        dto.setJobName(trainingJob.getName());
        dto.setJobStatus(msg);
        dto.setSendAccount(trainingJob.getCreatedBy());
        dto.setSendUserSid(trainingJob.getUserSid());
        trainingJobMsgUtil.sendMsg(dto);
    }

    @Override
    public void deleteTrainJob(TrainingJobEntity trainingJob) {
        StringBuilder cacheKey = new StringBuilder(RedisCacheKeyEnum.TRAINING_JOB_STATUS.getKey());
        cacheKey.append("::").append(trainingJob.getId());
        String msg = null;
        try {
            JobOperateResponse response = blockingStub.deleteTrainingJob(
                    JobOperateRequest.newBuilder().setJobId(trainingJob.getJobId()).build());
            log.info("作业[{}]删除操作响应值[{}]", trainingJob.getId(), response.getStatus());
            //删除成功
            if (response.getStatus()) {
                log.info("作业[{}]删除成功", trainingJob.getId());
                msg = "删除成功";
                trainingJob.setErrorMsg("");
                trainingJob.setStatus(TrainingJobStatusEnum.DELETED.getType());
            } else {
                //获取老的状态
                String status = redisUtil.get(cacheKey.toString());
                trainingJob.setStatus(status);
                trainingJob.setErrorMsg("底层算力中心删除失败！");
                msg = "删除失败";
                log.info("作业[{}]删除失败", trainingJob.getId());
            }
        } catch (Exception e) {
            //获取老的状态
            String status = redisUtil.get(cacheKey.toString());
            trainingJob.setErrorMsg("适配器通信异常，删除失败！");
            trainingJob.setStatus(status);
            msg = "删除失败";
            log.error("作业[{}]删除失败，失败原因：【{}】", trainingJob.getId(), e.getMessage());
        }
        trainingJobEntityMapper.updateById(trainingJob);
        redisUtil.delete(cacheKey.toString());
        TrainingJobMsgDto dto = new TrainingJobMsgDto();
        dto.setJobId(trainingJob.getId().toString());
        dto.setJobName(trainingJob.getName());
        dto.setJobStatus(msg);
        dto.setSendAccount(trainingJob.getCreatedBy());
        dto.setSendUserSid(trainingJob.getUserSid());
        trainingJobMsgUtil.sendMsg(dto);
    }

    @Override
    public TrainingJobLogResp queryTrainingJobLog(TrainingJobEntity trainingJob, String podName) {
        TrainingJobLogResp result = new TrainingJobLogResp();
        if (ObjectUtil.isEmpty(trainingJob) || ObjectUtil.isEmpty(trainingJob.getJobId())) {
            result.setContent("无法获取作业日志内容");
            result.setCurrentSize(10);
            result.setFullSize(10);
        } else {
            final JobOperateRequest.Builder builder = JobOperateRequest.newBuilder().setJobId(trainingJob.getJobId());
            if (ObjectUtil.isNotEmpty(podName)) {
                builder.setPodName(podName);
            }
            ShowTrainingJobLogsPreviewResp resp = blockingStub.showTrainingJobLogsPreview(builder.build());
            if (ObjectUtil.isNotEmpty(resp)) {
                result.setContent(resp.getContent());
                result.setCurrentSize(resp.getCurrentSize());
                result.setFullSize(resp.getFullSize());
            }
        }
        return result;
    }

    /**
     * 下发作业前置方法
     *
     * @param trainingJob 培训工作
     *
     * @return {@link ExecuteJobEntity}
     */
    private ExecuteJobEntity before(TrainingJobEntity trainingJob) {
        ClusterEntity clusterEntity = clusterEntityMapper.selectById(trainingJob.getClusterId());
        return ExecuteJobEntity.builder().trainingJobEntity(trainingJob).clusterEntity(clusterEntity).build();
    }
}
