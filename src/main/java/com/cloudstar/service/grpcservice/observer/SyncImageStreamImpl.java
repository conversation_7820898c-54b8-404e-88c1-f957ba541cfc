package com.cloudstar.service.grpcservice.observer;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.common.base.enums.MirrorAccessType;
import com.cloudstar.common.base.enums.MirrorUseType;
import com.cloudstar.common.base.pojo.mq.MirrorEventMessage;
import com.cloudstar.componet.ClusterAttrUtil;
import com.cloudstar.dao.mapper.res.ResImageMapper;
import com.cloudstar.dao.model.res.ResImage;
import com.cloudstar.service.grpc.AgentImageProto.ImageDate;
import com.cloudstar.service.grpc.AgentImageProto.SyncImageDataProtoResponse;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 同步镜像列表实现类
 *
 * <AUTHOR>
 * @date 2024/07/05
 */
@Slf4j
public class SyncImageStreamImpl implements StreamObserver<SyncImageDataProtoResponse> {

    public static final String STRING = ":";

    public static final String DOCKER_PULL = "docker pull ";


    private static ResImageMapper resImageMapper;

    final List<ImageDate> imageDates = new ArrayList<>();

    private final MirrorEventMessage entity;

    private final Long clusterId;

    private static ClusterAttrUtil clusterAttrUtil;

    public SyncImageStreamImpl(ResImageMapper resImageMapper, MirrorEventMessage entity, Long clusterId,
                               ClusterAttrUtil clusterAttrUtil) {
        SyncImageStreamImpl.resImageMapper = resImageMapper;
        SyncImageStreamImpl.clusterAttrUtil = clusterAttrUtil;
        this.entity = entity;
        this.clusterId = clusterId;
    }

    /**
     * 下一个
     *
     * @param syncImageDataProtoResponse 同步图像数据原型响应
     */
    @Override
    public void onNext(SyncImageDataProtoResponse syncImageDataProtoResponse) {
        boolean success = syncImageDataProtoResponse.getSuccess();
        if (!success) {
            log.error("同步镜像结果失败: {}", syncImageDataProtoResponse.getMessage());
            return;
        }
        List<ImageDate> storageListList = syncImageDataProtoResponse.getDataStorageListList();
        imageDates.addAll(storageListList);
    }

    /**
     * 出现错误时
     *
     * @param throwable 可丢弃
     */
    @Override
    public void onError(Throwable throwable) {
        log.error("同步镜像列表异常失败: {}", throwable.getMessage());
    }

    /**
     * 在完成
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onCompleted() {
        log.info("同步镜像列表成功, 共{}个镜像", imageDates.size());

        // 查询旧的镜像列表
        List<ResImage> oldList =
                Optional.ofNullable(resImageMapper.selectList(
                                new LambdaQueryWrapper<ResImage>()
                                        .eq(ResImage::getUseType, MirrorUseType.VERSION.getEnName())
                                        .eq(ResImage::getOwnerId, entity.getUserSid())))
                        .orElse(new ArrayList<>());

        // 使用哈希表存储旧列表和新列表中的元素
        Map<String, ResImage> oldMap = oldList.stream().collect(Collectors.toMap(ResImage::getUuid, Function.identity()));
        Map<String, ImageDate> newMap = imageDates.stream().collect(Collectors.toMap(ImageDate::getId, Function.identity()));

        List<ResImage> updatedList = new ArrayList<>();
        List<ResImage> deletedList = new ArrayList<>();
        List<ResImage> addedList = new ArrayList<>();
        Map<String, ResImage> imageMap = new HashMap<>();

        // 检查新增和更新
        handleAdditionsAndUpdates(imageDates, oldMap, updatedList, addedList, imageMap);

        // 检查删除
        handleDeletions(oldList, newMap, deletedList, imageMap);

        // 输出结果
        log.info("更新的元素: {}, 删除的元素: {}, 新增的元素: {}",
                 updatedList.size(), deletedList.size(), addedList.size());

        // 更新镜像
        updateImages(updatedList);

        // 删除镜像
        deleteImages(deletedList);

        // 新增镜像
        addImages(addedList);

        // 维护镜像信息
        maintainImageInformation(imageMap);
    }

    /**
     * 处理新增和更新的镜像
     */
    private void handleAdditionsAndUpdates(List<ImageDate> imageDates, Map<String, ResImage> oldMap,
                                           List<ResImage> updatedList, List<ResImage> addedList,
                                           Map<String, ResImage> imageMap) {
        for (ImageDate newImage : imageDates) {
            ResImage oldImage = oldMap.get(newImage.getId());
            double size = newImage.getSize();
            if (size > 0.0) {
                resImageMapper.updateZipCapacityById(newImage.getId(), size);
            }
            if (oldImage != null) {
                // 如果镜像名称发生变化，则更新旧镜像的信息
                if (!Objects.equals(oldImage.getName(), newImage.getName())) {
                    updateOldImage(oldImage, newImage);
                    updatedList.add(oldImage);
                }
            } else {
                // 创建新的镜像信息
                ResImage newResImage = createNewResImage(newImage);
                addedList.add(newResImage);
                imageMap.put(newImage.getName(), newResImage);
            }
        }
    }


    /**
     * 处理删除的镜像
     */
    private void handleDeletions(List<ResImage> oldList, Map<String, ImageDate> newMap,
                                 List<ResImage> deletedList, Map<String, ResImage> imageMap) {
        for (ResImage oldImage : oldList) {
            if (!newMap.containsKey(oldImage.getUuid())) {
                deletedList.add(oldImage);
                imageMap.put(oldImage.getName(), oldImage);
            }
        }
    }

    /**
     * 更新镜像列表
     *
     * @param updatedList 更新列表
     */
    private void updateImages(List<ResImage> updatedList) {
        updatedList.forEach(resImageMapper::updateById);
    }

    /**
     * 删除镜像列表
     *
     * @param deletedList 已删除列表
     */
    private void deleteImages(List<ResImage> deletedList) {
        if (CollUtil.isNotEmpty(deletedList)) {
            resImageMapper.deleteBatchIds(deletedList.stream().map(ResImage::getId).collect(Collectors.toList()));
        }
    }

    /**
     * 新增镜像列表
     *
     * @param addedList 添加列表
     */
    private void addImages(List<ResImage> addedList) {
        if (CollUtil.isNotEmpty(addedList)) {
            resImageMapper.batchInsert(addedList);
        }
    }

    /**
     * 维护镜像信息
     *
     * @param imageMap 图像地图
     */
    private void maintainImageInformation(Map<String, ResImage> imageMap) {
        Map<String, ResImage> resImageMap = new HashMap<>();
        Map<String, Long> imageGroup =
                Optional.ofNullable(resImageMapper.selectList(
                                new LambdaQueryWrapper<ResImage>()
                                        .in(CollUtil.isNotEmpty(imageMap), ResImage::getName, imageMap.keySet())
                                        .eq(ResImage::getOwnerId, entity.getUserSid())))
                        .orElse(new ArrayList<>()).stream()
                        .map(resImage -> {
                            if (resImage.getUseType().equals(MirrorUseType.IMAGE.getEnName())) {
                                resImageMap.put(resImage.getName(), resImage);
                                return null;
                            }
                            return resImage;
                        })
                        .filter(Objects::nonNull)
                        .collect(Collectors.groupingBy(ResImage::getName, Collectors.counting()));
        imageMap.clear();
        int oldSize = resImageMap.size();
        imageGroup.forEach((k, v) -> {
            ResImage resImage = resImageMap.get(k);
            if (Objects.isNull(resImage)) {
                ResImage newResImage = new ResImage();
                newResImage.setCreatedBy(String.valueOf(entity.getUserSid()));
                newResImage.setCreatedDt(new Date());
                newResImage.setUpdatedBy(String.valueOf(entity.getUserSid()));
                newResImage.setUpdatedDt(new Date());
                newResImage.setOwnerId(entity.getUserSid());
                newResImage.setUseType(MirrorUseType.IMAGE.getEnName());
                newResImage.setName(k);
                newResImage.setVersion(String.valueOf(v));
                newResImage.setAccessType(clusterAttrUtil.getPublicSwrAccount(clusterId).equals(entity.getMirrorUsersName())
                                                  ? MirrorAccessType.PUBLIC.getEnName()
                                                  : MirrorAccessType.PRIVATE.getEnName());
                imageMap.put(k, newResImage);
            } else {
                resImage.setVersion(String.valueOf(v));
                resImage.setUpdatedBy(String.valueOf(entity.getUserSid()));
                resImage.setUpdatedDt(new Date());
                resImageMap.remove(k);
                resImageMapper.updateById(resImage);
            }
        });

        log.info("维护镜像信息结束, 共{}个新增镜像, {}个更新镜像, {}个删除镜像",
                 resImageMap.size(), oldSize - resImageMap.size(), resImageMap.size());
        // 删除多余的镜像信息
        if (CollUtil.isNotEmpty(resImageMap.values())) {
            resImageMapper.delete(new LambdaQueryWrapper<ResImage>()
                                          .eq(ResImage::getUseType, MirrorUseType.IMAGE.getEnName())
                                          .eq(ResImage::getOwnerId, entity.getUserSid())
                                          .in(ResImage::getName, resImageMap.keySet()));
        }

        // 保存新的镜像信息
        if (CollUtil.isNotEmpty(imageMap.values())) {
            resImageMapper.batchInsert(new ArrayList<>(imageMap.values()));
        }
    }

    /**
     * 更新旧的镜像信息
     *
     * @param oldImage 旧图像
     * @param newImage 新图像
     */
    private void updateOldImage(ResImage oldImage, ImageDate newImage) {
        oldImage.setName(newImage.getName());
        String tag = builderTag(newImage);
        oldImage.setTag(tag);
        oldImage.setPath(DOCKER_PULL + tag);
        oldImage.setZipCapacity(newImage.getSize());
        oldImage.setDigest(String.valueOf(newImage.hashCode()));
        oldImage.setVersion(newImage.getVersion());
        oldImage.setUpdatedDt(new Date());
    }

    /**
     * 创建新的镜像信息
     *
     * @param newImage 新图像
     *
     * @return {@link ResImage }
     */
    private ResImage createNewResImage(ImageDate newImage) {
        ResImage newResImage = new ResImage();
        newResImage.setUseType(MirrorUseType.VERSION.getEnName());
        String tag = builderTag(newImage);
        newResImage.setTag(tag);
        newResImage.setPath(DOCKER_PULL + tag);
        newResImage.setDigest(String.valueOf(newImage.hashCode()));
        newResImage.setVersion(newImage.getVersion());
        newResImage.setCreatedBy(String.valueOf(entity.getUserSid()));
        newResImage.setCreatedDt(new Date());
        newResImage.setUpdatedBy(String.valueOf(entity.getUserSid()));
        newResImage.setUpdatedDt(new Date());
        newResImage.setUuid(newImage.getId());
        newResImage.setName(newImage.getName());
        newResImage.setOwnerId(entity.getUserSid());
        newResImage.setClusterId(clusterId);
        newResImage.setZipCapacity(newImage.getSize());
        newResImage.setAccessType(clusterAttrUtil.getPublicSwrAccount(clusterId).equals(entity.getMirrorUsersName())
                                          ? MirrorAccessType.PUBLIC.getEnName()
                                          : MirrorAccessType.PRIVATE.getEnName());
        return newResImage;
    }

    /**
     * 构造tag
     *
     * @param newImage 新图像
     *
     * @return {@link String }
     */
    private String builderTag(ImageDate newImage) {
        return clusterAttrUtil.getSwrUrl(clusterId) + "/" + entity.getMirrorUsersName() + "/" + newImage.getName()
                + STRING + newImage.getVersion();
    }
}
