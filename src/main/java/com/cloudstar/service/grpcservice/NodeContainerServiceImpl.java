package com.cloudstar.service.grpcservice;

import com.cloudstar.service.grpc.AgentNodeContainerGrpc;
import com.cloudstar.service.grpc.AgentNodeContainerProto;
import com.cloudstar.service.grpc.GrpcManage;
import com.cloudstar.service.grpcservice.facade.NodeContainerService;
import io.grpc.stub.AbstractStub;

public class NodeContainerServiceImpl implements NodeContainerService {

    private AgentNodeContainerGrpc.AgentNodeContainerBlockingStub blockingStub;
    @Override
    public void build(String targetServer, Class<? extends AbstractStub<?>> clazz) {
        blockingStub = AgentNodeContainerGrpc.newBlockingStub(GrpcManage.getChannel(targetServer));
    }

    @Override
    public AgentNodeContainerProto.QueryNodeContainerResp queryNodeContainer(AgentNodeContainerProto.QueryNodeContainerReq request) {
        try {
            return blockingStub.queryNodeContainer(request);
        } catch (Exception e) {
            return AgentNodeContainerProto.QueryNodeContainerResp.newBuilder()
                    .build();
        }
    }
}
