package com.cloudstar.service.grpc;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.51.0)",
    comments = "Source: agent_Inference_Watch.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class AgentInferenceWatchGrpc {

  private AgentInferenceWatchGrpc() {}

  public static final String SERVICE_NAME = "protocol.AgentInferenceWatch";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest,
      com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse> getWatchInferenceMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "WatchInference",
      requestType = com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest.class,
      responseType = com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest,
      com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse> getWatchInferenceMethod() {
    io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest, com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse> getWatchInferenceMethod;
    if ((getWatchInferenceMethod = AgentInferenceWatchGrpc.getWatchInferenceMethod) == null) {
      synchronized (AgentInferenceWatchGrpc.class) {
        if ((getWatchInferenceMethod = AgentInferenceWatchGrpc.getWatchInferenceMethod) == null) {
          AgentInferenceWatchGrpc.getWatchInferenceMethod = getWatchInferenceMethod =
              io.grpc.MethodDescriptor.<com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest, com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "WatchInference"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse.getDefaultInstance()))
              .setSchemaDescriptor(new AgentInferenceWatchMethodDescriptorSupplier("WatchInference"))
              .build();
        }
      }
    }
    return getWatchInferenceMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static AgentInferenceWatchStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<AgentInferenceWatchStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<AgentInferenceWatchStub>() {
        @java.lang.Override
        public AgentInferenceWatchStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new AgentInferenceWatchStub(channel, callOptions);
        }
      };
    return AgentInferenceWatchStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static AgentInferenceWatchBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<AgentInferenceWatchBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<AgentInferenceWatchBlockingStub>() {
        @java.lang.Override
        public AgentInferenceWatchBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new AgentInferenceWatchBlockingStub(channel, callOptions);
        }
      };
    return AgentInferenceWatchBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static AgentInferenceWatchFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<AgentInferenceWatchFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<AgentInferenceWatchFutureStub>() {
        @java.lang.Override
        public AgentInferenceWatchFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new AgentInferenceWatchFutureStub(channel, callOptions);
        }
      };
    return AgentInferenceWatchFutureStub.newStub(factory, channel);
  }

  /**
   */
  public static abstract class AgentInferenceWatchImplBase implements io.grpc.BindableService {

    /**
     */
    public void watchInference(com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest request,
        io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getWatchInferenceMethod(), responseObserver);
    }

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getWatchInferenceMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest,
                com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse>(
                  this, METHODID_WATCH_INFERENCE)))
          .build();
    }
  }

  /**
   */
  public static final class AgentInferenceWatchStub extends io.grpc.stub.AbstractAsyncStub<AgentInferenceWatchStub> {
    private AgentInferenceWatchStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected AgentInferenceWatchStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new AgentInferenceWatchStub(channel, callOptions);
    }

    /**
     */
    public void watchInference(com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest request,
        io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getWatchInferenceMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   */
  public static final class AgentInferenceWatchBlockingStub extends io.grpc.stub.AbstractBlockingStub<AgentInferenceWatchBlockingStub> {
    private AgentInferenceWatchBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected AgentInferenceWatchBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new AgentInferenceWatchBlockingStub(channel, callOptions);
    }

    /**
     */
    public com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse watchInference(com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getWatchInferenceMethod(), getCallOptions(), request);
    }
  }

  /**
   */
  public static final class AgentInferenceWatchFutureStub extends io.grpc.stub.AbstractFutureStub<AgentInferenceWatchFutureStub> {
    private AgentInferenceWatchFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected AgentInferenceWatchFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new AgentInferenceWatchFutureStub(channel, callOptions);
    }

    /**
     */
    public com.google.common.util.concurrent.ListenableFuture<com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse> watchInference(
        com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getWatchInferenceMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_WATCH_INFERENCE = 0;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final AgentInferenceWatchImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(AgentInferenceWatchImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_WATCH_INFERENCE:
          serviceImpl.watchInference((com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceRequest) request,
              (io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentInferenceWatchProto.InferenceResponse>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class AgentInferenceWatchBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    AgentInferenceWatchBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.cloudstar.service.grpc.AgentInferenceWatchProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("AgentInferenceWatch");
    }
  }

  private static final class AgentInferenceWatchFileDescriptorSupplier
      extends AgentInferenceWatchBaseDescriptorSupplier {
    AgentInferenceWatchFileDescriptorSupplier() {}
  }

  private static final class AgentInferenceWatchMethodDescriptorSupplier
      extends AgentInferenceWatchBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    AgentInferenceWatchMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (AgentInferenceWatchGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new AgentInferenceWatchFileDescriptorSupplier())
              .addMethod(getWatchInferenceMethod())
              .build();
        }
      }
    }
    return result;
  }
}
