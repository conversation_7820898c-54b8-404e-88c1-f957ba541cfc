package com.cloudstar.service.grpc;

import static io.grpc.MethodDescriptor.generateFullMethodName;

/**
 */
@javax.annotation.Generated(
    value = "by gRPC proto compiler (version 1.51.0)",
    comments = "Source: agent_bill_collect.proto")
@io.grpc.stub.annotations.GrpcGenerated
public final class GetAgentBillGRPCServiceGrpc {

  private GetAgentBillGRPCServiceGrpc() {}

  public static final String SERVICE_NAME = "protocol.GetAgentBillGRPCService";

  // Static method descriptors that strictly reflect the proto.
  private static volatile io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillRequest,
      com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse> getGetAgentBillGRPCServiceMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "GetAgentBillGRPCService",
      requestType = com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillRequest.class,
      responseType = com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
  public static io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillRequest,
      com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse> getGetAgentBillGRPCServiceMethod() {
    io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillRequest, com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse> getGetAgentBillGRPCServiceMethod;
    if ((getGetAgentBillGRPCServiceMethod = GetAgentBillGRPCServiceGrpc.getGetAgentBillGRPCServiceMethod) == null) {
      synchronized (GetAgentBillGRPCServiceGrpc.class) {
        if ((getGetAgentBillGRPCServiceMethod = GetAgentBillGRPCServiceGrpc.getGetAgentBillGRPCServiceMethod) == null) {
          GetAgentBillGRPCServiceGrpc.getGetAgentBillGRPCServiceMethod = getGetAgentBillGRPCServiceMethod =
              io.grpc.MethodDescriptor.<com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillRequest, com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.SERVER_STREAMING)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "GetAgentBillGRPCService"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse.getDefaultInstance()))
              .setSchemaDescriptor(new GetAgentBillGRPCServiceMethodDescriptorSupplier("GetAgentBillGRPCService"))
              .build();
        }
      }
    }
    return getGetAgentBillGRPCServiceMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest,
      com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse> getGetLatelyBillMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "getLatelyBill",
      requestType = com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest.class,
      responseType = com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest,
      com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse> getGetLatelyBillMethod() {
    io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest, com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse> getGetLatelyBillMethod;
    if ((getGetLatelyBillMethod = GetAgentBillGRPCServiceGrpc.getGetLatelyBillMethod) == null) {
      synchronized (GetAgentBillGRPCServiceGrpc.class) {
        if ((getGetLatelyBillMethod = GetAgentBillGRPCServiceGrpc.getGetLatelyBillMethod) == null) {
          GetAgentBillGRPCServiceGrpc.getGetLatelyBillMethod = getGetLatelyBillMethod =
              io.grpc.MethodDescriptor.<com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest, com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "getLatelyBill"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse.getDefaultInstance()))
              .setSchemaDescriptor(new GetAgentBillGRPCServiceMethodDescriptorSupplier("getLatelyBill"))
              .build();
        }
      }
    }
    return getGetLatelyBillMethod;
  }

  private static volatile io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq,
      com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp> getBmsBillCollectMethod;

  @io.grpc.stub.annotations.RpcMethod(
      fullMethodName = SERVICE_NAME + '/' + "bmsBillCollect",
      requestType = com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq.class,
      responseType = com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp.class,
      methodType = io.grpc.MethodDescriptor.MethodType.UNARY)
  public static io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq,
      com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp> getBmsBillCollectMethod() {
    io.grpc.MethodDescriptor<com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq, com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp> getBmsBillCollectMethod;
    if ((getBmsBillCollectMethod = GetAgentBillGRPCServiceGrpc.getBmsBillCollectMethod) == null) {
      synchronized (GetAgentBillGRPCServiceGrpc.class) {
        if ((getBmsBillCollectMethod = GetAgentBillGRPCServiceGrpc.getBmsBillCollectMethod) == null) {
          GetAgentBillGRPCServiceGrpc.getBmsBillCollectMethod = getBmsBillCollectMethod =
              io.grpc.MethodDescriptor.<com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq, com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp>newBuilder()
              .setType(io.grpc.MethodDescriptor.MethodType.UNARY)
              .setFullMethodName(generateFullMethodName(SERVICE_NAME, "bmsBillCollect"))
              .setSampledToLocalTracing(true)
              .setRequestMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq.getDefaultInstance()))
              .setResponseMarshaller(io.grpc.protobuf.ProtoUtils.marshaller(
                  com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp.getDefaultInstance()))
              .setSchemaDescriptor(new GetAgentBillGRPCServiceMethodDescriptorSupplier("bmsBillCollect"))
              .build();
        }
      }
    }
    return getBmsBillCollectMethod;
  }

  /**
   * Creates a new async stub that supports all call types for the service
   */
  public static GetAgentBillGRPCServiceStub newStub(io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<GetAgentBillGRPCServiceStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<GetAgentBillGRPCServiceStub>() {
        @java.lang.Override
        public GetAgentBillGRPCServiceStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new GetAgentBillGRPCServiceStub(channel, callOptions);
        }
      };
    return GetAgentBillGRPCServiceStub.newStub(factory, channel);
  }

  /**
   * Creates a new blocking-style stub that supports unary and streaming output calls on the service
   */
  public static GetAgentBillGRPCServiceBlockingStub newBlockingStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<GetAgentBillGRPCServiceBlockingStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<GetAgentBillGRPCServiceBlockingStub>() {
        @java.lang.Override
        public GetAgentBillGRPCServiceBlockingStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new GetAgentBillGRPCServiceBlockingStub(channel, callOptions);
        }
      };
    return GetAgentBillGRPCServiceBlockingStub.newStub(factory, channel);
  }

  /**
   * Creates a new ListenableFuture-style stub that supports unary calls on the service
   */
  public static GetAgentBillGRPCServiceFutureStub newFutureStub(
      io.grpc.Channel channel) {
    io.grpc.stub.AbstractStub.StubFactory<GetAgentBillGRPCServiceFutureStub> factory =
      new io.grpc.stub.AbstractStub.StubFactory<GetAgentBillGRPCServiceFutureStub>() {
        @java.lang.Override
        public GetAgentBillGRPCServiceFutureStub newStub(io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
          return new GetAgentBillGRPCServiceFutureStub(channel, callOptions);
        }
      };
    return GetAgentBillGRPCServiceFutureStub.newStub(factory, channel);
  }

  /**
   */
  public static abstract class GetAgentBillGRPCServiceImplBase implements io.grpc.BindableService {

    /**
     * <pre>
     * Deprecated: GetAgentBillGRPCService 话单采集
     * </pre>
     */
    public void getAgentBillGRPCService(com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillRequest request,
        io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetAgentBillGRPCServiceMethod(), responseObserver);
    }

    /**
     * <pre>
     *获取最近时间的话单文件
     * </pre>
     */
    public void getLatelyBill(com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest request,
        io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getGetLatelyBillMethod(), responseObserver);
    }

    /**
     * <pre>
     *裸金属话单采集
     * </pre>
     */
    public void bmsBillCollect(com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq request,
        io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp> responseObserver) {
      io.grpc.stub.ServerCalls.asyncUnimplementedUnaryCall(getBmsBillCollectMethod(), responseObserver);
    }

    @java.lang.Override public final io.grpc.ServerServiceDefinition bindService() {
      return io.grpc.ServerServiceDefinition.builder(getServiceDescriptor())
          .addMethod(
            getGetAgentBillGRPCServiceMethod(),
            io.grpc.stub.ServerCalls.asyncServerStreamingCall(
              new MethodHandlers<
                com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillRequest,
                com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse>(
                  this, METHODID_GET_AGENT_BILL_GRPCSERVICE)))
          .addMethod(
            getGetLatelyBillMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest,
                com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse>(
                  this, METHODID_GET_LATELY_BILL)))
          .addMethod(
            getBmsBillCollectMethod(),
            io.grpc.stub.ServerCalls.asyncUnaryCall(
              new MethodHandlers<
                com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq,
                com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp>(
                  this, METHODID_BMS_BILL_COLLECT)))
          .build();
    }
  }

  /**
   */
  public static final class GetAgentBillGRPCServiceStub extends io.grpc.stub.AbstractAsyncStub<GetAgentBillGRPCServiceStub> {
    private GetAgentBillGRPCServiceStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected GetAgentBillGRPCServiceStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new GetAgentBillGRPCServiceStub(channel, callOptions);
    }

    /**
     * <pre>
     * Deprecated: GetAgentBillGRPCService 话单采集
     * </pre>
     */
    public void getAgentBillGRPCService(com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillRequest request,
        io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncServerStreamingCall(
          getChannel().newCall(getGetAgentBillGRPCServiceMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     *获取最近时间的话单文件
     * </pre>
     */
    public void getLatelyBill(com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest request,
        io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getGetLatelyBillMethod(), getCallOptions()), request, responseObserver);
    }

    /**
     * <pre>
     *裸金属话单采集
     * </pre>
     */
    public void bmsBillCollect(com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq request,
        io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp> responseObserver) {
      io.grpc.stub.ClientCalls.asyncUnaryCall(
          getChannel().newCall(getBmsBillCollectMethod(), getCallOptions()), request, responseObserver);
    }
  }

  /**
   */
  public static final class GetAgentBillGRPCServiceBlockingStub extends io.grpc.stub.AbstractBlockingStub<GetAgentBillGRPCServiceBlockingStub> {
    private GetAgentBillGRPCServiceBlockingStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected GetAgentBillGRPCServiceBlockingStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new GetAgentBillGRPCServiceBlockingStub(channel, callOptions);
    }

    /**
     * <pre>
     * Deprecated: GetAgentBillGRPCService 话单采集
     * </pre>
     */
    public java.util.Iterator<com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse> getAgentBillGRPCService(
        com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillRequest request) {
      return io.grpc.stub.ClientCalls.blockingServerStreamingCall(
          getChannel(), getGetAgentBillGRPCServiceMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     *获取最近时间的话单文件
     * </pre>
     */
    public com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse getLatelyBill(com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getGetLatelyBillMethod(), getCallOptions(), request);
    }

    /**
     * <pre>
     *裸金属话单采集
     * </pre>
     */
    public com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp bmsBillCollect(com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq request) {
      return io.grpc.stub.ClientCalls.blockingUnaryCall(
          getChannel(), getBmsBillCollectMethod(), getCallOptions(), request);
    }
  }

  /**
   */
  public static final class GetAgentBillGRPCServiceFutureStub extends io.grpc.stub.AbstractFutureStub<GetAgentBillGRPCServiceFutureStub> {
    private GetAgentBillGRPCServiceFutureStub(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      super(channel, callOptions);
    }

    @java.lang.Override
    protected GetAgentBillGRPCServiceFutureStub build(
        io.grpc.Channel channel, io.grpc.CallOptions callOptions) {
      return new GetAgentBillGRPCServiceFutureStub(channel, callOptions);
    }

    /**
     * <pre>
     *获取最近时间的话单文件
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse> getLatelyBill(
        com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getGetLatelyBillMethod(), getCallOptions()), request);
    }

    /**
     * <pre>
     *裸金属话单采集
     * </pre>
     */
    public com.google.common.util.concurrent.ListenableFuture<com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp> bmsBillCollect(
        com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq request) {
      return io.grpc.stub.ClientCalls.futureUnaryCall(
          getChannel().newCall(getBmsBillCollectMethod(), getCallOptions()), request);
    }
  }

  private static final int METHODID_GET_AGENT_BILL_GRPCSERVICE = 0;
  private static final int METHODID_GET_LATELY_BILL = 1;
  private static final int METHODID_BMS_BILL_COLLECT = 2;

  private static final class MethodHandlers<Req, Resp> implements
      io.grpc.stub.ServerCalls.UnaryMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ServerStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.ClientStreamingMethod<Req, Resp>,
      io.grpc.stub.ServerCalls.BidiStreamingMethod<Req, Resp> {
    private final GetAgentBillGRPCServiceImplBase serviceImpl;
    private final int methodId;

    MethodHandlers(GetAgentBillGRPCServiceImplBase serviceImpl, int methodId) {
      this.serviceImpl = serviceImpl;
      this.methodId = methodId;
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public void invoke(Req request, io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        case METHODID_GET_AGENT_BILL_GRPCSERVICE:
          serviceImpl.getAgentBillGRPCService((com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillRequest) request,
              (io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse>) responseObserver);
          break;
        case METHODID_GET_LATELY_BILL:
          serviceImpl.getLatelyBill((com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillRequest) request,
              (io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentBillCollectProto.getLatelyBillResponse>) responseObserver);
          break;
        case METHODID_BMS_BILL_COLLECT:
          serviceImpl.bmsBillCollect((com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectReq) request,
              (io.grpc.stub.StreamObserver<com.cloudstar.service.grpc.AgentBillCollectProto.BmsBillCollectResp>) responseObserver);
          break;
        default:
          throw new AssertionError();
      }
    }

    @java.lang.Override
    @java.lang.SuppressWarnings("unchecked")
    public io.grpc.stub.StreamObserver<Req> invoke(
        io.grpc.stub.StreamObserver<Resp> responseObserver) {
      switch (methodId) {
        default:
          throw new AssertionError();
      }
    }
  }

  private static abstract class GetAgentBillGRPCServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoFileDescriptorSupplier, io.grpc.protobuf.ProtoServiceDescriptorSupplier {
    GetAgentBillGRPCServiceBaseDescriptorSupplier() {}

    @java.lang.Override
    public com.google.protobuf.Descriptors.FileDescriptor getFileDescriptor() {
      return com.cloudstar.service.grpc.AgentBillCollectProto.getDescriptor();
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.ServiceDescriptor getServiceDescriptor() {
      return getFileDescriptor().findServiceByName("GetAgentBillGRPCService");
    }
  }

  private static final class GetAgentBillGRPCServiceFileDescriptorSupplier
      extends GetAgentBillGRPCServiceBaseDescriptorSupplier {
    GetAgentBillGRPCServiceFileDescriptorSupplier() {}
  }

  private static final class GetAgentBillGRPCServiceMethodDescriptorSupplier
      extends GetAgentBillGRPCServiceBaseDescriptorSupplier
      implements io.grpc.protobuf.ProtoMethodDescriptorSupplier {
    private final String methodName;

    GetAgentBillGRPCServiceMethodDescriptorSupplier(String methodName) {
      this.methodName = methodName;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.MethodDescriptor getMethodDescriptor() {
      return getServiceDescriptor().findMethodByName(methodName);
    }
  }

  private static volatile io.grpc.ServiceDescriptor serviceDescriptor;

  public static io.grpc.ServiceDescriptor getServiceDescriptor() {
    io.grpc.ServiceDescriptor result = serviceDescriptor;
    if (result == null) {
      synchronized (GetAgentBillGRPCServiceGrpc.class) {
        result = serviceDescriptor;
        if (result == null) {
          serviceDescriptor = result = io.grpc.ServiceDescriptor.newBuilder(SERVICE_NAME)
              .setSchemaDescriptor(new GetAgentBillGRPCServiceFileDescriptorSupplier())
              .addMethod(getGetAgentBillGRPCServiceMethod())
              .addMethod(getGetLatelyBillMethod())
              .addMethod(getBmsBillCollectMethod())
              .build();
        }
      }
    }
    return result;
  }
}
