package com.cloudstar.service.impl.bigscreen;

import com.cloudstar.ConfigService;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.enums.TrainingJobGroupStatusEnum;
import com.cloudstar.common.base.enums.TrainingJobStatusEnum;
import com.cloudstar.config.SynergyBigScreenConfig;
import com.cloudstar.dao.mapper.bigscreen.SynergyBigScreenMapper;
import com.cloudstar.service.facade.bigscreen.SynergyBigScreenService;
import com.cloudstar.service.pojo.dto.bigscreen.ModelArtsResourceDto;
import com.cloudstar.service.pojo.dto.bigscreen.StatusTotalDto;
import com.cloudstar.service.pojo.dto.bigscreen.SynergyClusterDto;
import com.cloudstar.service.pojo.dto.bigscreen.TrainingJobGroupDto;
import com.cloudstar.service.pojo.dto.bigscreen.TrainingJobPoolDto;
import com.cloudstar.service.pojo.dto.bigscreen.TrainingJobTotalDto;
import com.cloudstar.service.pojo.vo.responsevo.bigscreen.StatusTotalResp;
import com.cloudstar.service.pojo.vo.responsevo.bigscreen.TrainingJobPoolResp;
import com.cloudstar.service.pojo.vo.responsevo.bigscreen.TrendResp;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 协同大屏impl
 *
 * <AUTHOR>
 * @date 2022/11/12 15:31
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public class SynergyBigScreenServiceImpl implements SynergyBigScreenService {

    SynergyBigScreenMapper synergyBigScreenMapper;

    ConfigService configService;


    @Override
    public List<SynergyClusterDto> getSynergyCluster() {
        SynergyBigScreenConfig config = configService.getConfig(ConfigType.SYNERGY_BIG_SCREEN_CONFIG);
        List<String> clusterNameList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(config) && ObjectUtil.isNotEmpty(config.getClusterName())) {
            clusterNameList = (List<String>) config.getClusterName().listValue();
        }
        List<SynergyClusterDto> resp = new ArrayList<>();
        List<SynergyClusterDto> synergyCluster = synergyBigScreenMapper.getSynergyCluster();
        clusterNameList.forEach(clusterName -> {
            SynergyClusterDto dto = new SynergyClusterDto();
            dto.setIsInput(false);
            dto.setIsOutput(false);
            dto.setClusterName(clusterName);
            dto.setIsAccess(false);
            synergyCluster.stream().forEach(synergyClusterDto -> {
                if (synergyClusterDto.getClusterName().equals(clusterName)) {
                    dto.setIsOutput(synergyClusterDto.getIsOutput());
                    dto.setIsInput(synergyClusterDto.getIsInput());
                    dto.setIsAccess(true);
                }
            });
            resp.add(dto);
        });
        List<Integer> indexList = new ArrayList<>();
        //获得接入集群的数组下标
        for (int i = 0; i < resp.size(); i++) {
            if (resp.get(i).getIsAccess()) {
                indexList.add(i);
            }
        }
        if (indexList.size() > 0) {
            Integer factor = resp.size() / indexList.size();
            // 均匀散列已接入的集群数据
            for (int i = 0; i < indexList.size(); i++) {
                Integer index = factor * i;
                if (index >= resp.size()) {
                    Collections.swap(resp, resp.size() - 1, indexList.get(i));
                } else {
                    Collections.swap(resp, factor * i, indexList.get(i));
                }

            }
        }
        return resp;
    }

    @Override
    public TrendResp getTrainingJobGroupTrend(String type) {
        TrendResp resp = new TrendResp();
        String endTime = DateUtil.today();
        String startTime = DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -6), DatePattern.NORM_DATE_FORMAT);
        if ("simple".equals(type)) {
            resp.setCompleted(synergyBigScreenMapper.getTrainingJobGroupTrend(startTime, endTime, "completed"));
            resp.setTotal(synergyBigScreenMapper.getTrainingJobGroupTrend(startTime, endTime, "total"));
        } else {
            resp.setCompleted(synergyBigScreenMapper.getTrainingJobGroupTrend(startTime, endTime, "completed"));
            resp.setFailed(synergyBigScreenMapper.getTrainingJobGroupTrend(startTime, endTime, "failed"));
            resp.setOther(synergyBigScreenMapper.getTrainingJobGroupTrend(startTime, endTime, "notCompletedAndFailed"));
            resp.setTotal(synergyBigScreenMapper.getTrainingJobGroupTrend(startTime, endTime, "total"));
        }
        return resp;
    }

    @Override
    public TrendResp getTrainingJobTrend(String type) {
        TrendResp resp = new TrendResp();
        String endTime = DateUtil.today();
        String startTime = DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -6), DatePattern.NORM_DATE_FORMAT);
        if ("simple".equals(type)) {
            resp.setCompleted(synergyBigScreenMapper.getTrainingJobTrend(startTime, endTime, "completed"));
            resp.setTotal(synergyBigScreenMapper.getTrainingJobTrend(startTime, endTime, "total"));
        } else {
            resp.setCompleted(synergyBigScreenMapper.getTrainingJobTrend(startTime, endTime, "completed"));
            resp.setFailed(synergyBigScreenMapper.getTrainingJobTrend(startTime, endTime, "failed"));
            resp.setOther(synergyBigScreenMapper.getTrainingJobTrend(startTime, endTime, "notCompletedAndFailed"));
            resp.setTotal(synergyBigScreenMapper.getTrainingJobTrend(startTime, endTime, "total"));
        }
        return resp;
    }

    @Override
    public StatusTotalResp getTrainingJobGroupStatusTotal() {
        List<StatusTotalDto> list = synergyBigScreenMapper.getTrainingJobGroupStatus();
        return getStatusTotal(list);
    }

    @Override
    public StatusTotalResp getTrainingJobStatusTotal() {
        List<StatusTotalDto> list = synergyBigScreenMapper.getTrainingJobStatus();
        return getStatusTotal(list);
    }

    @Override
    public List<TrainingJobGroupDto> getTrainingJobGroupNew() {
        return synergyBigScreenMapper.getTrainingJobGroupNew();
    }

    @Override
    public List<TrainingJobPoolResp> getTrainingJobPool() {
        List<TrainingJobPoolResp> respList = new ArrayList<>();
        List<TrainingJobPoolDto> completedList = synergyBigScreenMapper.getTrainingJobByPoolId(
                TrainingJobStatusEnum.COMPLETED.getType());
        List<TrainingJobPoolDto> runningList = synergyBigScreenMapper.getTrainingJobByPoolId(
                TrainingJobStatusEnum.RUNNING.getType());
        completedList.stream().forEach(trainingJobPoolDto -> {
            TrainingJobPoolResp resp = new TrainingJobPoolResp();
            resp.setPoolName(trainingJobPoolDto.getPoolName());
            resp.setCompletedTotal(trainingJobPoolDto.getTotal());
            runningList.stream().forEach(dto -> {
                if (dto.getPoolName().equals(trainingJobPoolDto.getPoolName())) {
                    resp.setRunningTotal(dto.getTotal());
                }
            });
            respList.add(resp);
        });
        List<TrainingJobPoolResp> collect = respList.stream()
                                                    .sorted(Comparator.comparing(TrainingJobPoolResp::getTotal).reversed())
                                                    .collect(Collectors.toList());
        return collect;
    }

    @Override
    public TrainingJobTotalDto getTrainingJobTotal() {
        TrainingJobTotalDto dto = new TrainingJobTotalDto();
        DateTime dateTime = DateUtil.offsetDay(DateUtil.date(), -6);
        dto.setJobTotal(synergyBigScreenMapper.getTrainingJobTotal(dateTime));
        dto.setGroupTotal(synergyBigScreenMapper.getTrainingJobGroupTotal(dateTime));
        return dto;
    }

    @Override
    public List<ModelArtsResourceDto> getUserResourceTop() {
        return synergyBigScreenMapper.getUserResourceTop();
    }

    /**
     * 获取状态数量
     *
     * @param list 列表
     *
     * @return 响应值
     */
    private StatusTotalResp getStatusTotal(List<StatusTotalDto> list) {
        StatusTotalResp resp = new StatusTotalResp();
        list.stream().forEach(statusTotalDto -> {
            //已完成
            if (TrainingJobGroupStatusEnum.COMPLETED.getType().equals(statusTotalDto.getStatus())) {
                resp.setCompleted(statusTotalDto.getTotal());
            }
            //已完成
            if (TrainingJobGroupStatusEnum.FAILED.getType().equals(statusTotalDto.getStatus())) {
                resp.setFailed(statusTotalDto.getTotal());
            }
        });
        int sum = list.stream().mapToInt(StatusTotalDto::getTotal).sum();
        resp.setTotal(sum);
        resp.setOther(sum - resp.getCompleted() - resp.getFailed());
        return resp;
    }
}
