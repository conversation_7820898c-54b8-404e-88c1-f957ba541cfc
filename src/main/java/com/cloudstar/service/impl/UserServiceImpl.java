package com.cloudstar.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.ConfigService;
import com.cloudstar.PasswordService;
import com.cloudstar.bean.dto.PasswordPolicyDto;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.constant.OrgTypeEnum;
import com.cloudstar.common.base.constant.UserAuthStatusEnum;
import com.cloudstar.common.base.constant.UserStatusEnum;
import com.cloudstar.common.base.constant.UserTypeEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.util.CrytoUtilSimple;
import com.cloudstar.config.PasswordValidTimeConfig;
import com.cloudstar.config.Unification;
import com.cloudstar.dao.mapper.SysMOrgMapper;
import com.cloudstar.dao.mapper.UserEntityMapper;
import com.cloudstar.dao.model.SysMOrg;
import com.cloudstar.dao.model.UserEntity;
import com.cloudstar.keycloak.KeycloakService;
import com.cloudstar.keycloak.KeycloakUtil;
import com.cloudstar.keycloak.constants.KeycloakAttrConstants;
import com.cloudstar.pojo.user.req.UserRegisterReq;
import com.cloudstar.service.facade.UserService;

import org.keycloak.representations.idm.UserRepresentation;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 租户serviceImpl
 *
 * <AUTHOR> &#064;date  2022/7/20 17:39
 */
@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class UserServiceImpl extends ServiceImpl<UserEntityMapper, UserEntity> implements UserService {

    private final SysMOrgMapper sysMOrgMapper;

    private final PasswordService passwordService;

    private final ConfigService configService;

    private final KeycloakService keycloakService;
    /**
     * 是否重置密码
     */
    static final String IS_RESET_PASSWORD = "false";

    @SuppressFBWarnings("HARD_CODE_PASSWORD")
    @Override
    public boolean register(UserRegisterReq req) {
        Map<String, Object> map = addUser(req);
        return Boolean.getBoolean(map.get("result").toString());
    }

    @SuppressFBWarnings("HARD_CODE_PASSWORD")
    private Map<String, Object> addUser(UserRegisterReq req) {
        checkAccount(req.getAccount());
        checkMobile(req.getMobile());
        checkEmail(req.getEmail());
        checkPassword(req.getPassword(), req.getAccount());
        UserEntity user = new UserEntity();
        user.setUuid(IdUtil.simpleUUID());
        user.setAccount(req.getAccount());
        user.setPassword(StrUtil.isBlank(req.getPassword()) ? null : CrytoUtilSimple.encodeHash(req.getPassword()));
        //电话、邮箱加密存储
        user.setEmail(req.getEmail());
        user.setMobile(req.getMobile());
        user.setUserType(UserTypeEnum.ACCOUNT.getType());
        user.setAuthStatus(UserAuthStatusEnum.UPLOAD.getType());
        user.setStatus(UserStatusEnum.ENABLE.getType());
        user.setIsResetPassword(IS_RESET_PASSWORD);
        user.setCreatedDt(new Date());
        user.setUpdatedDt(new Date());
        //密码天数校验配置
        PasswordValidTimeConfig config = configService.getConfig(ConfigType.PASSWORD_VALID_TIME_CONFIG);
        Integer days = config.getDays().intValue();
        if (!ObjectUtils.isEmpty(days) && !Unification.isUnification) {
            user.setPasswordExpiresAt(DateUtil.offsetDay(new Date(), days));
        }
        //租户没得有效期这一说法
        //创建组织
        String orgName = user.getAccount() + "的组织";
        List<SysMOrg> orgList = sysMOrgMapper.selectList(
                new QueryWrapper<SysMOrg>().lambda().eq(SysMOrg::getOrgName, orgName));
        if (CollectionUtil.isNotEmpty(orgList)) {
            log.error("{},该企业已经被注册", orgName);
            throw new BizException("该企业已经被注册");
        }
        SysMOrg org = new SysMOrg();
        org.setOrgName(orgName);
        org.setOrgType(OrgTypeEnum.COMPANY.getType());
        org.setTreePath("/");
        org.setCreatedBy("admin");
        org.setCreatedDt(new Date());
        this.sysMOrgMapper.insert(org);
        user.setOrgSid(org.getOrgSid());
        boolean save = this.save(user);

        Map<String, Object> map = keycloakService.getUserAttrMap(user);
        map.put("result", save);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sync(UserRegisterReq req) {
        log.info("同步账号信息:{}", req);
        UserRepresentation userInfo = keycloakService.getUserConsoleUser(req.getAccount(), req.getEmail());
        if (Objects.isNull(userInfo)) {
            log.error("用户不存在, {}", req);
            throw new BizException("用户不存在");
        }

        Map<String, Object> map;
        //兼容管理员在算力创建租户的情况
        var users = this.list(Wrappers.<UserEntity>lambdaQuery().eq(UserEntity::getAccount, req.getAccount()));
        if (users.size() > 0) {
            log.info("已在算力注册账号：{}", req.getAccount());
            var user = users.get(0);
            map = keycloakService.getUserAttrMap(user);
            map.put("result", true);
            if (UserStatusEnum.DISABLE.getType().equals(user.getStatus())
                    && UserAuthStatusEnum.UPLOAD.getType().equals(user.getAuthStatus())) {
                log.info("启用账号");
                this.update(Wrappers.<UserEntity>lambdaUpdate().eq(UserEntity::getUserSid, user.getUserSid())
                                    .set(UserEntity::getUpdatedDt, new Date())
                                    .set(UserEntity::getStatus, UserStatusEnum.ENABLE.getType()));
            } else {
                log.error("用户{}已经在算力平台存在", user.getAccount());
                throw new BizException("用户状态异常");
            }
        } else {
            map = addUser(req);
            log.info("创建租户账号:{}", req.getAccount());
        }

        log.info("result:{}", map.get("result"));
        if (Boolean.parseBoolean(map.get("result").toString())) {
            log.info("注册keycloak账号");
            map.put(KeycloakAttrConstants.MOBILE_NUMBER, req.getMobile());

            if (StrUtil.isNotBlank(req.getMobile())) {
                map.put(KeycloakAttrConstants.MOBILE_NUMBER, req.getMobile());
            }

            if (StrUtil.isNotBlank(req.getEmail())) {
                map.put(KeycloakAttrConstants.EMAIL, req.getEmail());
            }

            keycloakService.registerUser(userInfo, map);
            return true;
        }
        return false;
    }

    /**
     * 校验账号
     *
     * @param account 账号
     */
    public void checkAccount(String account) {
        List<UserEntity> list = this.list(new QueryWrapper<UserEntity>().lambda()
                                                                        .eq(UserEntity::getAccount, account)
                                                                        .ne(UserEntity::getStatus,
                                                                            UserStatusEnum.DELETED.getType()));
        if (CollectionUtil.isNotEmpty(list)) {
            log.error("账号:{},{}", BizErrorEnum.MSG_1006_ACCOUNT_EXIST.getDescribe(), account);
            throw new BizException(BizErrorEnum.MSG_1006_ACCOUNT_EXIST);
        }
    }

    /**
     * 校验邮箱
     *
     * @param email 邮箱
     */
    public void checkEmail(String email) {
        List<UserEntity> list = this.list(new QueryWrapper<UserEntity>().lambda()
                                                                        .ne(UserEntity::getStatus,
                                                                            UserStatusEnum.DELETED.getType()));
        for (UserEntity userEntity : list) {
            if (email.equals(userEntity.getEmail())) {
                log.error("账号:{},{}", userEntity.getAccount(), BizErrorEnum.MSG_1008_EMAIL_EXIST.getDescribe());
                throw new BizException(BizErrorEnum.MSG_1008_EMAIL_EXIST);
            }
        }
    }

    /**
     * 校验电话号码
     *
     * @param mobile 手机号
     */
    public void checkMobile(String mobile) {
        List<UserEntity> list = this.list(new QueryWrapper<UserEntity>().lambda()
                                                                        .ne(UserEntity::getStatus,
                                                                            UserStatusEnum.DELETED.getType()));
        for (UserEntity userEntity : list) {
            if (mobile.equals(userEntity.getMobile())) {
                log.error("账号:{}，{}", userEntity.getAccount(), BizErrorEnum.MSG_1007_MOBILE_EXIST.getDescribe());
                throw new BizException(BizErrorEnum.MSG_1007_MOBILE_EXIST);
            }
        }
    }

    /**
     * 校验密码强度
     *
     * @param password 密码
     * @param account 账号
     */
    @Override
    public void checkPassword(String password, String account) {
        if (Unification.isUnification) {
            return;
        }

        PasswordPolicyDto policy = passwordService.defaultPolicy();
        if (policy.isNotSameAccount() && Objects.nonNull(account) && Objects.equals(account, password)) {
            throw new BizException(BizErrorEnum.MSG_1009_PASSWORD_RULE_ERROR);
        }
        if (!passwordService.validPassword(policy, password)) {
            throw new BizException(BizErrorEnum.MSG_1009_PASSWORD_RULE_ERROR);
        }
    }

    @Override
    public boolean deleteKcUser(Long userSid) {
        return KeycloakUtil.deleteUserByUserSid(userSid);
    }
}
