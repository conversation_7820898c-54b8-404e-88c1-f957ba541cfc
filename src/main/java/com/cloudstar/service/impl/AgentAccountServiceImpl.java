package com.cloudstar.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.dao.mapper.AgentAccountMapper;
import com.cloudstar.dao.mapper.AgentUserServiceMapper;
import com.cloudstar.dao.model.HcsoAgentAccount;
import com.cloudstar.dao.model.SlurmAgentAccount;
import com.cloudstar.service.facade.AgentAccountService;
import com.cloudstar.dao.model.AgentAccount;
import com.cloudstar.dao.model.AgentUser;
import com.cloudstar.dao.model.requests.AccountRequest;
import com.cloudstar.dao.model.resp.AccountResp;
import com.cloudstar.service.pojo.dto.ImportValidMessageDto;
import com.cloudstar.utils.AppUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.BindingResult;

/**
 * AgentAccountServiceImpl 类实现了 AgentAccountService 接口。
 * 提供了分页查询账户、验证用户、删除用户、获取用户和更新用户状态等功能。
 *
 * <AUTHOR>
 * @createDate 2025/3/3 11:22
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Slf4j
public class AgentAccountServiceImpl extends ServiceImpl<AgentAccountMapper, AgentAccount> implements AgentAccountService {

    private final AgentUserServiceMapper agentUserServiceMapper;

    @Override
    public Page<AccountResp> page(AccountRequest request, BindingResult bindingResult, PageForm pageForm) {
        QueryWrapper<AgentUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_type", request.getValidType()); // 字段可以和数据库不同
        // 根据 request 设置查询条件
        Page<AgentUser> userPage = agentUserServiceMapper.page(pageForm.pageRequest(), queryWrapper);

        // 根据环境类型设置用户类型
        if (AppUtil.isSlurmEnv()) {
            userPage.getRecords().forEach(user -> user.setUserType("slurm"));
        }

        // 执行分页查询
        return new Page<>(userPage.getCurrent(), userPage.getSize(), userPage.getTotal());
    }

    @Override
    public ImportValidMessageDto validUser(AgentUser importAgentUser) {
        AgentAccount agentAccount = getAgentAccount(importAgentUser.getUserType());
        if (agentAccount == null) {
            log.info("models.agentAccount - 找不到对应集群账号 :{}", importAgentUser.getUserType());
            return new ImportValidMessageDto(importAgentUser.getUserId(), false, "找不到对应集群账号");
        }
        return agentAccount.validUser(importAgentUser);
    }

    @Override
    public ImportValidMessageDto verifyUser(AgentUser importAgentUser) {
        AgentAccount agentAccount = getAgentAccount(importAgentUser.getUserType());
        if (agentAccount == null) {
            log.info("models.agentAccount 找不到对应集群账号 : {}", importAgentUser.getUserType());
            return new ImportValidMessageDto(importAgentUser.getUserId(), false, "找不到对应集群账号");
        }
        return agentAccount.verifyUser(importAgentUser);
    }

    @Override
    public int deleteUserByUserId(String userId) {
        return agentUserServiceMapper.deleteById(userId);
    }

    @Override
    public AgentUser getByUserId(String userId) {
        return agentUserServiceMapper.selectById(userId);
    }

    @Override
    public int updateStatus(AgentUser agUser) {
        return agentUserServiceMapper.updateById(agUser);
    }

    /**
     * 根据集群类型获取相应的 AgentAccount 实例。
     *
     * @param clusterType 集群类型
     * @return 对应的 AgentAccount 实例，如果未找到则返回 null
     */
    private AgentAccount getAgentAccount(String clusterType) {
        if (clusterType.equalsIgnoreCase("HCSO")) {
            return new HcsoAgentAccount();
        } else if (clusterType.equalsIgnoreCase("Slurm")) {
            return new SlurmAgentAccount();
        }
        return null;
    }
}
