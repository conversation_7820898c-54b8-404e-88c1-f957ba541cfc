package com.cloudstar.service.impl;

import com.cloudstar.common.base.constant.RedisCacheKeyEnum;
import com.cloudstar.common.component.redis.util.RedisUtil;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.training.TrainingJobEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.training.TrainingJobEntity;
import com.cloudstar.sdk.schedule.pojo.TrainingJobLogResp;
import com.cloudstar.service.facade.SendJobLogService;
import com.cloudstar.service.grpc.AgentTrainingJobGrpc;
import com.cloudstar.service.grpc.AgentTrainingJobProto.JobOperateRequest;
import com.cloudstar.service.grpc.AgentTrainingJobProto.JobOperateRequest.Builder;
import com.cloudstar.service.grpc.AgentTrainingJobProto.ShowTrainingJobLogsPreviewResp;
import com.cloudstar.service.grpc.GrpcManage;
import com.cloudstar.service.grpcservice.facade.GrpcServiceBase;
import com.cloudstar.websocket.enums.MsgTypeEnum;
import com.cloudstar.websocket.message.MessageDto;

import org.springframework.stereotype.Service;

import java.util.Set;

import javax.annotation.Resource;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import io.grpc.stub.AbstractStub;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class SendJobLogServiceImpl implements SendJobLogService, GrpcServiceBase {

    private AgentTrainingJobGrpc.AgentTrainingJobBlockingStub blockingStub;

    @Resource
    private TrainingJobEntityMapper trainingJobEntityMapper;

    @Resource
    private ClusterEntityMapper clusterEntityMapper;

    @Resource
    private RedisUtil redisUtil;


    @Override
    public void sendJobLog() {
        String jobLogKey = new StringBuilder(RedisCacheKeyEnum.JOB_LOG.getKey()).append("*").toString();
        Set<String> keys = redisUtil.scan(jobLogKey, 1000);
        for (String key : keys) {
            //改造下，支持分布式作业训练的日子获取
            String jobId = redisUtil.get(key);
            final String[] jobIdSplit = jobId.split(":");
            TrainingJobEntity trainingJob = trainingJobEntityMapper.selectById(Long.valueOf(jobIdSplit[0]));
            if (ObjectUtil.isEmpty(trainingJob.getJobId())) {
                continue;
            }
            ClusterEntity entity = clusterEntityMapper.selectById(trainingJob.getClusterId());
            TrainingJobLogResp result = new TrainingJobLogResp();
            final Builder builder = JobOperateRequest.newBuilder().setJobId(trainingJob.getJobId());
            if (ObjectUtil.isNotEmpty(jobIdSplit[1])) {
                builder.setPodName(jobIdSplit[1]);
            }
            String adapterUuid = entity.getAdapterUuid();
            this.build(adapterUuid, AgentTrainingJobGrpc.AgentTrainingJobBlockingStub.class);
            ShowTrainingJobLogsPreviewResp resp = blockingStub.showTrainingJobLogsPreview(builder.build());
            if (ObjectUtil.isNotEmpty(resp)) {
                result.setContent(resp.getContent());
                result.setCurrentSize(resp.getCurrentSize());
                result.setFullSize(resp.getFullSize());
            }
            String[] split = key.split(":");
            MessageDto messageDto = MessageDto.builder()
                                              .msgType(MsgTypeEnum.LOG_INFO)
                                              .msgContent(result)
                                              .userSid(Long.parseLong(split[1]))
                                              .msgId(IdUtil.getSnowflakeNextId())
                                              .pushDate(DateUtil.date().toString()).build();
            redisUtil.getRedisTemplate().convertAndSend(RedisCacheKeyEnum.WEBSOCKET_MESSAGE.getKey(), JSONUtil.toJsonStr(messageDto));
            //日志内容太多了 不打印出来
            messageDto.setMsgContent("");
            log.info("websocket推送作业日志信息:{}", JSONUtil.toJsonStr(messageDto));
        }

    }

    @Override
    public void build(String targetServer, Class<? extends AbstractStub<?>> clazz) {
        blockingStub = (AgentTrainingJobGrpc.AgentTrainingJobBlockingStub) GrpcManage.getStub(targetServer, clazz);
    }
}
