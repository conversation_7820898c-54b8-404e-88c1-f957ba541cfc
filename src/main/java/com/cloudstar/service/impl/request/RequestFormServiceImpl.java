package com.cloudstar.service.impl.request;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.NotificationService;
import com.cloudstar.bean.enums.MessageTemplateEnum;
import com.cloudstar.bean.enums.MessageType;
import com.cloudstar.bean.pojo.NotificationParam;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.constant.RequestFormConstant.RequestFormStatus;
import com.cloudstar.common.base.constant.RequestFormConstant.RequestFormType;
import com.cloudstar.common.base.constant.UserAuthStatusEnum;
import com.cloudstar.common.base.enums.ClusterFlagEnum;
import com.cloudstar.common.base.exception.BizError;
import com.cloudstar.common.base.util.WebUtil;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.mapper.request.RequestFormMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.cluster.ClusterUserMappingLog;
import com.cloudstar.dao.model.request.RequestForm;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.consoleserver.client.ConsoleServerClient;
import com.cloudstar.sdk.consoleserver.pojo.UpdateUserAuthReq;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.sdk.server.pojo.FixedMappingReq;
import com.cloudstar.service.facade.cluster.ClusterEntityService;
import com.cloudstar.service.facade.cluster.ClusterUserMappingLogService;
import com.cloudstar.service.facade.cluster.ClusterUserMappingService;
import com.cloudstar.service.facade.request.RequestFormService;
import com.cloudstar.service.facade.tenant.UserService;
import com.cloudstar.service.pojo.vo.requestvo.cluster.ClusterUserMappingSaveReq;
import com.cloudstar.service.pojo.vo.requestvo.request.RequestFormAuditReq;
import com.cloudstar.service.pojo.vo.requestvo.request.RequestFormSelectListReq;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUserMappingResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUserUnmappedResp;
import com.cloudstar.service.pojo.vo.responsevo.request.RequestFormInfoResponse;
import com.cloudstar.service.pojo.vo.responsevo.request.RequestFormResponse;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;


/**
 * 请求表单服务impl
 *
 * <AUTHOR>
 * @date 2022/08/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class RequestFormServiceImpl extends ServiceImpl<RequestFormMapper, RequestForm> implements RequestFormService {


    ConsoleServerClient userAuthClient;

    ClusterUserMappingService clusterUserMappingService;

    ClusterEntityService clusterEntityService;

    UserService userService;

    NotificationService notificationService;

    ClusterUserMappingLogService mappingLogService;

    /**
     * 请求表单列表
     *
     * @param req 要求事情
     *
     * @return {@link PageResult}<{@link RequestFormResponse}>
     */
    @Override
    public PageResult<RequestFormResponse> getRequestFormList(RequestFormSelectListReq req) {
        Page<RequestFormResponse> pageQuery = new Page<>(req.getPageNo(),
                                                         req.getPageSize());
        if (ObjectUtil.isNotNull(req.getStatus())) {
            req.setAuthStatus(List.of(req.getStatus()));
        } else { // 查询已审核
            req.setAuthStatus(
                    Arrays.asList(RequestFormStatus.APPROVAL.getStatus(), RequestFormStatus.REFUSE.getStatus()));
        }
        if (!ObjectUtils.isEmpty(req.getSortDataField())) {
            if (WebUtil.checkFields(new RequestFormResponse(), req.getSortDataField())) {
                req.setSortDataField(req.getSortDataField() + " " + (req.getAsc() ? "asc" : "desc"));
            }
        } else {
            req.setSortDataField("snCode desc");
        }
        Page<RequestFormResponse> list = baseMapper.getRequestFormList(pageQuery, req);
        list.getRecords().forEach(requestFormResponse -> {
            requestFormResponse.setStatusName(RequestFormStatus.getDesc(requestFormResponse.getStatus()));
            requestFormResponse.setTypeName(RequestFormType.getDesc(requestFormResponse.getType()));
        });
        return PageResult.of(list);
    }


    /**
     * 请求表单id
     *
     * @param id id
     *
     * @return {@link RequestFormInfoResponse}
     */
    @Override
    public RequestFormInfoResponse getRequestFormById(Long id) {
        RequestFormInfoResponse response = baseMapper.getRequestFormById(id);
        if (Objects.isNull(response)) {
            return null;
        }
        response.setStatusName(RequestFormStatus.getDesc(response.getStatus()));
        response.setTypeName(RequestFormType.getDesc(response.getType()));
        return response;
    }


    /**
     * 审核申请表
     *
     * @param id id
     * @param req 要求事情
     *
     * @return {@link ClusterUserMappingResp}
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClusterUserMappingResp auditRequestForm(Long id, RequestFormAuditReq req) {
        ClusterUserMappingResp clusterUserMappingResp = new ClusterUserMappingResp();
        RequestForm requestForm = getById(id);

        if (!Objects.equals(req.getStatus(), RequestFormStatus.APPROVAL.getStatus()) && !Objects.equals(req.getStatus(),
                                                                                                        RequestFormStatus.REFUSE
                                                                                                                .getStatus())) {
            BizError.e(BizErrorEnum.MSG_1018_REQUEST_AUDIT_STATUS_ERROR);
        }
        Long userSid = this.auditUserAuthRequest(requestForm, req);
        if (Objects.isNull(userSid)) {
            BizError.e(BizErrorEnum.MSG_1039_REQUEST_AUDIT_ERROR);
        }
        if (Objects.equals(RequestFormType.USER_AUTH.getType(), requestForm.getType())) {
            //自动租户映射
            List<ClusterUserUnmappedResp> respList = clusterEntityService.queryHaveClusterAccount(null);
            if (Objects.equals(req.getStatus(), UserAuthStatusEnum.APPROVAL.getType()) && CollectionUtil.isNotEmpty(
                    respList)) {
                clusterUserMappingResp = autoUserMapping(respList, userSid, null);
            }
        }
        this.lambdaUpdate().eq(RequestForm::getId, id).set(RequestForm::getStatus, req.getStatus()).update();

        UserEntity userEntity = userService.getById(userSid);
        //配置消息地址,配置在update后面，邮箱和电话会加密
        Map<String, String> sendAddress = new HashMap<>();
        sendAddress.put("email", userEntity.getEmail());
        sendAddress.put("mobile", userEntity.getMobile());
        //配置消息模板内容
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Map<String, String> messageContent = new HashMap<>();
        messageContent.put("owner", userEntity.getAccount());
        for (UserAuthStatusEnum status : UserAuthStatusEnum.values()) {
            if (req.getStatus().equals(status.getType())) {
                String auditStatus = status.getDesc();
                messageContent.put("authStatus", auditStatus);
            }
        }
        messageContent.put("auditUserName", authUser.getAccount());
        messageContent.put("orderSn", requestForm.getSnCode());
        //消息通知类型
        List<MessageType> messageTypeList = new ArrayList<>();
        messageTypeList.add(MessageType.SMS);
        messageTypeList.add(MessageType.EMAIL);
        messageTypeList.add(MessageType.PLATFORM);
        NotificationParam notificationParam = NotificationParam.builder()
                                                               .messageType(messageTypeList)
                                                               .sendAddress(sendAddress)
                                                               .messageContent(messageContent)
                                                               .sendUserSid(userEntity.getUserSid())
                                                               .sendAccount(userEntity.getAccount())
                                                               .isCC(false)
                                                               .messageTemplateId(
                                                                       MessageTemplateEnum.USER_AUTH_COMPLETE
                                                                               .getMessageTemplateId()).build();
        notificationService.sendNotification(notificationParam);
        return clusterUserMappingResp;
    }

    private ClusterUserMappingResp autoUserMapping(List<ClusterUserUnmappedResp> respList, Long userSid,
                                                   String clusterUserId) {
        //自动租户映射
        List<Long> clusterIds = respList.stream()
                                        .map(ClusterUserUnmappedResp::getClusterId)
                                        .collect(Collectors.toList());
        ClusterUserMappingSaveReq build = ClusterUserMappingSaveReq.builder()
                                                                   .userId(userSid)
                                                                   .clusterIds(clusterIds)
                                                                   .skipCheckAuth(Boolean.TRUE)
                                                                   .clusterUserId(clusterUserId)
                                                                   .build();
        return clusterUserMappingService.insertMappedClusters(build);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean fixedMapping(FixedMappingReq req) {
        List<ClusterUserUnmappedResp> respList = clusterEntityService.queryHaveClusterAccount(req.getClusterUserId());
        if (CollectionUtil.isEmpty(respList)) {
            log.error("fixedMapping queryHaveClusterAccount is empty");
            //如果是统一入口,保存系统集群固定映射日志
            ClusterEntity clusterEntity = clusterEntityService.getOne(
                    new LambdaQueryWrapper<ClusterEntity>().eq(ClusterEntity::getClusterFlag, ClusterFlagEnum.SYSTEM.getType()));
            if (ObjectUtil.isNotEmpty(clusterEntity)) {
                LambdaQueryWrapper<ClusterUserMappingLog> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(ClusterUserMappingLog::getClusterId, clusterEntity.getId());
                queryWrapper.eq(ClusterUserMappingLog::getClusterAccount, req.getClusterUserId());
                queryWrapper.eq(ClusterUserMappingLog::getUserSid, req.getUserSid());
                ClusterUserMappingLog userMappingLog = mappingLogService.getOne(queryWrapper);
                //没操作记录 就新增一个
                if (ObjectUtil.isEmpty(userMappingLog)) {
                    userMappingLog = new ClusterUserMappingLog();
                    userMappingLog.setClusterId(clusterEntity.getId());
                    userMappingLog.setUserSid(req.getUserSid());
                    userMappingLog.setClusterAccount(req.getClusterUserId());
                    userMappingLog.setCreatedDt(new Date());
                    userMappingLog.setStatus("FAIL");
                    mappingLogService.save(userMappingLog);
                } else {
                    userMappingLog.setStatus("FAIL");
                    userMappingLog.setUpdatedDt(new Date());
                    mappingLogService.updateById(userMappingLog);
                }
                log.info("新增租户固定映射操作日志:{}", userMappingLog);
            }
            return false;
        }
        var clusterUserMappingResp = autoUserMapping(respList, req.getUserSid(), req.getClusterUserId());
        log.info("fixedMapping clusterUserMappingResp:{}", clusterUserMappingResp);
        return !clusterUserMappingResp.getClusterList().isEmpty() && !clusterUserMappingResp.getUserList().isEmpty();
    }

    /**
     * 审计用户身份请求
     *
     * @param requestForm 请求表单
     * @param req 申请请求
     */
    private Long auditUserAuthRequest(RequestForm requestForm, RequestFormAuditReq req) {
        UpdateUserAuthReq updateUserAuthReq = new UpdateUserAuthReq();
        updateUserAuthReq.setId(requestForm.getRelationId());
        updateUserAuthReq.setStatus(req.getStatus());
        updateUserAuthReq.setRemark(req.getRemark());
        return userAuthClient.editStatus(updateUserAuthReq).getData();
    }

}
