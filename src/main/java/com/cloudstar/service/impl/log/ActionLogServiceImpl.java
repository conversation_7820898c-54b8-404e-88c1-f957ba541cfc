package com.cloudstar.service.impl.log;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.dao.mapper.ActionLogMapper;
import com.cloudstar.dao.mapper.user.UserEntityMapper;
import com.cloudstar.dao.model.ActionLog;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.service.facade.log.ActionLogService;
import com.cloudstar.service.pojo.vo.requestvo.tenant.UserActionLogRequest;
import com.cloudstar.service.pojo.vo.responsevo.tenant.UserActionLogResponse;

import org.springframework.stereotype.Service;

import java.util.List;

import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;


/**
 * 操作日志实现
 *
 * <AUTHOR>
 * @description 针对表【action_log(操作日志表)】的数据库操作Service实现
 * @createDate 2022-09-21 17:11:32
 */
@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ActionLogServiceImpl extends ServiceImpl<ActionLogMapper, ActionLog> implements ActionLogService {

    UserEntityMapper userEntityMapper;

    /**
     * 查询用户操作日志
     *
     * @param userActionLogRequest 查询参数
     * @param pageForm 分页参数
     *
     * @return {@link PageResult}<{@link UserActionLogResponse}>
     */
    @Override
    public PageResult<UserActionLogResponse> getUserActionLogs(UserActionLogRequest userActionLogRequest,
                                                               PageForm pageForm) {
        //若前端不传排序参数，则默认以actionTime降序展示
        if (ObjectUtil.isEmpty(pageForm.getSortDataField()) && ObjectUtil.isEmpty(pageForm.getAsc())) {
            pageForm.setSortDataField("actionTime");
            pageForm.setAsc(false);
        }
        //分页查询
        Page<ActionLog> actionLogPage = this.page(pageForm.pageRequest(),
                                                  SpecWrapperUtil.filter(userActionLogRequest));

        //在用户表中查出用户的用户名
        List<UserEntity> userEntities = userEntityMapper.selectList(new LambdaQueryWrapper<UserEntity>()
                                                                                .eq(UserEntity::getUserSid, userActionLogRequest.getAccountId()));

        PageResult<UserActionLogResponse> result = PageResult.of(actionLogPage, UserActionLogResponse.class);
        //组装用户名和操作结果返回
        for (UserActionLogResponse userActionLogResponse : result.getList()) {
            userActionLogResponse.setRealName(userEntities.get(0).getRealName());
            if (Boolean.FALSE.equals(userActionLogResponse.getActionResult())) {
                userActionLogResponse.setResultName("失败");
            } else {
                userActionLogResponse.setResultName("成功");
            }
        }
        return result;
    }
}




