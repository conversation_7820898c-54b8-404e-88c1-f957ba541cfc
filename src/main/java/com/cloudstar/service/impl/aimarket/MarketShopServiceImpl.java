package com.cloudstar.service.impl.aimarket;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.aimarket.common.utils.BeanConvertUtil;
import com.cloudstar.aimarket.common.utils.PageUtil;
import com.cloudstar.aimarket.enums.MarketShopSourceEnum;
import com.cloudstar.aimarket.enums.MarketShopStatusEnum;
import com.cloudstar.aimarket.enums.MarketSkuTypeEnum;
import com.cloudstar.aimarket.pojo.entity.MarketShop;
import com.cloudstar.aimarket.pojo.entity.MarketShopSku;
import com.cloudstar.aimarket.pojo.entity.MarketShopSkuRelevance;
import com.cloudstar.aimarket.pojo.entity.MarketShopTag;
import com.cloudstar.aimarket.pojo.request.market.MarketShopMgtRequest;
import com.cloudstar.aimarket.pojo.response.market.MarketShopDetailResp;
import com.cloudstar.aimarket.pojo.response.market.MarketShopMgtResp;
import com.cloudstar.aimarket.pojo.vo.market.MarketShopSkuVo;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.Criteria;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.mapper.SysMConfigMapper;
import com.cloudstar.dao.mapper.aimarket.MarketShopMapper;
import com.cloudstar.dao.mapper.aimarket.MarketShopSkuMapper;
import com.cloudstar.dao.mapper.aimarket.MarketShopTagMapper;
import com.cloudstar.dao.model.SysMConfig;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.service.facade.aimarket.MarketAuditService;
import com.cloudstar.service.facade.aimarket.MarketShopService;
import com.cloudstar.service.facade.aimarket.MarketShopSkuRelevanceService;
import com.cloudstar.service.facade.aimarket.MarketShopTagRelevanceService;
import com.cloudstar.service.utils.BasicWebUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MarketShopServiceImpl extends ServiceImpl<MarketShopMapper, MarketShop> implements MarketShopService {

    private static final Integer SHOP_TYPE_FLAG_1 = 1;

    private static final Integer SHOP_TYPE_FLAG_3 = 3;

    MarketShopMapper marketShopMapper;

    MarketShopTagMapper marketShopTagService;

    MarketShopSkuMapper shopSkuMapper;

    SysMConfigMapper sysMConfigMapper;

    MarketShopTagRelevanceService marketShopTagRelevanceService;

    MarketAuditService marketAuditService;

    MarketShopSkuRelevanceService marketShopSkuRelevanceService;

    @Override
    public PageResult<MarketShopMgtResp> selectAllShops(MarketShopMgtRequest shopReq) {
        if (StrUtil.isBlank(shopReq.getPageNo()) || StrUtil.isBlank(shopReq.getPageSize())) {
            shopReq.setPageSize("6");
            shopReq.setPageNo("1");
        }
        QueryWrapper<MarketShop> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("sort_order");

        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        shopReq.setOwnerId(authUser.getUserSid());

        if (!Objects.isNull(shopReq.getOwnerId())) {
            queryWrapper.lambda().eq(MarketShop::getOwnerId, shopReq.getOwnerId());
        }
        if (StringUtils.isNotBlank(shopReq.getShopName())) {
            queryWrapper.lambda().like(MarketShop::getTitle, shopReq.getShopName());
        }
        if (!Objects.isNull(shopReq.getShopSource())) {
            queryWrapper.lambda().eq(MarketShop::getShopSource, shopReq.getShopSource());
        }
        if (StringUtils.isNotBlank(shopReq.getStatus())) {
            queryWrapper.lambda().like(MarketShop::getStatus, shopReq.getStatus());
        }
        if (StringUtils.isNotBlank(shopReq.getIntroduce())) {
            queryWrapper.lambda().eq(MarketShop::getIntroduce, shopReq.getIntroduce());
        }
        if (!Objects.isNull(shopReq.getShopType())) {
            queryWrapper.lambda().eq(MarketShop::getShopType, shopReq.getShopType());
        }
        Page<MarketShop> page = PageUtil.preparePageParams(shopReq);
        Page<MarketShop> pageResult = marketShopMapper.selectPage(page, queryWrapper);

        for (MarketShop marketShop : pageResult.getRecords()) {
            String shopId = marketShop.getShopId();
            List<MarketShopTag> list = marketShopTagService.selectByShopId(shopId);
            List<String> collect = list.stream().map(MarketShopTag::getTagName).collect(Collectors.toList());
            marketShop.setLabelNameList(collect);

            if (SHOP_TYPE_FLAG_1.equals(marketShop.getShopType()) || SHOP_TYPE_FLAG_3.equals(marketShop.getShopType())) {
                List<MarketShopSku> shopSkus = shopSkuMapper.selectListByShopId(shopId);
                List<MarketShopSkuVo> marketShopSkuVos = new ArrayList<>();
                shopSkus.forEach(shopSku -> {
                    MarketShopSkuVo vo = new MarketShopSkuVo();
                    vo.setId(shopSku.getId());
                    vo.setAttrName(shopSku.getAttrName());
                    vo.setType(shopSku.getType());
                    vo.setUnit(shopSku.getUnit());
                    if (MarketSkuTypeEnum.ENUM.getType().equals(shopSku.getType())) {
                        if (shopSku.getEnumValues() == null) {
                            vo.setEnums(null);
                        } else {
                            vo.setEnums(Arrays.asList(shopSku.getEnumValues().split(",")));
                        }
                    }
                    marketShopSkuVos.add(vo);
                });
                marketShop.setShopSkuVos(marketShopSkuVos);
            }
        }
        IPage<MarketShopMgtResp> marketCustomerShopRespIPage = BeanConvertUtil.convertPage(pageResult, MarketShopMgtResp.class);
        // IPage转成Page
        Page<MarketShopMgtResp> pageResultResp = new Page<>();
        pageResultResp.setRecords(marketCustomerShopRespIPage.getRecords());
        pageResultResp.setTotal(marketCustomerShopRespIPage.getTotal());
        pageResultResp.setCurrent(marketCustomerShopRespIPage.getCurrent());
        pageResultResp.setSize(marketCustomerShopRespIPage.getSize());
        pageResultResp.setPages(marketCustomerShopRespIPage.getPages());

        return PageResult.of(pageResultResp);
    }

    @Override
    public Rest selectShopDetailsById(String shopId) {
        MarketShop marketShop = this.getById(shopId);
        if (ObjectUtil.isNull(marketShop)) {
            return Rest.e("商品不存在");
        }
        MarketShopDetailResp result = new MarketShopDetailResp();
        BeanUtil.copyProperties(marketShop, result);

        List<MarketShopTag> list = marketShopTagService.selectByShopId(shopId);
        result.setLabels(list);

        if (SHOP_TYPE_FLAG_1.equals(marketShop.getShopType()) || SHOP_TYPE_FLAG_3.equals(marketShop.getShopType())) {
            List<MarketShopSku> shopSkus = shopSkuMapper.selectListByShopId(marketShop.getShopId());
            List<MarketShopSkuVo> marketShopSkuVos = new ArrayList<>();
            shopSkus.forEach(shopSku -> {
                MarketShopSkuVo vo = new MarketShopSkuVo();
                vo.setId(shopSku.getId());
                vo.setAttrName(shopSku.getAttrName());
                vo.setType(shopSku.getType());
                vo.setUnit(shopSku.getUnit());
                if (MarketSkuTypeEnum.ENUM.getType().equals(shopSku.getType())) {
                    vo.setEnums(Arrays.asList(shopSku.getEnumValues().split(",")));
                }

                marketShopSkuVos.add(vo);
            });
            result.setShopSkuVos(marketShopSkuVos);
        }
        return Rest.ok(result);
    }

    @Override
    public Rest addShop(MarketShop req) {

        impressiveWordVerification(req);
        this.setEntity(req);
        this.save(req);

        String shopId = req.getShopId();
        List<Long> labelIds = req.getLabelIds();

        marketShopTagRelevanceService.saveBatchTag(shopId, labelIds);

        this.saveSpec(req);

        // 如果是发布并且供应商，需要生成审批单
        if (MarketShopStatusEnum.PENDING.getType().equals(req.getStatus()) && req.getShopSource().equals(MarketShopSourceEnum.SUPPLIER.getType())) {
            marketAuditService.startShopProcess(shopId);
        }

        return Rest.ok("添加成功");
    }

    @Override
    public Rest updateShop(MarketShop req) {
        MarketShop marketShop = this.getById(req.getShopId());
        if (ObjectUtil.isNull(marketShop)) {
            return Rest.e("商品不存在");
        }
        if (MarketShopStatusEnum.checkUpdateStatus(marketShop.getStatus())) {
            return Rest.e("当前状态下不可修改，请稍后重试");
        }
        String shopId = req.getShopId();

        if (MarketShopStatusEnum.PENDING.getType().equals(req.getStatus())
                && marketShop.getShopSource().equals(MarketShopSourceEnum.PLATFORM.getType())) {
            req.setStatus(MarketShopStatusEnum.ONLINE.getType());
        }
        this.updateById(req);

        // 如果是发布并且是供应商，需要生成审批单
        if (MarketShopStatusEnum.PENDING.getType().equals(req.getStatus())
                && marketShop.getShopSource().equals(MarketShopSourceEnum.SUPPLIER.getType())) {
            marketAuditService.startShopProcess(shopId);
        }

        marketShopTagRelevanceService.deleteByShopId(shopId);
        marketShopTagRelevanceService.saveBatchTag(shopId, req.getLabelIds());

        return Rest.ok("修改成功");
    }

    /**
     * 敏感词验证
     *
     * @param req 请求
     */
    private void impressiveWordVerification(MarketShop req) {
        Set<String> sensitiveWordList = getSensitiveWordList();
        sensitiveWordList.stream().forEach(word -> {
            if (req.getTitle().contains(word)) {
                throw new BizException("商品名称不能包含敏感词");
            }
            if (req.getIntroduce().contains(word)) {
                throw new BizException("商品简介不能包含敏感词");
            }
            if (req.getDescription().contains(word)) {
                throw new BizException("商品描述不能包含敏感词");
            }
        });
    }

    private Set<String> getSensitiveWordList() {
        Criteria criteria = new Criteria();
        criteria.put("configType", "sensitive_word_config");
        List<SysMConfig> configs = sysMConfigMapper.displaySystemConfigList(criteria);
        Set<String> sensitiveWordList = new HashSet<>();
        configs.forEach(sysConfig -> {
            String configValue = Optional.ofNullable(sysConfig.getConfigValue()).orElse("");
            if (StrUtil.isBlank(configValue)) {
                return;
            }
            List<String> strings = Arrays.stream(configValue.replace("；", ";").split(";")).collect(Collectors.toList());
            sensitiveWordList.addAll(strings);
        });
        return sensitiveWordList;
    }

    /**
     * 设置商品入库属性
     * @param req 请求
     */
    private void setEntity(MarketShop req) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        req.setOwnerId(authUser.getUserSid());
        req.setOrgSid(authUser.getOrgSid());
        if (StrUtil.isBlank(req.getPayType())) {
            req.setPayType("free");
        }
        BasicWebUtil.prepareInsertParams(req);

        MarketShop marketShop = marketShopMapper.selectSortTopShop();
        req.setSortOrder(Objects.isNull(marketShop) ? 1 : marketShop.getSortOrder() + 1);

    }

    /**
     * 保存规格
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveSpec(MarketShop req) {
        Integer shopType = req.getShopType();
        if (SHOP_TYPE_FLAG_1.equals(shopType) || SHOP_TYPE_FLAG_3.equals(shopType)) {
            List<MarketShopSkuVo> shopSkuVos = req.getShopSkuVos();
            List<MarketShopSkuRelevance> skuList = new ArrayList<>();
            shopSkuVos.forEach(sku -> {
                MarketShopSkuRelevance skuRelevance = new MarketShopSkuRelevance();
                skuRelevance.setSkuId(sku.getId());
                skuRelevance.setSkuType(sku.getType());
                skuRelevance.setShopId(req.getShopId());
                skuList.add(skuRelevance);

                if (MarketSkuTypeEnum.ENUM.getType().equals(sku.getType())) {
                    MarketShopSku shopSku = new MarketShopSku();
                    shopSku.setId(sku.getId());
                    shopSku.setAvailableStatus(0);
                    shopSkuMapper.updateById(shopSku);
                }
            });
            marketShopSkuRelevanceService.saveBatch(skuList);
        }
    }
}
