package com.cloudstar.service.impl.aimarket;


import cn.hutool.core.map.MapUtil;
import com.cloudstar.NotificationService;
import com.cloudstar.aimarket.camunda.CamundaHelper;
import com.cloudstar.aimarket.enums.ShopTypeEnum;
import com.cloudstar.aimarket.pojo.entity.MarketShop;
import com.cloudstar.bean.enums.MessageTemplateEnum;
import com.cloudstar.bean.enums.MessageType;
import com.cloudstar.bean.pojo.NotificationParam;
import com.cloudstar.common.base.constant.MsgCd;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.Org;
import com.cloudstar.common.base.util.WebUtil;
import com.cloudstar.dao.mapper.aimarket.BasicOrgMapper;
import com.cloudstar.dao.mapper.aimarket.MarketShopMapper;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.service.facade.aimarket.MarketAuditService;
import com.cloudstar.service.facade.manager.ManagerEntityService;
import com.cloudstar.service.pojo.dto.manager.ManagerEntityDto;
import com.google.common.collect.Maps;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.repository.Deployment;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class MarketAuditServiceImpl implements MarketAuditService {

    MarketShopMapper marketShopMapper;

    BasicOrgMapper basicOrgMapper;

    NotificationService notificationService;

    ManagerEntityService managerEntityService;

    @Override
    public String startShopProcess(String shopId) {
        long count = CamundaHelper.runtimeService.createProcessInstanceQuery().processDefinitionKey(
                CamundaHelper.DEFAULT_PROCESS_DEFINITION_KEY).processInstanceBusinessKey(shopId).count();

        if (count > 0) {
            throw new BizException(WebUtil.getMessage(MsgCd.NOT_SUBMIT_AGAIN));
        }
        MarketShop marketShop = marketShopMapper.selectById(shopId);
        String shopType = ShopTypeEnum.transformDesc(marketShop.getShopType());
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Org org = basicOrgMapper.selectByPrimaryKey(authUser.getOrgSid());
        Map<String, Object> map = MapUtil.ofEntries(
                MapUtil.entry("shopName", marketShop.getTitle()),
                MapUtil.entry("shopId", marketShop.getShopId()),
                MapUtil.entry("shopType", shopType),
                MapUtil.entry("payType", marketShop.getPayType()),
                MapUtil.entry("applyName", marketShop.getTitle()),
                MapUtil.entry("applyDescribe", marketShop.getIntroduce()),
                MapUtil.entry("applyType", "商品上架申请"),
                MapUtil.entry("userEmail", authUser.getEmail()),
                MapUtil.entry("userPhone", authUser.getMobile()),
                MapUtil.entry("userName", authUser.getAccount()),
                MapUtil.entry("companyName", Objects.nonNull(org) ? org.getOrgName() : null)
        );

        ProcessInstance processInstance;


        try {
            processInstance = CamundaHelper.runtimeService.startProcessInstanceByKey(
                    CamundaHelper.DEFAULT_PROCESS_DEFINITION_KEY, shopId, map);
        } catch (NullPointerException e) {
            checkExistDeploy(CamundaHelper.DEFAULT_PROCESS_DEFINITION_KEY);
            throw new BizException(WebUtil.getMessage(MsgCd.SYSTEM_ERROR));
        }
        //通知
        pendingMsg(marketShop, processInstance.getId());
        return processInstance.getId();
    }

    private void checkExistDeploy(String processKey) {
        List<Deployment> list = CamundaHelper.repositoryService.createDeploymentQuery()
                .deploymentName(processKey)
                .list();
        log.info("之前的商品审批流程,count:{}", list.size());
        for (Deployment deployment : list) {
            log.info("deploy:{}", deployment);
        }
        Deployment deploy = CamundaHelper.repositoryService.createDeployment()
                .addClasspathResource(
                        "bpmn/aimarket_shop_audit.bpmn")
                .name(processKey)
                .deploy();
        log.info("发布deploy:{}", deploy);
    }

    private void pendingMsg(MarketShop req, String auditId) {

        // 商品待审核通知
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        Map<String, String> messageContent = Maps.newHashMap();
        messageContent.put("subscribeAccount", authUser.getAccount());
        messageContent.put("subId", req.getShopId());
        messageContent.put("auditUrl", "#/appmodel/aimarket/check/" + auditId + "?shopId=" + req.getShopId());
        messageContent.put("shopTitle", req.getTitle());
        messageContent.put("time", cn.hutool.core.date.DateUtil.format(req.getCreatedDt(), "yyyy-MM-dd HH:mm:ss"));
        messageContent.put("shopId", req.getShopId());
        List<MessageType> messageTypeList = new ArrayList<>();
        messageTypeList.add(MessageType.PLATFORM);
        // TODO 给管理员发送消息且保证不重复
        List<ManagerEntityDto> managerEntityDtoList = new ArrayList<>(managerEntityService.selectOperationManagerAndSystemManager().stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ManagerEntityDto::getUserSid, m -> m, (existing, replacement) -> existing))
                .values());

        for (ManagerEntityDto managerEntityDto : managerEntityDtoList) {
            NotificationParam notificationParam = NotificationParam.builder()
                    .messageType(messageTypeList)
                    .messageContent(messageContent)
                    .sendUserSid(managerEntityDto.getUserSid())
                    .sendAccount(managerEntityDto.getAccount()).isCC(false)
                    .messageTemplateId(MessageTemplateEnum.BSSMGT_MARKET_PENDING_APPROVAL.getMessageTemplateId()).build();
            notificationService.sendNotification(notificationParam);
        }
    }
}
