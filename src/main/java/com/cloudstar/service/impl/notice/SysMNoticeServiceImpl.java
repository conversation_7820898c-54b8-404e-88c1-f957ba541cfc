package com.cloudstar.service.impl.notice;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.ConfigService;
import com.cloudstar.common.base.enums.NoticeStatusEnum;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.dao.mapper.notice.SysMNoticeMapper;
import com.cloudstar.dao.model.notice.SysMNotice;
import com.cloudstar.integration.bss.pojo.notice.BssNoticeResp;
import com.cloudstar.integration.bss.service.facade.BssNoticeService;
import com.cloudstar.service.facade.notice.SysMNoticeService;
import com.cloudstar.service.pojo.vo.requestvo.notice.NoticePageRequest;
import com.cloudstar.service.pojo.vo.responsevo.notice.NoticeListResponse;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 公告表service实现
 *
 * <AUTHOR>
 * @description 针对表【sys_m_notice(公告表)】的数据库操作Service实现
 * @createDate 2022-09-20 11:31:14
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
@Slf4j
public class SysMNoticeServiceImpl extends ServiceImpl<SysMNoticeMapper, SysMNotice> implements SysMNoticeService {

    SysMNoticeMapper sysMNoticeMapper;

    BssNoticeService bssNoticeService;

    ConfigService configService;

    @Override
    public PageResult<NoticeListResponse> getNoticeInfoPage(NoticePageRequest noticePageRequest) {
        //若前端不传排序参数，则默认以publishDt降序展示
        if (ObjectUtil.isEmpty(noticePageRequest.getSortDataField()) && ObjectUtil.isEmpty(
                noticePageRequest.getAsc())) {
            noticePageRequest.setSortDataField("publishDt");
            noticePageRequest.setAsc(false);
        }
        //判断是否统一入口 注入资源池采集任务
        String getenv = System.getenv("CFN_MQ_START");
        String configValue = configService.getConfigByConfigKey("operation.platform").getConfigValue();
        log.info("configValue:{}", configValue);
        if ("true".equals(configValue)) {
            List<BssNoticeResp> list = bssNoticeService.getPushNotice(0, 10);
            Page<NoticeListResponse> sysMNoticePage = new Page<>();
            if (CollectionUtil.isNotEmpty(list)) {
                List<NoticeListResponse> noticeList = new ArrayList<>();
                list.stream().forEach(resp -> {
                    NoticeListResponse response = new NoticeListResponse();
                    response.setNoticeTitle(resp.getNoticeTitle());
                    response.setId(resp.getNoticeId());
                    response.setPublishDt(resp.getPublishDate());
                    noticeList.add(response);
                });
                sysMNoticePage.setRecords(noticeList);
            }
            return PageResult.of(sysMNoticePage);
        } else {
            //分页查询公告列表
            Page<SysMNotice> sysMNoticePage = sysMNoticeMapper.selectPage(noticePageRequest.pageRequest(),
                                                                          SpecWrapperUtil.filter(noticePageRequest));

            List<SysMNotice> collect = sysMNoticePage.getRecords()
                                                     .stream()
                                                     .filter(item -> NoticeStatusEnum.PUBLISH.getCode()
                                                                                             .equals(item.getNoticeStatus()))
                                                     .collect(Collectors.toList());
            for (SysMNotice resp : collect) {
                resp.setStatusName(NoticeStatusEnum.getByCode(resp.getNoticeStatus()).getMessage());
            }
            sysMNoticePage.getRecords().clear();
            sysMNoticePage.setRecords(collect);
            return PageResult.of(sysMNoticePage, NoticeListResponse.class);
        }
    }

    @Override
    public SysMNotice getNotice(String id) {
        SysMNotice sysMNotice = sysMNoticeMapper.selectById(id);
        return sysMNotice;
    }
}




