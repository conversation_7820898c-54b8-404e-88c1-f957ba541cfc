package com.cloudstar.service.impl.alarm;


import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.pojo.result.RightCloudResult;
import com.cloudstar.sdk.monitor.client.MonitorClient;
import com.cloudstar.sdk.monitor.form.MorCommonMetricCreateForm;
import com.cloudstar.sdk.monitor.form.MorCommonMetricGetListForm;
import com.cloudstar.sdk.monitor.form.MorCommonMetricGetPageForm;
import com.cloudstar.sdk.monitor.form.MorCommonMetricUpdateForm;
import com.cloudstar.sdk.monitor.result.MorCommonMetricGetDetailsResult;
import com.cloudstar.sdk.monitor.result.MorCommonMetricGetListResult;
import com.cloudstar.sdk.monitor.result.MorCommonMetricGetPageResult;
import com.cloudstar.sdk.monitor.result.PageResult;
import com.cloudstar.service.facade.alarm.MorCommonMetricService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class MorCommonMetricServiceImpl implements MorCommonMetricService {

    private final MonitorClient monitorClient;

    @Override
    public Rest<PageResult<MorCommonMetricGetPageResult>> getCommonMetricPageResult(MorCommonMetricGetPageForm form) {
        return convertToRest(monitorClient.getCommonMetricPageResult(form));
    }

    @Override
    public Rest<List<MorCommonMetricGetListResult>> getCommonMetricListResult(MorCommonMetricGetListForm form) {
        return convertToRest(monitorClient.getCommonMetricListResult(form));
    }

    @Override
    public Rest<MorCommonMetricGetDetailsResult> getCommonMetricDetailsResult(Long id) {
        return convertToRest(monitorClient.getCommonMetricDetailsResult(id));
    }

    @Override
    public Rest<Long> createCommonMetric(MorCommonMetricCreateForm form) {
        return convertToRest(monitorClient.createCommonMetric(form));
    }

    @Override
    public Rest<Boolean> updateCommonMetric(Long id, MorCommonMetricUpdateForm form) {
        return convertToRest(monitorClient.updateCommonMetric(id, form));
    }

    @Override
    public Rest<Boolean> deleteCommonMetric(Long id) {
        return convertToRest(monitorClient.deleteCommonMetric(id));
    }

    @Override
    public Rest<Boolean> deleteCommonMetricInner(List<Long> ids) {
        return convertToRest(monitorClient.deleteCommonMetricInner(ids));
    }

    @Override
    public Rest<Boolean> updateCommonMetricStatus(Long id) {
        return convertToRest(monitorClient.updateCommonMetricStatus(id));
    }


    /**
     * 将 PageResult 转换为 Rest 响应对象
     */
    private <T> Rest<T> convertToRest(RightCloudResult<T> page) {
        Rest<T> rest = new Rest<>();
        rest.setCode(page.getCode());
        rest.setMessage(page.getMessage());
        rest.setData(page.getData());
        rest.setSuccess(page.isSuccess());
        return rest;
    }
}
