package com.cloudstar.service.impl.alarm;


import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.pojo.result.RightCloudResult;
import com.cloudstar.sdk.monitor.client.MonitorClient;
import com.cloudstar.sdk.monitor.result.MorAlertEventsListResult;
import com.cloudstar.sdk.monitor.form.MorAlertEventsPageForm;
import com.cloudstar.sdk.monitor.form.MorAlertEventsProcessForm;
import com.cloudstar.sdk.monitor.result.PageResult;
import com.cloudstar.service.facade.alarm.MorAlertEventsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class MorAlertEventsServiceImpl implements MorAlertEventsService {


    private final MonitorClient monitorClient;

    @Override
    public Rest<MorAlertEventsListResult> info(String hash) {
        return convertToRest(monitorClient.info(hash));
    }

    @Override
    public Rest<PageResult<MorAlertEventsListResult>> page(MorAlertEventsPageForm form) {
        return convertToRest(monitorClient.page(form));
    }

    @Override
    public Rest<Void> export(MorAlertEventsPageForm form) {
        return convertToRest(monitorClient.export(form));
    }

    @Override
    public Rest<Boolean> process(MorAlertEventsProcessForm form, String method) {
        return convertToRest(monitorClient.process(form, method));
    }

    @Override
    public Rest<Boolean> clear(String hashList) {
        return convertToRest(monitorClient.clear(hashList));
    }

    /**
     * 将 PageResult 转换为 Rest 响应对象
     */
    private <T> Rest<T> convertToRest(RightCloudResult<T> page) {
        Rest<T> rest = new Rest<>();
        rest.setCode(page.getCode());
        rest.setMessage(page.getMessage());
        rest.setData(page.getData());
        rest.setSuccess(page.isSuccess());
        return rest;
    }
}
