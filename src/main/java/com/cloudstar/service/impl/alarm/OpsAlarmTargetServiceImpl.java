package com.cloudstar.service.impl.alarm;

import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.pojo.result.RightCloudResult;
import com.cloudstar.sdk.monitor.client.MonitorClient;
import com.cloudstar.sdk.monitor.result.OpsCategoryPropertiesGetListResult;
import com.cloudstar.service.facade.alarm.OpsAlarmTargetService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;



@Service
@RequiredArgsConstructor
@Slf4j
public class OpsAlarmTargetServiceImpl implements OpsAlarmTargetService {

    private final MonitorClient monitorClient;

    @Override
    public Rest<OpsCategoryPropertiesGetListResult> getAlarmTargetData(String code) {
        return convertToRest(monitorClient.getAlarmTargetData(code));
    }

    /**
     * 将 PageResult 转换为 Rest 响应对象
     */
    private <T> Rest<T> convertToRest(RightCloudResult<T> page) {
        Rest<T> rest = new Rest<>();
        rest.setCode(page.getCode());
        rest.setMessage(page.getMessage());
        rest.setData(page.getData());
        rest.setSuccess(page.isSuccess());
        return rest;
    }
}
