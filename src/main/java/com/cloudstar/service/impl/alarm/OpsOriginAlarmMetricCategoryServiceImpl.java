package com.cloudstar.service.impl.alarm;

import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.pojo.result.RightCloudResult;
import com.cloudstar.sdk.monitor.client.MonitorClient;
import com.cloudstar.sdk.monitor.form.OpsOriginAlarmMetricCategoryCreateForm;
import com.cloudstar.sdk.monitor.form.OpsOriginAlarmMetricCategoryTreeFrom;
import com.cloudstar.sdk.monitor.form.OpsOriginAlarmMetricCategoryUpdateForm;
import com.cloudstar.sdk.monitor.result.OpsOriginAlarmMetricCategoryDetailsResult;
import com.cloudstar.sdk.monitor.result.OpsOriginAlarmMetricCategoryTreeListResult;
import com.cloudstar.service.facade.alarm.OpsOriginAlarmMetricCategoryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Service
@RequiredArgsConstructor
@Slf4j
public class OpsOriginAlarmMetricCategoryServiceImpl implements OpsOriginAlarmMetricCategoryService {

    private final MonitorClient monitorClient;


    @Override
    public Rest<Long> createOriginAlarmMetricCategory(OpsOriginAlarmMetricCategoryCreateForm form) {
        return convertToRest(monitorClient.createOriginAlarmMetricCategory(form));
    }

    @Override
    public Rest<Boolean> updateOriginAlarmMetricCategory(Long id, OpsOriginAlarmMetricCategoryUpdateForm form) {
        return convertToRest(monitorClient.updateOriginAlarmMetricCategory(id, form));
    }

    @Override
    public Rest<Boolean> deleteOriginAlarmMetricCategory(Long id) {
        return convertToRest(monitorClient.deleteOriginAlarmMetricCategory(id));
    }

    @Override
    public Rest<OpsOriginAlarmMetricCategoryDetailsResult> getOriginAlarmMetricCategoryDetails(Long id) {
        return convertToRest(monitorClient.getOriginAlarmMetricCategoryDetails(id));
    }

    @Override
    public Rest<List<OpsOriginAlarmMetricCategoryTreeListResult>> getOriginAlarmMetricCategoryList(
            OpsOriginAlarmMetricCategoryTreeFrom from) {
        return convertToRest(monitorClient.getOriginAlarmMetricCategoryList(from));
    }


    /**
     * 将 PageResult 转换为 Rest 响应对象
     */
    private <T> Rest<T> convertToRest(RightCloudResult<T> page) {
        Rest<T> rest = new Rest<>();
        rest.setCode(page.getCode());
        rest.setMessage(page.getMessage());
        rest.setData(page.getData());
        rest.setSuccess(page.isSuccess());
        return rest;
    }
}
