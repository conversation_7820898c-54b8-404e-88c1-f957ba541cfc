package com.cloudstar.service.impl.bill;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.common.base.enums.BizBillEnum;
import com.cloudstar.common.base.enums.ProductComponentEnum;
import com.cloudstar.dao.mapper.bill.HwsSdrDrpDataMapper;
import com.cloudstar.dao.model.bill.BizBillUsageItem;
import com.cloudstar.dao.model.bill.HwsSdrDrpData;
import com.cloudstar.service.facade.bill.BizBillUsageItemService;
import com.cloudstar.service.facade.bill.HwsSdrDrpDataService;
import com.cloudstar.service.grpc.AgentBillCollectProto.AgentBillResponse;
import com.cloudstar.service.grpcservice.facade.AgentBillCollectService;
import com.cloudstar.service.pojo.dto.cluster.ClusterUserMappingBillDto;
import com.cloudstar.service.utils.ProtoJsonUtils;
import com.google.common.util.concurrent.ThreadFactoryBuilder;

import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TimeZone;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import io.grpc.stub.StreamObserver;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;


/**
 * impl探测sdr组成数据服务
 *
 * <AUTHOR>
 * @date 2022/11/25
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class HwsSdrDrpDataServiceImpl extends ServiceImpl<HwsSdrDrpDataMapper, HwsSdrDrpData>
        implements HwsSdrDrpDataService {

    BizBillUsageItemService usageItemService;

    private static final String BROKER_CACHE = "cache";

    /**
     * 同步数据
     *
     * @param users 用户
     */
    @Override
    public void syncData(List<ClusterUserMappingBillDto> users) {
        log.info("开始同步Modelarts专属资源池话单数据");
        final CountDownLatch maxCountDown = new CountDownLatch(users.size());
        final int poolSize = 5;
        ExecutorService singleThreadPool = new ThreadPoolExecutor(poolSize, poolSize, 20L, TimeUnit.MILLISECONDS,
                                                                  new LinkedBlockingQueue<>(1024),
                                                                  new ThreadFactoryBuilder().setNameFormat(
                                                                          "hws-bill-modelarts-pool-%d").build());
        Map<String, List<ClusterUserMappingBillDto>> collect = users.stream()
                                                                    .collect(Collectors.groupingBy(
                                                                            ClusterUserMappingBillDto::getAdapterUuid));

        collect.forEach((key, value) -> {
            AgentBillCollectService service = AgentBillCollectService.build(key);
            value.forEach(dto -> {
                singleThreadPool.execute(() -> {
                    if (Objects.isNull(dto.getStartTime())) {
                        List<HwsSdrDrpData> list = this.lambdaQuery()
                                                       .eq(HwsSdrDrpData::getAccountUuid, dto.getUserId())
                                                       .orderByDesc(HwsSdrDrpData::getTimeStamp)
                                                       .last("limit 1")
                                                       .list();

                        if (CollectionUtil.isNotEmpty(list)) {
                            dto.setStartTime(Long.valueOf(list.get(0).getTimeStamp()));
                        }
                    }
                    ClusterUserMappingBillDto req = new ClusterUserMappingBillDto();
                    req.setUserId(dto.getUserId());
                    req.setStartTime(dto.getStartTime());
                    req.setEndTime(dto.getEndTime());
                    List<HwsSdrDrpData> dataList = new ArrayList<>();
                    List<BizBillUsageItem> billItemList = new ArrayList<>();
                    try {
                        StreamObserver<AgentBillResponse> responseData = new StreamObserver<>() {
                            @Override
                            public void onNext(AgentBillResponse response) {
                                String jsonStr = ProtoJsonUtils.toJson(response);
                                JSONObject entries = JSONUtil.parseObj(jsonStr);
                                HwsSdrDrpData data = BeanUtil.copyProperties(entries, HwsSdrDrpData.class);
                                if (StrUtil.startWithAnyIgnoreCase(data.getResourceId(), BROKER_CACHE)) {
                                    return;
                                }
                                if (StrUtil.equals(req.getType(), "DRP")) {
                                    log.error("DRP话单类型错误,请求类型：【{}】", req.getType());
                                }
                                data.setBillFlag("Y");
                                data.setUserId(dto.getUserSid());
                                data.setAccountUuid(dto.getUserId());
                                data.setAdapterUuid(key);
                                dataList.add(data);
                                BizBillUsageItem billUsageItem = buildBillItem(response, dto.getUserSid(), key);
                                if (Objects.nonNull(billUsageItem)) {
                                    billItemList.add(billUsageItem);
                                }
                            }

                            @Override
                            public void onError(Throwable t) {
                                // 回滚数据
                                rollBack(dataList, billItemList);
                            }

                            @Override
                            public void onCompleted() {
                                if (CollectionUtil.isNotEmpty(dataList)) {
                                    try {
                                        saveBatch(dataList, 200);
                                        // 出账
                                        usageItemService.saveBatch(billItemList, 200);
                                    } catch (Exception e) {
                                        // 回滚数据
                                        rollBack(dataList, billItemList);
                                    }

                                }
                            }
                        };
                        req.setType("DRP");
                        service.getAgentBillByUser(req, responseData);
                    } catch (Exception e) {
                        log.error("同步modelArts专属紫资源池话单出错", e);
                    } finally {
                        maxCountDown.countDown();
                    }
                });

            });
        });
        singleThreadPool.shutdown();
        try {
            maxCountDown.await();
        } catch (Exception e) {
            log.error("同步modelArts专属紫资源池话单出错", e);
        }
    }

    /**
     * 回滚
     *
     * @param dataList 数据列表
     * @param billItemList 比尔项目列表
     */
    private void rollBack(List<HwsSdrDrpData> dataList, List<BizBillUsageItem> billItemList) {
        List<Long> dataIds = dataList.stream()
                                     .map(HwsSdrDrpData::getId)
                                     .collect(Collectors.toList());
        List<Long> itemIds = billItemList.stream()
                                         .map(BizBillUsageItem::getId)
                                         .collect(Collectors.toList());
        removeByIds(dataIds);
        usageItemService.removeByIds(itemIds);
    }

    /**
     * 构建计量明细
     *
     * @param response 响应
     * @param userSid 用户sid
     * @param adapterUUid 适配器uuid
     *
     * @return {@link BizBillUsageItem}
     */
    private BizBillUsageItem buildBillItem(AgentBillResponse response, Long userSid, String adapterUUid) {
        if (StrUtil.isEmpty(response.getResourceId())) {
            return null;
        }
        if (StrUtil.isEmpty(response.getAccumulateFactorValue()) || StrUtil.equals(response.getAccumulateFactorValue(),
                                                                                   "0")) {
            return null;
        }
        String value = response.getAccumulateFactorValue();
        return BizBillUsageItem.builder()
                               .billNo(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSSSS")))
                               .billingCycle(getBillingCycle(response.getBeginTime()))
                               .jobId(response.getResourceId())
                               .configuration(response.getResourceSpecCode())
                               .ownerSid(userSid)
                               .product(ProductComponentEnum.DEDICATEDRESOURCEPOOL.getKey().get(0))
                               .summaryFlag("N")
                               .usageCount(new BigDecimal(value))
                               .usageStartDate(formatDate(response.getBeginTime()))
                               .usageEndDate(formatDate(response.getEndTime()))
                               .billType(BizBillEnum.RESOURCE_BILLING.getType())
                               .adapterUuid(adapterUUid)
                               .build();
    }


    private Date formatDate(String time) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        dateFormat.setTimeZone(TimeZone.getTimeZone("UTC"));
        DateTime dateTime;
        try {
            dateTime = new DateTime(dateFormat.parse(time));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        return dateTime;
    }

    /**
     * 获取账单周期
     *
     * @param billingCycle 账单日期
     */
    private String getBillingCycle(String billingCycle) {
        DateTime parse = DateUtil.parse(billingCycle, DatePattern.PURE_DATETIME_PATTERN);
        return DateUtil.format(parse, DatePattern.NORM_MONTH_PATTERN);
    }
}
