package com.cloudstar.service.impl;

import com.cloudstar.dao.model.SysDict;
import com.cloudstar.sdk.schedule.client.ScheduleClient;
import com.cloudstar.service.facade.SysDictService;
import com.cloudstar.service.facade.TestService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import org.springframework.stereotype.Service;

import java.util.List;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 测试服务impl
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
public class TestServiceImpl implements TestService {

    SysDictService sysDictService;
    ObjectMapper objectMapper;
    ScheduleClient scheduleClient;

    @Override
    public String test() {
        List<SysDict> list = sysDictService.list();
        try {
            return objectMapper.writeValueAsString(list);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Object callSchedule() {
        log.info("调用testScheduleClient方法");
        return scheduleClient.testSchedule();
    }
}
