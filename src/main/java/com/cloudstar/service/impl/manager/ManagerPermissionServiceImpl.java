package com.cloudstar.service.impl.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.dao.model.manager.ManagerPermission;
import com.cloudstar.service.facade.manager.ManagerPermissionService;
import com.cloudstar.dao.mapper.manager.ManagerPermissionMapper;
import org.springframework.stereotype.Service;

/**
 * 管理员权限服务impl
 *
 * <AUTHOR>
 * @description 针对表【manager_permission(运营运维权限表)】的数据库操作Service实现
 * @createDate 2022-08-10 11:04:13
 * @date 2022/08/10
 */
@Service
public class ManagerPermissionServiceImpl extends ServiceImpl<ManagerPermissionMapper, ManagerPermission>
        implements ManagerPermissionService {

}




