package com.cloudstar.service.impl.manager;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.dao.model.manager.ManagerRoleMenu;
import com.cloudstar.service.facade.manager.ManagerRoleMenuService;
import com.cloudstar.dao.mapper.manager.ManagerRoleMenuMapper;
import org.springframework.stereotype.Service;

/**
 * 管理员角色菜单服务impl
 *
 * <AUTHOR>
 * @description 针对表【manager_role_menu(运营运维角色与菜单表)】的数据库操作Service实现
 * @createDate 2022-08-10 11:05:11
 * @date 2022/08/10
 */
@Service
public class ManagerRoleMenuServiceImpl extends ServiceImpl<ManagerRoleMenuMapper, ManagerRoleMenu>
        implements ManagerRoleMenuService {

}




