package com.cloudstar.service.impl.sdr;

import com.cloudstar.common.base.enums.ProductComponentEnum;
import com.cloudstar.componet.ClusterAttrUtil;
import com.cloudstar.dao.mapper.SysMCodeMapper;
import com.cloudstar.dao.model.bill.BizBillUsageItem;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import com.cloudstar.service.facade.datastorage.SlurmAossService;
import com.cloudstar.service.facade.sdr.SdrExportService;
import com.cloudstar.service.pojo.dto.bill.ProductTypeDetail;
import com.obs.services.model.HttpMethodEnum;

import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveOutputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorOutputStream;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestMethod;

import java.io.BufferedOutputStream;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * sdr导出服务impl
 *
 * <AUTHOR>
 * @date 2024/07/18
 */
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Service
public class SdrExportServiceImpl implements SdrExportService {

    public static final ClusterSubAccountRespDto DEFAULT_SUB_ACCOUNT = new ClusterSubAccountRespDto();
    ClusterAttrUtil clusterAttrUtil;
    //产品类型map
    private static final Map<ProductComponentEnum, ProductTypeDetail> PRODUCT_TYPE_MAP = Map.of(
            ProductComponentEnum.BMS_NOTEBOOK, new ProductTypeDetail("cfn.service.type.ai-bms",
                                                                     "cfn.service.type.ai-bms",
                                                                     "duration",
                                                                     "notebook"),
            ProductComponentEnum.BMS_OBS, new ProductTypeDetail("cfn.service.type.ai-bms-obs",
                                                                "cfn.service.type.ai-bms-obs",
                                                                "size",
                                                                "obs"),
            ProductComponentEnum.BMS_JOB, new ProductTypeDetail("cfn.service.type.ai-bms",
                                                                "cfn.service.type.ai-bms",
                                                                "duration",
                                                                "job"),
            ProductComponentEnum.BMS_STORAGE, new ProductTypeDetail("cfn.service.type.ai-bms",
                                                                    "cfn.service.type.ai-storage",
                                                                    "size",
                                                                    "storage")
    );
    private static final String REGION_CODE = "cn-north-4";
    private static final String USER_ROOT_PATH = System.getProperty("user.home") + "/";
    SlurmAossService slurmAossService;
    SysMCodeMapper sysMCodeMapper;

    /**
     * 时间取整并转为utc
     *
     * @param time 时间
     *
     * @return {@link DateTime }
     */
    public static DateTime roundTime(DateTime time) {
        int minute = time.minute();
        if (minute >= 30) {
            time = DateUtil.offsetHour(time, 1);
        }
        DateTime dateTime = DateUtil.beginOfHour(time);
        //转为utc
        return toUtc(dateTime);
    }

    /**
     * 到utc
     *
     * @param dateTime 日期时间
     *
     * @return {@link DateTime }
     */
    private static DateTime toUtc(DateTime dateTime) {
        ZonedDateTime zonedDateTime = dateTime.toLocalDateTime().atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneOffset.UTC);
        return new DateTime(zonedDateTime);
    }

    /**
     * 创建csv文件
     *
     * @param dir dir
     * @param fileName 文件名
     * @param data 数据
     *
     * @throws IOException IOException
     */
    private static void createCsvFile(String dir, String fileName, List<String> data) throws IOException {
        Path filePath = Paths.get(dir, fileName);
        try (BufferedWriter writer = Files.newBufferedWriter(filePath)) {
            for (String line : data) {
                writer.write(line);
                writer.newLine();
            }
        }
    }

    /**
     * 压缩嵌套zip文件
     *
     * @param dir dir
     * @param zipFileName zip文件名
     *
     * @return {@link Path }
     *
     * @throws IOException IOException
     */
    private static Path compressNestedZipFiles(String dir, String zipFileName, String sdrPath) throws IOException {
        Path firstZip = compressAllFilesInFolder(dir.substring(0, dir.lastIndexOf("/")), zipFileName, sdrPath);
        Path path = compressFilesIntoZip(dir, zipFileName, zipFileName, firstZip);
        Files.delete(firstZip);
        return path;
    }

    /**
     * 将文件压缩成zip
     *
     * @param dir dir
     * @param zipFileName zip文件名
     *
     * @return {@link Path }
     *
     * @throws IOException IOException
     */
    private static Path compressAllFilesInFolder(String dir, String zipFileName, String sourceDir) throws IOException {
        Path zipFilePath = Paths.get(dir, zipFileName);
        try (ZipOutputStream zipOut = new ZipOutputStream(Files.newOutputStream(zipFilePath))) {
            Path sourcePath = Paths.get(sourceDir);
            try (Stream<Path> paths = Files.walk(sourcePath);) {
                for (Path path : (Iterable<Path>) paths::iterator) {
                    ZipEntry zipEntry = new ZipEntry(sourcePath.relativize(path).toString());
                    if (!Files.isDirectory(path)) {
                        zipOut.putNextEntry(zipEntry);
                        Files.copy(path, zipOut);
                        Files.delete(path);
                        zipOut.closeEntry();
                    }
                }
                zipOut.finish();
            }
        }

        return zipFilePath;
    }

    /**
     * 将文件压缩成zip
     *
     * @param dir dir
     * @param zipFileName zip文件名
     * @param fileName 文件名
     * @param filePath 文件路径
     *
     * @return {@link Path }
     *
     * @throws IOException IOException
     */
    private static Path compressFilesIntoZip(String dir, String zipFileName, String fileName, Path filePath) throws IOException {
        Path zipFilePath = Paths.get(dir, zipFileName);
        try (ZipOutputStream zipOut = new ZipOutputStream(Files.newOutputStream(zipFilePath))) {
            ZipEntry zipEntry = new ZipEntry(fileName);
            zipOut.putNextEntry(zipEntry);
            if (!zipEntry.isDirectory()) {
                Files.copy(filePath, zipOut);
            }
            zipOut.closeEntry();
        }
        return zipFilePath;
    }

    /**
     * 删除目录和文件
     *
     * @param file 文件
     */
    private static void deleteDirectory(File file) {
        if (file.isDirectory()) {
            for (File subFile : file.listFiles()) {
                deleteDirectory(subFile);
            }
        }
        file.delete();
    }

    private static void spikSsl() {
        // 创建一个信任所有证书的 TrustManager
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {

                    }

                    @Override

                    public void checkServerTrusted(X509Certificate[] certs, String authType) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                }
        };
        SSLContext sc = null;

        try {
            // 创建一个忽略证书验证的 SSLContext
            sc = SSLContext.getInstance("SSL");
            sc.init(null, trustAllCerts, new SecureRandom());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
        HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
    }

    /**
     * 将目录压缩到targz
     *
     * @param byteArrayOutputStream 字节数组输出流
     * @param sourceDir 源目录
     *
     * @throws IOException IOException
     */
    private static void compressDirectoryToTarGz(OutputStream byteArrayOutputStream, String sourceDir) throws IOException {
        try (
                BufferedOutputStream bos = new BufferedOutputStream(byteArrayOutputStream);
                GzipCompressorOutputStream gcos = new GzipCompressorOutputStream(bos);
                TarArchiveOutputStream taos = new TarArchiveOutputStream(gcos)) {

            Path sourcePath = Paths.get(sourceDir);

            try (Stream<Path> paths = Files.walk(sourcePath)) {
                for (Path path : (Iterable<Path>) paths::iterator) {
                    TarArchiveEntry tarEntry = new TarArchiveEntry(path.toFile(), sourcePath.relativize(path).toString());
                    try {
                        taos.putArchiveEntry(tarEntry);
                        if (!Files.isDirectory(path)) {
                            Files.copy(path, taos);
                        }
                        taos.closeArchiveEntry();
                    } catch (IOException e) {
                        log.error("压缩为tar.gz 失败: {}", e.getMessage());
                    }
                }
                taos.finish();
            }

        }
    }

    /**
     * 将目录压缩到targz
     *
     * @param tarGzFileName tar gz文件名
     * @param sourceDir 源目录
     *
     * @throws IOException IOException
     */
    private static void compressDirectoryToTarGz(String tarGzFileName, String sourceDir) throws IOException {
        FileOutputStream fos = new FileOutputStream(tarGzFileName);
        try (
                BufferedOutputStream bos = new BufferedOutputStream(fos);
                GzipCompressorOutputStream gcos = new GzipCompressorOutputStream(bos);
                TarArchiveOutputStream taos = new TarArchiveOutputStream(gcos)) {

            Path sourcePath = Paths.get(sourceDir);

            try (Stream<Path> paths = Files.walk(sourcePath);) {

                for (Path path : (Iterable<Path>) paths::iterator) {
                    TarArchiveEntry tarEntry = new TarArchiveEntry(path.toFile(), sourcePath.relativize(path).toString());
                    try {
                        taos.putArchiveEntry(tarEntry);
                        if (!Files.isDirectory(path)) {
                            Files.copy(path, taos);
                        }
                        taos.closeArchiveEntry();
                    } catch (IOException e) {
                        System.err.printf("压缩为tar.gz 失败: %s%n", e.getMessage());
                    }
                }
                taos.finish();
            }

        }
    }

    /**
     * init
     */
    public void init(Long clusterId) {
        spikSsl();

        DEFAULT_SUB_ACCOUNT.setAccessKey(clusterAttrUtil.getSdrAk(clusterId));
        DEFAULT_SUB_ACCOUNT.setSecretKey(clusterAttrUtil.getSdrSk(clusterId));
        DEFAULT_SUB_ACCOUNT.setObsUrl(clusterAttrUtil.getSdrAddress(clusterId));
        DEFAULT_SUB_ACCOUNT.setBucketName(clusterAttrUtil.getSdrBucketName(clusterId));
    }

    @Override
    public void exportSdr(Long clusterId, List<BizBillUsageItem> billDataList, Map<Long, String> accountMap) {
        exportSdr(clusterId, billDataList, accountMap, DEFAULT_SUB_ACCOUNT);
    }

    @Override
    public void exportSdr(Long clusterId, List<BizBillUsageItem> billDataList, Map<Long, String> accountMap,
                          ClusterSubAccountRespDto clusterSubAccountRespDto) {
        try {
            init(clusterId);
            //防止空指针
            billDataList = Objects.nonNull(billDataList) ? billDataList : List.of();
            accountMap = Objects.nonNull(accountMap) ? accountMap : Map.of();
            //根据产品类型分组 key:产品类型 value:账单数据列表
            Map<String, List<BizBillUsageItem>> sdrProductMap = billDataList.stream().collect(Collectors.groupingBy(BizBillUsageItem::getProduct));
            log.info("开始导出话单数据到OBS, 账单数据条数: {},产品类型: {}", billDataList.size(), JSONUtil.toJsonStr(sdrProductMap.keySet()));
            if (CollUtil.isEmpty(sdrProductMap)) {
                log.warn("账单数据为空，mock空数据");
                sdrProductMap = Map.of(ProductComponentEnum.BMS_NOTEBOOK.getKey().get(0), new ArrayList<>());
            }
            recordFiling(sdrProductMap, accountMap, clusterSubAccountRespDto);
            log.info("导出话单数据到OBS完成");
        } catch (Exception e) {
            log.error("话单保存OBS失败:", e);
        }
    }

    /**
     * 构建话单数据
     *
     * @param billDataList 账单数据列表
     * @param accountMap 账户ID-账户名称映射
     * @param typeDetail 产品类型详细信息
     *
     * @return {@link List }<{@link List }<{@link String }>>
     */
    private List<String> buildSdrData(List<BizBillUsageItem> billDataList, Map<Long, String> accountMap,
                                      ProductTypeDetail typeDetail) {
        // 构建话单数据
        List<String> sdr = new ArrayList<>();
        String formatted = DateUtil.format(roundTime(new DateTime()), "yyyyMMddHHmmss");
        sdr.add(String.format("10|%s|01|text", formatted));
        for (BizBillUsageItem billData : billDataList) {
            sdr.add(String.format("20|%s|%s|%s|-|%s|%s|%s|%s|-|%s|%s|%s|%s|%s|%s|%s",
                                  formatted, accountMap.get(billData.getOwnerSid()), REGION_CODE,
                                  sysMCodeMapper.getCloudServiceByType(billData.getProduct()),
                                  sysMCodeMapper.getResourceByType(billData.getProduct()),
                                  billData.getConfiguration(), billData.getJobId(),
                                  billData.getProduct(), typeDetail.getProductType(), typeDetail.getResourceCode(),
                                  DateUtil.format(billData.getUsageStartDate(), DatePattern.PURE_DATETIME_PATTERN),
                                  DateUtil.format(billData.getUsageEndDate(), DatePattern.PURE_DATETIME_PATTERN),
                                  typeDetail.getAccumulateFactorName(), Optional.ofNullable(billData.getUsageCount()).orElse(BigDecimal.ZERO)));
        }
        sdr.add(String.format("90|%s|10", formatted));
        return sdr;
    }

    /**
     * 记录账单
     */
    public void recordFiling(Map<String, List<BizBillUsageItem>> sdrProductMap, Map<Long, String> accountMap,
                             ClusterSubAccountRespDto clusterSubAccount) {
        try {

            // 获取当前时间并格式化
            String timestamp = DateUtil.format(roundTime(new DateTime()), DatePattern.PURE_DATETIME_PATTERN);
            final String baseDir = USER_ROOT_PATH + "cdr-" + timestamp.substring(0, 12);
            final String tarGzFileName = baseDir + ".tar.gz";
            final String bakfilesDir = baseDir + "/bakfiles";

            // 创建目录
            String bmsDir = bakfilesDir + "/bms";
            Files.createDirectories(Paths.get(bmsDir));

            sdrProductMap.forEach((productType, sdrData) -> {
                try {
                    ProductComponentEnum productComponentEnum = ProductComponentEnum.getEnumByKey(productType);
                    if (productComponentEnum == null) {
                        log.warn("未知产品类型: {}", productType);
                        return;
                    }
                    ProductTypeDetail typeDetail = PRODUCT_TYPE_MAP.get(productComponentEnum);
                    List<String> sdrDateList = buildSdrData(sdrData, accountMap, typeDetail);

                    // 为每个产品类型创建单独目录
                    //String productDir = bmsDir + "/" + timestamp + "/" + typeDetail.getFileName();
                    //Files.createDirectories(Paths.get(productDir));

                    // 创建 CSV 文件
                    String fileName = "CFN_bms_service." + typeDetail.getFileName() + "_" + timestamp + ".csv";
                    createCsvFile(bmsDir, fileName, sdrDateList);

                    // 压缩 CSV 文件为 ZIP 文件并嵌套
                    String zipFileName = "CFN_bms_service." + typeDetail.getFileName() + "_" + timestamp + ".zip";
                    compressNestedZipFiles(bmsDir, zipFileName, bmsDir);

                    //生成md5校验文件
                    generateMd5File(bmsDir + "/" + zipFileName);

                } catch (Exception e) {
                    log.error("账单导出失败", e);
                    return;
                }
            });

            // 将整个 bakfiles 目录压缩为 tar.gz
            compressDirectoryToTarGz(tarGzFileName, baseDir);
            log.info("tar.gz 文件创建成功: {}", tarGzFileName);
            // 将流输出到指定目录
            String outputFilePath = "cn-central" + "/" + timestamp.substring(0, 10) + "/" + baseDir.replace(USER_ROOT_PATH, "") + ".tar.gz";

            //上传到 OBS
            uploadFiles(outputFilePath, baseDir, clusterSubAccount);
            // 清理目录
            deleteDirectory(Paths.get(baseDir).toFile());
        } catch (Exception e) {
            log.error("账单导出失败", e);
        }
    }

    /**
     * 生成MD5校验文件
     */
    private void generateMd5File(String zipFilePath) {
        File zipFile = new File(zipFilePath);
        if (!zipFile.exists()) {
            log.error("ZIP文件不存在: {}", zipFilePath);
            throw new IllegalStateException("ZIP文件不存在");
        }

        try {
            // 计算MD5摘要
            String md5 = SecureUtil.md5(zipFile);
            log.info("ZIP文件: {}, MD5: {}", zipFile.getName(), md5);

            // 生成.sha校验文件
            String shaFilePath = zipFilePath + ".sha";
            String content = String.format("ID:%s\nFile_Digest:%s", "********", md5);
            try (FileWriter writer = new FileWriter(shaFilePath)) {
                writer.write(content);
            }
            log.info("生成校验文件成功: {}", shaFilePath);
        } catch (IOException e) {
            log.error("生成校验文件失败: {}", zipFilePath, e);
            throw new RuntimeException("生成校验文件失败", e);
        }
    }

    /**
     * 上传文件
     *
     * @param outputFilePath 输出文件路径
     * @param bakfilesDir bakfiles目录
     */
    private void uploadFiles(String outputFilePath, String bakfilesDir, ClusterSubAccountRespDto clusterSubAccount) {
        String resignedUrl = slurmAossService.getObsTemporaryVoucher(clusterSubAccount, outputFilePath, HttpMethodEnum.PUT.getOperationType());
        try {
            URL url = new URL(resignedUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod(RequestMethod.PUT.name());
            connection.setDoOutput(true);
            // 上传文件内容
            compressDirectoryToTarGz(connection.getOutputStream(), bakfilesDir);

            int responseCode = connection.getResponseCode();
            if (responseCode == HttpURLConnection.HTTP_OK) {
                log.info("sdr upload success. Response code: {}", responseCode);
            } else {
                log.error("sdr upload failed. Response code: {}", responseCode);
            }
        } catch (Exception e) {
            log.error("上传文件失败", e);
        }
    }

}
