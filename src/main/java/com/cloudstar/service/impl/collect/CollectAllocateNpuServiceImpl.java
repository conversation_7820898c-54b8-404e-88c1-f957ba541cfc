package com.cloudstar.service.impl.collect;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.dao.mapper.collect.CollectAllocateNpuMapper;
import com.cloudstar.dao.model.collect.CollectAllocateNpu;
import com.cloudstar.service.facade.collect.CollectAllocateNpuService;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

@Service
@DS("monitor")
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class CollectAllocateNpuServiceImpl extends ServiceImpl<CollectAllocateNpuMapper, CollectAllocateNpu> implements
        CollectAllocateNpuService {

    CollectAllocateNpuMapper collectAllocateNpuMapper;

    @Override
    public Integer selectUnusedNpu(Long clusterId, String poolId) {
        return collectAllocateNpuMapper.selectUnusedNpu(clusterId, poolId);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void dataCleanFromWeekAgo() {
        //查询一周前的每日平均值
        final List<CollectAllocateNpu> npus = collectAllocateNpuMapper.selectDayAvgNpu();
        log.info("7天前每日npu平均值数据:{}", npus);
        if (CollectionUtil.isNotEmpty(npus)) {
            //删除一周前的所有数据
            final Integer integer = collectAllocateNpuMapper.deleteWeekAgoData();
            log.info("7天前npu数据已删除:{}条", integer);
            //批量新增数据
            List<List<CollectAllocateNpu>> partition = ListUtil.partition(npus, 500);
            if (CollectionUtil.isNotEmpty(partition)) {
                partition.stream().forEach(list -> {
                    //批量新增
                    this.saveBatch(list);
                    log.info("已批量新增{}条npu平均值数据", list.size());
                });
            }
        }

    }
}