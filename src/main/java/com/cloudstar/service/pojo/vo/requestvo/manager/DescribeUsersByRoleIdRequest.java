package com.cloudstar.service.pojo.vo.requestvo.manager;

import java.util.List;

import lombok.Data;

/**
 * 描述用户通过角色id请求
 *
 * <AUTHOR>
 * Created on 2022/8/12
 * @date 2022/08/12
 */
@Data
public class DescribeUsersByRoleIdRequest {
    /**
     * 角色ID
     */
    private Long roleSid;
    /**
     * 查询范围包括[姓名|用户名]
     */
    private String searchFilter;

    /**
     * 状态
     */
    private String status;

    private List<Long> roleIds;
}
