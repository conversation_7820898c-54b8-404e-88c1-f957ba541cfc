package com.cloudstar.service.pojo.vo.requestvo.res;

import com.cloudstar.common.util.page.PageForm;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 创建模型请求体
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ModelPageRequest extends PageForm {

    /**
     * 模型名称
     */
    private String name;

    /**
     * 筛选开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 筛选结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
