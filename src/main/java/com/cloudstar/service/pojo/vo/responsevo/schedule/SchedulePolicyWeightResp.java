package com.cloudstar.service.pojo.vo.responsevo.schedule;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 权重返回值
 *
 * <AUTHOR>
 * @date 2022/9/14 10:16
 */
@Data
public class SchedulePolicyWeightResp {

    /**
     * 集群id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 集群名称
     */
    private String clusterType;

    /**
     * 权重
     */
    private Integer weight;

    /**
     * 其他信息
     */
    private Object otherInfo;

}
