package com.cloudstar.service.pojo.vo.responsevo.manager;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.List;

import lombok.Data;

/**
 * 管理器菜单树反应
 *
 * <AUTHOR>
 * Created on 2022/8/18
 * @date 2022/08/19
 */
@Data
public class ManagerMenuTreeResponse {
    /**
     * 菜单id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long menuSid;

    /**
     * 菜单编码
     */
    private String code;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 菜单英文名称
     */
    private String engName;

    /**
     * 菜单类型
     */
    private String menuType;

    /**
     * 菜单图标
     */
    private String ico;

    /**
     * 路由类型;路由:ROUTE   外联:OUTREACH   页面嵌入:PAGE_EMBEDDING
     */
    private String routeType;

    /**
     * 路由地址
     */
    private String routePath;

    /**
     * 状态;启用:ENABLE  禁用:DISABLED
     */
    private String status;
    /**
     * 状态;启用:ENABLE  禁用:DISABLED
     */
    private String statusName;

    /**
     * 描述
     */
    private String description;

    /**
     * 父级id, 默认0
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 下级关系链表ids
     */
    private String lowerPathIds;

    /**
     * 类型;SYSTEM：系统内置，customize：自定义
     */
    private String type;

    /**
     * 排序
     */
    private Long sort;

    /**
     * 是否有子菜单
     */
    private boolean child;

    /**
     * 是否显示
     */
    private String display;

    /**
     * 子菜单
     */
    private List<ManagerMenuTreeResponse> childMenu;


}
