package com.cloudstar.service.pojo.vo.responsevo.tenant;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 镜像信息表
 * <AUTHOR>
 * @TableName res_image
 */
@Data
public class ResImageResponse implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long ownerId;

    /**
     * 组织ID
     */
    private Long orgSid;

    /**
     * 底层资源ID
     */
    private String uuid;

    /**
     * 镜像名称
     */
    private String name;

    /**
     * 使用类型
     */
    private String useType;

    /**
     * 标签
     */
    private String label;

    /**
     * 所属仓库
     */
    private String registry;
    /**
     * 镜像tab名称
     */
    private String tag;

    /**
     * 镜像拉取地址
     */
    private String path;

    /**
     * 镜像Hash值
     */
    private String digest;

    /**
     * 版本数
     */
    private String version;

    /**
     * 已使用空间
     */
    private String usedCapacity;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

}
