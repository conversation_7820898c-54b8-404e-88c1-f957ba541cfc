package com.cloudstar.service.pojo.vo.requestvo.manager;

import com.cloudstar.common.base.constant.UserAuthStatusEnum;
import com.cloudstar.common.base.constant.UserStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;


import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import lombok.Data;
import lombok.experimental.FieldDefaults;

import org.hibernate.validator.constraints.Length;

@Data
@FieldDefaults(level = lombok.AccessLevel.PRIVATE)
public class UpdateManagerReq {


    /**
     * 用户名
     */
    @Length(max = 64)
    String account;

    /**
     * 真实姓名
     */
    @Length(max = 64)
    String realName;

    /**
     * 租户id
     */
    @NotNull(message = "用户sid不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    Long userSid;

    /**
     * 状态
     */
    UserStatusEnum status;

    /**
     * 认证状态
     */
    UserAuthStatusEnum authStatus;

    /**
     * 有效期开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date startTime;

    /**
     * 有效期结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date endTime;

    /**
     * 邮件名称
     */
    @Email
    String email;

    /**
     * 手机号码
     */
    @Length(min = 11, max = 11, message = "手机号只能为11位")
    @Pattern(regexp = "^[1][3,4,5,6,7,8,9][0-9]{9}$", message = "手机号格式有误")
    String mobile;


    List<Long> roleIds;

    /**
     * 角色代码
     */
    Set<String> roleCodes;
}
