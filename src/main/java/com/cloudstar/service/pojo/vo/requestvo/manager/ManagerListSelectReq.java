package com.cloudstar.service.pojo.vo.requestvo.manager;
import com.cloudstar.common.util.page.PageForm;

import java.util.List;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;


@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ManagerListSelectReq extends PageForm {

    /**
     * 当前租户的id
     */
    Long userSid;

    /**
     * 用户名
     */
    String account;

    /**
     * 真实姓名
     */
    String realName;

    /**
     * 邮箱
     */
    String email;

    /**
     * 手机号
     */
    String mobile;

    /**
     * 用户类型
     */
    String userType;

    /**
     * 父账号id
     */
    Long parentSid;

    /**
     * 状态
     */
    List<String> status;

    /**
     * 邮箱
     */
    private List<String> notEmail;
    /**
     * 电话
     */
    private List<String> notMobile;

    /**
     * 账号
     */
    private String accountLike;

    /**
     * 状态
     */
    List<Long> userSids;


    /**
     * 需要过滤的用户id列表
     */
    List<Long> userSidsNot;

}
