package com.cloudstar.service.pojo.vo.requestvo.model;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * req
 *
 * <AUTHOR>
 * @date 2025/4/17 10:42
 */
@Data
public class ModelHealthRequest {

    /**
     * 检查方式
     */
    @NotEmpty
    private String method;
    /**
     * 检查接口地址
     */
    @NotEmpty
    private String url;
    /**
     * 检查周期
     */
    @NotNull
    private Integer periodTime;
    /**
     * 延迟时间
     */
    @NotNull
    private Integer delayTime;
    /**
     * 超时时间
     */
    @NotNull
    private Integer timeoutTime;
    /**
     * 最大失败次数
     */
    @NotNull
    private Integer failNum;
}
