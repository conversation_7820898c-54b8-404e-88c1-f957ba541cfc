package com.cloudstar.service.pojo.vo.requestvo.model;

import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import lombok.Data;

/**
 * 创建模型请求体
 *
 * <AUTHOR>
 */
@Data
public class ModelCreateRequest {

    /**
     * 模型名称
     */
    @NotNull(message = "模型名称不能为空")
    @Length(min = 2, max = 100, message = "名称长度必须在2-64个字符之间")
    @Pattern(regexp = "^[a-z0-9]([-a-z0-9]*[a-z0-9])?", message = "名称不合法，只允许小写字母、数字、中划线")
    private String name;

    /**
     * 备注
     */
    private String remark;


    /**
     * 版本信息
     */
    @NotNull
    private ModelVersionCreateRequest version;

    /**
     * 集群id
     */
    private Long clusterId;

}
