package com.cloudstar.service.pojo.vo.requestvo.manager;

import org.hibernate.validator.constraints.Length;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 创建菜单请求
 *
 * <AUTHOR> Created on 2022/8/18
 * @date 2022/08/18
 */
@Data
public class CreateMenuRequest {

    /**
     * 父级id, 默认0
     */
    @NotNull
    private Long parentId;
    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @Length(max = 32, message = "菜单名称不能超过32")
    private String name;
    /**
     * 菜单标识
     */
    @Pattern(regexp = "^[a-zA-Z_]+$", message = "菜单标识有误")
    @Length(max = 32, message = "菜单标识不能超过32")
    private String code;
    /**
     * 菜单英文名名称
     */
    @Length(max = 32, message = "菜单英文名不能超过32")
    private String engName;
    /**
     * 菜单类型
     */
    @Length(max = 32, message = "菜单类型不能超过32")
    private String menuType;
    /**
     * 菜单图标
     */
    @Length(max = 128, message = "菜单图标不能超过128")
    private String ico;
    /**
     * 显示排序
     */
    private Long sort;
    /**
     * 路由类型;路由:ROUTE   外联:OUTREACH   页面嵌入:PAGE_EMBEDDING
     */
    @Length(max = 32, message = "路由类型不能超过32")
    private String routeType;
    /**
     * 路由地址
     */
    @Length(max = 128, message = "路由地址不能超过128")
    private String routePath;
    /**
     * 组件地址
     */
    @Length(max = 128, message = "组件地址不能超过128")
    private String modulePath;
    /**
     * 路由参数
     */
    @Length(max = 128, message = "路由参数不能超过128")
    private String meta;
    /**
     * 是否显示;true:显示 不显示:false
     */
    @Length(max = 32, message = "是否显示长度不能超过32")
    private String display;
    /**
     * 状态;启用:ENABLE  禁用:DISABLED
     */
    @NotNull
    @Length(max = 32, message = "状态长度不能超过32")
    private String status;

}
