package com.cloudstar.service.pojo.vo.responsevo.access;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * 断言响应
 *
 * <AUTHOR>
 * Created on 2022/8/16
 * @date 2022/08/16
 */
@Data
public class AssertionProductResponse {
    /**
     * 断言sid
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long assertionSid;

    /**
     * 服务代码
     */
    private String serviceCode;

    /**
     * 服务名称
     */
    private String serviceName;

    /**
     * 断言类型
     */
    private List<AssertionTypeVO> assertionTypes = Lists.newLinkedList();
}
