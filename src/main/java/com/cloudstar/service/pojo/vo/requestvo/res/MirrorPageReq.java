package com.cloudstar.service.pojo.vo.requestvo.res;

import com.cloudstar.common.util.page.PageForm;

import javax.validation.constraints.NotBlank;

import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 镜像分页请求
 *
 * <AUTHOR>
 * @date 2024/07/05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MirrorPageReq extends PageForm {

    /**
     * 镜像名称
     */
    @NotBlank(message = "镜像名称不能为空")
    private String imageName;
    /**
     * 镜像Hash值
     */
    private String digest;

    /**
     * 版本数
     */
    private String version;
}
