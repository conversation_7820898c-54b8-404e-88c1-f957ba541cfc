package com.cloudstar.service.pojo.vo.requestvo.ticket;

import java.util.List;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 工单留言
 *
 * <AUTHOR>
 *  2022-09-21 14:54
 */
@Data
public class TicketInfoMessageReq {

    /**
     * id
     */
    @NotNull(message = "工单ID不能为空")
    private Long id;

    /**
     * 文件id
     */
    private List<Long> fileIds;

    /**
     * 工单内容
     */
    @NotBlank(message = "留言内容不能为空")
    @Length(max = 512)
    private String ticketContent;

    /**
     * 状态
     */
    @NotBlank(message = "留言状态不能为空")
    private String status;
}
