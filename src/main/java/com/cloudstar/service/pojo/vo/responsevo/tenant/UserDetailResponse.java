package com.cloudstar.service.pojo.vo.responsevo.tenant;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.cloudstar.common.base.constant.UserAuthStatusEnum;
import com.cloudstar.common.base.constant.UserStatusEnum;
import com.cloudstar.common.desensitized.annotation.DesensitizationField;
import com.cloudstar.common.desensitized.enums.DesensitizedType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import java.io.Serializable;
import java.util.Date;

/**
 * 租户详情响应实体
 */
@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class UserDetailResponse implements Serializable {

    /**
     * 租户id
     */
    @ExcelIgnore
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    Long userSid;

    /**
     * 租户名称
     */
    @ExcelProperty("租户名称")
    @DesensitizationField(type = DesensitizedType.NAME)
    String realName;

    /**
     * 用户名
     */
    @ExcelProperty("用户名")
    String account;

    /**
     * 邮箱
     */
    @ExcelProperty("邮箱")
    @DesensitizationField(type = DesensitizedType.EMAIL)
    String email;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    @DesensitizationField(type = DesensitizedType.MOBILE)
    String mobile;

    /**
     * 用户类型
     */
    @ExcelProperty("用户类型")
    String userType;


    /**
     * 状态
     */
    @ExcelIgnore
    UserStatusEnum status;

    /**
     * 认证状态
     */

    @ExcelIgnore
    UserAuthStatusEnum authStatus;

    /**
     * 帐户冻结状态
     */
    @ExcelIgnore
    private String accountStatus;

    /**
     * 账号有效开始时间
     */
    @ExcelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date startTime;

    /**
     * 账号有效结束时间
     */
    @ExcelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date endTime;

    /**
     * 是否首次登录重置密码
     */
    @ExcelIgnore
    String isResetPassword;

    /**
     * 最后登录IP
     */
    @ExcelIgnore
    String lastLoginIp;

    /**
     * 创建时间
     */
    @ExcelIgnore
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    Date createdDt;


    Boolean lock;

    @ExcelProperty("认证状态")
    private String statusName;

    @ExcelProperty("状态")
    private String authStatusName;

    @ExcelIgnore
    private String contactName;

    /**
     * 地址
     */
    @ExcelIgnore
    private String address;

    /**
     * 联系电话
     */
    @ExcelIgnore
    @DesensitizationField(type = DesensitizedType.MOBILE)
    private String contactPhone;

    /**
     * 应用场景
     */
    @ExcelIgnore
    private String applicationScenario;

    /**
     * 人员规模
     */
    @ExcelIgnore
    private String personnelSize;

    /**
     * 所属行业
     */
    @ExcelIgnore
    private String industryType;

    /**
     * 备注
     */
    @ExcelIgnore
    private String remark;



    public String getStatusName() {
        if (status == null) {
            return null;
        }
        return status.getDesc();
    }

    public String getAuthStatusName() {
        if (authStatus == null) {
            return null;
        }
        return authStatus.getDesc();
    }


}
