package com.cloudstar.service.pojo.vo.requestvo.manager;

import org.hibernate.validator.constraints.Length;

import lombok.Data;
import javax.validation.constraints.NotBlank;

import javax.validation.constraints.Pattern;


/**
 * 创建角色
 * <AUTHOR>
 * Created on 2022/8/9
 */
@Data
public class CreateRoleRequest {

    /**
     *角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Length(min = 0, max = 32, message = "名称不能超过32")
    private String name;
    /**
     *角色类型
     */
    private String roleType;
    /**
     *角色标识
     */
    @Pattern(regexp = "^[a-zA-Z_]+$", message = "角色标识有误")
    @NotBlank(message = "角色标识不能为空")
    @Length(min = 0, max = 32, message = "标识不能超过32")
    private String code;
    /**
     *角色描述
     */
    @Length(min = 0, max = 32, message = "描述不能超过32")
    private String description;
    /**
     *基础角色ID
     */
    private Long baseRoleSid;

}