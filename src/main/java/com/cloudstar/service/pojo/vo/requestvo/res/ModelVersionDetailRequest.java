package com.cloudstar.service.pojo.vo.requestvo.res;

import com.cloudstar.common.util.page.PageForm;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 创建模型请求体
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ModelVersionDetailRequest extends PageForm {

    /**
     * 模型id
     */
    @NotNull
    private Long modelId;

}
