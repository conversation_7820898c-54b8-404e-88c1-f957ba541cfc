package com.cloudstar.service.pojo.vo.requestvo.request;

import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

import lombok.Data;

/**
 * 审核申请
 *
 * <AUTHOR>
 * @date 2022-08-13 15:57
 */
@Data
public class RequestFormAuditReq {


    private Long id;
    /**
     * 审核状态
     */
    @NotBlank
    private String status;

    /**
     * 审核描述
     */
    @Length(min = 0, max = 512, message = "描述长度不能超过512")
    private String remark;
}
