package com.cloudstar.service.pojo.vo.requestvo.cluster;

import org.hibernate.validator.constraints.Length;
import org.springframework.lang.NonNull;

import javax.validation.constraints.NotEmpty;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 资源池入参
 *
 * <AUTHOR>
 * @date 2022/8/26 11:20
 */
@Data
@NoArgsConstructor
public class ClusterResourcePoolReq {


    /**
     * id
     */
    private Long id;

    /**
     * 集群id
     */
    @NonNull
    private Long clusterId;

    /**
     * 资源池名称
     */
    @Length(max = 64)
    @NotEmpty
    private String poolName;

    /**
     * 资源池id
     */
    @Length(max = 64)
    @NotEmpty
    private String poolId;

    /**
     * 资源规格id
     */
    private Long flavorId;

    /**
     * 资源池类型;share:共享资源池,exclusive:专属资源池
     */
    @Length(max = 32)
    @NotEmpty
    private String poolType;

    /**
     * 用户id;多个账号之间逗号隔开
     */
    @Length(max = 512)
    private String userIds;

    /**
     * 规格id;多个规格id之间逗号隔开
     */
    @Length(max = 512)
    private String jobFlavorId;

    /**
     * 区
     */
    private String zone;

    /**
     * 协同代理ip
     */
    private String collaborationAgentIp;


}
