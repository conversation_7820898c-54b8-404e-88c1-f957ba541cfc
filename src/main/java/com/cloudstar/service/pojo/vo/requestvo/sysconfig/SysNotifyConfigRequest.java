package com.cloudstar.service.pojo.vo.requestvo.sysconfig;

import javax.validation.constraints.NotBlank;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * <p>
 * 系统消息通知配置请求参数
 * </p>
 *
 * <AUTHOR>
 * @since 2021/3/12
 */
@Data
public class SysNotifyConfigRequest {

    /**
     * 业务分类 billing（费用通知）sfs_storage（sfs存储通知）
     */
    @NotBlank
    private String bssType;

    /**
     * 对比符号 >,<
     */
    private String symbol;

    /**
     * 阈值类型 number（数字），percent（百分比）,gigaByte (GB)
     */
    private String thresholdValueType;

    /**
     * 通知阈值
     */
    @NotNull
    private Long thresholdValue;

    /**
     * 消息通知类型 通知方式：以逗号分隔,mail:邮件,sms:短信,默认站内信,station
     */
    @NotBlank
    private String notifyType;

    /**
     * 通知频率 day（每天），默认16：00点
     */
    @NotNull
    private Long frequency;

    /**
     * 通知次数
     */
    @NotNull
    private Long noticeCount;

    /**
     * 处理方式 none（暂不处理）, prohibit（禁止新建）, freeze（冻结使用）
     */
    @NotBlank
    private String expireStrategy;

    /**
     * 白名单
     */
    private String whitelist;

    /**
     * 白名单ID，orgSid以,号隔开
     */
    private String whitelistId;
}
