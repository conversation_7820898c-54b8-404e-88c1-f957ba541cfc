package com.cloudstar.service.pojo.vo.requestvo.user;


import com.cloudstar.dao.model.user.UserEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

import org.springframework.beans.BeanUtils;


import javax.validation.constraints.NotBlank;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class FindPwdReq {

    /**
     * 新密码
     */
    @NotBlank String password;

    /**
     * 电话
     */
    String mobile;

    /**
     * 邮箱
     */
    String email;

    /**
     * 验证码
     */
    String validCode;

    /**
     * 账号
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
     Long userSid;

    /**
     * 获取用户实体
     */
    public static UserEntity getUserEntity(FindPwdReq req) {
        UserEntity userEntity = new UserEntity();
        BeanUtils.copyProperties(req, userEntity);
        return userEntity;
    }
}
