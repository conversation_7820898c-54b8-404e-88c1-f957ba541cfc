package com.cloudstar.service.pojo.vo.requestvo.access;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 政策克隆请求
 *
 * <AUTHOR>
 * Created on 2022/8/23
 * @date 2022/08/23
 */
@Data
public class PolicyCloneReq {
    /**
     * 策略标识
     */
    @NotNull
    private Long basePolicySid;

    /**
     * 策略标识
     */
    @NotBlank
    @Pattern(regexp = "^[a-zA-Z][a-zA-Z0-9-_.]{3,15}$", message = "权限标识无效")
    private String policyName;

    /**
     * 显示名称
     */
    private String displayName;

    /**
     * 描述
     */
    private String description;
}
