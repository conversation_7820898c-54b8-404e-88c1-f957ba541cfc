package com.cloudstar.service.pojo.vo.responsevo.res;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * 镜像版本详情实体类。
 *
 * <AUTHOR>
 * @date 2024/07/01
 */
@Data
public class MirrorVersionRes {

    /**
     * 主键
     */
    private String id;
    /**
     * 镜像版本号。
     */
    @Getter
    @Setter
    private String version;

    /**
     * 镜像版本大小。
     */
    @Getter
    @Setter
    private String size;

    /**
     * 更新时间。
     */
    @Getter
    @Setter
    private String updateTime;

    /**
     * Docker拉取指令。
     */
    @Getter
    @Setter
    private String pullCommand;

    /**
     * 镜像摘要信息。
     */
    @Getter
    @Setter
    private String digest;
}
