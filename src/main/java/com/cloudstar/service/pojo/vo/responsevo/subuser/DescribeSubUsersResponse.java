package com.cloudstar.service.pojo.vo.responsevo.subuser;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * 描述子用户响应
 *
 * <AUTHOR>
 * Created on 2022/8/15
 * @date 2022/08/17
 */
@Data
public class DescribeSubUsersResponse {
    /**
     * 租户id
     */
    @TableId(value = "user_sid", type = IdType.AUTO)
    private Long userSid;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 用户名
     */
    private String account;

    /**
     * 密码;AES加密存储
     */
    private String password;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 邮箱;AES加密存储
     */
    private String email;

    /**
     * 手机号码;AES加密存储
     */
    private String mobile;

    /**
     * 租户类型;0:租户账号 1:租户子账户
     */
    private String userType;

    /**
     * 父账号id;账号类型为子账户时父账号id不能为空
     */
    private Long parentSid;

    /**
     * 认证状态;0:待上传实名认证资料,1:待审核，3:已认证
     */
    private String authStatus;

    /**
     * 状态;0:启用,1:禁用，3:注销--逻辑删除
     */
    private String status;

    /**
     * 账号有效开始时间
     */
    private Date startTime;

    /**
     * 创建时间
     */
    private Date createdDt;

}
