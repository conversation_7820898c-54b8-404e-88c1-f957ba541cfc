package com.cloudstar.service.pojo.vo.requestvo.sysconfig;

import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotBlank;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 批量更新配置请求参数
 */
@Data
public class UpdateSysConfigsRequest implements Serializable {

    /**
     * 配置SID
     */
    @NotNull
    private Long configSid;

    /**
     * 配置类型
     */
    @NotBlank
    private String configType;

    /**
     * 配置名称
     */
    @NotBlank
    private String configName;

    /**
     * 配置Key
     */
    @NotBlank
    private String configKey;

    /**
     * 配置值
     */
    @Length(max = 8192, message = "内容超长")
    private String configValue;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 性能单位
     */
    private String unit;
}
