package com.cloudstar.service.pojo.vo.requestvo.tenant;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotBlank;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class ValidateUserPwdReq {
    /**
     * 密码
     */
    @NotBlank
    String password;


    String account;
    
    /**
     * 租户id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    Long userSid;
}
