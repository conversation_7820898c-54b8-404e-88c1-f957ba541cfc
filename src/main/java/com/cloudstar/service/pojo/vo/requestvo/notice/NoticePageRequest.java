package com.cloudstar.service.pojo.vo.requestvo.notice;

import com.cloudstar.common.util.page.PageForm;

import lombok.Data;

@Data
public class NoticePageRequest extends PageForm {

    /**
     * 公告开始时间
     */
    private String publishStartDate;

    /**
     * 公告结束时间
     */
    private String publishEndDate;


    /**
     * 公告类型（1通知;2公告）
     */
    private Integer noticeTypeId;


    /**
     * 公告标题
     */
    private String noticeTitle;

    /**
     * 公告状态（0待发布;1已发布）
     */
    private String noticeStatus;

    /**
     * 创建者
     */
    private String createBy;

}
