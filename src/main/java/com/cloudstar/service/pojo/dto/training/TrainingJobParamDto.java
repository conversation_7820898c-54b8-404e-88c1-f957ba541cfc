package com.cloudstar.service.pojo.dto.training;

import javax.validation.constraints.Pattern;

import lombok.Data;

/**
 * 练作业超参dto
 *
 * <AUTHOR>
 * Created on 2022/8/25
 * @date 2022/08/25
 */
@Data
public class TrainingJobParamDto {
    /**
     * 参数id
     */
    private Long id;
    /**
     * 名字
     */
    @Pattern(regexp = "^.{0,64}$", message = "参数名长度不合法")
    private String name;
    /**
     * 类型:hyper_params:超参;input_params:输入参数;output_params:输出参数
     */
    private String type;
    /**
     * 值
     */
    @Pattern(regexp = "^((?![\\u4e00-\\u9fa5]).)*${0,512}", message = "参数值不合法")
    @Pattern(regexp = "^.{0,512}", message = "参数值长度不合法")
    private String value;
}
