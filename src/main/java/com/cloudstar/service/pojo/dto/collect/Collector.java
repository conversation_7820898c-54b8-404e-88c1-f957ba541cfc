package com.cloudstar.service.pojo.dto.collect;

import lombok.Data;

/**
 * 话单
 *
 * <AUTHOR>
 * @date 2022/12/5 11:27
 */

@Data
public class Collector {

    /**
     * id
     */
    private String id;

    /**
     * 离线话单记录标记
     */
    private String recordType;

    /**
     * 离线话单记录产生时间
     */
    private String timeStamp;

    /**
     * 用户编码
     */
    private String accountId;

    /**
     * 片区编码
     */
    private String regionCode;

    /**
     * AvailableZoneCode
     */
    private String azCode;

    /**
     * 云服务类型编码
     */
    private String cloudServiceTypeCode;

    /**
     * 资源类型编码
     */
    private String resourceTypeCode;

    /**
     * 资源规格编码
     */
    private String resourceSpecCode;

    /**
     * 资源实例ID
     */
    private String resourceId;

    /**
     * 运营参数
     */
    private String csbParams;

    /**
     * 统计周期开始时间
     */
    private String beginTime;

    /**
     * 统计结束时间
     */
    private String endTime;

    /**
     * (计费因子名)累积因子名
     */
    private String accumulateFactorName;

    /**
     * （计费因子值）累积因子值
     */
    private String accumulateFactorValue;

    /**
     * 扩展字段
     */
    private String extendParams;

    /**
     * 资源tag
     */
    private String tag;

    /**
     * 企业资源组ID
     */
    private String enterprisePorjectId;

    /**
     * 采集记录ID
     */
    private String collectionRecordId;

}
