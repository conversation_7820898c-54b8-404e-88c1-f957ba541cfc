package com.cloudstar.service.utils;

import cn.hutool.core.util.ObjectUtil;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.pojo.result.RestCodeEnum;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountReqDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 获取裸金属底层账号
 *
 * <AUTHOR>
 * @date 2024/12/26
 */
@Component
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class GetBmsAccountUtil {

    private ServerClient serverClient;

    /**
     * 获取BMS底层账号
     * @param clusterId 集群id
     * @param userSid 用户id
     * @return 账号信息
     */
    public ClusterSubAccountRespDto getBmsAccount(Long clusterId, Long userSid) {
        //创建的作业子用户id
        ClusterSubAccountReqDto clusterSubAccountReqDto = new ClusterSubAccountReqDto();
        clusterSubAccountReqDto.setClusterId(clusterId);
        clusterSubAccountReqDto.setUserSid(userSid);
        final Rest<ClusterSubAccountRespDto> createResponse = serverClient.getClusterSubAccount(
                clusterSubAccountReqDto);
        if (ObjectUtil.isEmpty(createResponse) || !ObjectUtil.equals(createResponse.getCode(), RestCodeEnum.SUCCESS.getCode())) {
            throw new BizException("获取BMS底层账号失败");
        }
        return createResponse.getData();
    }
}
