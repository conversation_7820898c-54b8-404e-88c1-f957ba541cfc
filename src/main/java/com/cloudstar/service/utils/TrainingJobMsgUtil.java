package com.cloudstar.service.utils;

import com.cloudstar.NotificationService;
import com.cloudstar.bean.enums.MessageTemplateEnum;
import com.cloudstar.bean.enums.MessageType;
import com.cloudstar.bean.pojo.NotificationParam;
import com.cloudstar.integration.bss.pojo.message.BssMessageReq;
import com.cloudstar.integration.bss.service.facade.BssMessageService;
import com.cloudstar.service.pojo.dto.trainingjob.TrainingJobGroupMsgDto;
import com.cloudstar.service.pojo.dto.trainingjob.TrainingJobMsgDto;

import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 训练作业 消息发送工具类
 *
 * <AUTHOR>
 * @date 2022/11/7 15:45
 */
@Component
@Slf4j
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class TrainingJobMsgUtil {

    NotificationService notificationService;

    BssMessageService bssMessageService;

    /**
     * 发生消息
     *
     * @param dto dto
     */
    public void sendMsg(TrainingJobMsgDto dto) {
        try {
            Map<String, String> messageContent = new HashMap<>();
            messageContent.put("jobName", dto.getJobName());
            String messageTemplate = MessageTemplateEnum.SEND_JOB_STATUS_INFO.getMessageTemplateId();
            messageContent.put("jobStatus", dto.getJobStatus());
            messageContent.put("jobId", dto.getJobId());
            //消息内容
            NotificationParam notificationParam = NotificationParam.builder()
                                                                   .messageType(Collections.singletonList(
                                                                           MessageType.PLATFORM))
                                                                   .messageContent(messageContent)
                                                                   .messageTemplateId(messageTemplate)
                                                                   .sendUserSid(dto.getSendUserSid())
                                                                   .sendAccount(dto.getSendAccount())
                                                                   .build();
            notificationService.sendNotification(notificationParam);
            //发送运营平台消息
            String getenv = System.getenv("CFN_MQ_START");
            if ("true".equals(getenv)) {
                BssMessageReq req = new BssMessageReq();
                req.setAccount(dto.getSendAccount());
                req.setMessageId(messageTemplate);
                req.setMessageType(new int[]{0});
                req.setNotifyAdmin(false);
                String cfnConsoleUrl = System.getenv("CFN_CONSOLE_URL");
                messageContent.put("jumpUrl", cfnConsoleUrl);
                req.setMessageContent(messageContent);
                bssMessageService.sendMsg(req);
            }
            log.error("训练作业【{}】发送消息成功", dto.getJobId());
        } catch (Exception e) {
            log.error("训练作业【{}】发送消息失败，错误详情：【{}】", dto.getJobId(), e);
        }

    }


    /**
     * 发生协同作业消息
     *
     * @param dto dto
     */
    public void sendJobGroupMsg(TrainingJobGroupMsgDto dto) {
        try {
            Map<String, String> messageContent = new HashMap<>();
            messageContent.put("jobGroupName", dto.getJobGroupName());
            String messageTemplate = MessageTemplateEnum.SEND_JOB_GROUP_STATUS_INFO.getMessageTemplateId();
            messageContent.put("jobGroupStatus", dto.getJobStatus());
            messageContent.put("jobGroupId", dto.getJobGroupId());
            //消息内容
            NotificationParam notificationParam = NotificationParam.builder()
                                                                   .messageType(Collections.singletonList(
                                                                           MessageType.PLATFORM))
                                                                   .messageContent(messageContent)
                                                                   .messageTemplateId(messageTemplate)
                                                                   .sendUserSid(dto.getSendUserSid())
                                                                   .sendAccount(dto.getSendAccount())
                                                                   .build();
            notificationService.sendNotification(notificationParam);
            String getenv = System.getenv("CFN_MQ_START");
            if ("true".equals(getenv)) {
                BssMessageReq req = new BssMessageReq();
                req.setAccount(dto.getSendAccount());
                req.setMessageId(messageTemplate);
                req.setMessageType(new int[]{0});
                req.setNotifyAdmin(false);
                String cfnConsoleUrl = System.getenv("CFN_CONSOLE_URL");
                messageContent.put("jumpUrl", cfnConsoleUrl);
                req.setMessageContent(messageContent);
                bssMessageService.sendMsg(req);
            }
            log.error("作业组【{}】发送消息成功", dto.getJobGroupId());
        } catch (Exception e) {
            log.error("作业组【{}】发送消息失败，错误详情：【{}】", dto.getJobGroupId(), e);
        }

    }
}
