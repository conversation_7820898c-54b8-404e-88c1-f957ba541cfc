package com.cloudstar.service.util;

import com.cloudstar.service.pojo.dto.cluster.ClusterSchedulerResourceGroupDto;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import freemarker.template.Configuration;
import freemarker.template.Template;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 生成k8s的yaml文件
 *
 * <AUTHOR>
 * 2024/11/13
 */
public class YamlGeneratorUtil {
    private static final String TEMPLATE_FILE = "k8s_template.yaml";

    /**
     * 生成 YAML 文件并返回内容
     *
     * @param resourceGroups 资源组列表
     * @param outputFilePath 输出文件路径
     * @return 生成的 YAML 内容
     * @throws Exception 异常
     */
    @SuppressFBWarnings({"PATH_TRAVERSAL_IN", "TEMPLATE_INJECTION_FREEMARKER"})
    public static String generateYaml(List<ClusterSchedulerResourceGroupDto> resourceGroups, String outputFilePath) throws Exception {
        // 创建模板配置
        Configuration cfg = new Configuration(Configuration.VERSION_2_3_31);
        cfg.setClassForTemplateLoading(YamlGeneratorUtil.class, "/k8s"); // 确保模板路径正确
        cfg.setDefaultEncoding("UTF-8");

        // 加载模板
        Template template;
        try {
            template = cfg.getTemplate(TEMPLATE_FILE);
        } catch (IOException e) {
            throw new Exception("模板文件未找到：" + e.getMessage(), e);
        }

        // 创建数据模型
        List<Map<String, Object>> resourceGroupData = new ArrayList<>();
        for (ClusterSchedulerResourceGroupDto group : resourceGroups) {
            Map<String, Object> groupData = new HashMap<>();
            groupData.put("groupName", group.getGroupName());
            groupData.put("annotation", group.getAnnotation());
            groupData.put("schedulerName", group.getSchedulerName());
            resourceGroupData.add(groupData);
        }

        Map<String, Object> model = new HashMap<>();
        model.put("resourceGroups", resourceGroupData);

        // 确保输出目录存在
        File outputDir = new File(System.getProperty("user.dir") + "/cfn-server/src/main/resources/k8s/");
        if (!outputDir.exists()) {
            outputDir.mkdirs(); // 创建目录
        }

        // 创建输出文件
        File outputFile = new File(outputDir, outputFilePath);
        FileWriter fileWriter = null;
        StringWriter stringWriter = null;
        try {
            fileWriter = new FileWriter(outputFile);
            stringWriter = new StringWriter();
            template.process(model, stringWriter);
            String yamlContent = stringWriter.toString();
            fileWriter.write(yamlContent); // 写入文件
            return yamlContent; // 返回生成的内容
        } catch (IOException e) {
            throw new Exception("写入文件失败：" + e.getMessage(), e);
        } finally {
            // 关闭资源
            if (fileWriter != null) {
                try {
                    fileWriter.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (stringWriter != null) {
                try {
                    stringWriter.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}