package com.cloudstar.service.config;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.service.facade.cluster.ClusterEntityService;

import org.apache.commons.lang3.StringUtils;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.Tracer;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

import cn.hutool.extra.spring.SpringUtil;
import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import lombok.extern.slf4j.Slf4j;

/**
 * 客户端token拦截器
 *
 * <AUTHOR>
 * @date 2022/10/11 15:33
 */
@Slf4j
public class ClientTokenGrpcInterceptor implements ClientInterceptor {

    private final String targetType;

    private final ClusterEntityService clusterEntityService;
    private final Tracer tracer;

    private static final String ACCESS_KEY = "access_key";

    private static final String SECURITY_KEY = "security_key";
    private static final String TRACE_ID = "trace_id";
    private static final String SPAN_ID = "span_id";

    @Override
    public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(MethodDescriptor<ReqT, RespT> method,
                                                               CallOptions callOptions, Channel channel) {

        return new ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(channel.newCall(method, callOptions)) {
            @Override
            public void start(Listener<RespT> responseListener, Metadata headers) {
                List<ClusterEntity> clusterEntityList = clusterEntityService.list(
                        new QueryWrapper<ClusterEntity>().lambda().eq(ClusterEntity::getAdapterUuid, targetType));
                ClusterEntity entity = clusterEntityList.get(0);
                headers.put(Metadata.Key.of(ACCESS_KEY, Metadata.ASCII_STRING_MARSHALLER),
                            entity.getAdapterAccount());
                headers.put(Metadata.Key.of(SECURITY_KEY, Metadata.ASCII_STRING_MARSHALLER),
                            entity.getAdapterPassword());

                traceInfo(headers);
                super.start(responseListener, headers);
            }
        };
    }

    private void traceInfo(Metadata headers) {
        Optional.ofNullable(tracer.currentSpan()).map(Span::context).ifPresent(c -> {
            headers.put(Metadata.Key.of(TRACE_ID, Metadata.ASCII_STRING_MARSHALLER), c.traceId());
            headers.put(Metadata.Key.of(SPAN_ID, Metadata.ASCII_STRING_MARSHALLER), c.spanId());
        });

        if (StringUtils.isEmpty(headers.get(Metadata.Key.of(SPAN_ID, Metadata.ASCII_STRING_MARSHALLER)))) {
            headers.put(Metadata.Key.of(SPAN_ID, Metadata.ASCII_STRING_MARSHALLER),
                        UUID.randomUUID().toString().replaceAll("-", ""));
        }
        log.info("span_id-------------{}", headers.get(Metadata.Key.of(SPAN_ID, Metadata.ASCII_STRING_MARSHALLER)));
    }

    public ClientTokenGrpcInterceptor(String t) {
        targetType = t;
        clusterEntityService = SpringUtil.getBean(ClusterEntityService.class);
        tracer = SpringUtil.getBean(Tracer.class);
    }
}
