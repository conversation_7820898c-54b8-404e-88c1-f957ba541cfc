/*
 * Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
 */

package com.cloudstar.service.config;

import com.cloudstar.service.task.init.SpringBeanJobFactory;
import com.cloudstar.service.task.listener.JobExceptionListener;
import com.zaxxer.hikari.HikariDataSource;

import org.quartz.listeners.JobListenerSupport;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.autoconfigure.quartz.SchedulerFactoryBeanCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.util.StringUtils;

import java.util.Properties;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

import javax.sql.DataSource;


/**
 * quartz bean config
 *
 * <AUTHOR> created by ShiWenQiang
 */
@Configuration
public class QuartzConfig {

    @Value("${spring.datasource.dynamic.datasource.cfn.url}")
    private String dbUrl;

    @Value("${spring.datasource.dynamic.datasource.cfn.username}")
    private String username;

    @Value("${spring.datasource.dynamic.datasource.cfn.password}")
    private String password;

    @Value("${spring.datasource.dynamic.datasource.cfn.driver-class-name}")
    private String driverClassName;


    /**
     * 创建 quartz 数据源的配置对象
     */
    public DataSourceProperties quartzDataSourceProperties() {
        DataSourceProperties properties = new DataSourceProperties();
        properties.setDriverClassName(driverClassName);
        properties.setUrl(dbUrl);
        properties.setUsername(username);
        properties.setPassword(password);
        return properties;
    }

    /**
     * quartz数据源
     * @return
     */
    public DataSource quartzDataSource() {
        // 获得 DataSourceProperties 对象
        DataSourceProperties properties = this.quartzDataSourceProperties();
        // 创建 HikariDataSource 对象
        return createHikariDataSource(properties);
    }

    @Order(Ordered.HIGHEST_PRECEDENCE)
    @Bean
    public SchedulerFactoryBeanCustomizer schedulerFactoryBeanCustomizer() {
        return schedulerFactoryBean -> {
            schedulerFactoryBean.setDataSource(quartzDataSource());
            schedulerFactoryBean.setTransactionManager(quartzDataSourceTransactionManager());
        };
    }

    @Bean
    public DataSourceTransactionManager quartzDataSourceTransactionManager() {
        return new DataSourceTransactionManager(quartzDataSource());
    }


    private static HikariDataSource createHikariDataSource(DataSourceProperties properties) {
        // 创建 HikariDataSource 对象
        HikariDataSource dataSource = properties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
        // 设置线程池名
        if (StringUtils.hasText(properties.getName())) {
            dataSource.setPoolName(properties.getName());
        }
        return dataSource;
    }

    private JobListenerSupport[] globalJobListeners() {
        return new JobListenerSupport[]{new JobExceptionListener()};
    }

    @Bean(name = "threadPool")
    public ThreadPoolTaskExecutor threadPool() {
        ThreadPoolTaskExecutor threadPoolTaskExecutor = new ThreadPoolTaskExecutor();
        threadPoolTaskExecutor.setCorePoolSize(10);
        threadPoolTaskExecutor.setMaxPoolSize(100);
        threadPoolTaskExecutor.setKeepAliveSeconds(300);
        threadPoolTaskExecutor.setAwaitTerminationSeconds(10);
        threadPoolTaskExecutor.setQueueCapacity(1024);
        threadPoolTaskExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        threadPoolTaskExecutor.initialize();
        return threadPoolTaskExecutor;
    }


    @Bean(name = "jobFactory")
    public SpringBeanJobFactory springBeanJobFactory() {
        return new SpringBeanJobFactory();
    }

    @Bean(name = "scheduler")
    public SchedulerFactoryBean schedulerFactory(
            @Qualifier("jobFactory") SpringBeanJobFactory jobFactory,
            @Qualifier("threadPool") Executor taskExecutor) {
        SchedulerFactoryBean schedulerFactoryBean = new SchedulerFactoryBean();
        schedulerFactoryBean.setDataSource(quartzDataSource());
        schedulerFactoryBean.setQuartzProperties(quartzProperties());
        //多数据源才需要
        //schedulerFactoryBean.setTransactionManager(tm);
        schedulerFactoryBean.setOverwriteExistingJobs(true);
        schedulerFactoryBean.setWaitForJobsToCompleteOnShutdown(true);
        schedulerFactoryBean.setStartupDelay(20);
        schedulerFactoryBean.setApplicationContextSchedulerContextKey("applicationContext");
        schedulerFactoryBean.setJobFactory(jobFactory);
        schedulerFactoryBean.setTaskExecutor(taskExecutor);
        schedulerFactoryBean.setGlobalJobListeners(this.globalJobListeners());
        return schedulerFactoryBean;
    }

    /**
     * quartz 额外配置
     */
    private Properties quartzProperties() {
        Properties prop = new Properties();
        prop.put("quartz.scheduler.instanceName", "instance_" + UUID.randomUUID());
        prop.put("org.quartz.scheduler.instanceId", "AUTO");
        prop.put("org.quartz.scheduler.skipUpdateCheck", "true");
        prop.put("org.quartz.jobStore.class", "org.springframework.scheduling.quartz.LocalDataSourceJobStore");
        //这个会解决分布式重复执行问题
        prop.put("org.quartz.jobStore.isClustered", "true");
        prop.put("org.quartz.jobStore.acquireTriggersWithinLock", "true");
        prop.put("org.quartz.jobStore.driverDelegateClass", "org.quartz.impl.jdbcjobstore.PostgreSQLDelegate");
        return prop;
    }

}
