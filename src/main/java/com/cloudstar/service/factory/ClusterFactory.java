package com.cloudstar.service.factory;

import com.cloudstar.service.facade.ClusterObjService;
import com.cloudstar.service.impl.HCsoClusterImpl;
import com.cloudstar.service.impl.SlurmClusterImpl;
import com.cloudstar.dao.model.requests.ClusterRequest;

public class ClusterFactory {
    public static ClusterObjService getClusterObject(ClusterRequest clusterInfo) {
        switch (clusterInfo.getClusterType().toUpperCase()) {
            case "HCSO":
                return new HCsoClusterImpl(clusterInfo);
            case "SLURM":
                return new SlurmClusterImpl(clusterInfo);
            default:
                throw new IllegalArgumentException("Unsupported cluster type");
        }
    }
}
