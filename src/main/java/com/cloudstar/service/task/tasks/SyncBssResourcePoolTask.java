package com.cloudstar.service.task.tasks;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.common.base.constant.UserStatusEnum;
import com.cloudstar.common.base.enums.ClusterResPoolTypeEnum;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.cluster.ClusterFlavorMapper;
import com.cloudstar.dao.mapper.cluster.ClusterResourcePoolMapper;
import com.cloudstar.dao.mapper.user.UserEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.cluster.ClusterFlavor;
import com.cloudstar.dao.model.cluster.ClusterResourcePool;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.integration.bss.pojo.pool.CfnResourcePool;
import com.cloudstar.integration.bss.service.facade.BssResourcePoolService;
import com.cloudstar.service.task.base.bean.AddJobParam;
import com.cloudstar.service.utils.ListUtil;

import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 运营平台资源池同步定时任务
 *
 * <AUTHOR>
 * @date 2023/5/26 14:22
 */
@Slf4j
@Component
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class SyncBssResourcePoolTask implements Job {

    BssResourcePoolService bssResourcePoolService;

    ClusterResourcePoolMapper clusterResourcePoolMapper;

    ClusterFlavorMapper clusterFlavorMapper;

    UserEntityMapper userEntityMapper;

    ClusterEntityMapper clusterEntityMapper;


    /**
     * 初始化工作参数
     *
     * @return {@link AddJobParam}
     */
    public static AddJobParam initJobParam() {
        return AddJobParam.builder()
                          //三十分钟同步一次
                          .cronExpression("0 */30 * * * ?")
                          .jobName("sync-bss-pool-job")
                          .jobGroup("sync-bss-pool-job-group")
                          .triggerName("sync-bss-pool-job-trigger")
                          .triggerGroup("sync-bss-pool-job-trigger")
                          .description("定时同步bss资源池")
                          .clazz(SyncBssResourcePoolTask.class)
                          .build();
    }

    @Override
    @Transactional
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        //获取bss专属资源池数据
        List<CfnResourcePool> drpResourcePools = bssResourcePoolService.getDrpResourcePools();
        if (CollectionUtil.isNotEmpty(drpResourcePools)) {
            delDrpPools(drpResourcePools);
            addDrpPools(drpResourcePools);
        }
        //获取bss共享资源池
        List<CfnResourcePool> shareResourcePools = bssResourcePoolService.getShareResourcePools();
        if (CollectionUtil.isNotEmpty(shareResourcePools)) {
            saveOrUpdateSharePool(shareResourcePools);
        }
    }

    /**
     * 保存或修改共享资源池
     *
     * @param list 列表
     */
    private void saveOrUpdateSharePool(List<CfnResourcePool> list) {
        ClusterEntity clusterEntity = clusterEntityMapper.selectOne(
                new LambdaQueryWrapper<ClusterEntity>().like(ClusterEntity::getAttrData, list.get(0).getClusterId()));
        if (ObjectUtil.isNotEmpty(clusterEntity)) {
            ClusterResourcePool clusterResourcePool = clusterResourcePoolMapper.selectOne(
                    new LambdaQueryWrapper<ClusterResourcePool>().eq(ClusterResourcePool::getClusterId, clusterEntity.getId())
                                                                 .eq(ClusterResourcePool::getPoolType, ClusterResPoolTypeEnum.SHARE.getType()));
            //修改
            if (ObjectUtil.isNotEmpty(clusterResourcePool)) {
                clusterResourcePool.setUserIds(getUserIds(list, clusterResourcePool.getUserIds()));
                clusterResourcePoolMapper.updateById(clusterResourcePool);
            } else {
                //新增
                addSharePool(list, clusterEntity);
            }
        }
    }

    /**
     * 新增共享资源池
     *
     * @param addList 新增列表
     * @param clusterEntity 集群
     */
    private void addSharePool(List<CfnResourcePool> addList, ClusterEntity clusterEntity) {
        if (CollectionUtil.isNotEmpty(addList)) {
            ClusterResourcePool clusterResourcePool = new ClusterResourcePool();
            clusterResourcePool.setClusterId(clusterEntity.getId());
            clusterResourcePool.setPoolName(clusterEntity.getClusterName() + ClusterResPoolTypeEnum.SHARE.getDesc());
            String attrData = clusterEntity.getAttrData();
            JSONObject jsonObject = JSONUtil.parseObj(attrData);
            if (ObjectUtil.isNotEmpty(jsonObject.get("sharePoolId"))) {
                clusterResourcePool.setPoolId(jsonObject.get("sharePoolId").toString());
            }
            clusterResourcePool.setPoolType(ClusterResPoolTypeEnum.SHARE.getType());
            clusterResourcePool.setCreatedDt(new Date());
            clusterResourcePool.setUserIds(getUserIds(addList, null));
            clusterResourcePoolMapper.insert(clusterResourcePool);
        }
    }

    /**
     * 获取用户id字符串
     *
     * @param list 新增列表
     *
     * @return 返回值
     */
    private String getUserIds(List<CfnResourcePool> list, String oldUserIds) {
        List<CfnResourcePool> addList = list.stream().filter(pool -> !pool.isDel()).collect(Collectors.toList());
        List<CfnResourcePool> delList = list.stream().filter(pool -> pool.isDel()).collect(Collectors.toList());
        List<Long> updateList = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(oldUserIds)) {
            final String[] oldString = oldUserIds.split(",");
            log.info("共享资源池原始userIds:{}", oldString);
            //获取原始id列表
            List<Long> oldIds = Arrays.asList(oldString)
                                      .stream()
                                      .filter(s -> ObjectUtil.isNotEmpty(s))
                                      .map(s -> Long.parseLong(s))
                                      .collect(Collectors.toList());
            //获取新增的id列表
            List<Long> addIds = getUserList(addList);
            log.info("BSS共享资源池新增userIds:{}", addIds);
            //新增相加
            oldIds.addAll(addIds);
            //去重
            oldIds = oldIds.stream().distinct().collect(Collectors.toList());
            log.info("相加去重后userIds:{}", oldIds);
            //获取删除的id列表
            List<Long> delIds = getUserList(delList);
            log.info("BSS共享资源池删除userIds:{}", delIds);
            updateList = CollectionUtil.subtract(oldIds, delIds).stream().collect(Collectors.toList());
            log.info("相减去重后userIds:{}", updateList);

        } else {
            updateList = getUserList(addList);
            log.info("BSS共享资源池userIds:{}", updateList);
        }
        StringBuilder ids = new StringBuilder();
        updateList.stream().forEach(id -> {
            ids.append(id).append(",");
        });
        if (ids.length() > 0) {
            String s = ids.toString();
            s = s.substring(0, s.length() - 1);
            return s;
        }
        return ids.toString();
    }

    /**
     * 获取资源池userId
     *
     * @param list list
     */
    private List<Long> getUserList(List<CfnResourcePool> list) {
        List<Long> ids = new ArrayList<>();
        list.stream().forEach(pool -> {
            UserEntity userEntity = userEntityMapper.selectOne(
                    new LambdaQueryWrapper<UserEntity>().eq(UserEntity::getAccount, pool.getAccount()));
            if (ObjectUtil.isNotEmpty(userEntity)) {
                ids.add(userEntity.getUserSid());
            }
        });
        return ids;
    }

    /**
     * 删除资源池
     *
     * @param list bss数据
     */
    private void delDrpPools(List<CfnResourcePool> list) {
        ClusterEntity clusterEntity = clusterEntityMapper.selectOne(
                new LambdaQueryWrapper<ClusterEntity>().like(ClusterEntity::getAttrData, list.get(0).getClusterId()));
        if (ObjectUtil.isNotEmpty(clusterEntity)) {
            List<CfnResourcePool> bssDelPools = list.stream().filter(pool -> pool.isDel()).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(bssDelPools)) {
                log.info("BSS删除专属资源池信息:{}", bssDelPools);
                List<String> poolIds = bssDelPools.stream().map(CfnResourcePool::getPoolId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(poolIds)) {
                    clusterResourcePoolMapper.delete(new LambdaQueryWrapper<ClusterResourcePool>().in(ClusterResourcePool::getPoolId, poolIds)
                                                                                                  .eq(ClusterResourcePool::getClusterId,
                                                                                                      clusterEntity.getId()));
                }
            }
            //查询已删除用户
            List<UserEntity> userEntities = userEntityMapper.selectList(
                    new LambdaQueryWrapper<UserEntity>().eq(UserEntity::getStatus, UserStatusEnum.DELETED.getType()));
            if (CollectionUtil.isNotEmpty(userEntities)) {
                //删除已删除的用户开通的专属资源池
                userEntities.stream().forEach(userEntity -> {
                    clusterResourcePoolMapper.delete(
                            new LambdaQueryWrapper<ClusterResourcePool>().like(ClusterResourcePool::getUserIds, userEntity.getUserSid())
                                                                         .eq(ClusterResourcePool::getClusterId, clusterEntity.getId()));
                });
            }
        }

    }

    /**
     * 新增资源池
     *
     * @param list bss数据
     */
    private void addDrpPools(List<CfnResourcePool> list) {
        List<CfnResourcePool> bssAddPools = list.stream().filter(pool -> !pool.isDel()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(bssAddPools) && ObjectUtil.isNotEmpty(bssAddPools.get(0).getClusterId())) {
            log.info("BSS新增专属资源池信息:{}", bssAddPools);
            ClusterEntity clusterEntity = clusterEntityMapper.selectOne(
                    new LambdaQueryWrapper<ClusterEntity>().like(ClusterEntity::getAttrData, bssAddPools.get(0).getClusterId()));
            if (ObjectUtil.isNotEmpty(clusterEntity)) {
                List<ClusterResourcePool> cfnPools = clusterResourcePoolMapper.selectList(
                        new LambdaQueryWrapper<ClusterResourcePool>().eq(ClusterResourcePool::getClusterId, clusterEntity.getId()));
                // 判断条件
                BiPredicate<CfnResourcePool, ClusterResourcePool> matchPredicate = (res1, res2) -> Objects.equals(
                        res1.getPoolId(), res2.getPoolId());
                // 需要插入的数据
                List<CfnResourcePool> subtract = ListUtil.subtract(bssAddPools, cfnPools, matchPredicate);
                log.info("CFN新增专属资源池信息:{}", subtract);
                subtract.stream().forEach(bssPool -> {
                    ClusterResourcePool cfnPool = new ClusterResourcePool();
                    cfnPool.setPoolId(bssPool.getPoolId());
                    cfnPool.setPoolName(bssPool.getPoolName());
                    cfnPool.setPoolType(bssPool.getPoolType());
                    cfnPool.setClusterId(clusterEntity.getId());
                    UserEntity userEntity = userEntityMapper.selectOne(
                            new LambdaQueryWrapper<UserEntity>().eq(UserEntity::getAccount, bssPool.getAccount())
                                                                .ne(UserEntity::getStatus, UserStatusEnum.DELETED.getType()));
                    if (ObjectUtil.isNotEmpty(userEntity)) {
                        cfnPool.setUserIds(userEntity.getUserSid().toString());
                    }
                    ClusterFlavor flavor = clusterFlavorMapper.selectOne(
                            new LambdaQueryWrapper<ClusterFlavor>().eq(ClusterFlavor::getFlavorId, bssPool.getFlavor())
                                                                   .eq(ClusterFlavor::getClusterId, clusterEntity.getId()));
                    if (ObjectUtil.isNotEmpty(flavor)) {
                        cfnPool.setFlavorId(flavor.getId());
                        cfnPool.setJobFlavorId(flavor.getId().toString());
                    }
                    cfnPool.setCreatedDt(new Date());
                    clusterResourcePoolMapper.insert(cfnPool);
                });
            }
        }
    }
}
