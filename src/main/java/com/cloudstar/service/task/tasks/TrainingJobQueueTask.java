package com.cloudstar.service.task.tasks;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cloudstar.common.base.constant.RedisCacheKeyEnum;
import com.cloudstar.common.base.enums.TrainingJobStatusEnum;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.component.redis.util.RedisUtil;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.cluster.ClusterFlavorMapper;
import com.cloudstar.dao.mapper.cluster.ClusterResourcePoolMapper;
import com.cloudstar.dao.mapper.training.TrainingJobEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.cluster.ClusterResourcePool;
import com.cloudstar.dao.model.training.TrainingJobEntity;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.MonitorUnusedResponse;
import com.cloudstar.service.grpc.AgentCollectGrpc;
import com.cloudstar.service.grpc.AgentCollectGrpc.AgentCollectBlockingStub;
import com.cloudstar.service.grpc.GrpcManage;
import com.cloudstar.service.grpc.agentMonitorCollectProto.syncCollectRequest;
import com.cloudstar.service.grpc.agentMonitorCollectProto.syncCollectResponse;
import com.cloudstar.service.grpcservice.facade.AgentTrainingJobGroupService;
import com.cloudstar.service.grpcservice.facade.AgentTrainingJobService;
import com.cloudstar.service.pojo.dto.cluster.ClusterEngineFlavorDto;
import com.cloudstar.service.pojo.dto.cluster.ClusterFlavorResourceDto;
import com.cloudstar.service.pojo.dto.training.CoordinationJobInfo;
import com.cloudstar.service.pojo.dto.trainingjob.TrainingJobMsgDto;
import com.cloudstar.service.task.base.bean.AddJobParam;
import com.cloudstar.service.utils.RabbitMqMsgUtil;
import com.cloudstar.service.utils.TrainingJobMsgUtil;

import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 作业排队定时任务
 *
 * <AUTHOR>
 * @date 2022/9/28 15:46
 */
@Slf4j
@Component
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class TrainingJobQueueTask implements Job {

    RedisUtil redisUtil;

    TrainingJobEntityMapper trainingJobEntityMapper;

    ClusterEntityMapper clusterEntityMapper;

    ClusterFlavorMapper clusterFlavorMapper;

    ClusterResourcePoolMapper clusterResourcePoolMapper;

    ServerClient serverClient;

    TrainingJobMsgUtil trainingJobMsgUtil;

    RabbitMqMsgUtil rabbitMqMsgUtil;


    /**
     * 定时任务配置
     */
    public static AddJobParam initJobParam() {
        return AddJobParam.builder()
                          //每30秒执行一次
                          .cronExpression("0/30 * * * * ? *")
                          .jobName("training-job-queue-job")
                          .jobGroup("training-job-queue-group")
                          .triggerName("training-job-queue-trigger")
                          .triggerGroup("training-job-queue-trigger-group")
                          .description("training job queue")
                          .clazz(TrainingJobQueueTask.class)
                          .build();

    }


    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void execute(JobExecutionContext context) throws JobExecutionException {
        log.info("training job queue schedule begin");
        trainingJobDequeue();
        log.info("training job queue schedule finish");

    }

    /**
     * 作业出队
     */
    private void trainingJobDequeue() {
        //获取最新的监控数据
        StringBuilder cacheKeySb = new StringBuilder(RedisCacheKeyEnum.TRAINING_JOB_QUEUE.getKey());
        cacheKeySb.append("::").append("*");
        Set<String> allKeys = redisUtil.scan(cacheKeySb.toString(), 1000);
        log.info("作业队列：【{}】", allKeys.toString());
        if (ObjectUtil.isNotEmpty(allKeys)) {
            for (String cacheKey : allKeys) {
                //获取第一个数据
                List<String> jobIds = redisUtil.lRange(cacheKey, 0, 0);
                if (CollectionUtil.isNotEmpty(jobIds)) {
                    String[] split = cacheKey.split("::");
                    String clusterId = split[1];
                    String poolId = split[2];
                    TrainingJobEntity jobEntity = trainingJobEntityMapper.selectById(jobIds.get(0));

                    if (Objects.isNull(jobEntity)) {
                        log.info("异常数据，查询不到作业实例，应移除数据");
                        redisUtil.lRemove(cacheKey, 0, jobIds.get(0));
                        log.info("移除数据：作业队列：【{}】,队列队首作业信息：【{}】", cacheKey, jobIds.get(0));
                        continue;
                    }
                    log.info("作业队列：【{}】,队列队首作业信息：【{}】", cacheKey, jobEntity.toString());
                    //协同作业
                    if ("COORDINATION".equals(jobEntity.getJobType())) {
                        //判断监控是否满足数据 满足下发数据，并将其他队列里面数据删除
                        if (flavorIsMatch(Long.parseLong(clusterId), poolId, Long.parseLong(jobEntity.getSpec()))) {
                            Map<String, Object> params = new HashMap<>();
                            params.put("pool_id", poolId);
                            List<ClusterResourcePool> poolList = clusterResourcePoolMapper.selectByMap(params);
                            if (poolList != null) {
                                jobEntity.setPoolId(poolList.get(0).getId().toString());
                            }
                            jobEntity.setStatus(TrainingJobStatusEnum.SCHEDULING.getType());
                            trainingJobEntityMapper.updateById(jobEntity);
                            sendTrainingJob(jobEntity, cacheKey);
                            removeOtherQueue(allKeys, cacheKey, jobEntity.getId().toString());
                        }
                    } else {
                        //普通作业
                        //只有排队中的作业才下发，变成调度中，或者其他状态的作业就不下发，直接从队列移除
                        if (TrainingJobStatusEnum.QUEUING.getType().equals(jobEntity.getStatus())) {
                            ClusterEngineFlavorDto dto = null;
                            if (ObjectUtil.isNotEmpty(jobEntity.getFlavorId())) {
                                //镜像方式提交作业，只获取规格id
                                if (ObjectUtil.isNotEmpty(jobEntity.getInputImageId())) {
                                    dto = clusterEntityMapper.queryClusterIdByFlavor(jobEntity.getFlavorId(),
                                                                                     Long.parseLong(clusterId));
                                } else {
                                    //引擎方式提交作业，获取规格id和引擎id
                                    dto = clusterEntityMapper.queryClusterIdByEngineAndFlavor(
                                            jobEntity.getEngineVersion(),
                                            jobEntity.getFlavorId(),
                                            Long.parseLong(clusterId));
                                }
                            } else {
                                dto = new ClusterEngineFlavorDto();
                                dto.setFlavorId(Long.parseLong(jobEntity.getSpec()));
                                dto.setEngineId(jobEntity.getEngineId());
                            }
                            //判断监控是否满足数据 满足下发数据，并将其他队列里面数据删除
                            if (flavorIsMatch(Long.parseLong(clusterId), poolId, dto.getFlavorId())) {
                                jobEntity.setClusterId(Long.parseLong(clusterId));
                                jobEntity.setSpec(dto.getFlavorId().toString());
                                //引擎方式提交作业
                                if (ObjectUtil.isEmpty(jobEntity.getInputImageId())) {
                                    jobEntity.setEngineId(dto.getEngineId());
                                }
                                jobEntity.setStatus(TrainingJobStatusEnum.SCHEDULING.getType());
                                Map<String, Object> params = new HashMap<>();
                                params.put("pool_id", poolId);
                                List<ClusterResourcePool> poolList = clusterResourcePoolMapper.selectByMap(params);
                                if (poolList != null) {
                                    ClusterResourcePool pool = poolList.get(0);
                                    jobEntity.setPoolId(pool.getId().toString());
                                }
                                trainingJobEntityMapper.updateById(jobEntity);
                                sendTrainingJob(jobEntity, cacheKey);
                                removeOtherQueue(allKeys, cacheKey, jobEntity.getId().toString());
                            }
                        } else {
                            redisUtil.lRemove(cacheKey, 0, jobEntity.getId().toString());
                            log.info("作业队列：【{}】,作业【{}】已下发，无需再次下发", cacheKey, jobEntity);
                        }
                    }

                }
            }
        }
    }

    /**
     * 判断规格是否满足作业需求
     *
     * @param clusterId 集群id
     * @param poolId 资源池id
     * @param flavorId 规格id
     */
    private boolean flavorIsMatch(Long clusterId, String poolId, Long flavorId) {
        String flavorMatch = System.getenv("MONITOR_FLAVOR_MATCH");
        if ("true".equalsIgnoreCase(flavorMatch)) {
            log.warn("跳过监控资源检查，直接下发到对应集群!!");
            return true;
        }

        final ClusterFlavorResourceDto clusterFlavorDto = clusterFlavorMapper.queryFlavorById(flavorId);
        boolean flag = false;
        if (ObjectUtil.isEmpty(clusterFlavorDto)) {
            log.error("作业规格信息为空！");
        }
        //获取调度中的作业占用资源池情况
        ClusterFlavorResourceDto queueSum = clusterFlavorMapper.queryFlavorSum(clusterId, poolId);
        //获取集群的监控信息 查询资源池和集群下的最新监控数据
        ClusterFlavorResourceDto monitorDto = new ClusterFlavorResourceDto();
        log.info("获取监控信息入参：集群id【{}】，资源池id：【{}】", clusterId, poolId);
        Rest<MonitorUnusedResponse> result = serverClient.getUnusedMonitor(clusterId, poolId);
        if (ObjectUtil.isNotEmpty(result.getData())) {
            MonitorUnusedResponse response = result.getData();
            log.info("资源池监控数据：【{}】", result.getData());
            monitorDto.setCpu(response.getCpu() - queueSum.getCpu());
            monitorDto.setMemory(response.getMemory() - queueSum.getMemory());
            monitorDto.setNpu(response.getNpu() - queueSum.getNpu());
            monitorDto.setGpu(response.getGpu() - queueSum.getGpu());
            log.info("实际资源池监控数据（减去调度中占用规格）【{}】", monitorDto);
            log.info("作业下发需满足资源情况：【{}】", clusterFlavorDto.toString());
            if (clusterFlavorDto.getCpu() <= monitorDto.getCpu()
                    && clusterFlavorDto.getNpu() <= monitorDto.getNpu()
                    && clusterFlavorDto.getGpu() <= monitorDto.getGpu()
                    && clusterFlavorDto.getMemory() <= monitorDto.getMemory()) {
                ClusterEntity entity = clusterEntityMapper.selectById(clusterId);
                //判断集群状态，只用正常状态的集群才能下发
                if ("NORMAL".equals(entity.getStatus())) {
                    flag = true;
                }
            }
        } else {
            log.error("监控数据采集失败！");
        }
        log.info("是否满足作业下发需求：【{}】", flag);
        return flag;
    }

    /**
     * 下发作业
     *
     * @param jobEntity 作业实体
     */
    private void sendTrainingJob(TrainingJobEntity jobEntity, String cacheKey) {
        log.info("作业队列：【{}】,作业【{}】开始出队", cacheKey, jobEntity);
        ClusterEntity entity = clusterEntityMapper.selectById(jobEntity.getClusterId());
        try {
            //协同作业子作业
            if ("COORDINATION".equals(jobEntity.getJobType())) {
                StringBuilder key = new StringBuilder(RedisCacheKeyEnum.TRAINING_JOB_GROUP_SERVER_INFO.getKey());
                key.append("::").append(jobEntity.getJobGroupId()).append("::").append(jobEntity.getId());
                String info = redisUtil.get(key.toString());
                log.info("作业信息：【{}】: 【{}】", key, info);
                if (ObjectUtil.isNotEmpty(info)) {
                    CoordinationJobInfo coordinationJobInfo = JSONUtil.toBean(info, CoordinationJobInfo.class);
                    jobEntity.setCoordinationJob(coordinationJobInfo);
                } else {
                    jobEntity.setStatus(TrainingJobStatusEnum.TERMINATED.getType());
                    trainingJobEntityMapper.updateById(jobEntity);
                    //从队列里面移除作业
                    redisUtil.lRemove(cacheKey, 0, jobEntity.getId().toString());
                    log.info("作业队列：【{}】已终止,作业【{}】已出队完成", cacheKey, jobEntity);
                    return;
                }
            }
            AgentTrainingJobService jobService = AgentTrainingJobService.build(entity.getAdapterUuid());
            jobService.addTrainingJob(jobEntity);
            //从队列里面移除作业
            redisUtil.lRemove(cacheKey, 0, jobEntity.getId().toString());
            log.info("作业队列：【{}】,作业【{}】已出队完成", cacheKey, jobEntity);

            rabbitMqMsgUtil.recordJobEvent(jobEntity.getId(), jobEntity.getStatus(), new Date());
        } catch (Exception e) {
            //从队列里面移除作业
            jobEntity.setStatus(TrainingJobStatusEnum.FAILED.getType());
            jobEntity.setErrorMsg("作业调度异常，调度失败！");
            trainingJobEntityMapper.updateById(jobEntity);
            redisUtil.lRemove(cacheKey, 0, jobEntity.getId().toString());
            //停掉作业组
            if ("COORDINATION".equals(jobEntity.getJobType())) {
                Map<String, Object> params = new HashMap<>();
                params.put("cluster_flag", "SYSTEM");
                ClusterEntity clusterEntity = clusterEntityMapper.selectByMap(params).get(0);
                AgentTrainingJobGroupService agentTrainingJobGroupService = AgentTrainingJobGroupService.build(
                        clusterEntity.getAdapterUuid());
                agentTrainingJobGroupService.stopTrainingJobGroup(jobEntity.getJobGroupId());
            }
            //发送消息
            TrainingJobMsgDto dto = new TrainingJobMsgDto();
            dto.setJobId(jobEntity.getId().toString());
            dto.setJobName(jobEntity.getName());
            dto.setJobStatus("调度失败");
            dto.setSendAccount(jobEntity.getCreatedBy());
            dto.setSendUserSid(jobEntity.getUserSid());
            trainingJobMsgUtil.sendMsg(dto);
            log.error("作业下发失败，队列信息：【{}】，作业信息：【{}】，错误消息：【{}】", cacheKey, jobEntity, e);

            rabbitMqMsgUtil.recordJobEvent(jobEntity.getId(), jobEntity.getStatus(), new Date());
        }

    }

    /**
     * 获取最新的监控数据
     *
     * @throws InterruptedException 异常
     */
    private void syncCollect() throws InterruptedException {
        try {
            List<ClusterEntity> clusterEntityList = clusterEntityMapper.selectList(new QueryWrapper<>());
            clusterEntityList.stream().forEach(entity -> {
                AgentCollectBlockingStub blockingStub = (AgentCollectBlockingStub) GrpcManage.getStub(
                        entity.getAdapterUuid(),
                        AgentCollectGrpc.AgentCollectBlockingStub.class);
                syncCollectResponse allocated = blockingStub.syncCollect(
                        syncCollectRequest.newBuilder().setCollectType("allocated").build());
                syncCollectResponse job = blockingStub.syncCollect(
                        syncCollectRequest.newBuilder().setCollectType("job").build());
                log.info("手动采集监控信息响应值:集群:[{}],job采集情况:[{}],allocated采集情况:[{}]",
                         entity.getClusterName(),
                         job.getStatus(),
                         allocated.getStatus());
            });
        } catch (Exception e) {
            log.error("手动采集监控数据失败:[{}]", e);
        }
        Thread.sleep(300);
    }

    /**
     * 移除其他队列里面的这个job
     *
     * @param keys key队列
     * @param cacheKey 当前缓存key
     * @param jobId 作业id
     */
    private void removeOtherQueue(Set<String> keys, String cacheKey, String jobId) {
        for (String thisKey : keys) {
            //不是当前队列 就将队列里面的数据移除掉
            if (!thisKey.equals(cacheKey)) {
                //从其他队列里面移除作业
                redisUtil.lRemove(thisKey, 0, jobId);
                log.info("作业队列：【{}】，取消排队的作业id：【{}】", thisKey, jobId);
            }
        }
    }

}
