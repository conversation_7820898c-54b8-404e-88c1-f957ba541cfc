package com.cloudstar.service.task.tasks;

import com.cloudstar.service.facade.bill.BizBillUsageItemService;
import com.cloudstar.service.task.base.bean.AddJobParam;

import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 做业计量按月统计
 *
 * <AUTHOR>
 * @date 2022-09-29 09:39
 */
@Slf4j
@Component
@DisallowConcurrentExecution
@PersistJobDataAfterExecution
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class BillSummaryTask implements Job {

    /**
     * 初始化工作参数
     *
     * @return {@link AddJobParam}
     */
    public static AddJobParam initJobParam() {
        return AddJobParam.builder()
                          //一个小时统计一次
                          .cronExpression("0 0 /1 * * ?")
                          .jobName("bill-job")
                          .jobGroup("bill-job-group")
                          .triggerName("bill-trigger")
                          .triggerGroup("bill-trigger-group")
                          .description("it's bill")
                          .clazz(BillSummaryTask.class)
                          .build();
    }

    BizBillUsageItemService bizBillUsageItemService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {

        log.info("开始执行月度账单定时任务!");
        bizBillUsageItemService.billSummary();
        log.info("月度账单定时任务已完成!");

    }
}
