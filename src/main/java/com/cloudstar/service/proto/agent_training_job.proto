syntax = "proto3";
package protocol;
option go_package = "../grpc/;protocol";
option java_multiple_files = false;
option java_package = "com.cloudstar.service.grpc";
option java_outer_classname = "AgentTrainingJobProto";

service AgentTrainingJob{
  //创建训练作业
  rpc addTrainingJob(AddTrainingJobRequest) returns(AddTrainingJobResponse);
  rpc DeleteTrainingJob(JobOperateRequest) returns(JobOperateResponse);
  rpc StopTrainingJob(JobOperateRequest) returns(JobOperateResponse);
  rpc ShowTrainingJobLogsPreview(JobOperateRequest) returns(ShowTrainingJobLogsPreviewResp);
  rpc ShowTrainingJobLogsFromObs(JobOperateRequest) returns(ShowTrainingJobLogsFromObsResp);
}
message AddTrainingJobRequest{
  //作业用户id（子用户）
  string userId = 1;
  //作业元数据
  Metadata metadata = 2;
  //输入数据
  repeated dataResource inputs = 3;
  //输出数据
  repeated dataResource outputs = 4;
  //镜像文件
  dataResource inputImage = 5;
  // 自定义镜像启动命令
  string command = 6;
  //Ai引擎
  Engine engine = 7;
  //算法文件
  Algorithm algorithm = 9;
  //超参组。
  repeated ParamsGroup paramsGroup = 20;
  //规格参数
  SpecObj spec = 24;
  //资源信息
  FlavorInfo flavorInfo = 23;
  //子账号信息
  SubAccount subAccount = 26;
  //平台训练作业ID
  int64 cfnJobId = 27;
  //商汤作业请求
  Slurm slurm = 31;
}
//裸金属额外参数
message BmsExtraParam{

  //是否虚拟化
  bool  isVirtualized = 2;
  //虚拟化百分比
  int32 virtualizationPercentage = 3;

  //计算卡型号
  string computeProductName = 4;

  //节点用途
  string nodePurpose = 5;
}

message Slurm{
  //执行角色 目前只支持单个角色
  repeated TaskRoles taskRoles = 7;
  //执行类型 Start
  string executionType = 11;
}
message TaskRoles{
  //角色名
  string roleName = 1;
  //实例数 目前只支持1个
  string instances = 5;
  //规格
  Resources resources = 9;
}
message Resources {
  //资源规格
  string spec = 1;
  //描述
  string description = 3;
}
message SubAccount{
  //子账号id 上传算法账号
  string accountUuid = 1;
  //上传数据集账号uuid
  string dataUuid = 2;
  //ak
  string accessKey = 3;
  //sk
  string secretKey = 5;
  //obs地址
  string obsUrl = 7;
  //桶
  string bucket = 9;
}
message FlavorInfo{
  //资源池名称
  string name = 1;
  //资源池类型 专属或共享
  string type = 3;
  //调度方式 资源优先 费用优先等等
  string scheduleMode = 5;
}
message SpecObj {
  //资源规格id 需要被包含在modelarts所拥有的规格内
  string flavorId = 1;
  //节点数
  int32 nodeCount = 3;
  //资源池id
  string poolId = 4;
  //日志输出目录
  string logExportPathObj = 5;
  //裸金属作业额外参数
  BmsExtraParam bmsParam = 32;
}
message Metadata {
  //作业名称
  string name = 1;
  //作业类型 默认传job 底层作业类型
  string kind = 2;
  //作业描述。
  string description = 3;
  //作业分类 synergy协同作业 normal普通作业 image镜像作业
  string jobCategory = 5;
  //作业分步启动 preStart数据准备 start开始作业
  string jobCmd = 7;
  // 注解
  repeated annotationsOption annotations = 8;
}
// 注解
message annotationsOption {
  string key = 1;
  string value = 2;
}
message dataResource{
  //数据输入通道名称
  string name = 1 ;
  //数据输入通道描述信息
  string description = 3;
  //obs地址 就是训练文件所在的目录
  string obsUrl = 7;
  //数据集id
  int64 dataId = 10;
}
message AddTrainingJobResponse{
  //底层job_id
  //  string job_id = 1;
  string status = 1;
}
message InputDataResource{
  //数据集id
  int64  id = 1;
  //数据集名称
  string name = 5;
  //桶名称
  string bucket = 7;
  //地址
  string path = 9;
}
message Engine{
  //引擎名称
  string engineName = 1;
  //引擎版本
  string engineVersion = 3;
  //商汤作业需要指定镜像名称
  string imageName = 5;
}
message Algorithm{
  //算法文件id
  int64  id = 1;
  //算法名称
  string name = 5;
  //代码目录
  string code_dir = 7;
  //启动文件目录
  string bootFile = 8;
  //是否订阅模式
  bool isSubscribe = 9;
}
message ParamsGroup{
  //参数名
  string name = 1;
  //参数描述
  string description = 5;
  //值
  string value = 9;
  //参数类型
  string type = 11;
}

// 作业Request
message JobOperateRequest{
  string jobId = 1;
  //操作类型 eg:暂停[Suspend]、重启[Restart].
  string executionType = 3;
  //训练任务预定启动时间
  int64 presetTimestamp = 5;
  //容器名称
  string podName = 6;
}

message JobOperateResponse{
  bool status = 1;
}
// DeleteTrainingJob 删除作业
// StopTrainingJob 终止作业

// ShowTrainingJobLogsPreview 查看作业日志预览
message ShowTrainingJobLogsPreviewResp{
  string content = 1;
  int32 current_size = 2;
  int32 full_size = 3;
}
// ShowTrainingJobLogsFromObs 查看作业日志 OBS连接
message ShowTrainingJobLogsFromObsResp{
  string obs_url = 1;
}
