syntax = "proto3";
package protocol;
option go_package = "../grpc/;protocol";
option java_multiple_files = false;
option java_package = "com.cloudstar.service.grpc";
option java_outer_classname = "AgentObsProto";

service AgentObs{
  //创建文件目录
  rpc createFileDir(createFileDirRequest) returns(createFileDirResponse);
  //删除数据对象
  rpc deleteObject(deleteObjectRequest) returns(deleteObjectResponse);
  // 同步数据资源
  rpc syncDataStorage(SyncDataStorageProtoRequest) returns(SyncDataStorageProtoResponse);
  //判断文件或目录是否存在
  rpc existsFileOrFileDir(existsFileOrFileDirRequest) returns(existsFileOrFileDirResponse);

  // 创建桶策略
  rpc CreatePolicy(BucketPolicyRequest) returns (BucketPolicyResponse);
  // 删除桶策略
  rpc DeletePolicy(BucketPolicyRequest) returns (BucketPolicyResponse);
  // 查询桶策略
  rpc QueryPolicy(BucketPolicyRequest) returns (BucketPolicyResponse);

  // 发布数据集
  rpc publishDataStorage(PublishDataStorageRequest) returns(PublishDataStorageResponse);

  // 订阅数据集
  rpc subscribeDataStorage(SubscribeDataStorageRequest) returns(SubscribeDataStorageResponse);

  rpc unsubscribeDataStorage(UnsubscribeDataStorageRequest) returns(UnsubscribeDataStorageResponse);
}
message existsFileOrFileDirResponse{
  //true存在 false不存在
  bool isExists = 1;
}
message existsFileOrFileDirRequest{
  //子账号id
  string userId = 1;
  //文件或文件夹的路径 应从根目录开始拼接 目录user/local/ 文件user/a.txt
  string objectKey = 3;
}
message SyncDataStorageProtoRequest{
  // 租户子账号ID
  string userId = 1;
  // 文件夹
  string fileDir = 2;
  // 是否排除文件夹
  bool excludeFileDir = 3;
}

message SyncDataStorageProtoResponse{
  // 存储使用量
  uint64 storageUsage = 4;
  // 文件个数
  uint64 fileNum = 5;
}
message deleteObjectResponse{
  //删除标记 false不存在 true存在
  bool deleteMarker = 1;
}
message deleteObjectRequest{
  //子账号id
  string userId = 1;
  //桶名称
  string bucketName = 3;
  //删除对象名称（需要包含文件夹的路径 data_input/user/animal.jpg 删除animal图片）
  string objectKey = 5;
}

message createFileDirRequest{
  //子用户id
  string userId = 1;
  //文件目录名称
  string fileDir = 3;
  //桶名称
  string bucketName = 5;
}
message createFileDirResponse{}


// 定义请求消息
message BucketPolicyRequest {
  // 桶名称
  string bucket_name = 1;
  // 文件目录路径
  string fileDir = 2;
  // 账户UUID
  string account_uuid = 3;
}

// 定义响应消息
message BucketPolicyResponse {
  // 操作是否成功
  bool success = 1;
  // 返回的消息
  string message = 2;
  // 返回的账户UUID列表，可以为空
  repeated string account_uuids = 3;
}

message PublishDataStorageRequest {

  //数据集ID
  string dataStorageId = 1;

  //发布ID
  string publishId = 2;

  //发布者账 底层账号
  string userSid = 3;

}

message PublishDataStorageResponse {
  //发布状态
  bool status = 1;

  //发布后桶名称
  string publishBucket = 2;

  //发布后数据集地址
  string publishPath = 3;
}

message SubscribeDataStorageRequest {
  //发布数据集ID
  string publishDataStorageId = 1;

  //订阅人 底层账号
  string userSid = 2;
}


message SubscribeDataStorageResponse {
  bool status = 1;

}

message UnsubscribeDataStorageRequest {
  //发布数据集ID
  string publishDataStorageId = 1;

  //订阅人 底层账号
  string userSid = 2;
}

message UnsubscribeDataStorageResponse {

  bool status = 1;
}