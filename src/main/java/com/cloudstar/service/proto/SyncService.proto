syntax = "proto3";
package protocol;
option java_multiple_files = false;
option java_package = "com.cloudstar.service.grpc";
option java_outer_classname = "SyncServiceProto";
option go_package = "./;protocol";


service SyncService{
  //登录
  rpc clusterLoginIn(ClusterLoginInReq) returns(ClusterLoginInResp){};
  //集群同步
  rpc clusterSync(ClusterSyncReq) returns(ClusterSyncResp){};
  //集群账号同步
  rpc clusterAccountSync(ClusterAccountSyncReq) returns(ClusterAccountSyncResp){};
  //集群引擎同步
  rpc clusterEngineSync(ClusterEngineSyncReq) returns(ClusterEngineSyncResp){};
  //集群资源规格同步
  rpc clusterFlavorSync(ClusterFlavorSyncReq) returns(ClusterFlavorSyncResp){};
  //心跳检查
  rpc clusterCheckHeartbeat(ClusterCheckHeartbeatReq) returns(ClusterCheckHeartbeatReply){};

  //集群引擎同步-裸金属
  rpc clusterEngineBmsSync(ClusterEngineBmsSyncReq) returns(ClusterEngineBmsSyncResp){};

  //集群规格同步-裸金属
  rpc clusterFlavorBmsSync(ClusterFlavorBmsSyncReq) returns(ClusterFlavorBmsSyncResp){};
}

//集群同步入参
message ClusterSyncReq{
  //集群名称
  string clusterName = 1;
  //心跳采集频率
  int32 heartCheck = 2;
  //数据采集频率
  int32 dataCollect = 3;
  //监控采集频率
  int32 monitorCollect = 4;
  //适配器uuid
  string adapterUUid = 5;
  //算力底座
  string calculateBase = 6;
  //HCS地址
  string hcsAddress = 7;
  //AK
  string accessKey = 8;
  //SK
  string secretKey = 9;
  //对象存储
  string obs = 10;
  //华为obs地址
  string hwObsAddress = 11;
  //自建对象地址
  string selfObsAddress = 12;
  //自建对象账号
  string selfObsAccount = 13;
  //自建对象密码
  string selfObsPassword = 14;
  //镜像仓库
  string swr = 15;
  //华为swr地址
  string hwSwrAddress = 16;
  //公共镜像组织
  string hwPublicMirrorOrganization = 17;
  //自建swr地址
  string selfSwrAddress = 18;
  //自建swr账号
  string selfSwrAccount = 19;
  //自建swr密码
  string selfSwrPassword = 20;
  //公共镜像组织
  string selfPublicMirrorOrganization = 21;
}

//集群同步返回响应
message ClusterSyncResp{
  //同步结果
  int32 result = 1;
}

//集群账号入参
message ClusterAccountSyncReq{
}

//集群账号响应
message ClusterAccountSyncResp{
  //集群账号列表
  repeated ClusterAccountSync list = 1;
}

//集群账号
message ClusterAccountSync{
  //账号uuid
  string accountUuid = 1;
  //账号
  string account = 2;
  // 用户OBS id
  string obsId = 3;
  // 用户projectId
  string projectId = 4;
}

//集群引擎入参
message ClusterEngineSyncReq{

}

//集群引擎响应
message ClusterEngineSyncResp{
  //集群引擎列表
  repeated ClusterEngineSync list = 1;
}

//同步结果
message ClusterEngineSync{
  //引擎id
  string engineId = 1;
  //引擎名称
  string engineName = 2;
  //引擎版本
  string engineVersion = 3;
  //cpu镜像地址
  string cpuImageUrl = 4;
  //gpu镜像地址
  string gpuImageUrl = 5;
  //镜像版本
  string imageVersion = 6;
}

//集群规格入参
message ClusterFlavorSyncReq{

}

//集群规格响应
message ClusterFlavorSyncResp{
  //规格列表
  repeated ClusterFlavorSync list = 1;
}

message  ClusterFlavorSync{
  //规格id
  string flavorId = 1;
  //规格类型
  string flavorType = 2;
  //可以选择的最大节点数量
  int32 maxNum = 3;
  //cpu架构
  string cpuArch = 4;
  //cpu核数
  int32 cpuCoreNum = 5;
  //gpu卡数
  int32 gpuUnitNum = 6;
  //gpu产品名
  string gpuProductName = 7;
  //gpu内存
  string gpuMemory = 8;
  //npu卡数
  string npuUnitNum = 9;
  //npu产品名
  string npuProductName = 10;
  //npu内存
  string npuMemory = 11;
  //内存大小
  int32 memorySize = 12;
  //内存单元数
  string memoryUnit = 13;
  //磁盘大小
  int32 diskSize = 14;
  //磁盘大小单位
  string diskUnit = 15;
}

//登录入参
message ClusterLoginInReq{
  //账号
  string username = 1;
  //密码
  string password = 2;
}

//登录响应
message ClusterLoginInResp{
  //token
  string jwtToken = 1;
  //状态
  string status = 2;
}

// 健康检查
message ClusterCheckHeartbeatReq{
}

//健康检查响应
message ClusterCheckHeartbeatReply{
  //状态
  bool status = 1;
}

message ClusterEngineBmsSyncReq{
  //集群引擎列表
  repeated ClusterEngineBmsSync list = 1;
}

message ClusterEngineBmsSync{
  //id
  int64 id = 1;
  //底层引擎id
  string engineId = 2;
  //引擎名称
  string engineName = 3;
  //引擎版本
  string engineVersion = 4;
  //cpu规格下对应镜像
  string cpuImageUrl = 5;
  //gpu或者Ascend规格下对应镜像
  string gpuImageUrl = 6;
  //镜像版本
  string imageVersion = 7;
  //资源类型 cpu gpu npu
  string resourceType = 8;
  //架构类型 arm或者x86
  string archType = 9;
}

message ClusterEngineBmsSyncResp{
  //状态
  bool status = 1;
}

message ClusterFlavorBmsSyncReq{
  //集群规格列表
  repeated ClusterFlavorBmsSync list = 1;
}

message ClusterFlavorBmsSync{

  string flavorId = 1;
  //规格类型
  string flavorType = 2;
  //可以选择的最大节点数量
  int32 maxNum = 3;
  //cpu架构
  string cpuArch = 4;
  //cpu核数
  int32 cpuCoreNum = 5;
  //gpu卡数
  int32 gpuUnitNum = 6;
  //gpu产品名
  string gpuProductName = 7;
  //gpu内存
  string gpuMemory = 8;
  //npu卡数
  string npuUnitNum = 9;
  //npu产品名
  string npuProductName = 10;
  //npu内存
  string npuMemory = 11;
  //内存大小
  int32 memorySize = 12;
  //内存单元数
  string memoryUnit = 13;
  //磁盘大小
  int32 diskSize = 14;
  //磁盘大小单位
  string diskUnit = 15;
  //规格id
  int64 id = 16;

  int32 isVirtualized = 17;

  int32 virtualizationPercentage = 18;

  string computeProductName = 19;

  string nodePurpose = 20;

}

message ClusterFlavorBmsSyncResp{
  //状态
  bool status = 1;
}
