syntax = "proto3";
package protocol;
option go_package = "../grpc/;protocol";
option java_multiple_files = false;
option java_package = "com.cloudstar.service.grpc";
option java_outer_classname = "AgentTrainingJobCallBackProto";

service AgentTrainingJobCallBack{
  // 训练作业结束回调
  rpc trainingJobCallBack(TrainingJobCallBackRequest) returns(TrainingJobCallBackResponse);
  // 适配器进行步骤回调
  rpc agentPhaseCallBack(AgentPhaseCallBackRequest) returns(AgentPhaseCallBackResponse);
}
message AgentPhaseCallBackResponse{}
message AgentPhaseCallBackRequest{
  //回调状态的标识
  //type为job返回cfnJobId
  string markId = 1;
  //同步状态类型 job
  string type = 3;
  //同步状态值
  string statusCode = 4;
  string statusValue = 5;
  //描述
  string describe = 11;
  //状态产生时间
  int64 startTime = 12;
}

message TrainingJobCallBackRequest{
  // 作业ID
  string jobId = 1;
  // 执行状态： 运行完成：COMPLETED ；运行失败：FAILED；已终止：TERMINATED；停止中：STOPPING；删除中：STOPPING
  string status = 2;
  //底层创建时间
  int64  create_time = 3;
  //底层开始时间
  int64  start_time = 4;
  //作业运行时间
  int64 duration = 5;
  // 如果任务执行异常，返回该字段
  string message = 6;
  // 平台训练作业ID
  int64 cfnJobId = 7;

  //裸金属 容器id 用于监控采集
  string containerId = 8;
}

message TrainingJobCallBackResponse{

}