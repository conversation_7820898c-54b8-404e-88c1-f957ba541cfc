package com.cloudstar.service.facade.user;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.sdk.server.pojo.ResetPwdReq;
import com.cloudstar.service.pojo.dto.user.CheckUserStatusDto;
import com.cloudstar.service.pojo.vo.requestvo.subuser.CreateUsersRequest;
import com.cloudstar.service.pojo.vo.requestvo.subuser.QuerySubUserReq;
import com.cloudstar.service.pojo.vo.requestvo.subuser.SyncUsersRequest;
import com.cloudstar.service.pojo.vo.requestvo.user.FindPwdReq;
import com.cloudstar.service.pojo.vo.requestvo.user.ResetSubUserPwdReq;
import com.cloudstar.service.pojo.vo.requestvo.user.UpdateUserEmailReq;
import com.cloudstar.service.pojo.vo.requestvo.user.UpdateUserMobileReq;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUserMappingResp;
import com.cloudstar.service.pojo.vo.responsevo.user.ParentUserInfoRes;
import com.cloudstar.service.pojo.vo.responsevo.user.UserDetailResponse;

import java.util.List;

/**
 * 租户service
 *
 * <AUTHOR>
 * @date 2022/7/20 17:39
 */
public interface UserEntityService extends IService<UserEntity> {

    /**
     * 子用户分页查询
     */
    Page<UserEntity> selectSubUser(Page<UserEntity> pageRequest, QuerySubUserReq request);


    /**
     * 批量创建子用户
     */
    ClusterUserMappingResp createUser(CreateUsersRequest request);

    List<ParentUserInfoRes> getParentUserInfo();

    /**
     * 重置子用户状态
     */
    int resetStatusByUserSid(Long userSid);

    /**
     * 删除子用户
     */
    void deleteSubUserByIds(List<Long> userSid);

    /**
     *清空用户缓存
     */
    void clearLoginUserCache(Long userSid);

    /**
     * 检查用户状态
     *
     * @return 返回值
     */
    CheckUserStatusDto checkUserStatus();

    Boolean findPwd(FindPwdReq req);

    Boolean resetPassword(ResetPwdReq req);

    UserDetailResponse getUserDetail(Long id);

    Boolean updateUserMobile(UpdateUserMobileReq updateUserMobileReq);

    Boolean updateUserEmail(UpdateUserEmailReq updateUserEmailReq);

    Boolean resetSubPassword(ResetSubUserPwdReq request);

    Boolean createUserSync(SyncUsersRequest request);

    Rest<Boolean> resetPwd(ResetPwdReq request);
}
