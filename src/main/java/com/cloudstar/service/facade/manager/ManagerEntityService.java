package com.cloudstar.service.facade.manager;

import com.cloudstar.dao.model.ManagerEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.service.pojo.dto.manager.ManagerEntityDto;

import java.util.List;

/**
 * 管理员service
* <AUTHOR>
* @description 针对表【manager_entity(运营运维管理员账号表)】的数据库操作Service
* @createDate 2022-10-09 10:00:45
*/
public interface ManagerEntityService extends IService<ManagerEntity> {

    List<ManagerEntityDto> selectOperationManagerAndSystemManager();
}
