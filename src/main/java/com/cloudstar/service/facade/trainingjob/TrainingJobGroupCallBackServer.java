package com.cloudstar.service.facade.trainingjob;

/**
 * 训练作业回调接口
 *
 * <AUTHOR>
 * @date 2022-09-01 15:52
 */
public interface TrainingJobGroupCallBackServer {


    /**
     * 修改作业状态
     *
     * @param jobGroupId jobGroupId
     * @param status 状态
     * @param createTime 底层作业创建时间
     * @param startTime 底层作业开始时间
     * @param duration 作业运行时长
     */
    void updateTrainingJobGroupStatus(String jobGroupId, String status, Long createTime, Long startTime, Long duration);

    void trainingJobSuccess(String jobGroupId, String status, Long createTime, Long startTime, Long duration);

    boolean checkStatus(String jobGroupId, String status);

    void startSubJob(String jobGroupId, Integer port);

    void updateJobGroupDuration(String jobGroupId, long duration);

    void updateEndDt(String jobGroupId);
}
