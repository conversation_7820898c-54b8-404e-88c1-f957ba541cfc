package com.cloudstar.service.facade.bigscreen;

import com.cloudstar.dao.model.bigscreen.CollectAllocateNpu;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.service.pojo.dto.bigscreen.CollectCommonResourceDto;

import java.util.List;

/**
 * NPU采集service
* <AUTHOR>
* @description 针对表【collect_allocate_npu(资源池分配率-npu)】的数据库操作Service
* @createDate 2022-10-26 16:47:14
*/
public interface CollectAllocateNpuService extends IService<CollectAllocateNpu> {

    CollectCommonResourceDto getNpuResource(Long clusterId, Integer monitorCollect);

    List<CollectCommonResourceDto> getNpuAllocatedResourceTrend(Long clusterId, Integer monitorCollect, Integer trendMonitorCollect, String type);

}
