package com.cloudstar.service.facade.bigscreen;

import com.cloudstar.service.pojo.dto.bigscreen.ModelArtsResourceDto;
import com.cloudstar.service.pojo.dto.bigscreen.SynergyClusterDto;
import com.cloudstar.service.pojo.dto.bigscreen.TrainingJobGroupDto;
import com.cloudstar.service.pojo.dto.bigscreen.TrainingJobTotalDto;
import com.cloudstar.service.pojo.vo.responsevo.bigscreen.StatusTotalResp;
import com.cloudstar.service.pojo.vo.responsevo.bigscreen.TrainingJobPoolResp;
import com.cloudstar.service.pojo.vo.responsevo.bigscreen.TrendResp;

import java.util.List;

/**
 * 协同作业service
 *
 * <AUTHOR>
 * @date 2022/11/12 15:29
 */
public interface SynergyBigScreenService {

    List<SynergyClusterDto> getSynergyCluster();

    TrendResp getTrainingJobGroupTrend(String type);

    TrendResp getTrainingJobTrend(String type);

    StatusTotalResp getTrainingJobGroupStatusTotal();

    StatusTotalResp getTrainingJobStatusTotal();

    List<TrainingJobGroupDto> getTrainingJobGroupNew();

    List<TrainingJobPoolResp> getTrainingJobPool();

    TrainingJobTotalDto getTrainingJobTotal();

    List<ModelArtsResourceDto> getUserResourceTop();
}
