package com.cloudstar.service.facade.bigscreen;

/**
 * 对外大屏接口--上海超算调用
 *
 * <AUTHOR>
 * @date 2024/1/24 14:57
 */
public interface ExternalBigScreenService {

    /**
     * 获取当前系统租户信息
     */
    String getUserList();

    /**
     * 获取集群资源使用情况
     */
    String getClusterResourceUsage();

    /**
     * 获取集群 接入资源池信息
     */
    String getClusterPoolList();

    /**
     * 获取资源池资源使用情况
     */
    String getPoolResourceUsage();

    /**
     * 获取集群作业情况
     */
    String getClusterJobList();
}
