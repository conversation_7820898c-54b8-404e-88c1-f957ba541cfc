package com.cloudstar.service.facade.res;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.dao.model.user.ResImageHubUser;
import com.cloudstar.service.grpc.AgentImageProto.ImageDate;
import com.cloudstar.service.grpc.AgentUserProto;

import java.util.List;

/**
 * 资源镜像用户服务
 *
 * <AUTHOR>
 * @date 2024/06/17
 */
public interface ResImageHubUserService extends IService<ResImageHubUser> {

    /**
     * 创建存储库用户
     *
     * @param userId 用户id
     *
     * @return {@link AgentUserProto.UserImageRes }
     */
    AgentUserProto.UserImageRes createRepositoryUser(String userId);

    /**
     * 查询用户镜像列表
     */
    List<ImageDate> queryUserImage(String userId);

    /**
     * 删除镜像
     *
     * @param imageId 图像id
     */
    void deleteImage(String imageId);

    /**
     * 获取用户
     *
     * @param userId 用户id
     *
     * @return {@link ResImageHubUser }
     */
    ResImageHubUser getUser(String userId);

    /**
     * 删除镜像仓库用户
     *
     * @param userId userId
     */
    boolean deleteRepositoryUser(String userId);
}
