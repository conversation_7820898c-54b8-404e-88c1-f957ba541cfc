package com.cloudstar.service.facade.res;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.model.res.ResModel;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelCreateRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelDeleteRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelPageRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelUpdateRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelVersionCreateRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelVersionDeleteRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelVersionDetailRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelVersionStatusRequest;
import com.cloudstar.service.pojo.vo.requestvo.model.ModelVersionUpdateRequest;
import com.cloudstar.service.pojo.vo.responsevo.model.MetaModelResponse;
import com.cloudstar.service.pojo.vo.responsevo.model.ModelCreateResponse;
import com.cloudstar.service.pojo.vo.responsevo.model.ModelPageResponse;
import com.cloudstar.service.pojo.vo.responsevo.model.ModelVersionCreateResponse;
import com.cloudstar.service.pojo.vo.responsevo.model.ModelVersionDetailResponse;
import com.cloudstar.service.pojo.vo.responsevo.model.NormalModelResponse;

import java.util.List;

/**
 * 模型Service
 *
 * <AUTHOR>
 */
public interface ResModelService extends IService<ResModel> {

    ModelCreateResponse create(ModelCreateRequest request);

    ModelVersionCreateResponse createVersion(ModelVersionCreateRequest request);

    Boolean delete(ModelDeleteRequest request);

    Boolean deleteVersion(ModelVersionDeleteRequest request);

    PageResult<ModelPageResponse> selectPage(ModelPageRequest request);

    PageResult<ModelVersionDetailResponse> getVersions(ModelVersionDetailRequest request);

    List<MetaModelResponse> getCompletedJob(Long clusterId);

    Long insertResModelVersion(ModelVersionCreateRequest version, Long resModelId, AuthUser authUser);

    Boolean modifyVersionStatus(ModelVersionStatusRequest request);

    Boolean updateModel(ModelUpdateRequest request);

    Boolean updateVersion(ModelVersionUpdateRequest request);

    List<NormalModelResponse> getNormalModel();
}
