package com.cloudstar.service.facade.res;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.model.res.ResImage;
import com.cloudstar.service.pojo.vo.requestvo.res.MirrorBindTagReq;
import com.cloudstar.service.pojo.vo.requestvo.res.MirrorDeleteVersionReq;
import com.cloudstar.service.pojo.vo.requestvo.res.MirrorPageReq;
import com.cloudstar.service.pojo.vo.requestvo.res.MirrorSearchReq;
import com.cloudstar.service.pojo.vo.requestvo.res.MirrorTagPageReq;
import com.cloudstar.service.pojo.vo.responsevo.res.MirrorCapacityTopRes;
import com.cloudstar.service.pojo.vo.responsevo.res.MirrorDetailsRes;
import com.cloudstar.service.pojo.vo.responsevo.res.MirrorGuideInfoRes;
import com.cloudstar.service.pojo.vo.responsevo.res.MirrorImageTagRes;
import com.cloudstar.service.pojo.vo.responsevo.res.MirrorNumberRes;
import com.cloudstar.service.pojo.vo.responsevo.res.MirrorTagRes;
import com.cloudstar.service.pojo.vo.responsevo.res.MirrorVersionRes;

import java.util.List;

/**
 * 申请单;(request_form)表服务接口
 *
 * <AUTHOR>
 * @date 2022-08-12 17:26
 */
public interface MirrorFormService extends IService<ResImage> {

    /**
     * 获取镜像信息
     *
     * @param imageName 图像名称
     *
     * @return {@link MirrorDetailsRes }
     */
    MirrorDetailsRes getDetails(String imageName);

    /**
     * 获取镜像版本
     *
     * @param pageReq 页面需求
     *
     * @return {@link PageResult }<{@link MirrorVersionRes }>
     */
    PageResult<MirrorVersionRes> getPageVersions(MirrorPageReq pageReq);

    /**
     * 分页查询镜像信息
     *
     * @param params params
     *
     * @return {@link PageResult }<{@link MirrorDetailsRes }>
     */
    PageResult<MirrorDetailsRes> page(MirrorSearchReq params);

    /**
     * 同步镜像
     */
    void sync();

    /**
     * 获取指南
     *
     * @return {@link MirrorGuideInfoRes }
     */
    MirrorGuideInfoRes getGuide();

    /**
     * 绑定标签
     *
     * @param req req
     */
    void bindTag(MirrorBindTagReq req);

    /**
     * 获取当镜像绑定的标签
     *
     * @param imageId 镜像id
     *
     * @return {@link List }<{@link MirrorTagRes }>
     */
    List<MirrorImageTagRes> getMirrorTags(String imageId);

    /**
     * 获取未绑定的镜像的标签列表
     *
     * @param req 绿色
     *
     * @return {@link PageResult }<{@link MirrorTagRes }>
     */
    PageResult<MirrorTagRes> getTagPage(MirrorTagPageReq req);

    /**
     * 删去
     *
     * @param imageName 图像名称
     */
    void delete(String imageName);

    /**
     * 删除版本
     *
     * @param req req
     */
    void deleteVersion(MirrorDeleteVersionReq req);

    /**
     * 统计镜像数量以及存储容量
     *
     * @return {@link MirrorNumberRes}
     */
    MirrorNumberRes getTotalNumber();

    /**
     * 获取镜像存储情况
     *
     * @return {@link MirrorCapacityTopRes}
     */
    List<MirrorCapacityTopRes> getTop(Integer size);
}
