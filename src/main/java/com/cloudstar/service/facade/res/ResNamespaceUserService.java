package com.cloudstar.service.facade.res;


import com.cloudstar.service.grpc.AgentUserProto;

/**
 * namespace 用户服务接口
 */
public interface ResNamespaceUserService {

    /**
     * 创建命名空间
     *
     * @param name 用户name
     */
    AgentUserProto.UserNameSpaceRes createNameSpace(String name);

    /**
     * 查询命名空间
     *
     * @param name 用户name
     */
    AgentUserProto.UserNameSpaceRes findNameSpace(String name);

    boolean deleteNameSpace(String name);
}
