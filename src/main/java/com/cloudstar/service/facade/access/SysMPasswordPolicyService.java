package com.cloudstar.service.facade.access;

import com.cloudstar.dao.model.access.SysMPasswordPolicy;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.dao.model.user.UserEntity;

/**
 * sys mpassword政策服务
 * sys mpassword政策服务
 *
 * <AUTHOR>
 * @description 针对表【sys_m_password_policy(密码策略表)】的数据库操作Service
 * @createDate 2022-09-16 14:26:41
 * @date 2022/09/16
 */
public interface SysMPasswordPolicyService extends IService<SysMPasswordPolicy> {


    SysMPasswordPolicy getPasswordPolicyByOrgSid(UserEntity user);
}
