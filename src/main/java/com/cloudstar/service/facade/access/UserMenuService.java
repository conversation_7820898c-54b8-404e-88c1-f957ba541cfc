package com.cloudstar.service.facade.access;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.dao.model.access.UserMenu;
import com.cloudstar.service.pojo.vo.requestvo.manager.CreateMenuRequest;
import com.cloudstar.service.pojo.vo.requestvo.manager.ManagerMenuAuthorizeReq;
import com.cloudstar.service.pojo.vo.requestvo.manager.UpdateMenuRequest;
import com.cloudstar.service.pojo.vo.responsevo.access.UserMenuDetailRes;
import com.cloudstar.service.pojo.vo.responsevo.access.UserMenuTreeRes;
import com.cloudstar.service.pojo.vo.responsevo.manager.MenuPermissionRes;

import java.util.List;

/**
 * 用户菜单服务
 *
 * <AUTHOR>
 * @description 针对表【user_menu(租户菜单表)】的数据库操作Service
 * @createDate 2022-08-16 20:51:08
 * @date 2022/08/17
 */
public interface UserMenuService extends IService<UserMenu> {

    boolean add(CreateMenuRequest createMenuRequest);

    List<UserMenuTreeRes> getManagerMenu(Long parentId);

    void updMenu(UpdateMenuRequest createMenuRequest);

    boolean removeMenu(Long menusId);

    MenuPermissionRes selectPermissionByMenusSid(Long menusSid);

    boolean menuAuthorize(ManagerMenuAuthorizeReq req);

    UserMenuDetailRes getUserMenuDetail(Long menuSid);
}
