package com.cloudstar.service.facade;


import com.cloudstar.pojo.captcha.req.ImageCaptchaReq;
import com.cloudstar.pojo.captcha.resp.ImageCaptchaResp;

import javax.servlet.http.HttpServletRequest;

/**
 * 工具类
 *
 * <AUTHOR> created by yanbiao
 */
public interface CommonService {

    /**
     * 获取图像验证码
     */
    ImageCaptchaResp getImageCaptcha(ImageCaptchaReq req);


    /**
     * 根据账号获取短信验证码
     */
    boolean getSmsCaptchaByAccount(String account);


    /**
     * 获取短信验证码
     */
    boolean sendSms(String mobile);

    Boolean sedEmailCaptcha(String email);

    /**
     * 给登录人发送短息
     *
     * @return 是否发送成功
     */
    boolean sendSmsByLogin(HttpServletRequest httpServletRequest);
}
