package com.cloudstar.service.facade.monitor;

import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.sdk.server.bss.PageOperationAnalysisReq;
import com.cloudstar.sdk.server.bss.TheBarChartReq;
import com.cloudstar.sdk.server.bss.TheLineChartReq;
import com.cloudstar.sdk.server.pojo.ExternalMonitorReq;
import com.cloudstar.sdk.server.pojo.NpuUsageResp;
import com.cloudstar.sdk.server.pojo.PoolJobStatusNumResp;
import com.cloudstar.sdk.server.pojo.PoolJobStatusReq;
import com.cloudstar.sdk.server.pojo.PoolJobStatusResp;
import com.cloudstar.sdk.server.pojo.ResourcePoolResp;
import com.cloudstar.service.pojo.vo.requestvo.monitor.UserResourcePageReq;
import com.cloudstar.service.pojo.vo.responsevo.monitor.ClusterJobInfoResp;
import com.cloudstar.service.pojo.vo.responsevo.monitor.PageOperationAnalysisResp;
import com.cloudstar.service.pojo.vo.responsevo.monitor.TheBarChartResp;
import com.cloudstar.service.pojo.vo.responsevo.monitor.TheLineChartResp;
import com.cloudstar.service.pojo.vo.responsevo.monitor.UserResourcePageResp;

import java.util.List;

/**
 * 对外提供监控数据
 *
 * <AUTHOR>
 * @date 2023/7/5 9:49
 */
public interface ExternalMonitorService {

    NpuUsageResp getPoolNpuUsage(ExternalMonitorReq req);

    PoolJobStatusNumResp getPoolJobStatusNum(ExternalMonitorReq req);

    List<PoolJobStatusResp> getPoolJob(PoolJobStatusReq req);

    /**
     * 分页查询
     *
     * @param req 筛选条件
     */
    PageResult<UserResourcePageResp> userResourcePage(UserResourcePageReq req, PageForm pageForm);

    List<ResourcePoolResp> getResourcePool(ExternalMonitorReq req);

    /**
     * 获取集群作业信息
     */
    ClusterJobInfoResp getClusterJobInfo();

    List<PageOperationAnalysisResp> getAnalysis(PageOperationAnalysisReq req);

    List<TheLineChartResp> getTheLineChart(TheLineChartReq req);

    List<TheBarChartResp> getTheBarChart(TheBarChartReq req);
}
