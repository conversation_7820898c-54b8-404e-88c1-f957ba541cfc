package com.cloudstar.filter;


import com.cloudstar.common.base.constant.AuthConstants;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.pojo.result.RestCodeEnum;
import com.cloudstar.common.base.util.ThreadLocalHolder;
import com.cloudstar.sdk.iam.client.IamClient;
import com.cloudstar.sdk.iam.pojo.AuthReqDto;
import com.cloudstar.util.SkipOpenApiPathUtil;
import com.cloudstar.util.SkipPathUtil;

import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;

import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import cn.hutool.json.JSONUtil;
import cn.hutool.system.SystemUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * 身份验证验证过滤器
 *
 * <AUTHOR>
 * @description: 权限认证过滤器
 * @date 2022/7/20 13:54
 */
@Slf4j
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class AuthVerifyFilter implements GlobalFilter, Ordered {

    IamClient iamClient;

    SkipPathUtil skipPathUtil;

    SkipOpenApiPathUtil skipOpenApiPathUtil;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String path = exchange.getRequest().getURI().getPath();
        final String method = Objects.requireNonNull(exchange.getRequest().getMethod()).name();
        //判断来源
        if (path.contains("openapi")) {
            ThreadLocalHolder.setRequestFrom("openapi");
            if (!skipOpenApiPathUtil.skipFilter(path)) {
                return errorOut(exchange.getResponse());
            }
            //登录接口 直接放行 设置来源
            if ("/api/v1/openapi/user/account/token".equals(path)) {
                ServerHttpRequest host = exchange.getRequest()
                                                 .mutate()
                                                 .header(AuthConstants.REQUEST_FROM, "openapi")
                                                 .build();
                ServerWebExchange build = exchange.mutate().request(host).build();
                return chain.filter(build);
            }
        } else {
            ThreadLocalHolder.setRequestFrom("web");
            if (skipPathUtil.skipFilter(path)) {
                return chain.filter(exchange);
            }
        }

        return CompletableFuture.supplyAsync(() -> {
            //白名单直接放行
            AuthReqDto reqDto = new AuthReqDto();
            reqDto.setPath(path);
            reqDto.setMethod(method);
            String token = exchange.getRequest().getHeaders().getFirst(AuthConstants.HEADER_AUTH);
            ThreadLocalHolder.setToken(token);
            // 设置useragent
            String userAgent = exchange.getRequest().getHeaders().getFirst(HttpHeaders.USER_AGENT);
            ThreadLocalHolder.setUserAgent(userAgent);
            String customHeader = SystemUtil.get(AuthConstants.UNIFICATION_CUSTOM_HEADER,
                                                 AuthConstants.UNIFICATION_CUSTOM_HEADER);
            // 设置Cluster_id
            String clusterId = exchange.getRequest().getHeaders().getFirst(AuthConstants.HEADER_CLUSTER_ID);
            log.info("cluster_id:{}", clusterId);
            ThreadLocalHolder.setClusterId(clusterId);
            ThreadLocalHolder.setUnificationCustomHeader(
                    exchange.getRequest().getHeaders().getFirst(customHeader));
            return iamClient.auth(reqDto);
        }).thenApply((respDto) -> {
            if (!respDto.isPass()) {
                log.warn("接口:{},鉴权失败。respDto:{}", path, JSONUtil.toJsonStr(respDto));
                return errorOut(exchange.getResponse());
            }
            if (respDto.isTokenRefresh()) {
                exchange.getResponse().getHeaders().set("refresh_token", respDto.getRefreshToken());
            }
            ServerHttpRequest newRequest = exchange.getRequest()
                                                   .mutate()
                                                   .header(AuthConstants.HEADER_USER_ID,
                                                           String.valueOf(respDto.getUserSid()))
                                                   .header(AuthConstants.HEADER_USER_TYPE, respDto.getUserType())
                                                   .build();
            return chain.filter(exchange.mutate().request(newRequest).response(exchange.getResponse()).build());
        }).whenComplete((aVoid, throwable) -> {
            if (Objects.nonNull(throwable)) {
                log.error("鉴权异常", throwable);
            }
            ThreadLocalHolder.clear();
        }).join();
    }

    @Override
    public int getOrder() {
        return 0;
    }

    /**
     * 错误了 返回未授权输出
     *
     * @param response 响应
     *
     * @return {@link Mono}<{@link Void}>
     */
    private Mono<Void> errorOut(ServerHttpResponse response) {
        String body = null;
        body = JSONUtil.toJsonStr(Rest.e(null, RestCodeEnum.UNAUTHORIZED, RestCodeEnum.UNAUTHORIZED.getName()));
        response.setStatusCode(HttpStatus.UNAUTHORIZED);

        byte[] bytes = body.getBytes(StandardCharsets.UTF_8);
        DataBuffer buffer = response.bufferFactory().wrap(bytes);
        // 指定编码，否则在浏览器中会中文乱码
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * openapi 路由替换
     *
     * @param path 路径
     */
    private String openApiUrlReplace(String path) {
        if (path.contains("openapi")) {
            path = path.replace("openapi", "console");
        }
        return path;
    }
}
