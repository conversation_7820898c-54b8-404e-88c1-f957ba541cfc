package com.cloudstar.module.job.executor.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cloudstar.ConfigService;
import com.cloudstar.common.base.enums.ClusterPoolTypeEnum;
import com.cloudstar.common.base.enums.TrainingJobAnnotationsEnum;
import com.cloudstar.common.base.enums.TrainingJobParamsEnum;
import com.cloudstar.dao.mapper.algorithm.AlgorithmPublishMapper;
import com.cloudstar.dao.mapper.res.ResImageMapper;
import com.cloudstar.dao.model.algorithm.AlgorithmPublish;
import com.cloudstar.dao.model.cluster.ClusterEngine;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.cluster.ClusterFlavor;
import com.cloudstar.dao.model.cluster.ClusterResourcePool;
import com.cloudstar.dao.model.res.ResImage;
import com.cloudstar.dao.model.tenantmapping.ClusterSubAccount;
import com.cloudstar.dao.model.training.TrainingJobEntity;
import com.cloudstar.dao.model.training.TrainingJobParams;
import com.cloudstar.module.job.executor.JobExecutor;
import com.cloudstar.service.grpc.AgentTrainingJobProto;
import com.cloudstar.service.pojo.dto.trainingjob.ExecuteJobEntity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 裸金属作业执行器
 *
 * <AUTHOR>
 * @date 2024/7/5 14:09
 */
@Component
@Slf4j
public class BmsJobExecutor extends JobExecutor {


    @Autowired
    ConfigService configService;

    @Resource
    ResImageMapper resImageMapper;

    @Resource
    AlgorithmPublishMapper algorithmPublishMapper;

    static final String SHARE_POOL_TYPE = "SHARE";

    /**
     * 算法来源是我的订阅
     */
    static final String ALGORITHM_SOURCE_SUBSCRIBE = "subscribe";

    @Override
    public AgentTrainingJobProto.AddTrainingJobRequest.Builder execute(ExecuteJobEntity executeJob) {
        TrainingJobEntity trainingJob = executeJob.getTrainingJobEntity();
        //裸金属作业特殊处理
        if (ObjectUtil.isEmpty(trainingJob.getPoolId()) && SHARE_POOL_TYPE.equals(trainingJob.getJobPoolType())) {
            ClusterResourcePool clusterResourcePool = clusterResourcePoolMapper.selectOne(
                    new QueryWrapper<ClusterResourcePool>().lambda()
                                                           .eq(ClusterResourcePool::getClusterId, trainingJob.getClusterId())
                                                           .eq(ClusterResourcePool::getPoolType, SHARE_POOL_TYPE));
            if (ObjectUtil.isNotEmpty(clusterResourcePool)) {
                trainingJob.setPoolId(clusterResourcePool.getId().toString());
                trainingJobEntityMapper.updateById(trainingJob);
            }
        }
        final AgentTrainingJobProto.AddTrainingJobRequest.Builder builder = AgentTrainingJobProto.AddTrainingJobRequest.newBuilder();
        //查询创建作业、上传数据集、上传算法文件的子账户id
        selectBmsSubAccountUserId(trainingJob);
        builder.setUserId(trainingJob.getCreateJobUserId());
        builder.setCfnJobId(trainingJob.getId());

        //元数据
        AgentTrainingJobProto.Metadata.Builder metaDataBuilder = AgentTrainingJobProto.Metadata.newBuilder().setName(trainingJob.getName())
                                                                                               .setKind("job")
                                                                                               .setJobCmd(selectCmd(trainingJob))
                                                                                               .setDescription(trainingJob.getDescription());
        if (Objects.nonNull(trainingJob.getJobType())) {
            metaDataBuilder.setJobCategory(COORDINATION);
        }
        try {
            // 设置注解
            setAnnotation(trainingJob, metaDataBuilder);
        } catch (Exception e) {
            e.getMessage();
        }
        builder.setMetadata(metaDataBuilder);
        // 此id 用来获取模板，预先设计扩展性
        //AI引擎
        if (Objects.nonNull(trainingJob.getEngineId())) {
            ClusterEngine clusterEngine = clusterEngineMapper.selectById(trainingJob.getEngineId());
            AgentTrainingJobProto.Engine.Builder engineBuilder = AgentTrainingJobProto.Engine.newBuilder();
            engineBuilder.setEngineName(clusterEngine.getEngineId());
            // slurm 的engineVersion 应传完成tag号
            engineBuilder.setEngineVersion(clusterEngine.getEngineId());
            engineBuilder.setImageName(clusterEngine.getEngineId());
            builder.setEngine(engineBuilder.build());
        } else {
            //镜像文件
            if (Objects.isNull(trainingJob.getInputImageId())) {
                log.error("AI引擎和镜像为空，作业调度失败！");
                updateErrorJob(trainingJob, "AI引擎和镜像为空，作业调度失败！");
                return null;
            }
            if (ALGORITHM_SOURCE_SUBSCRIBE.equals(trainingJob.getAlgorithmSource())) {
                final AlgorithmPublish algorithmPublish = algorithmPublishMapper.selectById(trainingJob.getAlgorithmPublishId());
                AgentTrainingJobProto.dataResource.Builder inputImage = AgentTrainingJobProto.dataResource.newBuilder();
                inputImage.setDataId(algorithmPublish.getId());
                inputImage.setName(algorithmPublish.getPublishImageTag());
                inputImage.setObsUrl(algorithmPublish.getPublishImageTag());
                builder.setInputImage(inputImage);
            } else {
                //裸金属自定义镜像查询
                final ResImage resImage = resImageMapper.selectById(trainingJob.getInputImageId());
                AgentTrainingJobProto.dataResource.Builder inputImage = AgentTrainingJobProto.dataResource.newBuilder();
                inputImage.setDataId(resImage.getId());
                inputImage.setName(resImage.getTag());
                inputImage.setObsUrl(resImage.getTag());
                builder.setInputImage(inputImage);
            }
        }

        AgentTrainingJobProto.SpecObj.Builder specBuilder = AgentTrainingJobProto.SpecObj.newBuilder();
        boolean exclusiveFlag = false;
        //资源池
        if (StrUtil.isNotBlank(trainingJob.getPoolId())) {
            log.info("资源池id：{}", trainingJob.getPoolId());
            ClusterResourcePool clusterResourcePool = clusterResourcePoolMapper.selectById(
                    Long.valueOf(trainingJob.getPoolId()));
            specBuilder.setPoolId(clusterResourcePool.getPoolId());
            if (ClusterPoolTypeEnum.EXCLUSIVE.getType().equalsIgnoreCase(clusterResourcePool.getPoolType())) {
                exclusiveFlag = true;
            }
        }

        //资源规格
        if (StrUtil.isNotBlank(trainingJob.getSpec())) {
            ClusterFlavor flavor = clusterFlavorMapper.selectById(Long.valueOf(trainingJob.getSpec()));
            if (Objects.isNull(flavor) && Objects.isNull(trainingJob.getPoolId())) {
                log.error("资源类型：{},为空，作业调度失败！", trainingJob.getSpec());
                updateErrorJob(trainingJob, "资源类型为空，作业调度失败！");
                return null;
            }
            //专属资源池不支持自定义CPU的规格
            if (Objects.nonNull(flavor) && !(exclusiveFlag && CPU.equalsIgnoreCase(flavor.getFlavorType()))) {
                specBuilder.setFlavorId(flavor.getFlavorId());
            }
            //设置计算卡类型
            AgentTrainingJobProto.BmsExtraParam.Builder bmsBuilder = AgentTrainingJobProto.BmsExtraParam.newBuilder();
            bmsBuilder.setComputeProductName(flavor.getComputeProductName());
            bmsBuilder.setNodePurpose(flavor.getNodePurpose());
            bmsBuilder.setIsVirtualized(flavor.getIsVirtualized() == 1);
            bmsBuilder.setVirtualizationPercentage(flavor.getVirtualizationPercentage());
            specBuilder.setBmsParam(bmsBuilder.build());
        }

        specBuilder.setNodeCount(trainingJob.getSpecNum());

        //资源信息
        ClusterEntity clusterEntity = clusterEntityMapper.selectById(trainingJob.getClusterId());
        if (Objects.isNull(clusterEntity)) {
            log.error("集群：{},为空，作业调度失败！", trainingJob.getClusterId());
            updateErrorJob(trainingJob, "集群为空，作业调度失败！");
            return null;
        }
        AgentTrainingJobProto.FlavorInfo.Builder clusterBuilder = AgentTrainingJobProto.FlavorInfo.newBuilder()
                                                                                                  .setName(clusterEntity.getClusterName())
                                                                                                  .setType(clusterEntity.getClusterType())
                                                                                                  .setScheduleMode("资源优先");
        builder.setFlavorInfo(clusterBuilder);

        //预置框架提交作业
        if (StrUtil.isNotBlank(trainingJob.getCodeDir())
                && StrUtil.isNotBlank(trainingJob.getLanchScript())
                && StrUtil.isBlank(trainingJob.getCommand())) {
            AgentTrainingJobProto.Algorithm.Builder algorithmBuilder = AgentTrainingJobProto.Algorithm.newBuilder();
            algorithmBuilder.setCodeDir(trainingJob.getCodeDir()); //默认算法目录
            algorithmBuilder.setBootFile(trainingJob.getLanchScript());
            builder.setAlgorithm(algorithmBuilder.build());
        } else if (StrUtil.isNotBlank(trainingJob.getCodeDir())
                && StrUtil.isBlank(trainingJob.getLanchScript())
                && StrUtil.isNotBlank(trainingJob.getCommand())) {
            //自定义镜像提交 算法存在obs中
            AgentTrainingJobProto.Algorithm.Builder algorithmBuilder = AgentTrainingJobProto.Algorithm.newBuilder();
            algorithmBuilder.setCodeDir(trainingJob.getCodeDir()); //默认算法目录
            String bootFile = getBootFile(trainingJob.getCommand());
            String newBootFile = bootFile;
            //判断启动脚本是否在算法目录中，没在就拼接行算法目录
            if (!newBootFile.contains(trainingJob.getCodeDir())) {
                newBootFile = trainingJob.getCodeDir() + "/" + newBootFile;
            }
            if (!newBootFile.contains("/home/<USER>")) {
                newBootFile = "/home/<USER>/${POD_NAME}/" + newBootFile;
            }
            newBootFile = newBootFile.replaceAll("//+", "/");
            builder.setCommand(trainingJob.getCommand().replace(bootFile, newBootFile));
            builder.setAlgorithm(algorithmBuilder.build());
        } else if (StrUtil.isBlank(trainingJob.getCodeDir())
                && StrUtil.isBlank(trainingJob.getLanchScript())
                && StrUtil.isNotBlank(trainingJob.getCommand())) {
            //自定义镜像提交 算法在镜像中
            builder.setCommand(trainingJob.getCommand());
            //命令行只支持输入启动命令 其他超参从超参列表拿去
            log.info("启动命令：" + trainingJob.getCommand());
        }
        log.info("trainingJob.getUpdAlgorithmUserId:{}", trainingJob.getUpdAlgorithmUserId());
        List<ClusterSubAccount> subAccounts = subAccountMapper.selectList(Wrappers.<ClusterSubAccount>lambdaQuery()
                                                                                  .eq(ClusterSubAccount::getAccountUuid,
                                                                                      trainingJob.getUpdAlgorithmUserId()));
        if (CollectionUtil.isNotEmpty(subAccounts)) {
            log.info("account_uuid:{},设置obs信息开始...", subAccounts.get(0).getAccountUuid());
            AgentTrainingJobProto.SubAccount.Builder subAccountBuilder = AgentTrainingJobProto.SubAccount.newBuilder()
                                                                                                         .setAccessKey(
                                                                                                                 subAccounts.get(0).getAccessKey())
                                                                                                         .setSecretKey(
                                                                                                                 subAccounts.get(0).getSecretKey())
                                                                                                         .setObsUrl(subAccounts.get(0).getObsUrl())
                                                                                                         .setBucket(subAccounts.get(0).getAccount())
                                                                                                         .setAccountUuid(
                                                                                                                 subAccounts.get(0).getAccountUuid())
                                                                                                         .setDataUuid(trainingJob.getUpdDataUserId());
            builder.setSubAccount(subAccountBuilder);
        }

        //超参组:包括输入输出及超参
        List<TrainingJobParams> trainingJobParams = trainingJobParamsMapper.selectList(
                Wrappers.<TrainingJobParams>lambdaQuery()
                        .eq(TrainingJobParams::getGroupId, trainingJob.getParamsGroupId()));
        if (CollectionUtil.isEmpty(trainingJobParams)) {
            log.error("超参组：{},为空，作业调度失败！", trainingJob.getParamsGroupId());
            updateErrorJob(trainingJob, "超参组为空，作业调度失败！");
            return null;
        }
        //解析参数
        parseParams(trainingJobParams, builder, specBuilder, trainingJob);
        return builder;
    }

    /**
     * set annotation 为训练作业打上注解， 后续训练作业监控采集方便使用以下值
     *
     * @param trainingJob     training job
     * @param metaDataBuilder meta data builder
     */
    private void setAnnotation(TrainingJobEntity trainingJob, AgentTrainingJobProto.Metadata.Builder metaDataBuilder) {
        AgentTrainingJobProto.annotationsOption.Builder annotationBuilder = AgentTrainingJobProto.annotationsOption.newBuilder();
        annotationBuilder.setKey(TrainingJobAnnotationsEnum.JOB_NAME.getType());
        annotationBuilder.setValue(trainingJob.getName());
        metaDataBuilder.addAnnotations(annotationBuilder.build());
        annotationBuilder.setKey(TrainingJobAnnotationsEnum.POOL_ID.getType());
        annotationBuilder.setValue(trainingJob.getPoolId());
        metaDataBuilder.addAnnotations(annotationBuilder.build());
        annotationBuilder.setKey(TrainingJobAnnotationsEnum.JOB_ID.getType());
        annotationBuilder.setValue(trainingJob.getJobId());
        metaDataBuilder.addAnnotations(annotationBuilder.build());
        annotationBuilder.setKey(TrainingJobAnnotationsEnum.FLAVOR_ID.getType());
        annotationBuilder.setValue(trainingJob.getFlavorId());
        metaDataBuilder.addAnnotations(annotationBuilder.build());
        annotationBuilder.setKey(TrainingJobAnnotationsEnum.PROJECT_ID.getType());
        annotationBuilder.setValue(trainingJob.getOrgSid() + "");
        metaDataBuilder.addAnnotations(annotationBuilder.build());
        annotationBuilder.setKey(TrainingJobAnnotationsEnum.USER_ID.getType());
        annotationBuilder.setValue(trainingJob.getUserSid() + "");
        metaDataBuilder.addAnnotations(annotationBuilder.build());
        annotationBuilder.setKey(TrainingJobAnnotationsEnum.NODE_COUNT.getType());
        annotationBuilder.setValue(trainingJob.getSpecNum() + "");
        metaDataBuilder.addAnnotations(annotationBuilder.build());
    }

    @Override
    public List<AgentTrainingJobProto.ParamsGroup.Builder> extParams() {
        AgentTrainingJobProto.ParamsGroup.Builder paramsGroupBuilder = AgentTrainingJobProto.ParamsGroup.newBuilder();
        // 默认内置环境变量 slurm 环境
        paramsGroupBuilder.setName("JOB_DIR");
        paramsGroupBuilder.setValue("/home/<USER>/${POD_NAME}");
        paramsGroupBuilder.setType(TrainingJobParamsEnum.ENV_PARAMS.getType());

        List<AgentTrainingJobProto.ParamsGroup.Builder> builders = new ArrayList<>();
        builders.add(paramsGroupBuilder);
        return builders;
    }

    /**
     * 判断算法路径是否包含了 16位数字的根路径
     *
     * @param path path
     */
    private boolean checkPath(String path) {
        // 正则表达式匹配连续的16位数字
        String regex = "\\d{16}";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(path);

        if (matcher.find()) {
            return true;
        }
        return false;
    }

    /**
     * 获取启动脚本
     *
     * @param command command
     */
    private String getBootFile(String command) {
        // 正则表达式匹配Python脚本路径
        String regex = "(python|python3)\\s+([^\\s]+\\.py)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(command);

        if (matcher.find()) {
            String scriptPath = matcher.group(2); // 获取第二个分组的内容
            log.info("启动命令脚本地址:{}", scriptPath);
            return scriptPath;
        }
        return null;
    }

}
