package com.cloudstar.dao.mapper.request;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.dao.model.request.RequestForm;
import com.cloudstar.service.pojo.vo.requestvo.request.RequestFormSelectListReq;
import com.cloudstar.service.pojo.vo.responsevo.request.RequestFormInfoResponse;
import com.cloudstar.service.pojo.vo.responsevo.request.RequestFormResponse;

import org.apache.ibatis.annotations.Param;


/**
 * 申请表映射器
 *
 * <AUTHOR>
 * @date 2022/08/15
 */
public interface RequestFormMapper extends BaseMapper<RequestForm> {

    @InterceptorIgnore(tenantLine = "true")
    Page<RequestFormResponse> getRequestFormList(@Param("page") Page<RequestFormResponse> pageQuery,
                                                 @Param("req") RequestFormSelectListReq req);
    @InterceptorIgnore(tenantLine = "true")
    RequestFormInfoResponse getRequestFormById(Long id);
}
