package com.cloudstar.dao.mapper.bigscreen;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudstar.dao.model.bigscreen.CollectAllocateNpu;
import com.cloudstar.service.pojo.dto.bigscreen.CollectCommonResourceDto;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * NPU采集mapper
 *
 * <AUTHOR>
 * @description 针对表【collect_allocate_npu(资源池分配率-npu)】的数据库操作Mapper
 * @createDate 2022-10-26 16:47:14
 * @Entity com.cloudstar.dao.model.bigscreen.CollectAllocateNpu
 */
@DS("monitor")
public interface CollectAllocateNpuMapper extends BaseMapper<CollectAllocateNpu> {

    CollectCommonResourceDto getNpuResource(@Param("clusterId") Long clusterId, @Param("monitorCollect") Integer monitorCollect);

    CollectCommonResourceDto getPoolNpuResource(@Param("poolId") String poolId, @Param("monitorCollect") Integer monitorCollect);

    CollectCommonResourceDto getExclusiveNpuResource(@Param("clusterId") Long clusterId, @Param("poolId") String poolId,
                                                     @Param("monitorCollect") Integer monitorCollect);

    List<CollectCommonResourceDto> getNpuAllocatedResourceTrend(@Param("clusterId") Long clusterId,
                                                                @Param("monitorCollect") Integer monitorCollect,
                                                                @Param("trendMonitorCollect") Integer trendMonitorCollect,
                                                                @Param("capacity") Long capacity,
                                                                @Param("type") String type,
                                                                @Param("poolId") String poolId,
                                                                @Param("excluseiveNum") Long excluseiveNum);

    /**
     * 获取资源池id列表
     *
     * @param clusterId id
     */
    List<String> getPoolByClusterId(@Param("clusterId") Long clusterId);
}




