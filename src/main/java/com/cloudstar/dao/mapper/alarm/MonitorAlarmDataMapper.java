package com.cloudstar.dao.mapper.alarm;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudstar.service.pojo.vo.responsevo.alarm.MonitorAlarmData;

import org.springframework.stereotype.Repository;

/**
 * OpsAlarmDataMapper
 *
 * <AUTHOR>
 */
@DS("mysql")
@Repository
public interface MonitorAlarmDataMapper extends BaseMapper<MonitorAlarmData> {

}
