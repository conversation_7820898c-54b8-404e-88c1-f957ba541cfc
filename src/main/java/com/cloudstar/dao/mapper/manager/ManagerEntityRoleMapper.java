package com.cloudstar.dao.mapper.manager;

import com.cloudstar.dao.model.manager.ManagerEntityRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 管理员实体角色映射器
 *
 * <AUTHOR>
 * @date 2022/08/10
 */
@Repository
public interface ManagerEntityRoleMapper extends BaseMapper<ManagerEntityRole> {


    @Select("select mr.name "
            + "from manager_entity_role mer "
            + "left join manager_role mr "
            + "on mer.role_sid = mr.role_sid  "
            + "where mer.user_sid = #{userSid}")
    List<String> selectManagerRoleById(@Param("userSid") Long userSid);
}




