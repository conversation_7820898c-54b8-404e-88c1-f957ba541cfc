package com.cloudstar.dao.mapper.manager;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.common.base.pojo.Criteria;
import com.cloudstar.dao.model.manager.ManagerRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 管理员角色映射器
 *
 * <AUTHOR>
 * @description 针对表【manager_role(运营运维角色表)】的数据库操作Mapper
 * @createDate 2022-08-09 11:52:31
 * @Entity com.cloudstar.dao.model.role.ManagerRole
 * @date 2022/08/10
 */
@Repository
public interface ManagerRoleMapper extends BaseMapper<ManagerRole> {
    /**
     * 根据条件查询记录总数
     */
    int countByParams(Criteria criteria);
    /**
     * 查询最大的sid
     */
    String findMaxRoleId();
    /**
     * 根据条件查询记录集
     */
    List<ManagerRole> selectByParams(Criteria criteria);
    /**
     * 分页
     */
    Page<ManagerRole> selectByParamsPage(Page<ManagerRole> managerRolePage, @Param("condition") Map<String, Object> condition);

    List<ManagerRole> selectSystemRole(Criteria criteria);

    /**
     * 临时-根据管理员id查询其所有角色
     */
    @Select("select mr.* "
            + "from manager_role mr "
            + " left join manager_entity_role mer on mer.role_sid = mr.role_sid "
            + " left join manager_entity m on m.user_sid = mer.user_sid "
            + "where m.user_sid = #{managerId}")
    List<ManagerRole> findUserRole(Long managerId);
}




