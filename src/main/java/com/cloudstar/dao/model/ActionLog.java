package com.cloudstar.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 操作日志表
 * @TableName action_log
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "action_log")
public class ActionLog implements Serializable {
    /**
     * 日志id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 账号名
     */
    private String account;

    /**
     * 账号类型
     */
    private String accountType;

    /**
     * 角色
     */
    private String roleName;

    /**
     * 接口方法名
     */
    private String actionMethod;

    /**
     * 操作名称
     */
    private String actionName;

    /**
     * 操作时间
     */
    private Date actionTime;

    /**
     * 接口地址
     */
    private String actionPath;

    /**
     * 接口相关参数
     */
    private String param;

    /**
     * 客户端
     */
    private String client;

    /**
     * 接口返回值
     */
    private String result;

    /**
     * 访问IP
     */
    private String remoteIp;

    /**
     * 接口请求方式
     */
    private String httpMethod;

    /**
     * spanId
     */
    private String spanId;

    /**
     * 日志追踪ID
     */
    private String traceId;

    /**
     * 接口执行状态
     */
    private Boolean actionResult;

    /**
     * 日志防篡改
     */
    private String encryptLog;

    /**
     * 用户id
     */
    private String accountId;

    /**
     * 类名
     */
    private String className;

    

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}