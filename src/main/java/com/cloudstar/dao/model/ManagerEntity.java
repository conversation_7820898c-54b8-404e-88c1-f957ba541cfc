package com.cloudstar.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.common.encryptdata.annotation.EncryptDecryptClass;
import com.cloudstar.common.encryptdata.annotation.EncryptDecryptField;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 运营运维管理员账号表
 * @TableName manager_entity
 */
@TableName(value = "manager_entity")
@Data
@EncryptDecryptClass
public class ManagerEntity implements Serializable {
    /**
     * 租户id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long userSid;

    /**
     * uuid
     */
    private String uuid;

    /**
     * 用户名
     */
    private String account;

    /**
     * 真实名称
     */
    private String realName;

    /**
     * 密码;AES加密存储
     */
    private String password;

    /**
     * 邮箱;AES加密存储
     */
    @EncryptDecryptField
    private String email;

    /**
     * 手机号码;AES加密存储
     */
    @EncryptDecryptField
    private String mobile;

    /**
     * 状态;enable:启用,disable:禁用，deleted:注销--逻辑删除
     */
    private String status;

    /**
     * 上次登录时间
     */
    private Date lastLoginTime;

    /**
     * 上次登录IP地址
     */
    private String lastLoginIp;

    /**
     * 账号有效开始时间
     */
    private Date startTime;

    /**
     * 账号有效结束时间
     */
    private Date endTime;

    /**
     * 是否首次登录重置密码;false-否，true-是
     */
    private String isResetPassword;

    /**
     * 密码过期时间;密码过期时间
     */
    private Date passwordExpiresAt;

    /**
     * 乐观锁
     */
    private String version;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}