package com.cloudstar.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.common.encryptdata.annotation.EncryptDecryptClass;
import com.cloudstar.common.encryptdata.annotation.EncryptDecryptField;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 管理员实体类
 *
 * <AUTHOR>
 * @date 2022/8/9 16:09
 */
@TableName(value = "manager_entity")
@Data
@EncryptDecryptClass
public class ManagerEntity implements Serializable {

    @TableId(type = IdType.AUTO)
    private Long userSid;

    @TableField(value = "uuid")
    private String uuid;

    @TableField(value = "account")
    private String account;

    @TableField(value = "password")
    private String password;

    @TableField(value = "real_name")
    private String realName;

    @TableField(value = "email")
    @EncryptDecryptField
    private String email;

    @TableField(value = "mobile")
    @EncryptDecryptField
    private String mobile;

    @TableField(value = "status")
    private String status;

    @TableField(value = "last_login_time")
    private Date lastLoginTime;

    @TableField(value = "last_login_ip")
    private String lastLoginIp;

    @TableField(value = "start_time")
    private Date startTime;

    @TableField(value = "end_time")
    private Date endTime;

    @TableField(value = "password_expires_at")
    private Date passwordExpiresAt;

    @TableField(value = "is_reset_password")
    private String isResetPassword;

    @TableField(value = "created_by")
    private String createdBy;

    @TableField(value = "created_dt")
    private Date createdDt;

    @TableField(value = "updated_by")
    private String updatedBy;

    @TableField(value = "updated_dt")
    private Date updatedDt;
}
