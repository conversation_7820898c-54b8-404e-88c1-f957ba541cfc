package com.cloudstar.dao.model.sdr;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Builder;
import lombok.Data;

/**
 * 商汤obs话单表
 *
 * @TableName slurm_sdr_obs_data
 */
@Data
@Builder
@TableName(value = "slurm_sdr_obs_data")
public class SlurmSdrObsData implements Serializable {
    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 记录类型
     */
    @TableField(value = "record_type")
    private String recordType;

    /**
     * 采集时间
     */
    @TableField(value = "time_stamp")
    private String timeStamp;

    /**
     * 用户ID
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 区域代码
     */
    @TableField(value = "region_code")
    private String regionCode;

    /**
     * AvailableZoneCode
     */
    @TableField(value = "az_code")
    private String azCode;

    /**
     * 云服务类型编码
     */
    @TableField(value = "cloud_service_type_code")
    private String cloudServiceTypeCode;

    /**
     * 资源类型编码
     */
    @TableField(value = "resource_type_code")
    private String resourceTypeCode;

    /**
     * 资源规格编码
     */
    @TableField(value = "resource_spec_code")
    private String resourceSpecCode;

    /**
     * 资源实例ID
     */
    @TableField(value = "resource_id")
    private String resourceId;

    /**
     * 运营参数
     */
    @TableField(value = "csbparams")
    private String csbparams;

    /**
     * 统计周期开始时间
     */
    @TableField(value = "begin_time")
    private String beginTime;

    /**
     * 统计结束时间
     */
    @TableField(value = "end_time")
    private String endTime;

    /**
     * (计费因子名)累积因子名
     */
    @TableField(value = "accumulate_factor_name")
    private String accumulateFactorName;

    /**
     * (计费因子值)累积因子值
     */
    @TableField(value = "accumulate_factor_value")
    private String accumulateFactorValue;

    /**
     * 扩展字段
     */
    @TableField(value = "extend_params")
    private String extendParams;

    /**
     * 资源tag
     */
    @TableField(value = "tag")
    private String tag;

    /**
     * 企业资源组ID
     */
    @TableField(value = "enterprise_porject_id")
    private String enterprisePorjectId;

    /**
     * 乐观锁
     */
    @TableField(value = "version")
    private String version;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_dt")
    private Date createdDt;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_dt")
    private Date updatedDt;

    /**
     * iam账号ID
     */
    @TableField(value = "account_uuid")
    private String accountUuid;

    /**
     * 是否出账
     */
    @TableField(value = "bill_flag")
    private String billFlag;

    /**
     * 适配器uuid
     */
    @TableField(value = "adapter_uuid")
    private String adapterUuid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}