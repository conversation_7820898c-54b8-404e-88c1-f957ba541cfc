package com.cloudstar.dao.model.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 资源镜像中心用户
 *
 * <AUTHOR>
 * @date 2024/06/17
 */
@Data
@TableName("res_image_hub_user")
public class ResImageHubUser {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 底层资源ID
     */
    private String uuid;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 名字
     */
    private String firstName;
    /**
     * 姓氏
     */
    private String lastName;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 状态
     */
    private String status;
    /**
     * 关联角色及权限json存储
     */
    private String roles;
    /**
     * 细粒度授权json存储
     */
    private String contentSelector;
    /**
     * 操作用户ID
     */
    private Long ownerId;
    /**
     * 组织ID
     */
    private Long orgSid;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
}
