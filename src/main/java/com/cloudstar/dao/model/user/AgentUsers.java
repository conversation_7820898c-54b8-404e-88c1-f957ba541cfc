package com.cloudstar.dao.model.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 裸金属用户信息
 *
 * <AUTHOR>
 * @date 2024/6/12 15:04
 */
@Data
@TableName(value = "agent_users")
public class AgentUsers {


    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 账号类型
     * 标识用户来源，华为HCSO、商汤slurm
     */
    private String userType;

    /**
     * 账号ID
     */
    private String userId;

    /**
     * 账号名
     */
    private String userName;

    /**
     * 对象存储ID
     * 话单采集标识
     */
    private String obsId;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 验证类型
     * 账号的验证方式，aksk或password
     */
    private String validType;

    /**
     * 管理地址
     */
    private String manageUrl;

    /**
     * 密码
     * 加密
     */
    private String userPassword;

    /**
     * Access Key
     */
    private String userAk;

    /**
     * Secret Access Key
     * 加密
     */
    private String userSk;

    /**
     * 数据状态
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 父ID
     */
    private Long parentId;

    /**
     * IAM用户ID（domain）
     */
    private String domainId;

    /**
     * 订阅名
     */
    private String subscription;

    /**
     * 订阅的租户telemetryStation
     */
    @TableField(value = "telemetry_station")
    private String telemetryStation;
}
