package com.cloudstar.dao.model.subuser;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 租户权限策略表
 * @TableName sys_m_policy
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "sys_m_policy")
public class SysMPolicy implements Serializable {
    /**
     * 策略SID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long policySid;

    /**
     * 策略标识
     */
    @TableField(value = "policy_name")
    private String policyName;

    /**
     * 显示名称
     */
    @TableField(value = "display_name")
    private String displayName;

    /**
     * 策略类型;system[系统内置],custom[自定义]
     */
    @TableField(value = "policy_type")
    private String policyType;

    /**
     * 状态;0[禁用] 1[启用]
     */
    @TableField(value = "policy_status")
    private Integer policyStatus;

    /**
     * 资源类型;具体涉及到的策略资源类型如：["all"],["ecs","ebs"]
     */
    @TableField(value = "resource_types")
    private String resourceTypes;

    /**
     * 描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 组织SID
     */
    @TableField(value = "org_sid")
    private Long orgSid;

    /**
     * 所有者SID
     */
    @TableField(value = "owner_sid")
    private Long ownerSid;

    /**
     * 乐观锁
     */
    @TableField(value = "version")
    private String version;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_dt")
    private Date createdDt;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_dt")
    private Date updatedDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}