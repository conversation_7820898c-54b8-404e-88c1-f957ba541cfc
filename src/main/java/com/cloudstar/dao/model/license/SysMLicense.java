package com.cloudstar.dao.model.license;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * SysMLicense
 */
@Data
@TableName(value = "sys_m_license")
public class SysMLicense implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 许可证内容
     */
    @TableField(value = "license_body")
    private String licenseBody;

    /**
     * 乐观锁
     */
    @TableField(value = "version")
    private String version;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_dt")
    private Date createdDt;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_dt")
    private Date updatedDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}