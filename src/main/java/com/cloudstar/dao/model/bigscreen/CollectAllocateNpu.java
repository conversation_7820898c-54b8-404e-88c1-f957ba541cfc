package com.cloudstar.dao.model.bigscreen;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 资源池分配率-npu
 * @TableName collect_allocate_npu
 */
@TableName(value = "collect_allocate_npu")
@Data
public class CollectAllocateNpu implements Serializable {
    /**
     * 集群id
     */
    private Long clusterId;

    /**
     * time
     */
    private Date time;

    /**
     * 资源池id
     */
    private String poolId;

    /**
     * 资源池分配情况
     */
    private Object poolAllocated;

    /**
     * 资源池总量
     */
    private Object poolCapacity;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}