package com.cloudstar.dao.model.monitor;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 资源池分配率-CPU
 */
@Data
@TableName(value = "collect_allocate_cpu")
public class CollectAllocateCpu {

    /**
     * 记录时间
     */
    @TableField(value = "time")
    private Date time;

    /**
     * 资源池ID
     */
    @TableField(value = "pool_id")
    private String poolId;

    /**
     * 资源池分配情况
     */
    @TableField(value = "pool_allocated")
    private int poolAllocated;

    /**
     * 资源池总量
     */
    @TableField(value = "pool_capacity")
    private int poolCapacity;

    // Getters and Setters

    /**
     * 获取时间
     * @return 时间
     */
    public Date getTime() {
        return time;
    }

    /**
     * 设置时间
     * @param time 时间
     */
    public void setTime(Date time) {
        this.time = time;
    }

    /**
     * 获取资源池ID
     * @return 资源池ID
     */
    public String getPoolId() {
        return poolId;
    }

    /**
     * 设置资源池ID
     * @param poolId 资源池ID
     */
    public void setPoolId(String poolId) {
        this.poolId = poolId;
    }

    /**
     * 获取资源池分配情况
     * @return 资源池分配情况
     */
    public int getPoolAllocated() {
        return poolAllocated;
    }

    /**
     * 设置资源池分配情况
     * @param poolAllocated 资源池分配情况
     */
    public void setPoolAllocated(int poolAllocated) {
        this.poolAllocated = poolAllocated;
    }

    /**
     * 获取资源池总量
     * @return 资源池总量
     */
    public int getPoolCapacity() {
        return poolCapacity;
    }

    /**
     * 设置资源池总量
     * @param poolCapacity 资源池总量
     */
    public void setPoolCapacity(int poolCapacity) {
        this.poolCapacity = poolCapacity;
    }
}
