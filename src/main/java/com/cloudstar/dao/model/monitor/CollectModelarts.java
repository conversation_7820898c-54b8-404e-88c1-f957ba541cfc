package com.cloudstar.dao.model.monitor;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * collect_modelarts，ModelArts使用采集表
 */
@Data
@TableName("collect_modelarts")
public class CollectModelarts {

    /**
     * 记录时间
     */
    @TableField(value = "time", fill = FieldFill.INSERT)
    private Date time;

    /**
     * 用户ID
     */
    @TableField(value = "user_id", fill = FieldFill.INSERT)
    private String userId;

    /**
     * 资源实例ID
     */
    @TableField(value = "resource_id")
    private String resourceId;

    /**
     * 累积因子值;统计周期内的使用时长
     */
    @TableField(value = "accumulate_factor_value")
    private Date accumulateFactorValue;

    // Getters and Setters

    /**
     * 获取时间
     * @return 时间
     */
    public Date getTime() {
        return time;
    }

    /**
     * 设置时间
     * @param time 时间
     */
    public void setTime(Date time) {
        this.time = time;
    }

    /**
     * 获取用户编码
     * @return 用户编码
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 设置用户编码
     * @param userId 用户编码
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }

    /**
     * 获取资源实例ID
     * @return 资源实例ID
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * 设置资源实例ID
     * @param resourceId 资源实例ID
     */
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    /**
     * 获取累积因子值;统计周期内的使用时长
     * @return 累积因子值
     */
    public Date getAccumulateFactorValue() {
        return accumulateFactorValue;
    }

    /**
     * 设置累积因子值;统计周期内的使用时长
     * @param accumulateFactorValue 累积因子值
     */
    public void setAccumulateFactorValue(Date accumulateFactorValue) {
        this.accumulateFactorValue = accumulateFactorValue;
    }
}
