package com.cloudstar.dao.model.requests;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * ClusterRequest 类表示集群请求的信息。
 * 包含集群的 ID、名称、地址、类型、状态、访问密钥和安全密钥
 * @createDate 2025/3/6 11:10
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Component
public class ClusterRequest {

    /**
     * 集群ID
     */
    private String clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 集群地址
     */
    private String clusterAddress;

    /**
     * 集群类型
     */
    private String clusterType;

    /**
     * 集群状态
     */
    private String clusterStatus;

    /**
     * 集群访问密钥（Access Key）
     */
    private String clusterAccessKey;

    /**
     * 集群安全密钥（Secret Key）
     */
    private String clusterSecurityKey;
}
