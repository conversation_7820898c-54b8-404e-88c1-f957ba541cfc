package com.cloudstar.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@TableName("agent_obs_storage")
public class AgentObsStorage  {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private String userId;

    @TableField("file_number")
    private Long fileNumber;

    @TableField("storage_size")
    private Long storageSize;

    @TableField("filedir_number")
    private Long filedirNumber;

    @TableField("created_at")
    private LocalDateTime createdAt;

    @TableField("updated_at")
    private LocalDateTime updatedAt;

    @TableField("cache_file_number")
    private Long cacheFileNumber;

    @TableField("cache_storage_size")
    private Long cacheStorageSize;

    @TableField("cache_filedir_number")
    private Long cacheFiledirNumber;

}