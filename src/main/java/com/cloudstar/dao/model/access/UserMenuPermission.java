package com.cloudstar.dao.model.access;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * 租户菜单与权限表
 *
 * @TableName user_menu_permission
 */
@TableName(value = "user_menu_permission")
public class UserMenuPermission implements Serializable {
    /**
     * 菜单表
     */
    @TableField(value = "menu_sid")
    private Long menuSid;

    /**
     * 权限表
     */
    @TableField(value = "permission_sid")
    private Long permissionSid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 菜单表
     */
    public Long getMenuSid() {
        return menuSid;
    }

    /**
     * 菜单表
     */
    public void setMenuSid(Long menuSid) {
        this.menuSid = menuSid;
    }

    /**
     * 权限表
     */
    public Long getPermissionSid() {
        return permissionSid;
    }

    /**
     * 权限表
     */
    public void setPermissionSid(Long permissionSid) {
        this.permissionSid = permissionSid;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        UserMenuPermission other = (UserMenuPermission) that;
        return (this.getMenuSid() == null ? other.getMenuSid() == null : this.getMenuSid().equals(other.getMenuSid()))
                && (this.getPermissionSid() == null ? other.getPermissionSid() == null : this.getPermissionSid().equals(other.getPermissionSid()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getMenuSid() == null) ? 0 : getMenuSid().hashCode());
        result = prime * result + ((getPermissionSid() == null) ? 0 : getPermissionSid().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", menuSid=").append(menuSid);
        sb.append(", permissionSid=").append(permissionSid);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}