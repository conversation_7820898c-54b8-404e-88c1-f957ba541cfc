package com.cloudstar.dao.model.access;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 密码策略表
 *
 * @TableName sys_m_password_policy
 */
@Data
@TableName(value = "sys_m_password_policy")
public class SysMPasswordPolicy implements Serializable {
    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 用户id
     */
    @TableField(value = "user_sid")
    private Long userSid;

    /**
     * 密码包含规则
     */
    @TableField(value = "rule")
    private String rule;

    /**
     * 密码最小长度
     */
    @TableField(value = "min_length")
    private Integer minLength;

    /**
     * 不同字符数限制
     */
    @TableField(value = "character_limit")
    private Integer characterLimit;

    /**
     * 常用密码剔除
     */
    @TableField(value = "ruled_out")
    private String ruledOut;

    /**
     * 登陆失败锁定开启状态 0:关闭；1开启；
     */
    @TableField(value = "login_failure_enable")
    private Integer loginFailureEnable;

    /**
     * 登陆失败最大次数
     */
    @TableField(value = "login_failure_count")
    private Integer loginFailureCount;

    /**
     * 账号有效期 0:关闭；1开启；
     */
    @TableField(value = "account_validity")
    private Integer accountValidity;

    /**
     * 有效天数
     */
    @TableField(value = "expire_time")
    private Integer expireTime;

    /**
     * 组织id
     */
    @TableField(value = "org_sid")
    private Long orgSid;

    /**
     * 密码有效期
     */
    @TableField(value = "pwd_expire_time")
    private Long pwdExpireTime;

    /**
     * 密码有效期开启状态
     */
    @TableField(value = "pwd_expire_time_validity")
    private Integer pwdExpireTimeValidity;

    /**
     * 密码最少使用天数
     */
    @TableField(value = "pwd_least_used_day")
    private Integer pwdLeastUsedDay;

    /**
     * 密码不能与前N个历史密码重复
     */
    @TableField(value = "pwd_repeat_num")
    private Integer pwdRepeatNum;

    /**
     * 乐观锁
     */
    @TableField(value = "version")
    private String version;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_dt")
    private Date createdDt;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_dt")
    private Date updatedDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    public Long getId() {
        return id;
    }

    /**
     * id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 用户id
     */
    public Long getUserSid() {
        return userSid;
    }

    /**
     * 用户id
     */
    public void setUserSid(Long userSid) {
        this.userSid = userSid;
    }

    /**
     * 密码包含规则
     */
    public String getRule() {
        return rule;
    }

    /**
     * 密码包含规则
     */
    public void setRule(String rule) {
        this.rule = rule;
    }

    /**
     * 密码最小长度
     */
    public Integer getMinLength() {
        return minLength;
    }

    /**
     * 密码最小长度
     */
    public void setMinLength(Integer minLength) {
        this.minLength = minLength;
    }

    /**
     * 不同字符数限制
     */
    public Integer getCharacterLimit() {
        return characterLimit;
    }

    /**
     * 不同字符数限制
     */
    public void setCharacterLimit(Integer characterLimit) {
        this.characterLimit = characterLimit;
    }

    /**
     * 常用密码剔除
     */
    public String getRuledOut() {
        return ruledOut;
    }

    /**
     * 常用密码剔除
     */
    public void setRuledOut(String ruledOut) {
        this.ruledOut = ruledOut;
    }

    /**
     * 登陆失败锁定开启状态 0:关闭；1开启；
     */
    public Integer getLoginFailureEnable() {
        return loginFailureEnable;
    }

    /**
     * 登陆失败锁定开启状态 0:关闭；1开启；
     */
    public void setLoginFailureEnable(Integer loginFailureEnable) {
        this.loginFailureEnable = loginFailureEnable;
    }

    /**
     * 登陆失败最大次数
     */
    public Integer getLoginFailureCount() {
        return loginFailureCount;
    }

    /**
     * 登陆失败最大次数
     */
    public void setLoginFailureCount(Integer loginFailureCount) {
        this.loginFailureCount = loginFailureCount;
    }

    /**
     * 账号有效期 0:关闭；1开启；
     */
    public Integer getAccountValidity() {
        return accountValidity;
    }

    /**
     * 账号有效期 0:关闭；1开启；
     */
    public void setAccountValidity(Integer accountValidity) {
        this.accountValidity = accountValidity;
    }

    /**
     * 有效天数
     */
    public Integer getExpireTime() {
        return expireTime;
    }

    /**
     * 有效天数
     */
    public void setExpireTime(Integer expireTime) {
        this.expireTime = expireTime;
    }

    /**
     * 组织id
     */
    public Long getOrgSid() {
        return orgSid;
    }

    /**
     * 组织id
     */
    public void setOrgSid(Long orgSid) {
        this.orgSid = orgSid;
    }

    /**
     * 密码有效期
     */
    public Long getPwdExpireTime() {
        return pwdExpireTime;
    }

    /**
     * 密码有效期
     */
    public void setPwdExpireTime(Long pwdExpireTime) {
        this.pwdExpireTime = pwdExpireTime;
    }

    /**
     * 密码有效期开启状态
     */
    public Integer getPwdExpireTimeValidity() {
        return pwdExpireTimeValidity;
    }

    /**
     * 密码有效期开启状态
     */
    public void setPwdExpireTimeValidity(Integer pwdExpireTimeValidity) {
        this.pwdExpireTimeValidity = pwdExpireTimeValidity;
    }

    /**
     * 密码最少使用天数
     */
    public Integer getPwdLeastUsedDay() {
        return pwdLeastUsedDay;
    }

    /**
     * 密码最少使用天数
     */
    public void setPwdLeastUsedDay(Integer pwdLeastUsedDay) {
        this.pwdLeastUsedDay = pwdLeastUsedDay;
    }

    /**
     * 密码不能与前N个历史密码重复
     */
    public Integer getPwdRepeatNum() {
        return pwdRepeatNum;
    }

    /**
     * 密码不能与前N个历史密码重复
     */
    public void setPwdRepeatNum(Integer pwdRepeatNum) {
        this.pwdRepeatNum = pwdRepeatNum;
    }

    /**
     * 乐观锁
     */
    public String getVersion() {
        return version;
    }

    /**
     * 乐观锁
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 创建时间
     */
    public Date getCreatedDt() {
        return createdDt;
    }

    /**
     * 创建时间
     */
    public void setCreatedDt(Date createdDt) {
        this.createdDt = createdDt;
    }

    /**
     * 更新人
     */
    public String getUpdatedBy() {
        return updatedBy;
    }

    /**
     * 更新人
     */
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    /**
     * 更新时间
     */
    public Date getUpdatedDt() {
        return updatedDt;
    }

    /**
     * 更新时间
     */
    public void setUpdatedDt(Date updatedDt) {
        this.updatedDt = updatedDt;
    }

}