package com.cloudstar.dao.model.notify;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统消息通知记录表
 *
 * <AUTHOR>
 * @TableName sys_m_notify_record
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("sys_m_notify_record")
public class SysNotifyRecord implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 系统消息通知配置ID
     */
    private Long notifyConfigId;

    /**
     * 业务分类;password_expiration_replacement(密码到期更换通知)
     */
    private String bssType;

    /**
     * 阈值类别;balance（账户余额）, expire（资源到期）
     */
    private String thresholdCategory;

    /**
     * 对比符号;>,<
     */
    private String symbol;

    /**
     * 阈值类型;number（数字），percent（百分比）h
     */
    private String thresholdValueType;

    /**
     * 通知阈值
     */
    private Long thresholdValue;

    /**
     * 通知时的快照值
     */
    private Long snapshotValue;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 接收者
     */
    private String receiver;

    /**
     * 发送次数
     */
    private Integer sendCount;

    /**
     * 消息通知类型通知方式：以逗号分隔;mail:邮件,sms:短信,默认站内信,station
     */
    private String notifyType;

    /**
     * none（暂不处理）,;prohibit（禁止新建）, freeze（冻结使用）
     */
    private String expireStrategy;

    /**
     * 邮件发送时间
     */
    private Date mailSendDt;

    /**
     * 邮件发送状态
     */
    private Boolean mailSendStatus;

    /**
     * 短信发送时间
     */
    private Date smsSendDt;

    /**
     * 短信发送状态
     */
    private Boolean smsSendStatus;

    /**
     * 更新者
     */
    private String updatedBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 版本号
     */
    private Long version;

    /**
     * 账户ID
     */
    private Long accountSid;

}