package com.cloudstar.dao.model.collect;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 资源池分配率-npu
 *
 * @TableName collect_allocate_npu
 */
@TableName(value = "collect_allocate_npu")
public class CollectAllocateNpu implements Serializable {

    /**
     * 集群id
     */
    @TableField(value = "cluster_id")
    private Long clusterId;

    /**
     * time
     */
    @TableField(value = "time")
    private Date time;

    /**
     * 资源池id
     */
    @TableField(value = "pool_id")
    private String poolId;

    /**
     * 资源池分配情况
     */
    @TableField(value = "pool_allocated")
    private Object poolAllocated;

    /**
     * 资源池总量
     */
    @TableField(value = "pool_capacity")
    private Object poolCapacity;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 集群id
     */
    public Long getClusterId() {
        return clusterId;
    }

    /**
     * 集群id
     */
    public void setClusterId(Long clusterId) {
        this.clusterId = clusterId;
    }

    /**
     * time
     */
    public Date getTime() {
        return time;
    }

    /**
     * time
     */
    public void setTime(Date time) {
        this.time = time;
    }

    /**
     * 资源池id
     */
    public String getPoolId() {
        return poolId;
    }

    /**
     * 资源池id
     */
    public void setPoolId(String poolId) {
        this.poolId = poolId;
    }

    /**
     * 资源池分配情况
     */
    public Object getPoolAllocated() {
        return poolAllocated;
    }

    /**
     * 资源池分配情况
     */
    public void setPoolAllocated(Object poolAllocated) {
        this.poolAllocated = poolAllocated;
    }

    /**
     * 资源池总量
     */
    public Object getPoolCapacity() {
        return poolCapacity;
    }

    /**
     * 资源池总量
     */
    public void setPoolCapacity(Object poolCapacity) {
        this.poolCapacity = poolCapacity;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        CollectAllocateNpu other = (CollectAllocateNpu) that;
        return (this.getClusterId() == null ? other.getClusterId() == null
                : this.getClusterId().equals(other.getClusterId()))
                && (this.getTime() == null ? other.getTime() == null : this.getTime().equals(other.getTime()))
                && (this.getPoolId() == null ? other.getPoolId() == null : this.getPoolId().equals(other.getPoolId()))
                && (this.getPoolAllocated() == null ? other.getPoolAllocated() == null
                : this.getPoolAllocated().equals(other.getPoolAllocated()))
                && (this.getPoolCapacity() == null ? other.getPoolCapacity() == null
                : this.getPoolCapacity().equals(other.getPoolCapacity()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getClusterId() == null) ? 0 : getClusterId().hashCode());
        result = prime * result + ((getTime() == null) ? 0 : getTime().hashCode());
        result = prime * result + ((getPoolId() == null) ? 0 : getPoolId().hashCode());
        result = prime * result + ((getPoolAllocated() == null) ? 0 : getPoolAllocated().hashCode());
        result = prime * result + ((getPoolCapacity() == null) ? 0 : getPoolCapacity().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", clusterId=").append(clusterId);
        sb.append(", time=").append(time);
        sb.append(", poolId=").append(poolId);
        sb.append(", poolAllocated=").append(poolAllocated);
        sb.append(", poolCapacity=").append(poolCapacity);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}