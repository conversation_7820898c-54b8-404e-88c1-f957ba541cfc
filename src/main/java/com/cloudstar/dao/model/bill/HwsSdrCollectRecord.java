package com.cloudstar.dao.model.bill;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import lombok.Data;

/**
 * 话单记录表
 *
 * <AUTHOR>
 * @date 2022/12/5 18:41
 */
@Data
@TableName("hws_sdr_collect_record")
public class HwsSdrCollectRecord {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    //采集时间
    private String fileDate;

    //文件路径
    private String filePath;

    //文件名
    private String fileName;

    //数据条数
    private Integer sdrTotalCount;

    //采集开始时间
    private String beginTime;

    //采集结束时间
    private String endTime;

    //集群适配器uuid
    private String adapterUuid;

    //采集类型
    private Integer type;

    //采集类型名称
    private String typeName;

    /**
     * 创建时间
     */
    private Date createdDt;

}
