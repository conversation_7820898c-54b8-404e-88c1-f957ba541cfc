package com.cloudstar.dao.model.bill;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * obs话单表
 *
 * <AUTHOR>
 * @TableName hws_sdr_obs_data
 * @date 2022/09/26
 */
@Data
@TableName("hws_sdr_obs_data")
public class HwsSdrObsData implements Serializable {

    /**
     * ID
     */
    @TableId
    private Long id;

    /**
     * 记录类型
     */
    private String recordType;

    /**
     * 采集时间
     */
    private String timeStamp;

    /**
     * 用户ID
     */
    private Long userId;


    private String accountUuid;

    /**
     * 区域代码
     */
    private String regionCode;

    /**
     * AvailableZoneCode
     */
    private String azCode;

    /**
     * 云服务类型编码
     */
    private String cloudServiceTypeCode;

    /**
     * 资源类型编码
     */
    private String resourceTypeCode;

    /**
     * 资源规格编码
     */
    private String resourceSpecCode;

    /**
     * 资源实例ID
     */
    private String resourceId;

    /**
     * 运营参数
     */
    private String csbparams;

    /**
     * 统计周期开始时间
     */
    private String beginTime;

    /**
     * 统计结束时间
     */
    private String endTime;

    /**
     * (计费因子名)累积因子名
     */
    private String accumulateFactorName;

    /**
     * (计费因子值)累积因子值
     */
    private String accumulateFactorValue;

    /**
     * 扩展字段
     */
    private String extendParams;

    /**
     * 资源tag
     */
    private String tag;

    /**
     * 企业资源组ID
     */
    private String enterprisePorjectId;

    /**
     * 是否出账 Y：已出账；N：未出账
     */
    private String billFlag;

    /**
     * 乐观锁
     */
    private String version;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 适配器uuid
     */
    private String adapterUuid;

    /**
     * 更新时间
     */
    private Date updatedDt;

    private static final long serialVersionUID = 1L;
}