package com.cloudstar.dao.model;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
public class OverviewCharts {
    private static final String MODULE_NAME = "dashboard.OverviewCharts";

    private Object[] data;
    private Object[] xData;

    public OverviewCharts(Object[] data, Object[] xData) {
        this.data = data;
        this.xData = xData;
    }

    public Object[] getData() {
        return data;
    }

    public void setData(Object[] data) {
        this.data = data;
    }

    public Object[] getXData() {
        return xData;
    }

    public void setXData(Object[] xData) {
        this.xData = xData;
    }

    /**
     * Valid 验证横轴数轴长度一致
     * @createDate 2025/3/6 11:08
     */
    public boolean isValid() {
        if (data == null || xData == null) {
            return false;
        }
        return data.length == xData.length;
    }

    public static OverviewCharts getOverviewCharts(Object[] data, Object[] xData) {
        OverviewCharts a = new OverviewCharts(data, xData);
        if (!a.isValid()) {
            log.warn(MODULE_NAME, "GetOverviewCharts", "数据长度一致");
            throw new IllegalArgumentException("数据长度一致");
        }
        return a;
    }
}
