package com.cloudstar.dao.model;

import lombok.Data;

/**
 * Cluster 类表示集群的详细信息。
 * 包含集群ID、名称、类型、状态、地址、访问密钥和安全密钥等属性。
 */
@Data
public class Cluster {
    /**
     * 集群ID
     */
    private String clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 集群类型
     */
    private String clusterType;

    /**
     * 集群状态
     */
    private String clusterStatus;

    /**
     * 集群地址
     */
    private String clusterAddress;

    /**
     * 集群访问密钥（Access Key）
     */
    private String clusterAccessKey;

    /**
     * 集群安全密钥（Secret Key）
     */
    private String clusterSecurityKey;
}
