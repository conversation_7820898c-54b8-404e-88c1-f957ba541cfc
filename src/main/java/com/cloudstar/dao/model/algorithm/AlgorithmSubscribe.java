package com.cloudstar.dao.model.algorithm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 我的订阅
 *
 * @TableName algorithm_subscribe
 */
@TableName(value = "algorithm_subscribe")
@Data
public class AlgorithmSubscribe implements Serializable {
    /**
     * 订阅id
     */
    private Long id;

    /**
     * 算法id
     */
    private Long algorithmId;

    /**
     * 订阅人
     */
    private Long ownerId;

    /**
     * 算法名称
     */
    private String name;

    /**
     * 时长单位
     */
    private String unit;

    /**
     * 时长：几年或几个月；免费则为0
     */
    private Integer unitValue;

    /**
     * 版本数量
     */
    private Integer algorithmVersionNum;

    /**
     * 订阅开始时间
     */
    private Date subscribeStartTime;

    /**
     * 订阅结束时间
     */
    private Date subscribeEndTime;

    /**
     * 描述
     */
    private String description;

    /**
     * 乐观锁
     */
    private String version;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}