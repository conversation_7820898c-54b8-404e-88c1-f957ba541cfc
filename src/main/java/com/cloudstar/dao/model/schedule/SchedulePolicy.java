package com.cloudstar.dao.model.schedule;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 调度策略
 *
 * @TableName schedule_policy
 */
@Data
@TableName(value = "schedule_policy")
public class SchedulePolicy implements Serializable {

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 策略名称
     */
    @TableField(value = "policy_name")
    private String policyName;

    /**
     * 策略代码;COST:成本优先 QUEUE:排队时长  RESOURCE:资源分配
     */
    @TableField(value = "policy_code")
    private String policyCode;

    /**
     * 状态;ENABLE:启用 DISABLE:禁用
     */
    @TableField(value = "status")
    private String status;

    /**
     * 备注
     */
    @TableField(value = "remark")
    private String remark;

    /**
     * 乐观锁
     */
    @TableField(value = "version")
    private String version;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "created_dt")
    private Date createdDt;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @TableField(value = "updated_dt")
    private Date updatedDt;

    /**
     * 排序
     */
    @TableField(value = "sort")
    private Integer sort;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

}