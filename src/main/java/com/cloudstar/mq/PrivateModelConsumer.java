package com.cloudstar.mq;

import com.cloudstar.common.base.constant.RabbitMqConstants;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.mq.PrivateModelEventMessage;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.res.ResPrivateModelMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.res.ResPrivateModel;
import com.cloudstar.service.grpcservice.facade.AgentPrivateModelService;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 私有模型服务消费者
 *
 * <AUTHOR>
 * @date 2024/08/26
 */
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class PrivateModelConsumer {

    ClusterEntityMapper clusterEntityMapper;

    ResPrivateModelMapper resPrivateModelMapper;


    /**
     * 处理推理服务事件消息
     *
     * @param message 消息
     *
     * @throws Exception 例外
     */
    @RabbitListener(queues = RabbitMqConstants.PRIVATE_MODEL_QUEUE)
    @RabbitHandler
    @Transactional(rollbackFor = Exception.class)
    public void handle(PrivateModelEventMessage message) {
        log.info("接收到私有模型事件消息：{}", JSONUtil.toJsonStr(message));
        try {

            final ResPrivateModel resPrivateModel = resPrivateModelMapper.selectById(message.getId());
            if (ObjectUtil.isEmpty(resPrivateModel)) {
                log.error("未找到私有模型信息：{}", message.getId());
                return;
            }
            ClusterEntity entity =
                    Optional.ofNullable(clusterEntityMapper.selectById(resPrivateModel.getClusterId()))
                            .orElseThrow(() -> new BizException("未找到集群信息"));
            AgentPrivateModelService service = AgentPrivateModelService.build(entity.getAdapterUuid());
            switch (message.getType()) {
                case CREATE:
                    service.create(resPrivateModel);
                    break;
                case START:
                case STOP:
                case DELETE:
                    service.operate(resPrivateModel, message);
                    break;
                default:
                    log.error("未知的私有模型事件类型：{}", message.getType());
                    break;
            }

        } catch (Exception e) {
            log.error("处理私有模型事件消息失败, 抛出异常重试：", e);
            throw e;
        }
    }


}
