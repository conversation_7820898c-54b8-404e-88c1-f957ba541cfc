package com.cloudstar.mq;


import com.cloudstar.common.base.constant.RabbitMqConstants;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 错误消息打印mq消费者
 *
 * <AUTHOR>
 * @date 2024/2/27 11:42
 */
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class ErrorMsgConsumer {


    /**
     * 错误消息消费者
     *
     * @param message 消息
     */
    @RabbitListener(queues = RabbitMqConstants.SAVE_ERROR_MSG_DLX_QUEUE)
    public void consumeMessage(Message message) {
        // 消息内容
        log.error("mq消费失败消息头:{},消息体:{}", message.getMessageProperties(), new String(message.getBody()));
    }
}
