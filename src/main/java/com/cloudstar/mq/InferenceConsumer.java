package com.cloudstar.mq;

import cn.hutool.json.JSONUtil;
import com.cloudstar.common.base.constant.RabbitMqConstants;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.mq.InferenceEventMessage;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.service.grpcservice.facade.AgentInferenceService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 推理服务消费者
 *
 * <AUTHOR>
 * @date 2024/08/26
 */
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class InferenceConsumer {

    ClusterEntityMapper clusterEntityMapper;


    /**
     * 处理推理服务事件消息
     *
     * @param message 消息
     * @throws Exception 例外
     */
    @RabbitListener(queues = RabbitMqConstants.INFERENCE_SERVICE_QUEUE)
    @RabbitHandler
    @Transactional(rollbackFor = Exception.class)
    public void handle(InferenceEventMessage message) throws Exception {
        log.info("接收到推理服务事件消息：{}", JSONUtil.toJsonStr(message));
        try {
            ClusterEntity entity =
                    Optional.ofNullable(clusterEntityMapper.selectById(message.getClusterId()))
                            .orElseThrow(() -> new BizException("未找到集群信息"));
            AgentInferenceService service = AgentInferenceService.build(entity.getAdapterUuid());
            switch (message.getEvent()) {
                case CREATE:
                    service.create(message);
                    break;
                case START:
                    service.start(message);
                    break;
                case STOP:
                    service.stop(message);
                    break;
                case DELETE:
                    service.delete(message);
                    break;
                case RESTART:
                    service.restart(message);
                    break;
                default:
                    log.error("未知的推理服务事件类型：{}", message.getEvent());
                    break;
            }

        } catch (Exception e) {
            log.error("处理推理服务事件消息失败, 抛出异常重试：", e);
            throw e;
        }
    }


}
