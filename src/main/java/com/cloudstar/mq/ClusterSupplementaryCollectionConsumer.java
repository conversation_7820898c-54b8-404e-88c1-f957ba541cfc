package com.cloudstar.mq;

import com.cloudstar.common.base.constant.RabbitMqConstants;
import com.cloudstar.common.base.pojo.mq.ClusterSupplementaryCollectionMessage;
import com.cloudstar.dao.mapper.bill.HwsSdrCollectRecordMapper;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.service.facade.bill.HwsSdrModelartsDataService;
import com.cloudstar.service.facade.bill.HwsSdrObsDataService;
import com.cloudstar.service.facade.cluster.ClusterUserMappingService;
import com.cloudstar.service.grpcservice.facade.AgentBillCollectService;
import com.cloudstar.service.pojo.dto.cluster.ClusterUserMappingBillDto;

import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 集群补充采集消费
 *
 * <AUTHOR>
 * @date 2022-11-07 14:07
 */
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class ClusterSupplementaryCollectionConsumer {

    ClusterEntityMapper clusterEntityMapper;

    HwsSdrModelartsDataService modelartsDataService;

    HwsSdrObsDataService obsDataService;

    ClusterUserMappingService clusterUserMappingService;

    HwsSdrCollectRecordMapper hwsSdrCollectRecordMapper;

    ThreadPoolTaskExecutor threadPool;

    /**
     * 集群子账户
     *
     * @param message 消息
     */
    @RabbitListener(queues = RabbitMqConstants.CLUSTER_SUPPLEMENTARY_COLLECTION_QUEUE)
    @RabbitHandler
    @Transactional(rollbackFor = Exception.class)
    public void clusterSubAccount(ClusterSupplementaryCollectionMessage message) {
        try {
            //获取话单采集途径
            String collectWay = System.getenv("BILL_COLLECT_WAY");
            log.info("话单采集方式:【{}】", collectWay);
            if ("BSS".equals(collectWay)) {
                syncCollectBss(message);
            } else {
                syncCollectHcso(message);
            }

        } catch (Exception e) {
            log.error("补录话单异常", e);
            //手动抛出异常，触发重试机制
            throw e;
        }
    }

    /**
     * 从运营平台获取话单数据
     *
     * @param message 消息
     */
    private void syncCollectBss(ClusterSupplementaryCollectionMessage message) {
        // 创建线程
        final int poolSize = 2;
        final CountDownLatch maxCountDown = new CountDownLatch(poolSize);
        List<ClusterUserMappingBillDto> users = clusterUserMappingService.getAllUserByClusterId(
                message.getClusterId());
        if (CollectionUtil.isEmpty(users)) {
            return;
        }
        for (ClusterUserMappingBillDto user : users) {
            user.setEndTime(Long.parseLong(message.getSupplementaryCollectionEndTime()));
        }
        // 同步modelArts
        threadPool.execute(() -> {
            try {
                modelartsDataService.syncData(users);
            } catch (Exception e) {
                log.error("同步modelArts话单出错", e);
            } finally {
                maxCountDown.countDown();
            }
        });

        // 同步OBS
        threadPool.execute(() -> {
            try {
                obsDataService.syncData(users);
            } catch (Exception e) {
                log.error("同步OBS话单出错", e);
            } finally {
                maxCountDown.countDown();
            }
        });
        try {
            maxCountDown.await();
        } catch (InterruptedException e) {
            log.error("同步话单错误", e);
        }
    }

    /**
     * 从hsco获取话单
     *
     * @param message 消息
     */
    private void syncCollectHcso(ClusterSupplementaryCollectionMessage message) {
        ClusterEntity entity = clusterEntityMapper.selectById(message.getClusterId());
        if (Objects.isNull(entity)) {
            log.error("集群不存在，获取子账号失败");
        } else {
            String startTime = message.getSupplementaryCollectionStartTime();
            String endTime = message.getSupplementaryCollectionEndTime();
            DateTime start = DateUtil.parse(startTime, DatePattern.PURE_DATETIME_PATTERN);
            DateTime end = DateUtil.parse(endTime, DatePattern.PURE_DATETIME_PATTERN);
            List<DateTime> dateTimeList = DateUtil.rangeToList(start, end, DateField.HOUR_OF_DAY);
            List<String> timeList = new ArrayList<>();
            dateTimeList.stream().forEach(time -> {
                String format = DateUtil.format(time, DatePattern.NORM_DATETIME_PATTERN);
                timeList.add(format.split(":")[0]);
            });
            log.info("补采话单采集时间列表:【{}】", timeList);
            //已经采集过的时间
            List<String> list = hwsSdrCollectRecordMapper.queryCollectTimeByCluster(entity.getAdapterUuid());
            Iterator<String> iterator = timeList.iterator();
            //删除掉已经采集过的时间
            while (iterator.hasNext()) {
                String time = iterator.next();
                if (list.contains(time)) {
                    iterator.remove();
                }
            }
            log.info("去除已采集话单时间列表:【{}】", timeList);
            String fileDate = DateUtil.now();
            AgentBillCollectService service = AgentBillCollectService.build(entity.getAdapterUuid());
            service.getLatelyBill(timeList, entity.getAdapterUuid(), fileDate);
        }
    }
}
