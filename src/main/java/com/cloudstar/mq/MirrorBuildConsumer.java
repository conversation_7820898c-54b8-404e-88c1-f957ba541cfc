package com.cloudstar.mq;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.cloudstar.common.base.constant.RabbitMqConstants;
import com.cloudstar.common.base.enums.MirrorStatusEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.mq.MirrorBuildMessage;
import com.cloudstar.common.base.util.WebUtil;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.mirror.UserMirrorMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.mirror.UserMirror;
import com.cloudstar.service.grpc.MirrorBuildProto;
import com.cloudstar.service.grpcservice.facade.MirrorBuildService;
import io.grpc.stub.StreamObserver;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 镜像构建消费者
 *
 * <AUTHOR>
 * @date 2024/11/04
 */
@Component
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class MirrorBuildConsumer {

    UserMirrorMapper userMirrorMapper;

    ClusterEntityMapper clusterEntityMapper;

    public static Long mirrorId;

    /**
     * 处理来自cfn-console-server的指令，并调用grpc服务
     * @param message mq客户端发送的指令
     */
    @RabbitHandler
    @RabbitListener(queues = RabbitMqConstants.MIRROR_BUILD_QUEUE)
    public void mirrorBuildConsumer(MirrorBuildMessage message) {
        log.info("收到来自cfn-console-server发送的消息: {}", JSONUtil.toJsonStr(message));
        ClusterEntity entity =
                Optional.ofNullable(clusterEntityMapper.selectById(message.getClusterId()))
                        .orElseThrow(() -> new BizException("未找到集群信息"));

        // 构建req请求体
        MirrorBuildProto.MirrorBuildMessage mirrorReq = MirrorBuildProto.MirrorBuildMessage.newBuilder()
                .setAccount(message.getAccount())
                .setOrgSid(message.getOrgSid())
                .setPlatformArchitecture(message.getPlatformArchitecture())
                .setTrainingFramework(message.getTrainingFramework())
                .setTrainingDriver(message.getTrainingDriver())
                .setOperatingSystem(message.getOperatingSystem())
                .setMirrorName(message.getMirrorName())
                // 判断自定义内容是否为空，不为空则设置
                .setCustomContent(StrUtil.isNotBlank(message.getCustomContent()) ? message.getCustomContent() : "")
                .build();

        // 用户镜像数据写入数据库
        UserMirror userMirror = UserMirror.builder()
                .mirrorName(mirrorReq.getMirrorName())
                .orgSid(message.getOrgSid())
                .trainingFramework(mirrorReq.getTrainingFramework())
                .trainingDriver(mirrorReq.getTrainingDriver())
                .operatingSystem(mirrorReq.getOperatingSystem())
                .platformArchitecture(mirrorReq.getPlatformArchitecture())
                .status(String.valueOf(MirrorStatusEnum.CREATING))
                .dockerfileContent(null)
                .build();
        try {
            WebUtil.prepareInsertParams(userMirror, message.getAccount());
            userMirrorMapper.insert(userMirror);
            mirrorId = userMirror.getMirrorId();
            log.info("数据插入成功，mirrorId: {}", mirrorId);
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e.getMessage());
        }

        try {
            log.info("消费者开始调用 gRPC 服务");
            MirrorBuildService mirrorBuildService = MirrorBuildService.build(entity.getAdapterUuid());

            mirrorBuildService.mirrorBuild(mirrorReq, new StreamObserver<>() {
                @Override
                public void onNext(MirrorBuildProto.MirrorBuildResponse response) {
                    log.info("Build executed successfully: {}", response.getMessage());
                }

                @Override
                public void onError(Throwable throwable) {
                    log.error("gRPC call failed: {}", throwable.getMessage());
                }

                @Override
                public void onCompleted() {
                    log.info("gRPC call completed.");
                }
            });
        } catch (Exception e) {
            log.error("Failed to initiate build request: {}", e.getMessage());
            throw new RuntimeException(e.getMessage());
        }
    }
}