package com.cloudstar.security.filter.csrf;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.SecurityWebFiltersOrder;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;

import lombok.RequiredArgsConstructor;


/**
 * csrf配置
 *
 * <AUTHOR>
 */
@EnableWebFluxSecurity
@RequiredArgsConstructor
@ConditionalOnProperty(name = "cfn.gateway.csrf.enabled", havingValue = "true", matchIfMissing = true)
public class CsrfConfig {

    private final CsrfAuthMatcher csrfAuthMatcher;
    private final CsrfTokenRepository  csrfTokenRepository;

    @Bean
    public SecurityWebFilterChain springSecurityFilterChain(ServerHttpSecurity http) {
        return http
                .csrf(csrf -> {
                    //添加token来源
                    csrf.csrfTokenRepository(csrfTokenRepository);
                    //添加白名单规则
                    csrf.requireCsrfProtectionMatcher(csrfAuthMatcher);
                })
                //添加过滤器，再CsrfWebFilter后执行
                .addFilterAfter(new CsrfAfterTokenFilter(), SecurityWebFiltersOrder.CSRF)
                .build();
    }

}
