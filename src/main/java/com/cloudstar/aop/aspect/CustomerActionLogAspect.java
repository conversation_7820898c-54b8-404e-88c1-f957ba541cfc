package com.cloudstar.aop.aspect;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.aop.annotation.CustomerActionLog;
import com.cloudstar.common.base.enums.ActionLogTypeEnum;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.util.IpAddressUtil;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.config.DesensitizationUtil;
import com.cloudstar.dao.mapper.log.ActionLogMapper;
import com.cloudstar.dao.mapper.user.UserEntityMapper;
import com.cloudstar.dao.model.log.ActionLog;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.service.facade.user.UserEntityService;
import com.cloudstar.service.pojo.vo.requestvo.user.FindPwdReq;

import org.apache.poi.ss.formula.functions.T;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.cloud.sleuth.Span;
import org.springframework.cloud.sleuth.Tracer;
import org.springframework.context.MessageSource;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ClassUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 用户的操作写入日志切面
 *
 * <AUTHOR>
 * @date 2022.8.20
 */
@Order(1)
@Aspect
@Component
@Slf4j
@RequiredArgsConstructor()
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public class CustomerActionLogAspect {

    ActionLogMapper actionLogMapper;


    Tracer tracer;

    MessageSource messageSource;

    UserEntityService userEntityService;

    UserEntityMapper userEntityMapper;

    @Pointcut("@annotation(com.cloudstar.aop.annotation.CustomerActionLog)")
    public void pointCut() {

    }

    /**
     * 日志记录切面
     */
    @Around(value = "pointCut()")
    @Transactional(rollbackFor = Exception.class)
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {

        Method targetMethod = findTargetMethod(joinPoint);
        CustomerActionLog actionLog = AnnotationUtils.findAnnotation(targetMethod, CustomerActionLog.class);
        if (actionLog == null) {
            throw new Exception("日志记录对象不能为空");
        }
        final HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        //如果是feign调用就不记录操作日志
        String useFeign = request.getHeader("use_feign");
        if (ObjectUtil.isNotEmpty(useFeign) && "true".equals(useFeign)) {
            return joinPoint.proceed();
        }

        String userClient = request.getHeader("User-Agent");
        Span span = tracer.currentSpan();
        String traceId = "";
        String spanId = "";
        if (ObjectUtil.isNotEmpty(span)) {
            traceId = span.context().traceId();
            spanId = span.context().spanId();
        }
        String declaringTypeName = joinPoint.getSignature().getDeclaringTypeName();
        // 获取当前登录用户信息
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            if (ActionLogTypeEnum.FIND_USER_PASSWORD.equals(actionLog.Type())) {
                Object[] args = joinPoint.getArgs();
                FindPwdReq req = (FindPwdReq) args[0];
                UserEntity userEntity = userEntityService.list().stream().filter(item -> {
                    if (StrUtil.equals(item.getMobile(), req.getMobile())) {
                        return true;
                    } else if (StrUtil.equals(item.getEmail(), req.getEmail())) {
                        return true;
                    } else {
                        return false;
                    }
                }).findAny().orElse(null);
                if (ObjectUtil.isEmpty(userEntity)) {
                    return joinPoint.proceed();
                }
                authUser = BeanUtil.copyProperties(userEntity, AuthUser.class);
            } else {
                log.info("CustomerActionLogAspect authUser is null ");
                throw new BizException("无权限操作。");
            }
        }

        UserEntity userEntity = userEntityMapper.selectOne(
                new LambdaQueryWrapper<UserEntity>().select(UserEntity::getParentSid, UserEntity::getUserSid)
                                                    .eq(UserEntity::getUserSid, authUser.getUserSid()));
        if (Objects.isNull(userEntity)) {
            log.warn("没有找到账号:{}", authUser.getUserSid());
        }
        String roleName =
                Objects.nonNull(userEntity) && Objects.isNull(userEntity.getParentSid()) ? "租戶" : "租户子账号";
        try {

            Object proceed = null;
            String data = null;
            boolean success;
            Rest result = null;
            proceed = joinPoint.proceed();
            if (proceed instanceof Rest) {
                result = (Rest) proceed;
                success = result.isSuccess();
            } else {
                success = true;
            }
            if (ObjectUtil.isNotEmpty(result)) {
                if (Objects.nonNull(result.getData())) {
                    if (result.getData() instanceof PageResult) {
                        List<T> list = ((PageResult<T>) result.getData()).getList();
                        List<T> desensitization = DesensitizationUtil.desensitization(list);
                        ((PageResult<T>) result.getData()).setList(desensitization);
                        data = JSONUtil.toJsonStr(result.getData());
                    } else {
                        Object desensitization = DesensitizationUtil.desensitization(result.getData());
                        data = JSONUtil.toJsonStr(desensitization);
                    }

                }

            }

            ActionLog log = ActionLog.builder()
                                     .account(authUser.getAccount())
                                     .accountId(String.valueOf(authUser.getUserSid()))
                                     .accountType(authUser.getAccountType())
                                     .roleName(roleName)
                                     .actionMethod(targetMethod.getName())
                                     .actionName(actionLog.Type().getDesc())
                                     .actionTime(new Date())
                                     .actionPath(request.getRequestURI())
                                     .result(data)
                                     .remoteIp(IpAddressUtil.getRemoteHostIp(request))
                                     .httpMethod(request.getMethod())
                                     .client(userClient)
                                     .spanId(spanId)
                                     .traceId(traceId)
                                     .actionResult(success)
                                     .className(declaringTypeName)
                                     .build();
            String encryptLog = DigestUtil.sha256Hex(JSONUtil.toJsonStr(log));
            log.setEncryptLog(encryptLog);
            // 对不同请求的参数进行不同处理
            if (("POST").equals(request.getMethod())) {
                log.setParam(desensitizationP(joinPoint));
            } else {
                log.setParam(desensitizationG(joinPoint, request));
            }
            // 记录日志
            actionLogMapper.insert(log);
            return proceed;
        } catch (Throwable e) {
            String message = e.getMessage();
            if (e instanceof BizException) {
                String code = ((BizException) e).getCode();
                message = messageSource.getMessage(code, null, Locale.getDefault());
            }
            ActionLog failedLog = ActionLog.builder()
                                           .account(authUser.getAccount())
                                           .accountId(String.valueOf(authUser.getUserSid()))
                                           .accountType(authUser.getAccountType())
                                           .roleName(roleName)
                                           .actionMethod(targetMethod.getName())
                                           .actionName(actionLog.Type().getDesc())
                                           .actionTime(new Date())
                                           .actionPath(request.getRequestURI())
                                           .result(message)
                                           .remoteIp(IpAddressUtil.getRemoteHostIp(request))
                                           .httpMethod(request.getMethod())
                                           .client(userClient)
                                           .spanId(spanId)
                                           .traceId(traceId)
                                           .actionResult(false)
                                           .className(declaringTypeName).build();
            String encryptLog = DigestUtil.sha256Hex(JSONUtil.toJsonStr(log));
            failedLog.setEncryptLog(encryptLog);
            if (("POST").equals(request.getMethod())) {
                failedLog.setParam(desensitizationP(joinPoint));
            } else {
                failedLog.setParam(desensitizationG(joinPoint, request));
            }
            actionLogMapper.insert(failedLog);
            throw e;
        }
    }

    private Method findTargetMethod(JoinPoint joinPoint) throws NoSuchMethodException {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Class[] parameterTypes = signature.getParameterTypes();
        return ClassUtils.getUserClass(joinPoint.getTarget())
                         .getMethod(joinPoint.getSignature().getName(), parameterTypes);
    }

    /**
     * 日志记录敏感信息黑名单
     */
    public List<String> logDesensitization() {
        List<String> privacyFields = new ArrayList<>();
        Collections.addAll(privacyFields, "phone", "mobile", "email");
        return privacyFields;
    }

    /**
     * GET/DELETE 请求参数脱敏
     */
    public String desensitizationG(JoinPoint joinPoint, HttpServletRequest request) {
        Map<String, Object> map = new LinkedHashMap<>();
        // 遍历脱敏
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String[] parameterNames = signature.getParameterNames();
        for (int i = 0; i < parameterNames.length; i++) {
            for (String privacyField : logDesensitization()) {
                if (parameterNames[i].equals(privacyField)) {
                    map.put(parameterNames[i], "********");
                    break;
                } else {
                    map.put(parameterNames[i], request.getParameter(parameterNames[i]));
                }
            }
        }
        return JSONUtil.toJsonStr(map);
    }

    /**
     * POST/PUT 请求参数脱敏
     */
    public String desensitizationP(JoinPoint joinPoint) {
        Object[] params = joinPoint.getArgs();
        if (ObjectUtil.isNotEmpty(params)) {
            Object requestBody = params[0];
            Object desensitization = DesensitizationUtil.desensitization(requestBody);
            return JSONUtil.toJsonStr(desensitization);
        } else {
            return "";
        }
    }
}