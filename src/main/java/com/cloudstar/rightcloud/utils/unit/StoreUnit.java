package com.cloudstar.rightcloud.utils.unit;

import cn.hutool.core.util.EnumUtil;
import lombok.Getter;

/**
 * 容量单位
 *
 * <AUTHOR>
 * @since 2023/9/12 13:53
 */
@Getter
public enum StoreUnit {

    BIT(StoreUnit.BIT_SCALE),
    BYTE(StoreUnit.BYTE_SCALE),
    KB(StoreUnit.KB_SCALE),
    MB(StoreUnit.MB_SCALE),
    GB(StoreUnit.GB_SCALE),
    TB(StoreUnit.TB_SCALE),
    PB(StoreUnit.PB_SCALE);

    private static final long BIT_SCALE = 1L;

    private static final long BYTE_SCALE = 8L * BIT_SCALE;

    private static final long KB_SCALE = 1024L * BYTE_SCALE;

    private static final long MB_SCALE = 1024L * KB_SCALE;

    private static final long GB_SCALE = 1024L * MB_SCALE;

    private static final long TB_SCALE = 1024L * GB_SCALE;

    private static final long PB_SCALE = 1024L * TB_SCALE;

    /**
     * 单位比较
     *
     * @param unit 单位
     * @return 布尔值
     */
    public boolean equalsIgnoreCase(String unit) {
        return EnumUtil.equalsIgnoreCase(this, unit);
    }

    private final long scale;

    StoreUnit(long scale) {
        this.scale = scale;
    }

    /**
     * 获取枚举值
     *
     * @param unit 单位
     * @return CapacityUnit 枚举对象
     */
    public static StoreUnit getStoreUnit(String unit) {
        StoreUnit[] values = StoreUnit.values();
        for (StoreUnit storeUnit : values) {
            if (storeUnit.name().equalsIgnoreCase(unit)) {
                return storeUnit;
            }
        }
        return null;
    }

}
