package com.cloudstar.rightcloud.utils.tasklog.produce;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.cloudstar.rightcloud.common.exception.BizException;
import com.cloudstar.rightcloud.common.utils.dict.DictCodeUtil;
import com.cloudstar.rightcloud.common.utils.dict.SystemFeignUrlConfig;
import com.cloudstar.rightcloud.common.utils.http.HttpRestUtil;
import com.cloudstar.rightcloud.common.utils.web.CrudHelpUtil;
import com.cloudstar.rightcloud.utils.tasklog.model.TaskInfo;
import com.cloudstar.rightcloud.utils.tasklog.param.TaskLogCreateParam;
import com.cloudstar.rightcloud.utils.tasklog.param.TaskLogTraceUpdateParam;
import com.cloudstar.rightcloud.utils.tasklog.param.TaskLogUpdateParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 任务消息生产者
 *
 * <AUTHOR> Lesao
 */
public class TaskLogMqProduce {

    private static final Logger LOGGER = LoggerFactory.getLogger(DictCodeUtil.class);


    /**
     * 创建任务api
     */
    private static final String CREATE_TASK_API_URL = "/api/system/v1/task/logs";

    /**
     * 更新任务api
     */
    private static final String TASK_LOG_API_URL = "/api/system/v1/task/logs/update";

    /**
     * 更新任务链路信息api
     */
    private static final String TASK_LOG_TRACE_API_URL = "/api/system/v1/task/logs/update/trace";



    /**
     * 发送创建任务请求
     *
     * @param taskInfo 修改的调度日志
     * @return taskId 任务id
     */
    public static Long sendCreateTask(TaskInfo taskInfo) {
        TaskLogCreateParam taskLogCreateParam = BeanUtil.copyProperties(taskInfo, TaskLogCreateParam.class);
        CrudHelpUtil.prepareInsertParams(taskLogCreateParam);
        Long taskId = sendRequest(CREATE_TASK_API_URL, taskLogCreateParam);
        if (taskId == null) {
            throw new BizException("Task creation failure");
        }
        return taskId;
    }


    /**
     * 发送修改任务请求
     *
     * @param updateParam 修改的调度日志
     */
    public static void sendTaskLogUpdate(TaskLogUpdateParam updateParam) {
        sendRequest(TASK_LOG_API_URL, updateParam);
    }

    /**
     * 发送添加任务链路日志请求
     *
     * @param updateParam 修改的调度日志
     */
    public static void sendTaskLogTraceUpdate(TaskLogTraceUpdateParam updateParam) {
        sendRequest(TASK_LOG_TRACE_API_URL, updateParam);
    }

    /**
     * 发送调用任务中心接口
     *
     * @param param 入参
     * @return 出参（创建类会返回任务id）
     */
    public static Long sendRequest(String url, Object param) {
        SystemFeignUrlConfig urlConfig = SpringUtil.getBean(SystemFeignUrlConfig.class);
        String systemUrl = urlConfig.getSystemUrl();
        if (StrUtil.isBlank(systemUrl)) {
            LOGGER.warn("send task center system url is null，param[{}]", JSONObject.toJSONString(param));
            return null;
        }
        String result = HttpRestUtil.post(systemUrl + url, BeanUtil.beanToMap(param));
        if (StrUtil.isBlank(result)) {
            return null;
        }
        JSONObject jsonResult = JSONObject.parseObject(result);
        Boolean status = jsonResult.getBoolean("status");
        if (!status) {
            LOGGER.error("Failed to create a task：{}", result);
            return null;
        }
        return jsonResult.getLong("data");
    }

}
