package com.cloudstar.rightcloud.common.exception;

import com.cloudstar.rightcloud.common.enums.ResultCodeEnum;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 认证失败 异常
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @date: 2024/4/8 18:06
 */
@Data
@Slf4j
public class AuthException extends RuntimeException {

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 错误code
     */
    private int errorCode;

    /**
     * 构造 AuthException 的实例.
     */
    public AuthException() {
        this.errorMessage = super.getMessage();
        this.errorCode = ResultCodeEnum.UNAUTHORIZED_ACCESS.code();
    }


    /**
     * 构造 AuthException 的实例.
     *
     * @param message 错误信息
     */
    public AuthException(String message) {
        super(message);
        this.errorMessage = super.getMessage();
        this.errorCode = ResultCodeEnum.UNAUTHORIZED_ACCESS.code();
    }
}
