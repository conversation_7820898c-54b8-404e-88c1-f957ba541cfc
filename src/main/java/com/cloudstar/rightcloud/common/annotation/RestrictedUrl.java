package com.cloudstar.rightcloud.common.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * url 限制匹配  例如 /configs/{configType}接口在权限中配置了 /configs/system_config,则只有这个请求能够通过,/configs/login_tips_config类的请求就不能够通过
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RestrictedUrl {

    /**
     * 需要转换的参数,如果不设置 直接以请求的路径作为匹配
     */
    String[] convert() default {};
}
