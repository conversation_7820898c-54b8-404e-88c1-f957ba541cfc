package com.cloudstar.rightcloud.common.validator;

import cn.hutool.core.util.EnumUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.LinkedHashMap;
import java.util.Objects;
import java.util.Set;

@Slf4j
@SuppressWarnings("all")
public class EnumValidator implements ConstraintValidator<EnumValid, Object> {

    private Class<? extends Enum> enumClass;

    @Override
    public void initialize(EnumValid constraintAnnotation) {
        this.enumClass = constraintAnnotation.value();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        // null直接判定通过，若不能为空，交给注解@NotNull去验证
        if (Objects.isNull(value)) {
            return true;
        }
        if (EnumUtil.isEnum(this.enumClass)) {
            LinkedHashMap<String, Enum> enumMap = EnumUtil.getEnumMap(this.enumClass);
            Set<String> keyedSet = enumMap.keySet();
            for (String key : keyedSet) {
                if (StrUtil.equalsIgnoreCase(key, String.valueOf(value))) {
                    return true;
                }
            }
        }
        return false;
    }

}
