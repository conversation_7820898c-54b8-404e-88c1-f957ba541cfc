package com.cloudstar.rightcloud.common.validator;

import com.google.common.base.Strings;

import java.util.Objects;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;


/**
 * The type MobileConstraintValidator.
 *
 * <AUTHOR>
 * @date 2019/8/8
 */
public class EnumConstraintValidator implements ConstraintValidator<EnumValue, Object> {

    private String[] strValues;

    @Override
    public void initialize(EnumValue constraintAnnotation) {
        strValues = constraintAnnotation.strValues();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        // null直接判定通过，若不能为空，交给注解@NotNull去验证
        if (Objects.isNull(value)) {
            return true;
        }

        if (value instanceof String) {
            String strValue = value.toString();
            // 空字符串直接判定通过，若不能为空，交给注解@NotBlank去验证
            if (Strings.isNullOrEmpty(strValue)) {
                return true;
            }

            for (String s : strValues) {
                if (s.equals(strValue)) {
                    return true;
                }
            }
        }
        return false;

    }
}
