package com.cloudstar.rightcloud.common.utils.pdf;

import com.itextpdf.commons.utils.Base64;
import com.itextpdf.io.util.StreamUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PdfUtilTest {
    public static void main(String[] args) throws Exception {
        demo1();
        demo2();
        demo21();
        demo3();
        demo4();
        demo5();
    }

    /**
     * word转pdf
     */
    public static void demo1() {
        String absolutePath = "D:/pdf/" + System.currentTimeMillis() + ".pdf";
        String resourceLocation = "D:/pdf/temp/bug1.docx";
        Docx4jToPdfUtil.convertDocxToPdf(resourceLocation, absolutePath);
        System.out.println("doc转pdf完成");
    }

    /**
     * htm转pdf
     */
    public static void demo2() {
        Map<String, Object> params = new HashMap<>();
        params.put("captcha", "123456");
        Map<String, String> data1 = new HashMap<>();
        data1.put("name", "张三");
        data1.put("age", "18");
        data1.put("sex", "男");
        List<Map<String, String>> tempDatas = new ArrayList<>();
        tempDatas.add(data1);
        Map<String, String> data2 = new HashMap<>();
        data2.put("name", "李四");
        data2.put("age", "20");
        data2.put("sex", "女");
        tempDatas.add(data2);
        params.put("users", tempDatas);

        String htmlToString = ThymeleafResolverUtil.resolver("D:/pdf/temp/register-captcha2.html", params);
        ItextHtmlToPdfUtil.mkdirPdf("D:/pdf/" + System.currentTimeMillis() + ".pdf", htmlToString);
        System.out.println("htm转pdf完成");
    }

    /**
     * htm转pdf
     */
    public static void demo21() {
        Map<String, Object> params = new HashMap<>();
        params.put("captcha", "123456");
        String htmlToString = ThymeleafResolverUtil.resolver("D:/pdf/temp/register-captcha.html", params);
        ItextHtmlToPdfUtil.mkdirPdf("D:/pdf/" + System.currentTimeMillis() + ".pdf", htmlToString);
        System.out.println("htm转pdf完成");
    }

    /**
     * pdf模板填充文本和图片
     */
    public static void demo3() throws Exception {
        File file = new File("D:/pdf/temp/baidu.png");
        InputStream inputStream = new FileInputStream(file);
        String image = Base64.encodeBytes(StreamUtil.inputStreamToArray(inputStream));
        String resourceLocation = "D:/pdf/temp/personal-info.pdf";
        String absolutePath = "D:/pdf/" + System.currentTimeMillis() + ".pdf";
        Map<String, String> map = new HashMap<>();
        map.put("realName", "test");
        map.put("qrcode_af_image", image);
        ItextPdfUtil.mkdirTextPdf(resourceLocation, absolutePath, map);
        System.out.println("pdf模板填充文本和图片完成");
    }

    /**
     * htm转pdf 自定义字体，字体加粗
     */
    public static void demo4() throws Exception {
        String absolutePath = "D:/pdf/" + System.currentTimeMillis() + ".pdf";
        File file = new File("D:/pdf/temp/font-bold.html");
        InputStream inputStream = new FileInputStream(file);
        //InputStream inputStream = new ClassPathResource("D:/pdf/temp/font-bold.html").getInputStream();
        String htmlToString = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        ItextHtmlToPdfUtil.mkdirPdf(absolutePath, htmlToString);
        System.out.println("htm转pdf完成");
    }

    /**
     * pdf填充，返回字节流
     */
    public static void demo5() throws Exception {
        String resourceLocation = "D:/pdf/temp/personal-info.pdf";
        String absolutePath = "D:/pdf/" + System.currentTimeMillis() + ".pdf";
        Map<String, String> params = new HashMap<>();
        params.put("realName", "张飞");
        File resourceFile = new File(resourceLocation);
        InputStream inputStream = new FileInputStream(resourceFile);
        byte[] bytes = ItextPdfUtil.pdfToByte(inputStream, params);
        File outputFile = new File(absolutePath);
        FileUtils.writeByteArrayToFile(outputFile, bytes);
        System.out.println("pdf填充完成");
    }
}
