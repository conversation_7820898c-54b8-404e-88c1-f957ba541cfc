package com.cloudstar.rightcloud.common.utils.auth;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.cloudstar.rightcloud.common.pojo.user.AuthUser;

/**
 * 接口请求持有人基本信息
 *
 * @author: chengpeng
 * @date: 2022/10/17 15:14
 */
public final class AuthUserHolderUtil {
    private static final TransmittableThreadLocal<AuthUser> AUTH_USER_HOLDER = new TransmittableThreadLocal<>();
    /**
     * 获得用户基本信息
     *
     * @return {@link String}
     */
    public static AuthUser getAuthUser() {
        return AUTH_USER_HOLDER.get();
    }

    /**
     * 获得用户Id
     *
     * @return {@link String}
     */
    public static Long getAuthUserId() {
        return AUTH_USER_HOLDER.get().getUserId();
    }


    /**
     * 获得用户Id
     *
     * @return {@link String}
     */
    public static Long getAuthOrgId() {
        return AUTH_USER_HOLDER.get().getOrgId();
    }

    /**
     * 获得用户登录名
     *
     * @return {@link String}
     */
    public static String getAuthUserAccount() {
        return AUTH_USER_HOLDER.get().getAccount();
    }

    /**
     * 设置用户基本信息
     *
     * @param authUser 用户基本信息
     */
    public static void setAuthUser(AuthUser authUser) {
        AUTH_USER_HOLDER.set(authUser);
    }

    /**
     * 清除用户基本信息
     */
    public static void clear() {
        AUTH_USER_HOLDER.remove();
    }
}
