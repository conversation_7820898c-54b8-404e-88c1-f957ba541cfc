package com.cloudstar.rightcloud.common.utils.base;

import com.cloudstar.rightcloud.common.pojo.validate.ValidateResult;
import com.cloudstar.rightcloud.common.utils.LocaleLanguageContextUtil;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.common.utils.message.MessageUtil;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.ValidatorFactory;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * Validate 校验实体工具类
 *
 * @author: zhangqiang
 * @date: 2023/6/16 13:54
 */
@Component
public class ValidateUtil {

    private static ValidatorFactory validatorFactory;

    @Resource
    public void setValidator(ValidatorFactory validator) {
        ValidateUtil.validatorFactory = validator;
    }

    /**
     * 手动触发校验实体 默认不抛出异常
     *
     * @param entity 校验实体
     */
    public static <T> ValidateResult validateEntity(T entity) {
        return validateEntity(entity, Boolean.FALSE);
    }

    /**
     * 手动触发校验实体
     *
     * @param entity 校验实体
     * @param throwException 是否需要抛出异常
     */
    public static <T> ValidateResult validateEntity(T entity, Boolean throwException) {
        ValidateResult validateResult = new ValidateResult();
        if (ObjectUtil.isNull(entity)) {
            validateResult.setPass(Boolean.FALSE);
            return validateResult;
        }

        Set<ConstraintViolation<T>> validate = validatorFactory.getValidator().validate(entity);
        if (validate.size() > 0) {
            //处理国际化消息
            Class<?> entityClass = entity.getClass();
            Map<String, String> i18nMap = MessageUtil.getI18nMap(entityClass);
            List<String> errorMessageList = new ArrayList<>();

            //组装错误消息
            for (ConstraintViolation<T> cv : validate) {
                String filed = cv.getPropertyPath().toString();
                StringBuilder errorMessage = new StringBuilder();
                errorMessage.append(StrUtil.isNotBlank(i18nMap.get(filed)) ? i18nMap.get(filed) : filed);
                if (LocaleLanguageContextUtil.isEn()) {
                    errorMessage.append(" ");
                }
                errorMessage.append(cv.getMessage());
                errorMessageList.add(errorMessage.toString());
            }

            validateResult.setPass(Boolean.FALSE);
            validateResult.setErrorMessage(String.join(",", errorMessageList));
            if (ObjectUtil.isNotNull(throwException) && throwException) {
                BizAssertUtils.fail(validateResult.getErrorMessage());
            }
            return validateResult;
        }

        validateResult.setPass(Boolean.TRUE);
        return validateResult;
    }


}
