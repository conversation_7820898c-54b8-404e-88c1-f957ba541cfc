package com.cloudstar.rightcloud.common.utils.excel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 导出excel模板标题格式bean
 *
 * <AUTHOR> Lesao
 * @date : 2023/5/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExcelHeader {

    /**
     * 列key
     */
    private String columnKey;

    /**
     * 列名
     */
    private String columnName;

    /**
     * 列名国际化
     */
    private String columnNameEn;

    /**
     * 下拉框（excel单元格下拉框，该值不为空则默认生成）
     */
    private List<String> dropDownBox;

    /**
     * 是否必填（如果必填则把该标题背景颜色设为红色）
     */
    private Boolean required = false;


}
