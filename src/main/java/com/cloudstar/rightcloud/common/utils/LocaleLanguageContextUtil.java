package com.cloudstar.rightcloud.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import java.util.Locale;

import cn.hutool.core.util.StrUtil;

/**
 * 国际化环境工具类
 *
 * <AUTHOR> Lesao
 * @date : 2023/5/15
 */

@Component
public class LocaleLanguageContextUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(LocaleLanguageContextUtil.class);

    /**
     * 语言分割 字符串
     */
    private static final String LANGUAGE_SPILT_STR = "-";

    /**
     * 默认语言
     */
    private static Locale defaultLocale;

    @Value("${language.local:zh-CN}")
    public void setDefaultLocale(String defaultLocale) {
        LocaleLanguageContextUtil.defaultLocale = coverDefaultLocale(defaultLocale);
    }

    /**
     * 判断当前国际化环境是否为英文
     */
    public static Boolean isEn() {
        try {
            LocaleContextHolder.setDefaultLocale(defaultLocale);
            return LocaleContextHolder.getLocale().getLanguage().equals("en");
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return false;
        }
    }


    private Locale coverDefaultLocale(String language) {
        if (StrUtil.isNotBlank(language) && language.contains(LANGUAGE_SPILT_STR)) {
            String[] split = language.split(LANGUAGE_SPILT_STR);
            if (split.length == 2) {
                return new Locale(split[0].trim().toLowerCase(), split[1].trim().toUpperCase());
            }
        }
        return Locale.SIMPLIFIED_CHINESE;
    }
}
