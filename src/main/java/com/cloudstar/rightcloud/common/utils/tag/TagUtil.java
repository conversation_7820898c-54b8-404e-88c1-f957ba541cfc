package com.cloudstar.rightcloud.common.utils.tag;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * 标签工具类
 *
 * @author: kris
 * @date: 2024/5/21 16:23
 */
@Slf4j
public class TagUtil {


    private static Logger logger = LoggerFactory.getLogger(TagUtil.class);


    /**
     * JSON格式转化为key:value;key1:value1格式
     *
     * @param tag 标签
     */
    public static String convertTag(String tag) {
        List<String> tagResult = new ArrayList<>();
        if (StrUtil.isNotBlank(tag) && JSONUtil.isTypeJSONArray(tag)) {
            JSONArray tagArray = JSONUtil.parseArray(tag);
            if (ObjectUtil.isNotEmpty(tagArray)) {
                for (Object o : tagArray) {
                    JSONObject tagObj = (JSONObject) o;
                    tagResult.add(tagObj.getStr("name") + ":" + tagObj.getStr("value"));
                }
            }
            
        }
        return StrUtil.join(";", tagResult);
    }
}
