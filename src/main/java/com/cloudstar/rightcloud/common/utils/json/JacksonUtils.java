package com.cloudstar.rightcloud.common.utils.json;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.databind.type.MapType;
import com.google.common.base.Throwables;

import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.extern.slf4j.Slf4j;

/**
 * Jackson 工具类
 *
 * @author: zhang<PERSON><PERSON>
 * @date: 2022/10/17 15:29
 */
@Slf4j
public class JacksonUtils {

    private static final ObjectMapper OBJECT_MAPPER;

    static {
        OBJECT_MAPPER = generateMapper(JsonInclude.Include.ALWAYS);
    }

    /**
     * 转换为json字符串
     *
     * @param data 待转换object
     */
    public static String toJsonString(Object data) {
        try {
            return OBJECT_MAPPER.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            log.error("json转换异常：{}", data);
            return null;
        }
    }

    /**
     * json 转实体
     *
     * @param jsonData 待转换json字符串
     * @param beanType class类型
     */
    public static <T> T fromJson(String jsonData, Class<T> beanType) {
        try {
            return OBJECT_MAPPER.readValue(jsonData, beanType);
        } catch (Exception e) {
            log.error("json转换异常：{}", jsonData);
            return null;
        }
    }

    /**
     * 将json通过类型转换成List<p/>
     *
     * @param <T> the type parameter
     * @param inputStream 文件读入流
     * @param typeReference 引用类型
     *
     * @return 返回对象 t
     */
    public static <T> T fromJson(InputStream inputStream, TypeReference<?> typeReference) {
        try {
            return (T) (typeReference.getType().equals(String.class) ? OBJECT_MAPPER.readValue(inputStream, String.class)
                    : OBJECT_MAPPER.readValue(inputStream, typeReference));
        } catch (IOException ex) {
            log.error(Throwables.getStackTraceAsString(ex));
            throw new RuntimeException("JSON格式转换错误。");
        }
    }

    /**
     * From json json node.
     *
     * @param json the json
     *
     * @return the json node
     */
    public static JsonNode fromJson(String json) {
        if (json == null || json.length() == 0) {
            return null;
        }
        try {
            return mapper().getFactory().createParser(json).readValueAsTree();
        } catch (IOException e) {
            return null;
        }
    }

    /**
     * 复杂对象转化
     *
     * @param <T> the type parameter
     * @param json the json
     * @param parametrized the parametrized
     * @param parameterClasses the parameter classes
     *
     * @return the t
     */
    public static <T> T fromJson(String json, Class<T> parametrized, Class... parameterClasses) {
        try {
            JavaType javaType = OBJECT_MAPPER.getTypeFactory()
                                             .constructParametricType(parametrized, parameterClasses);
            return OBJECT_MAPPER.readValue(json, javaType);
        } catch (IOException ex) {
            throw new RuntimeException("JSON格式转换错误。");
        }

    }

    /**
     * json 转实体
     *
     * @param json 待转换json字符串
     * @param typeReference typeReference
     * @param <T> 转换后的类型
     */
    public static <T> T fromJson(String json, TypeReference<?> typeReference) {
        try {
            return (T) (typeReference.getType().equals(String.class) ? json
                    : OBJECT_MAPPER.readValue(json, typeReference));
        } catch (IOException ex) {
            throw new RuntimeException("JSON格式转换错误。");
        }
    }

    /**
     * json转换
     *
     * @param src 待转换
     * @param <T> 类型
     */
    public static <T> String toJson(T src) {
        String json = null;
        try {
            json = src instanceof String ? (String) src : OBJECT_MAPPER.writeValueAsString(src);
        } catch (IOException e) {
            throw new RuntimeException("JSON格式转换错误。");
        }
        return json;
    }

    /**
     * json转换
     *
     * @param src 待转换
     * @param <T> 类型
     */
    public static <T> String toJson(T src, JsonInclude.Include inclusion) {
        try {
            if (src instanceof String) {
                return (String) src;
            } else {
                ObjectMapper customMapper = generateMapper(inclusion);
                return customMapper.writeValueAsString(src);
            }
        } catch (IOException e) {
            throw new RuntimeException("JSON格式转换错误。");
        }
    }

    /**
     * 转换为List
     *
     * @param jsonData 待转换json字符串
     * @param beanClass class类型
     */
    public static <T> List<T> parseList(String jsonData, Class<T> beanClass) {
        try {
            CollectionType collectionType = OBJECT_MAPPER.getTypeFactory()
                                                         .constructCollectionType(ArrayList.class, beanClass);
            return OBJECT_MAPPER.readValue(jsonData, collectionType);
        } catch (Exception e) {
            log.error("json转换异常：{}", jsonData, e);
            return null;
        }
    }

    /**
     * 转换为map
     *
     * @param jsonData 待转换json字符串
     * @param keyClass key class类型
     * @param valueClass value class类型
     */
    public static <K, V> Map<K, V> parseMap(String jsonData, Class<K> keyClass, Class<V> valueClass) {
        try {
            MapType mapType = OBJECT_MAPPER.getTypeFactory().constructMapType(HashMap.class, keyClass, valueClass);
            return OBJECT_MAPPER.readValue(jsonData, mapType);
        } catch (Exception e) {
            log.error("json转换异常：{}", jsonData, e);
            return null;
        }
    }

    /**
     * 实体转换
     *
     * @param jsonData json 字符串
     * @param typeReference typeReference
     */
    public static <T> T parseObjectByType(String jsonData, TypeReference<T> typeReference) {
        try {
            return OBJECT_MAPPER.readValue(jsonData, typeReference);
        } catch (Exception e) {
            log.error("json转换异常：{}", jsonData, e);
            return null;
        }
    }

    /**
     * 获取ObjectMapper
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }


    /**
     * 生成mapper
     *
     * @param inclusion inclusion
     */
    public static ObjectMapper generateMapper(JsonInclude.Include inclusion) {

        ObjectMapper customMapper = new ObjectMapper();

        // 设置输出时包含属性的风格
        customMapper.setSerializationInclusion(inclusion);

        // 设置输入时忽略在JSON字符串中存在但Java对象实际没有的属性
        customMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 禁止使用int代表Enum的order()來反序列化Enum,非常危險
        customMapper.configure(DeserializationFeature.FAIL_ON_NUMBERS_FOR_ENUMS, true);

        // 所有日期格式都统一为以下样式
        customMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

        return customMapper;
    }

    /**
     * 返回{@link ObjectMapper ObjectMapper}对象, 用于定制性的操作
     *
     * @return {@link ObjectMapper ObjectMapper}对象
     */
    public static ObjectMapper mapper() {
        return OBJECT_MAPPER;
    }
}

