package com.cloudstar.rightcloud.common.properties;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 共同注入参数配置<br/>
 */
@Component
@Data
public class CommonProperties {

    /**
     * 批量数据插入条数
     */
    @Value("${common.mybatis-plus.batch-size:500}")
    private Integer batchSize;

    /**
     * excel最大数据量限制
     */
    @Value("${common.excel.max-data-size:10000}")
    private Long maxDataSize;

    /**
     * 全局cmp&opStar同步标识
     */
    @Value("${BSS_INTEGRATE_CMP:false}")
    private Boolean bssIntegrateCmp;

    /**
     * 跳转到运营management的uri
     */
    @Value("${INTEGRATE_MANAGEMENT_URL:}")
    private String mgtPageUri;

    /**
     * 跳转到运营console的uri
     */
    @Value("${INTEGRATE_CONSOLE_URL:}")
    private String consolePageUri;

}
