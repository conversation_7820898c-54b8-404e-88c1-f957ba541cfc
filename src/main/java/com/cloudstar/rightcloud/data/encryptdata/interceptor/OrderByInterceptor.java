package com.cloudstar.rightcloud.data.encryptdata.interceptor;

import cn.hutool.core.util.StrUtil;
import com.cloudstar.rightcloud.data.util.order.DbOrderUtil;
import com.github.pagehelper.parser.OrderByParser;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.DefaultReflectorFactory;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.reflection.SystemMetaObject;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.sql.Connection;

@Component
@Intercepts({@Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})})
public class OrderByInterceptor implements Interceptor {

    /**
     * 拦截 StatementHandler 的 prepare 方法
     */
    public Object intercept(Invocation invocation) throws Throwable {
        try {
            //1.判断是否需要排序
            String localOrder = DbOrderUtil.getOrderRule();
            if (StrUtil.isBlank(localOrder)) {
                return invocation.proceed();
            }

            //2.获取信息
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            MetaObject metaObject = MetaObject
                    .forObject(statementHandler, SystemMetaObject.DEFAULT_OBJECT_FACTORY, SystemMetaObject.DEFAULT_OBJECT_WRAPPER_FACTORY,
                               new DefaultReflectorFactory());
            MappedStatement mappedStatement = (MappedStatement) metaObject.getValue("delegate.mappedStatement");

            //3.只处理查询接口
            SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
            if (!SqlCommandType.SELECT.equals(sqlCommandType)) {
                return invocation.proceed();
            }

            //4.组装排序接口
            BoundSql boundSql = statementHandler.getBoundSql();
            String customerOrderSql = OrderByParser.converToOrderBySql(boundSql.getSql(), localOrder);

            //5.通过反射修改sql语句
            Field field = boundSql.getClass().getDeclaredField("sql");
            field.setAccessible(true);
            field.set(boundSql, customerOrderSql);
            return invocation.proceed();
        } finally {
            DbOrderUtil.clearLocalOrder();
        }
    }

}
