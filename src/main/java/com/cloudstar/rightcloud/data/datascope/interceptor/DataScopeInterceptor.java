package com.cloudstar.rightcloud.data.datascope.interceptor;


import com.alibaba.druid.DbType;
import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.statement.SQLExprTableSource;
import com.alibaba.druid.sql.ast.statement.SQLJoinTableSource;
import com.alibaba.druid.sql.ast.statement.SQLSelectQuery;
import com.alibaba.druid.sql.ast.statement.SQLSelectQueryBlock;
import com.alibaba.druid.sql.ast.statement.SQLSelectStatement;
import com.alibaba.druid.sql.ast.statement.SQLTableSource;
import com.alibaba.druid.sql.parser.SQLParserUtils;
import com.alibaba.druid.sql.parser.SQLStatementParser;
import com.cloudstar.rightcloud.common.validator.HorizontalAuthDto;
import com.cloudstar.rightcloud.common.validator.HorizontalAuthThreadContext;
import com.cloudstar.rightcloud.data.datascope.annotation.DataFilter;
import com.cloudstar.rightcloud.data.util.BasicInfoUtil;
import com.cloudstar.rightcloud.data.util.condition.DbConditionUtil;
import com.cloudstar.rightcloud.data.util.page.ConditionData;
import com.cloudstar.rightcloud.data.util.wrapper.SpecWrapperUtil;
import com.github.pagehelper.util.MetaObjectUtil;
import com.google.common.base.Strings;

import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;

import java.lang.reflect.Method;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 数据过滤 将拦截Executor.query()方法实现数据过滤.
 */
@Intercepts({@Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class,
        RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class,
                RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class})})
public class DataScopeInterceptor extends AbstractInterceptor {

    private static final long serialVersionUID = 2466462106327347435L;

    private static final String SEPARATION_MARK = ";";

    private static final int MAPPED_STATEMENT_INDEX = 0;

    private static final int PARAMETER_INDEX = 1;

    private String dbType;

    private static final Pattern LIKE_PARAM_PATTERN = Pattern.compile("like\\s+['\"%_]*\\?", Pattern.CASE_INSENSITIVE);
    private static final Pattern LIKE_CONCAT_PARAM_PATTERN = Pattern
            .compile("like\\s+concat\\s*\\(\\s*'%'\\s*,\\s*\\?,\\s*'%'\\s*\\)",
                     Pattern.CASE_INSENSITIVE);
    private static final List<String> SYMBOLS = List.of("\\", "%", "_");
    private static final String ESCAPE_SYMBOL = "\\";

    @Override
    public Object intercept(Invocation invocation) throws Throwable {

        final Object[] queryArgs = invocation.getArgs();
        final MappedStatement mappedStatement = (MappedStatement) queryArgs[MAPPED_STATEMENT_INDEX];
        final Object parameter = queryArgs[PARAMETER_INDEX];
        BoundSql boundSql = mappedStatement.getBoundSql(parameter);

        //1.获取dataFilter
        HorizontalAuthDto dataFilter = getDataFilter(mappedStatement);

        // 跳过权限过滤
        boolean ignoreDataFilter = false;
        boolean selfService = false;
        if (dataFilter != null && !ignoreDataFilter) {
            StringBuilder bufferSql = new StringBuilder(boundSql.getSql().trim());
            if (bufferSql.lastIndexOf(SEPARATION_MARK) == bufferSql.length() - 1) {
                bufferSql.deleteCharAt(bufferSql.length() - 1);
            }
            String originalSql = bufferSql.toString();
            escapeParameterIfContainingLike(boundSql);
            String tableAlias = dataFilter.getTableAlias();
            if (Strings.isNullOrEmpty(dataFilter.getTableAlias())) {
                SQLStatementParser parser = SQLParserUtils.createSQLStatementParser(originalSql, dbType);
                SQLStatement statement = parser.parseStatement();
                SQLSelectStatement selectStatement = (SQLSelectStatement) statement;
                SQLSelectQuery selectQuery = selectStatement.getSelect().getQuery();

                if (selectQuery instanceof SQLSelectQueryBlock) {
                    SQLSelectQueryBlock query = selectStatement.getSelect().getQueryBlock();
                    if (query.getFrom().getAlias() != null) {
                        tableAlias = query.getFrom().getAlias();
                    } else {
                        tableAlias = findTableAlias(query.getFrom());
                    }
                }
            }
            String sqlFilter = "";
            sqlFilter = BasicInfoUtil.getSqlFilter(tableAlias,
                                                   dataFilter.getOrgId(),
                                                   dataFilter.getProjectId(),
                                                   dataFilter.getOwnerAccount(),
                                                   dataFilter.isIgnoreProjectFilter(),
                                                   dataFilter.isContainParentOrg(),
                                                   dataFilter.isBigDatafilter(),
                                                   dataFilter.getOrOrgId(),
                                                   dataFilter.getOrProjectId(),
                                                   dataFilter.getOrOwnerAccount());

            //判断参数类型，如果是Crit````````````````````````````        eria，并且otherOrgSid不为空，则也otherOrgSid做数据过滤
            //...

            if (!Strings.isNullOrEmpty(sqlFilter)) {
                // 将权限语句加入sql
                //String condition = StrUtil.subAfter(sqlFilter, " AND", false);
                String condition = sqlFilter;
                String newSql = SQLUtils.addCondition(originalSql, condition, DbType.mysql);

                MappedStatement newMs = copyFromNewSql(mappedStatement, boundSql, newSql);
                queryArgs[MAPPED_STATEMENT_INDEX] = newMs;
            }

        }

        try {
            ConditionData conditionData = DbConditionUtil.getLocalCondition();
            if (ObjectUtil.isNotNull(conditionData)) {
                MappedStatement newMappedStatement = (MappedStatement) queryArgs[MAPPED_STATEMENT_INDEX];
                Object newParameter = queryArgs[PARAMETER_INDEX];
                BoundSql newBoundSql = newMappedStatement.getBoundSql(newParameter);
                StringBuilder bufferSql = new StringBuilder(newBoundSql.getSql().trim());
                if (bufferSql.lastIndexOf(SEPARATION_MARK) == bufferSql.length() - 1) {
                    bufferSql.deleteCharAt(bufferSql.length() - 1);
                }
                String tableAlias = "conditionData";
                String originalSql = "SELECT * FROM ( " + bufferSql + " ) as " + tableAlias;
                tableAlias += ".";
                if (StrUtil.isNotBlank(conditionData.getFieldIn()) && ObjectUtil.isNotEmpty(conditionData.getFieldInList())) {
                    String fieldInStr = joinStr(conditionData.getFieldInList());
                    String inStr = "( " + tableAlias + SpecWrapperUtil.camelToUnderline(conditionData.getFieldIn()) + " IN ( " + fieldInStr + "))";
                    originalSql = SQLUtils.addCondition(originalSql, inStr, DbType.mysql);
                }

                if (StrUtil.isNotBlank(conditionData.getFieldNotIn()) && ObjectUtil.isNotEmpty(conditionData.getFieldNotInList())) {
                    String fieldNotInStr = joinStr(conditionData.getFieldNotInList());
                    String notInStr =
                            "( " + tableAlias + SpecWrapperUtil.camelToUnderline(conditionData.getFieldNotIn()) + " NOT IN ( " + fieldNotInStr + "))";
                    originalSql = SQLUtils.addCondition(originalSql, notInStr, DbType.mysql);
                }
                MappedStatement newMs = copyFromNewSql(newMappedStatement, newBoundSql, originalSql);
                queryArgs[MAPPED_STATEMENT_INDEX] = newMs;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            DbConditionUtil.removeLocalCondition();
        }
        return invocation.proceed();
    }


    private HorizontalAuthDto getDataFilter(MappedStatement mappedStatement) throws ClassNotFoundException {
        //1.通过请求的mapper 获取dataFilter注解
        DataFilter dataFilter = getDataFilterByMapper(mappedStatement);
        if (ObjectUtil.isNotNull(dataFilter)) {
            return coverDataAuthDto(dataFilter);
        }
        //2.通过调用链获取
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        for (StackTraceElement stackTraceElement : stackTrace) {
            String className = stackTraceElement.getClassName();
            String methodName = stackTraceElement.getMethodName();

            //如果类路径中包含 佳节云星的统一报名 并且是在Dao层
            if (className.contains("cloudstar") && className.contains("Dao")) {
                Class<?> aClass = Class.forName(className);
                Method[] methods = aClass.getMethods();
                for (Method method : methods) {
                    if ((method.getName().equals(methodName) || methodName.equals("count"))
                            && method.isAnnotationPresent(DataFilter.class)) {
                        DataFilter dataFilterAnnotation = method.getAnnotation(DataFilter.class);
                        return coverDataAuthDto(dataFilterAnnotation);
                    }
                }
            }
        }

        //3.从上下文中获取
        return HorizontalAuthThreadContext.getLocalContext();
    }

    private DataFilter getDataFilterByMapper(MappedStatement mappedStatement) throws ClassNotFoundException {
        String id = mappedStatement.getId();
        String className = id.substring(0, id.lastIndexOf("."));
        String methodName = id.substring(id.lastIndexOf(".") + 1);
        final Class cls = Class.forName(className);
        final Method[] method = cls.getMethods();
        DataFilter dataFilter = null;
        for (Method me : method) {
            if ((me.getName().equals(methodName) || methodName.equals("count"))
                    && me.isAnnotationPresent(DataFilter.class)) {
                dataFilter = me.getAnnotation(DataFilter.class);
                break;
            }
        }
        return dataFilter;
    }

    private String findTableAlias(SQLTableSource from) {
        if (from instanceof SQLExprTableSource) {
            SQLExprTableSource tableSource = (SQLExprTableSource) from;
            return Strings.isNullOrEmpty(tableSource.getAlias())
                    ? tableSource.getExpr().toString()
                    : tableSource.getAlias();
        } else if (from instanceof SQLJoinTableSource) {
            return findTableAlias(((SQLJoinTableSource) from).getLeft());
        }
        return null;
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    private MappedStatement copyFromNewSql(MappedStatement mappedStatement, BoundSql boundSql, String sql) {
        BoundSql newBoundSql = new BoundSql(mappedStatement.getConfiguration(), sql, boundSql.getParameterMappings(),
                                            boundSql.getParameterObject());
        for (ParameterMapping mapping : boundSql.getParameterMappings()) {
            String prop = mapping.getProperty();
            if (boundSql.hasAdditionalParameter(prop)) {
                newBoundSql.setAdditionalParameter(prop, boundSql.getAdditionalParameter(prop));
            }
        }
        return copyFromMappedStatement(mappedStatement, new BoundSqlSqlSource(newBoundSql));
    }


    private String joinStr(List<String> list) {
        StringBuilder builder = new StringBuilder();
        for (String str : list) {
            builder.append("'").append(str).append("',");
        }
        if (StrUtil.isNotBlank(builder)) {
            return builder.substring(0, builder.length() - 1);
        }
        return builder.toString();
    }

    @Override
    public void setProperties(Properties properties) {
        dbType = properties.getProperty("dbType", "mysql");
    }

    void escapeParameterIfContainingLike(BoundSql boundSql) {
        if (boundSql == null) {
            return;
        }
        String prepareSql = boundSql.getSql();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        Map<Integer, Boolean> position = findLikeParam(prepareSql);
        if (position == null || position.isEmpty()) {
            return;
        }
        Map<ParameterMapping, Boolean> likeParameterMappings = new LinkedHashMap<>();
        MetaObject metaObject = MetaObjectUtil.forObject(boundSql.getParameterObject());
        for (int i = 0; i < parameterMappings.size(); i++) {
            ParameterMapping pm = parameterMappings.get(i);
            String property = pm.getProperty();
            if (metaObject.hasGetter(property)) {
                boundSql.setAdditionalParameter(property, metaObject.getValue(property));
                if (position.containsKey(i)) {
                    likeParameterMappings.put(pm, position.get(i));
                }
            }
        }
        delegateMetaParameterForEscape(boundSql, likeParameterMappings);
    }

    void delegateMetaParameterForEscape(BoundSql boundSql, Map<ParameterMapping, Boolean> likeParameterMappings) {
        for (ParameterMapping mapping : likeParameterMappings.keySet()) {
            String property = mapping.getProperty();
            MetaObject metaObject = MetaObjectUtil.forObject(boundSql.getParameterObject());
            Object value = metaObject.getValue(property);
            if (value instanceof String) {
                boundSql.setAdditionalParameter(property, escapeLike((String) value, likeParameterMappings.get(mapping)));
            }
        }
    }

    String escapeLike(String value, Boolean hasConcat) {
        if (StrUtil.isBlank(value)) {
            return null;
        }
        if (!hasConcat) {
            return value;
        }
        for (String symbol : SYMBOLS) {
            value = value.replace(symbol, ESCAPE_SYMBOL + symbol);
        }
        return value;
    }

    Map<Integer, Boolean> findLikeParam(String prepareSql) {
        Matcher matcher = LIKE_PARAM_PATTERN.matcher(prepareSql);
        Map<Integer, Boolean> indexes = new LinkedHashMap<>();
        while (matcher.find()) {
            int start = matcher.start();
            int index = StrUtil.count(prepareSql.substring(0, start), "?");
            indexes.put(index, false);
        }
        matcher = LIKE_CONCAT_PARAM_PATTERN.matcher(prepareSql);
        while (matcher.find()) {
            int start = matcher.start();
            int index = StrUtil.count(prepareSql.substring(0, start), "?");
            indexes.put(index, true);
        }
        return indexes;
    }


    /**
     * 转换实体
     *
     * @param dataFilter dataFilter注解
     */
    private HorizontalAuthDto coverDataAuthDto(DataFilter dataFilter) {
        if (ObjectUtil.isNotNull(dataFilter)) {
            HorizontalAuthDto horizontalAuthDto = new HorizontalAuthDto();
            horizontalAuthDto.setTableAlias(dataFilter.tableAlias());
            horizontalAuthDto.setOrgId(dataFilter.orgId());
            horizontalAuthDto.setOwnerAccount(dataFilter.ownerAccount());
            horizontalAuthDto.setProjectId(dataFilter.projectId());
            horizontalAuthDto.setIgnoreProjectFilter(dataFilter.ignoreProjectFilter());
            horizontalAuthDto.setContainParentOrg(dataFilter.containParentOrg());
            horizontalAuthDto.setBigDatafilter(dataFilter.bigDatafilter());
            horizontalAuthDto.setCloudEnvId(dataFilter.cloudEnvId());
            horizontalAuthDto.setUserIdAsValue(dataFilter.userIdAsValue());
            horizontalAuthDto.setIgnoreUserScope(dataFilter.ignoreUserScope());
            horizontalAuthDto.setOrOrgId(dataFilter.orOrgId());
            horizontalAuthDto.setOrProjectId(dataFilter.orProjectId());
            horizontalAuthDto.setOrOwnerAccount(dataFilter.orOwnerAccount());
            return horizontalAuthDto;
        }
        return null;
    }
}
