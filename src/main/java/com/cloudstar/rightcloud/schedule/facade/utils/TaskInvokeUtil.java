package com.cloudstar.rightcloud.schedule.facade.utils;


import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;

/**
 * 任务执行工具
 *
 * <AUTHOR> Lesao
 */
public class TaskInvokeUtil {

    /**
     * 执行方法
     *
     * @param bean bean类
     * @param methodName 方法
     * @param methodParam 方法参数
     */
    public static Object invokeMethod(Object bean, String methodName, List<String> methodParam) throws Exception {
        if (Objects.nonNull(methodParam) && methodParam.size() > 0) {
            String[] param = methodParam.toArray(new String[] {});
            Method method = bean.getClass().getMethod(methodName, getMethodParamsType(param));
            return method.invoke(bean, getMethodParamsValue(param));
        } else {
            Method method = bean.getClass().getMethod(methodName);
            return method.invoke(bean);
        }
    }


    /**
     * 获取参数类型 (暂时只考虑字符串形式 所以这里写死的String类型)
     *
     * @param methodParams 参数相关列表
     *
     * @return 参数类型列表
     */
    public static Class<?>[] getMethodParamsType(String[] methodParams) {
        Class<?>[] classes = new Class<?>[methodParams.length];
        for (int i = 0; i < methodParams.length; i++) {
            classes[i] = String.class;
        }
        return classes;
    }


    /**
     * 获取参数值
     *
     * @param methodParams 参数相关列表
     *
     * @return 参数值列表
     */
    public static Object[] getMethodParamsValue(String[] methodParams) {
        Object[] classes = new Object[methodParams.length];
        System.arraycopy(methodParams, 0, classes, 0, methodParams.length);
        return classes;
    }

}
