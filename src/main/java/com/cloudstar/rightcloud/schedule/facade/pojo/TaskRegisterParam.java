package com.cloudstar.rightcloud.schedule.facade.pojo;

import lombok.Data;

import java.io.Serializable;

/**
 * 任务注册参数
 *
 * <AUTHOR> Lesao
 */
@Data
public class TaskRegisterParam implements Serializable {

    private static final long serialVersionUID = 1616103987549730299L;

    /**
     * 服务名称
     */
    private String appName;

    /**
     * bean名称
     */
    private String beanName;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 方法参数数量
     */
    private Integer methodParamCount;

    /**
     * 任务调用地址
     */
    private String address;

    /**
     * 服务接口地址前缀
     */
    private String contextPath;


}
