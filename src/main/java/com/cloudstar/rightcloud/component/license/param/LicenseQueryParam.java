package com.cloudstar.rightcloud.component.license.param;

import lombok.Data;

/**
 * 许可信息查询类
 *
 * @author: zhuli
 * @date: 2022/12/19 18:58
 */
@Data
public class LicenseQueryParam {
    /**
     * 许可截止日期
     */
    private String expireDate;
    /**
     * 当前宿主机总数
     */
    private String physicalCount = "0";
    /**
     * 当前私有云实例数量
     */
    private String privateInstanceCount = "0";
    /**
     * 当前公有云实例总数
     */
    private String instanceCount = "0";
    /**
     * 版本
     */
    private Long version;
    /**
     * 支持
     */
    private String support;
    /**
     * 版本信息
     */
    private String versionInfo;
    /**
     * 授权模块
     */
    private String modules;
    /**
     * 排除授权模块，多个以,分割
     **/
    private String excludeModules;
    /**
     * 产品特征码
     */
    private String productSN;
    /**
     * 版权信息
     */
    private String copyright;
    /**
     * 支持云环境类型（使用简码），多个以逗号分隔。
     **/
    private String envType;
    /**
     * 授予对象
     **/
    private String licenseFor;
    /**
     * 支持PaaS服务
     */
    private String serviceSupport;
    /**
     * 支持PaaS服务总数
     */
    private String serviceSupportNum;
    /**
     * 是否限制可获取宿主机的私有云环境的PaaS服务，1:不限制，其他:限制
     */
    private String privatePaaSLimit;
}
