package com.cloudstar.rightcloud.component.license.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 菜单表(SysAuthModule)实体类
 *
 * <AUTHOR>
 * @since 2023-04-10 16:47:38
 */
@Data
public class LicenseModule implements Serializable {
    private static final long serialVersionUID = -15301062056754550L;
    /**
     * 权限标识
     */
    private String authKey;
    /**
     * 菜单名称
     */
    private String name;
    /**
     * 菜单名称(英文)
     */
    private String nameEn;
    /**
     * 父级id
     */
    private String parentAuthKey;
    /**
     * 父级访问路径
     */
    private String parentRequestUrl;
    /**
     * 图标
     */
    private String icon;
    /**
     * 排序
     */
    private Integer sortRank;
    /**
     * 状态
     */
    private String status;
    /**
     * 显示状态
     */
    private String displayStatus;
    private List<LicenseModule> licenseModuleChildren;
}

