/*
 * Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved..
 */

package com.cloudstar.rightcloud.component.license.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpStatus;
import com.cloudstar.rightcloud.common.exception.BizException;
import com.google.common.base.Strings;
import io.fabric8.kubernetes.api.model.Node;
import io.fabric8.kubernetes.client.Config;
import io.fabric8.kubernetes.client.ConfigBuilder;
import io.fabric8.kubernetes.client.DefaultKubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClientException;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * KubernetesUtil
 *
 * <AUTHOR>
 * @date 2016/11/10
 */
@Slf4j
public class KubernetesUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(LicenseUtil.class);
    private KubernetesClient client;

    public void setClient(KubernetesClient client) {
        this.client = client;
    }

    public static KubernetesUtil getInstance() {
        return new KubernetesUtil();
    }

    /**
     * set default kubernetes client
     *
     * @return KubernetesUtil
     */
    public KubernetesUtil withDefaultKubernetesClient(String authType, String token, String... masterUrl) {
        boolean connectedFlag = validateClusterConfigIsConnected(authType, token, masterUrl);
        if (!connectedFlag) {
            throw new BizException("集群无法连接");
        }
        return this;
    }

    /**
     * validate cluster fflug
     */
    public boolean validateClusterConfigIsConnected(String authType, String token, String... masterUrl) {
        boolean connectedFlag = false;
        for (String url : masterUrl) {
            this.client = new DefaultKubernetesClient(prepareConfig(authType, token, url));
            boolean isConnected = this.k8sClusterIsConnected();
            if (isConnected) {
                connectedFlag = true;
                break;
            }
        }
        return connectedFlag;
    }

    private Config prepareConfig(String authType, String token, String url) {
        Config config;
        if (StrUtil.isEmpty(authType)) {
            authType = KubernetesAuthType.TOKEN;
        }
        if (StrUtil.equalsIgnoreCase(KubernetesAuthType.TOKEN, authType)) {
            config = new ConfigBuilder().withMasterUrl(url).withTrustCerts(true).withOauthToken(token).build();
        } else if (StrUtil.equalsIgnoreCase(KubernetesAuthType.BASIC, authType)) {
            String[] usernamePassword = token.split(":");
            config = new ConfigBuilder().withMasterUrl(url)
                    .withTrustCerts(true)
                    .withUsername(usernamePassword[0])
                    .withPassword(usernamePassword[1])
                    .build();
        } else {
            config = new ConfigBuilder().withMasterUrl(url).withTrustCerts(false).build();
        }
        return config;
    }

    private String getFieldListFromJsonStr(String jsonStr, String fieldName) {
        String regex = "(?<=(\"" + fieldName + "\":\")).*?(?=(\"))";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(jsonStr);
        while (matcher.find()) {
            if (!Strings.isNullOrEmpty(matcher.group().trim())) {
                return matcher.group().trim();
            }
        }
        String regex1 = "(?<=(\"" + fieldName + "\":)).*?(?=(,))";
        Pattern pattern1 = Pattern.compile(regex1);
        Matcher matcher1 = pattern1.matcher(jsonStr);
        while (matcher1.find()) {
            if (!Strings.isNullOrEmpty(matcher1.group().trim())) {
                return matcher1.group().trim();
            }
        }
        return null;
    }

    public Node getFirstMasterNode() {
        List<Node> nodes = this.getClient().nodes().list().getItems();
        Node firstMasterNode = null;
        if (!CollectionUtils.isEmpty(nodes)) {
            for (Node node : nodes) {
                Map<String, String> metas = node.getMetadata().getLabels();
                if (CollectionUtil.isNotEmpty(metas)) {
                    boolean isMaster = false;
                    for (Entry<String, String> entry : metas.entrySet()) {
                        if ((Objects.nonNull(entry.getKey()) && Objects.nonNull(entry.getValue())) && (
                                entry.getKey().contains("master") || entry.getValue().contains("master"))) {
                            isMaster = true;
                            break;
                        }
                    }
                    if (isMaster) {
                        firstMasterNode = node;
                        break;
                    }
                }
            }
            if (Objects.isNull(firstMasterNode)) {
                firstMasterNode = nodes.get(0);
            }
        }
        return firstMasterNode;
    }

    public String getSystemAndMachineId() {
        Node node = getFirstMasterNode();
        if (Objects.nonNull(node)) {
            return node.getStatus().getNodeInfo().getSystemUUID() + node.getStatus().getNodeInfo().getMachineID();
        }
        return null;
    }

    public KubernetesClient getClient() {
        return client;
    }

    /**
     * k8s cluster is connected
     *
     * @return boolean
     */
    public boolean k8sClusterIsConnected() {
        boolean isConnected = false;
        try {
            String apiVersion = this.getClient().getApiVersion();
            this.getClient().componentstatuses().withName("scheduler").get();
            if (LOGGER.isDebugEnabled()) {
                LOGGER.debug(
                        "K8sClusterObserver------------K8SClusterIsConnected=true apiVersion={} apiUrl={} token={}",
                        apiVersion, this.getClient().getConfiguration().getMasterUrl(),
                        this.getClient().getConfiguration().getOauthToken());
            }
            isConnected = true;
        } catch (KubernetesClientException kce) {
            LOGGER.warn(
                    "K8sClusterObserver, K8SClusterIsConnected=false errorMessage=[{}] failed apiUrl=[{}] token=[{}]",
                    kce.getMessage(), this.getClient().getConfiguration().getMasterUrl(),
                    this.getClient().getConfiguration().getOauthToken());

            if (kce.getCode() == HttpStatus.HTTP_UNAUTHORIZED) {
                throw new BizException("集群无法连接");
            }
        }
        return isConnected;
    }


}

