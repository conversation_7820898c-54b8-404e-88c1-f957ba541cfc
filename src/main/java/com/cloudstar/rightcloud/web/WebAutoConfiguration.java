package com.cloudstar.rightcloud.web;

import com.cloudstar.rightcloud.common.utils.json.jackson.MaskSerializer;
import com.cloudstar.rightcloud.web.interceptor.AuthRoleInterceptor;
import com.cloudstar.rightcloud.web.interceptor.AuthUserInterceptor;
import com.cloudstar.rightcloud.web.serializer.LongSerializer;
import com.cloudstar.rightcloud.web.service.DefaultExtendedServiceImpl;
import com.cloudstar.rightcloud.web.service.ExtendedService;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.ContextualSerializer;

import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.AutoConfigureBefore;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.text.SimpleDateFormat;

@Configuration
@AutoConfigureBefore({WebMvcAutoConfiguration.class})
@ComponentScan(basePackageClasses = WebAutoConfiguration.class)
public class WebAutoConfiguration implements WebMvcConfigurer {

    private static final String DEFAULT_DATE_TIME_PATTERN = "yyyy-MM-dd HH:mm:ss";
    private static final String DEFAULT_DATE_PATTERN = "yyyy-MM-dd";
    private static final String DEFAULT_TIME_PATTERN = "HH:mm:ss";

    /**
     * 掩码json 序列化器
     */
    @Bean
    public ContextualSerializer maskContextualSerializer() {
        return new MaskSerializer();
    }


    /**
     * 认证拦截
     */
    @Bean
    public GlobalControllerAroundAop controllerAuthAop() {
        return new GlobalControllerAroundAop();
    }

    @Bean
    public AuthRoleInterceptor authRoleInterceptor() {
        return new AuthRoleInterceptor();
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 增加UserContext相关拦截器
        registry.addInterceptor(new AuthUserInterceptor())
                .addPathPatterns("/**").order(0);

        //registry.addInterceptor(authRoleInterceptor()).addPathPatterns("/**").order(1);
    }

    /**
     * 默认 ExtendedService
     */
    @Bean
    @ConditionalOnMissingBean(ExtendedService.class)
    public ExtendedService extendedService() {
        return new DefaultExtendedServiceImpl();
    }

    @NotNull
    @Bean
    public LocaleResolver localeResolver() {
        return new DataVizLocalResolver();
    }

    @Bean
    public MappingJackson2HttpMessageConverter jackson2HttpMessageConverter() {
        final MappingJackson2HttpMessageConverter mappingJackson2HttpMessageConverter
                = new MappingJackson2HttpMessageConverter();
        ObjectMapper customMapper = new ObjectMapper();
        // 设置输出时包含属性的风格
        customMapper.setSerializationInclusion(JsonInclude.Include.ALWAYS);
        // 设置输入时忽略在JSON字符串中存在但Java对象实际没有的属性
        customMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 禁止使用int代表Enum的order()來反序列化Enum,非常危險
        customMapper.configure(DeserializationFeature.FAIL_ON_NUMBERS_FOR_ENUMS, true);
        // 所有日期格式都统一为以下样式
        customMapper.setDateFormat(new SimpleDateFormat(DEFAULT_DATE_TIME_PATTERN));
        // 解决id超长问题这里将id序列化为String
        SimpleModule module = new SimpleModule();
        module.addSerializer(new LongSerializer(Long.class));
        customMapper.registerModule(module);
        mappingJackson2HttpMessageConverter.setObjectMapper(customMapper);
        return mappingJackson2HttpMessageConverter;
    }


}
