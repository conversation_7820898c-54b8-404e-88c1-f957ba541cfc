package com.cloudstar.rightcloud.web.interceptor;

import com.cloudstar.rightcloud.common.constant.base.HeaderConstant;
import com.cloudstar.rightcloud.common.pojo.user.AuthUser;
import com.cloudstar.rightcloud.common.utils.auth.AuthUserHolderUtil;
import com.cloudstar.rightcloud.common.utils.base.BooleanUtil;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 授权用户信息拦截处理
 *
 * @author: zhangqiang
 * @date: 2022/10/17 15:45
 */
@Slf4j
public class AuthUserInterceptor implements HandlerInterceptor {

    /**
     * null 字符串
     */
    private static final String EMPTY_STR = "null";

    /**
     * 授权用户信息拦截处理逻辑
     *
     * @param request current HTTP request
     * @param response current HTTP response
     * @param handler chosen handler to execute, for type and/or instance evaluation
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String userInfo = request.getHeader(HeaderConstant.HEADER_AUTH_USER);
        if (StrUtil.isNotBlank(userInfo)) {
            String userJsonStr = URLDecoder.decode(JSONUtil.toJsonStr(userInfo), StandardCharsets.UTF_8);
            AuthUser authUser = JSONUtil.toBean(userJsonStr, AuthUser.class);
            AuthUserHolderUtil.setAuthUser(authUser);
        }
        return true;
    }

    /**
     * 设置请求是否来源于 系统管理页面
     *
     * @param request 请求request
     * @param authUser 当前用户
     */
    private void setSystemManagementFlag(HttpServletRequest request, AuthUser authUser) {
        String systemManagementFlag = request.getHeader(HeaderConstant.HEADER_SYS_FLAG);
        if (StringUtils.isNotBlank(systemManagementFlag) && !EMPTY_STR.equalsIgnoreCase(systemManagementFlag)) {
            authUser.setSystemManagementFlag(BooleanUtil.toBoolean(systemManagementFlag));
        }
    }

    /**
     * 请求结束清除用户信息
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        AuthUserHolderUtil.clear();
    }
}
