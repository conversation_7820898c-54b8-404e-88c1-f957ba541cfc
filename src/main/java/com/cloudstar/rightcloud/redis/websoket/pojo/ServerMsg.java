package com.cloudstar.rightcloud.redis.websoket.pojo;

import com.cloudstar.rightcloud.redis.websoket.support.ServerMsgType;

import cn.hutool.core.util.IdUtil;
import lombok.Data;


/**
 * The type ServerMsg.
 *
 * @author: zhangqiang
 * @date: 2022/11/21 11:57
 */

@Data
public class ServerMsg {
    private ServerMsgType msgType;
    private String refId;
    private Object msgContent;
    private Boolean optResult;
    private String msgId;


    public ServerMsg(ServerMsgType msgType, String refId, Object msgContent, Boolean optResult) {
        this.msgType = msgType;
        this.refId = refId;
        this.msgContent = msgContent;
        this.optResult = optResult;
        this.msgId = IdUtil.fastSimpleUUID();
    }

    /**
     * Instantiates a new Server msg.
     *
     * @param msgType the msg type
     * @param refId the ref id
     * @param msgContent the msg content
     */
    public ServerMsg(ServerMsgType msgType, String refId, Object msgContent) {
        this.msgType = msgType;
        this.refId = refId;
        this.msgContent = msgContent;
        this.optResult = true;
        this.msgId = IdUtil.fastSimpleUUID();
    }

    /**
     * Create server msg.
     *
     * @param msgType the msg type
     * @param refId the ref id
     * @param msgContent the msg content
     *
     * @return the server msg
     */
    public static ServerMsg create(ServerMsgType msgType, String refId, Object msgContent) {
        return new ServerMsg(msgType, refId, msgContent);
    }

    /**
     * Create server msg.
     *
     * @param msgType the msg type
     * @param refId the ref id
     * @param msgContent the msg content
     *
     * @return the server msg
     */
    public static ServerMsg create(ServerMsgType msgType, String refId, Object msgContent, Boolean optResult) {
        return new ServerMsg(msgType, refId, msgContent, optResult);
    }



}
