package com.cloudstar.rightcloud.redis.websoket.annonation;

import com.cloudstar.rightcloud.redis.websoket.support.OperateEnum;
import com.cloudstar.rightcloud.redis.websoket.support.ResultEnum;
import com.cloudstar.rightcloud.redis.websoket.support.ServerMsgType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Inherited;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * The type ServerMessage.
 *
 * @author: zhangqiang
 * @date: 2022/11/21 11:56
 */

@Target(ElementType.METHOD)
@Inherited
@Retention(RetentionPolicy.RUNTIME)
public @interface Message {

    /**
     * refKey
     */
    String refKey() default "";

    /**
     * refNameKey
     */
    String refNameKey() default "";

    /**
     * envId
     */
    String envId() default "";

    /**
     * opUser
     */
    String opUser() default "";

    /**
     * errorMsg
     */
    String errorMsg() default "";

    /**
     * msgType
     */
    ServerMsgType msgType();

    /**
     * operate
     */
    OperateEnum operate();

    /**
     * operateAction
     */
    String operateAction() default "";

    /**
     * success
     */
    String success() default "";

    /**
     * successFrom
     */
    ResultEnum successFrom() default ResultEnum.PARAM_EL;
}
