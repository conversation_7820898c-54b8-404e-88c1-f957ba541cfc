package com.cloudstar.rightcloud.prom.util;

import cn.hutool.core.lang.Assert;
import com.cloudstar.rightcloud.common.exception.BizException;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.io.Resources;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.IdentityHashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;

/**
 * 反射 util
 *
 * @author: wanglang
 * @date: 2023/2/1 7:20 PM
 */
@Slf4j
public class ReflectUtils {

    private static final Map<Class<?>, List<Field>> CLASS_FIELD_CACHE = new ConcurrentHashMap<>();


    private static final Map<Class<?>, Class<?>> PRIMITIVE_WRAPPER_TYPE_MAP = new IdentityHashMap<>(8);

    static {
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Boolean.class, boolean.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Byte.class, byte.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Character.class, char.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Double.class, double.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Float.class, float.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Integer.class, int.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Long.class, long.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(Short.class, short.class);
        PRIMITIVE_WRAPPER_TYPE_MAP.put(String.class, String.class);
    }

    private static final List<String> PROXY_CLASS_NAMES = Arrays.asList("net.sf.cglib.proxy.Factory",
            "org.springframework.cglib.proxy.Factory",
            "javassist.util.proxy.ProxyObject", "org.apache.ibatis.javassist.util.proxy.ProxyObject");


    /**
     * 类中获取使用了某个注解的字段
     */
    public static List<String> getFieldLit(Object object, Class annotation) {
        List<String> fieldName = new ArrayList<>();
        Class clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Boolean isAnon = field.isAnnotationPresent(annotation);
            if (isAnon) {
                fieldName.add(field.getName());
            }
        }
        return fieldName;
    }


    /**
     * 类中获取字段
     */
    public static Field getField(Object object, String name) {
        Class clazz = object.getClass();
        Field field = null;
        try {
            field = clazz.getDeclaredField(name);
            field.setAccessible(true);
        } catch (Exception e) {
            log.error("get field error field name is:{},error message:{} ", name, e.getMessage());
        }
        return field;
    }


    /**
     * 类中获取使用了某个注解的字段
     */
    public static String getField(Object object, Class annotation) {
        Class clazz = object.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            Boolean isAnon = field.isAnnotationPresent(annotation);
            if (isAnon) {
                return field.getName();
            }
        }
        return null;
    }

    /**
     * 判断是否为代理对象
     *
     * @param clazz 传入 class 对象
     * @return 如果对象class是代理 class，返回 true
     */
    public static boolean isProxy(Class<?> clazz) {
        if (clazz != null) {
            for (Class<?> cls : clazz.getInterfaces()) {
                if (PROXY_CLASS_NAMES.contains(cls.getName())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * <p>
     * 获取当前对象的 class
     * </p>
     *
     * @param clazz 传入
     * @return 如果是代理的class，返回父 class，否则返回自身
     */
    public static Class<?> getUserClass(Class<?> clazz) {
        Assert.notNull(clazz, "Class must not be null");
        return isProxy(clazz) ? clazz.getSuperclass() : clazz;
    }


    /**
     * 判断是否为基本类型或基本包装类型
     *
     * @param clazz class
     * @return 是否基本类型或基本包装类型
     */
    public static boolean isPrimitiveOrWrapper(Class<?> clazz) {
        Assert.notNull(clazz, "Class must not be null");
        return (clazz.isPrimitive() || PRIMITIVE_WRAPPER_TYPE_MAP.containsKey(clazz));
    }

    /**
     * 判断是否为String
     *
     * @param clazz class
     * @return 是否基本类型或基本包装类型
     */
    public static boolean isString(Class<?> clazz) {
        Assert.notNull(clazz, "Class must not be null");
        return StringUtils.equals("String", clazz.getSimpleName());
    }

    /**
     * 判断是否为Date
     *
     * @param clazz class
     * @return 是否基本类型或基本包装类型
     */
    public static boolean isDate(Class<?> clazz) {
        Assert.notNull(clazz, "Class must not be null");
        Boolean result = StringUtils.equals("Date", clazz.getSimpleName()) || StringUtils.equals("Long", clazz.getSimpleName());
        return result;
    }

    /**
     * 格式化时间
     */
    public static Date parseData(Class<?> clazz, Object value) {
        Date date = null;
        if (StringUtils.equals("Date", clazz.getSimpleName())) {
            date = (Date) value;
        }
        if (StringUtils.equals("Long", clazz.getSimpleName())) {
            date = new Date((Long) value);
        }
        return date;
    }

    /**
     * 判断是否为Date
     *
     * @param source class
     * @return 是否基本类型或基本包装类型
     */
    public static boolean isClazz(Class<?> source, Class<?> target) {
        Assert.notNull(source, "Class must not be null");
        Assert.notNull(target, "Class must not be null");
        return source.equals(target);
    }


    /**
     * 格式化string
     */
    public static String parseString(Object object) {
        if (isString(object.getClass())) {
            return (String) object;
        } else {
            throw new BizException("Cannot find the specified class! Please only call "
                    + "this method when it is explicitly confirmed that there will be a class");
        }
    }


    /**
     * <p>
     * 获取该类的所有属性列表
     * </p>
     *
     * @param clazz 反射类
     */
    public static List<Field> getFieldList(Class<?> clazz) {
        if (Objects.isNull(clazz)) {
            return Collections.emptyList();
        }
        return CollectionUtils.computeIfAbsent(CLASS_FIELD_CACHE, clazz, k -> {
            Field[] fields = k.getDeclaredFields();
            List<Field> superFields = new ArrayList<>();
            Class<?> currentClass = k.getSuperclass();
            while (currentClass != null) {
                Field[] declaredFields = currentClass.getDeclaredFields();
                Collections.addAll(superFields, declaredFields);
                currentClass = currentClass.getSuperclass();
            }
            /* 排除重载属性 */
            Map<String, Field> fieldMap = excludeOverrideSuperField(fields, superFields);
            /*
             * 重写父类属性过滤后处理忽略部分，支持过滤父类属性功能
             * 场景：中间表不需要记录创建时间，忽略父类 createTime 公共属性
             * 中间表实体重写父类属性 ` private transient Date createTime; `
             */
            return fieldMap.values().stream()
                    /* 过滤静态属性 */
                    .filter(f -> !Modifier.isStatic(f.getModifiers()))
                    /* 过滤 transient关键字修饰的属性 */
                    .filter(f -> !Modifier.isTransient(f.getModifiers()))
                    .collect(Collectors.toList());
        });
    }

    /**
     * <p>
     * 排序重置父类属性
     * </p>
     *
     * @param fields         子类属性
     * @param superFieldList 父类属性
     */
    public static Map<String, Field> excludeOverrideSuperField(Field[] fields, List<Field> superFieldList) {
        // 子类属性
        Map<String, Field> fieldMap = Stream.of(fields).collect(toMap(Field::getName, identity(),
                (u, v) -> {
                    throw new IllegalStateException(String.format("Duplicate key %s", u));
                },
                LinkedHashMap::new));
        superFieldList.stream().filter(field -> !fieldMap.containsKey(field.getName()))
                .forEach(f -> fieldMap.put(f.getName(), f));
        return fieldMap;
    }


    /**
     * <p>
     * 请仅在确定类存在的情况下调用该方法
     * </p>
     *
     * @param name 类名称
     * @return 返回转换后的 Class
     */
    public static Class<?> toClassConfident(String name) {
        try {
            return Resources.classForName(name);
        } catch (ClassNotFoundException e) {
            try {
                return Class.forName(name);
            } catch (ClassNotFoundException ex) {
                throw new BizException("Cannot find the specified class! Please only call this "
                        + "method when it is explicitly confirmed that there will be a class" + ex.getMessage());
            }
        }
    }
}
