package com.cloudstar.rightcloud.prom.datasource.prometheus.enums;

import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.segments.ISqlSegment;
import lombok.AllArgsConstructor;

/**
 * prometheus 支持的函数
 *
 * @author: wanglang
 * @date: 2023/4/17 10:09 AM
 */
@AllArgsConstructor
public enum PrometheusFunction implements ISqlSegment {
    /**
     * AND
     */
    AND("and()"),
    /**
     * OR
     */
    OR("or()"),
    /**
     * AVG
     */
    AVG("avg()"),
    /**
     * AVG_OVER_TIME
     */
    AVG_OVER_TIME("avg_over_time()"),
    /**
     * MIN
     */
    MIN("min()"),
    /**
     * MIN_OVER_TIME
     */
    MIN_OVER_TIME("min_over_time()"),
    /**
     * MAX
     */
    MAX("max()"),
    /**
     * MAX_OVER_TIME
     */
    MAX_OVER_TIME("max_over_time()"),
    /**
     * SUM
     */
    SUM("sum()"),
    /**
     * SUM_OVER_TIME
     */
    SUM_OVER_TIME("sum_over_time()"),
    /**
     * time
     */
    TIME("time()"),
    /**
     * by
     */
    BY("by");


    private final String keyword;

    @Override
    public String getSqlSegment() {
        return this.keyword;
    }
}
