package com.cloudstar.rightcloud.prom.datasource.prometheus.promql;


import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.segments.ISqlSegment;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.segments.MergeSegments;

/**
 * prom sql
 *
 * @author: wanglang
 * @date: 2023/4/17 11:30 AM
 */
public abstract class BasePromql<T> implements ISqlSegment {

    /**
     * 实体对象（子类实现）
     *
     * @return 泛型 T
     */
    public abstract T getEntity();


    /**
     * 获取 MergeSegments
     */
    public abstract MergeSegments getExpression();

    /**
     * 条件清空
     */
    public abstract void clear();

    public String getSqlSelect() {
        return null;
    }

    public String getPromql() {
        return null;
    }
}
