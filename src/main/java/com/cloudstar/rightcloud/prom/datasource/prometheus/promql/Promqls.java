package com.cloudstar.rightcloud.prom.datasource.prometheus.promql;


import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.conditions.LambdaQueryBasePromql;

/**
 * promql
 *
 * @author: wanglang
 * @date: 2023/4/17 11:59 AM
 */
public final class Promqls {



    private Promqls() {
        // ignore
    }

    /**
     * 获取 LambdaQueryPromql&lt;T&gt;
     *
     * @param <T> 实体类泛型
     * @return LambdaQueryPromql&lt;T&gt;
     */
    public static <T> LambdaQueryBasePromql<T> lambdaQuery() {
        return new LambdaQueryBasePromql<>();
    }

    /**
     * 获取 LambdaQueryPromql&lt;T&gt;
     *
     * @param entity 实体类
     * @param <T>    实体类泛型
     * @return LambdaQueryPromql&lt;T&gt;
     */
    public static <T> LambdaQueryBasePromql<T> lambdaQuery(T entity) {
        return new LambdaQueryBasePromql<>(entity);
    }

    /**
     * 获取 LambdaQueryPromql&lt;T&gt;
     *
     * @param entityClass 实体类class
     * @param <T>         实体类泛型
     * @return LambdaQueryPromql&lt;T&gt;
     * @since 3.3.1
     */
    public static <T> LambdaQueryBasePromql<T> lambdaQuery(Class<T> entityClass) {
        return new LambdaQueryBasePromql<>(entityClass);
    }


}
