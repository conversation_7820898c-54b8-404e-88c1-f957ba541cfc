package com.cloudstar.rightcloud.prom.datasource.prometheus.promql;

import cn.hutool.core.lang.Assert;
import com.cloudstar.rightcloud.prom.datasource.prometheus.constant.StringPool;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.fc.PropertyNamer;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.fc.SFunction;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.support.SerializedLambda;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.util.LambdaUtil;

import java.util.Arrays;
import java.util.Map;

import static java.util.stream.Collectors.joining;

/**
 * prmql
 *
 * @author: wanglang
 * @date: 2023/4/17 11:35 AM
 */
public abstract class AbstractLambdaBasePromql<T, C extends AbstractLambdaBasePromql<T, C>>
        extends AbstractBasePromql<T, SFunction<T, ?>, C> {


    private Map<String, ColumnCache> columnMap = null;
    private boolean initColumnMap = false;

    @SuppressWarnings("unchecked")
    @Override
    protected String columnsToString(SFunction<T, ?>... columns) {
        return columnsToString(true, columns);
    }

    @SuppressWarnings("unchecked")
    protected String columnsToString(boolean onlyColumn, SFunction<T, ?>... columns) {
        return Arrays.stream(columns).map(i -> columnToString(i, onlyColumn)).collect(joining(StringPool.COMMA));
    }

    @Override
    protected String columnToString(SFunction<T, ?> column) {
        return columnToString(column, true);
    }

    protected String columnToString(SFunction<T, ?> column, boolean onlyColumn) {
        return getColumn(LambdaUtil.resolve(column), onlyColumn);
    }

    /**
     * 获取 SerializedLambda 对应的列信息，从 lambda 表达式中推测实体类
     * 如果获取不到列信息，那么本次条件组装将会失败
     *
     * @param lambda     lambda 表达式
     * @param onlyColumn 如果是，结果: "name", 如果否： "name" as "name"
     * @return 列
     */
    private String getColumn(SerializedLambda lambda, boolean onlyColumn) {
        Class<?> aClass = lambda.getInstantiatedType();
        tryInitCache(aClass);
        String fieldName = PropertyNamer.methodToProperty(lambda.getImplMethodName());
        ColumnCache columnCache = getColumnCache(fieldName, aClass);
        return onlyColumn ? columnCache.getColumn() : columnCache.getColumnSelect();
    }

    private void tryInitCache(Class<?> lambdaClass) {
        if (!initColumnMap) {
            final Class<T> entityClass = getEntityClass();
            if (entityClass != null) {
                lambdaClass = entityClass;
            }
            columnMap = LambdaUtil.getColumnMap(lambdaClass);
            initColumnMap = true;
        }
        Assert.notNull(columnMap, "can not find lambda cache for this entity [%s]", lambdaClass.getName());
    }

    private ColumnCache getColumnCache(String fieldName, Class<?> lambdaClass) {
        ColumnCache columnCache = columnMap.get(LambdaUtil.formatKey(fieldName));
        Assert.notNull(columnCache, "can not find lambda cache for this property [%s] of entity [%s]",
                fieldName, lambdaClass.getName());
        return columnCache;
    }

}
