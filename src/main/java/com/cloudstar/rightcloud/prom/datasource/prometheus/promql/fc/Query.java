package com.cloudstar.rightcloud.prom.datasource.prometheus.promql.fc;

import java.io.Serializable;

/**
 * 查询函数
 *
 * @author: wanglang
 * @date: 2023/5/30 5:13 PM
 */
public interface Query<C, T, R> extends Serializable {
    /**
     * 设置查询字段
     *
     * @param columns 字段数组
     * @return C
     */
    @SuppressWarnings("unchecked")
    C select(R... columns);

    /**
     * 查询条件 SQL 片段
     */
    String getSqlSelect();

    /**
     * 设置查询表明
     *
     * @param metric 指标
     * @return C this
     */
    C set(String metric);

}
