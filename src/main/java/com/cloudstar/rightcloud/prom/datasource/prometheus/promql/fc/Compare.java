package com.cloudstar.rightcloud.prom.datasource.prometheus.promql.fc;


import com.cloudstar.rightcloud.prom.datasource.prometheus.enums.PrometheusTime;

import java.io.Serializable;

/**
 * 查询条件
 *
 * @author: wanglang
 * @date: 2023/4/12 6:43 PM
 */
public interface Compare<C, R> extends Serializable {

    /**
     * ignore
     */
    default C eq(R column, Object val) {
        return eq(true, column, val);
    }

    /**
     * 等于 =
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return C
     */
    C eq(boolean condition, R column, Object val);


    /**
     * ignore
     */
    default C neq(R column, Object val) {
        return neq(true, column, val);
    }

    /**
     * 等于 =
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return C
     */
    C neq(boolean condition, R column, Object val);

    /**
     * 不等于 &lt;&gt;
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return C
     */
    C ne(boolean condition, R column, Object val);

    /**
     * ignore
     */
    default C ne(R column, Object val) {
        return ne(true, column, val);
    }

    /**
     * ignore
     */
    default C gt(R column, Object val) {
        return gt(true, column, val);
    }

    /**
     * 大于 &gt;
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return C
     */
    C gt(boolean condition, R column, Object val);


    /**
     * 大于等于 &gt;=
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return C
     */
    C ge(boolean condition, R column, Object val);

    /**
     * ignore
     */
    default C ge(R column, Object val) {
        return ge(true, column, val);
    }

    /**
     * 小于 &lt;
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return C
     */
    C lt(boolean condition, R column, Object val);

    /**
     * ignore
     */
    default C lt(R column, Object val) {
        return lt(true, column, val);
    }

    /**
     * ignore
     */
    default C le(R column, Object val) {
        return le(true, column, val);
    }

    /**
     * 小于等于 &lt;=
     *
     * @param condition 执行条件
     * @param column    字段
     * @param val       值
     * @return C
     */
    C le(boolean condition, R column, Object val);


    /**
     * 小于等于 &lt;=
     *
     * @param condition 执行条件
     * @param time      时间策略
     * @param val       值
     * @return C
     */
    C time(boolean condition, PrometheusTime time, Object val);

    /**
     * ignore
     */
    default C time(PrometheusTime time, Object val) {
        return time(true, time, val);
    }

}
