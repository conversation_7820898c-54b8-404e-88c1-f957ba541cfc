package com.cloudstar.rightcloud.prom.datasource.prometheus.promql.metadata;

import com.cloudstar.rightcloud.prom.datasource.prometheus.constant.StringPool;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.util.StringUtils;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;
import java.util.function.Predicate;

import static java.util.stream.Collectors.joining;

/**
 * promql 解析
 *
 * @author: wanglang
 * @date: 2023/2/3 6:31 PM
 */
@Data
public class PromqlInfo {

    @Setter(AccessLevel.NONE)
    @Getter(AccessLevel.NONE)
    private String sqlSelect;

    @Setter(AccessLevel.NONE)
    @Getter(AccessLevel.NONE)
    private String allSqlSelect;

    private Class<?> entityType;

    private Object entity;
    /**
     * 表名
     */
    private String promqlName;
    /**
     * 否使用驼峰转下划线命名
     */
    private boolean underCamel;

    /**
     * 大写命名,对表名和字段名均生效
     */
    private boolean capitalMode;

    /**
     * tag字段
     */
    private List<PromqlFieldInfo> tagFieldList;


    private String dateColumn;

    private Date dateValue;


    public PromqlInfo() {
    }

    /**
     * 字段
     */
    private List<PromqlFieldInfo> fieldList;


    public static PromqlInfo build() {
        return new PromqlInfo();
    }

    /**
     * 获取包含主键及字段的 select sql 片段
     *
     * @return sql 片段
     */
    public String getAllSqlSelect() {
        allSqlSelect = chooseSelect(PromqlFieldInfo::isSelect);
        return allSqlSelect;
    }

    /**
     * 获取需要进行查询的 select sql 片段
     *
     * @param predicate 过滤条件
     * @return sql 片段
     */
    public String chooseSelect(Predicate<PromqlFieldInfo> predicate) {
        String fieldsSqlSelect = fieldList.stream().filter(predicate)
                .map(PromqlFieldInfo::getSqlSelect).collect(joining(StringPool.COMMA));
        if (StringUtils.isNotBlank(fieldsSqlSelect)) {
            return fieldsSqlSelect;
        } else if (StringUtils.isNotBlank(fieldsSqlSelect)) {
            return fieldsSqlSelect;
        }
        return sqlSelect;
    }


}
