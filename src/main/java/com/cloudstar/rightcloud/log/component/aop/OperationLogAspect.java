package com.cloudstar.rightcloud.log.component.aop;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.cloudstar.rightcloud.common.constant.base.HeaderConstant;
import com.cloudstar.rightcloud.common.exception.BizException;
import com.cloudstar.rightcloud.common.pojo.user.AuthUser;
import com.cloudstar.rightcloud.common.utils.auth.AuthUserHolderUtil;
import com.cloudstar.rightcloud.common.utils.base.IpAddressUtil;
import com.cloudstar.rightcloud.common.utils.json.JacksonUtils;
import com.cloudstar.rightcloud.common.utils.message.MessageUtil;
import com.cloudstar.rightcloud.common.utils.web.WebUtil;
import com.cloudstar.rightcloud.log.common.annotation.OperationLog;
import com.cloudstar.rightcloud.log.component.context.OperationLogRecordContext;
import com.cloudstar.rightcloud.log.component.processor.HandleOperationLogService;
import com.cloudstar.rightcloud.log.component.processor.OperationLogPostProcessor;
import com.cloudstar.rightcloud.log.component.processor.dto.OperationLogBaseDto;
import com.cloudstar.rightcloud.log.component.registrar.OperationLogFunctionRegistrar;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 操作日志解析切面
 *
 * @author: zhangqiang
 * @date: 2022/10/28 11:02
 */

@Aspect
@Slf4j
public class OperationLogAspect {

    @Autowired
    private OperationLogPostProcessor processor;

    @Autowired
    private HandleOperationLogService handleOperationLogService;

    /**
     * SpEL标识解析器
     */
    private SpelExpressionParser parser = new SpelExpressionParser();

    /**
     * 织入切点
     */
    @Pointcut("@annotation(com.cloudstar.rightcloud.log.common.annotation.OperationLog)")
    public void pointCut() {
    }


    /**
     * 前置切面解析
     */
    @Before(value = "pointCut()")
    public void parseBefore(JoinPoint point) throws Throwable {
        // 来源是feign的接口不处理
        if (isFeign()) {
            return;
        }
        //1.获取当前登录用户
        final AuthUser authUser = AuthUserHolderUtil.getAuthUser();
        //2.获取当前方法
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        Method currentMethod = point.getTarget().getClass()
                .getMethod(methodSignature.getName(),
                        methodSignature.getParameterTypes());

        //3.把入参的参数信息注册到上下文中
        StandardEvaluationContext context = this.registerArgumentsToContext(point, currentMethod);

        //4.日志回调方法注册到上下文中
        OperationLogFunctionRegistrar.register(context);

        //5.构建日志基本信息
        OperationLogBaseDto operationLogBaseDto = this.buildBaseOperationLogDto(point, authUser);

        //6.解析注解，为日志dto赋值
        OperationLog operationLog = currentMethod.getAnnotation(OperationLog.class);
        this.resolveExpress(operationLog, context, operationLogBaseDto);

        //7.前置钩子方法
        OperationLogBaseDto beforeProcessorOperationLogBaseDto = processor.beforeProcessor(operationLogBaseDto);

        //8.把操作日志放入上下文
        OperationLogRecordContext.setLocalOperationLogContext(beforeProcessorOperationLogBaseDto);
    }


    /**
     * 后置切面解析
     */
    @AfterReturning(returning = "result", pointcut = "pointCut()")
    public void parseAfter(Object result) {
        try {
            if (isFeign()) {
                return;
            }
            OperationLogBaseDto operationLogBaseDto = OperationLogRecordContext.getLocalOperationLogContext();
            //后置钩子
            OperationLogBaseDto afterProcessorOperationLogBaseDto = processor.afterProcessor(operationLogBaseDto, result);
            //处理操作日志
            handleOperationLogService.handleLog(afterProcessorOperationLogBaseDto);
        } finally {
            //clear
            OperationLogRecordContext.clearContext();
        }

    }

    /**
     * 异常情况解析
     */
    @AfterThrowing(pointcut = "pointCut()", throwing = "ex")
    public void parseAfterThrowing(Throwable ex) {
        // 来源是feign的接口不处理
        if (isFeign()) {
            return;
        }
        OperationLogBaseDto operationLogBaseDto = OperationLogRecordContext.getLocalOperationLogContext();
        operationLogBaseDto.setSuccess(Boolean.FALSE);
        if (ex instanceof BizException) {
            operationLogBaseDto.setErrorMsg("Service exception, exception code【" + ((BizException) ex).getErrorMessage() + "】");
        } else {
            operationLogBaseDto.setErrorMsg(ex.getMessage());
        }
        handleOperationLogService.handleLog(operationLogBaseDto);
    }


    /**
     * 注册参数到Context
     */
    private StandardEvaluationContext registerArgumentsToContext(JoinPoint point, Method currentMethod) {
        Object[] arguments = point.getArgs();
        DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();
        String[] params = discoverer.getParameterNames(currentMethod);
        StandardEvaluationContext context = OperationLogRecordContext.getLocalStandardEvaluationContext();
        if (params != null) {
            for (int len = 0; len < params.length; len++) {
                context.setVariable(params[len], arguments[len]);
            }
        }
        return context;
    }


    /**
     * 解析日志注解
     */
    private void resolveExpress(OperationLog operationLog, StandardEvaluationContext context,
                                OperationLogBaseDto operationLogBaseDto) {
        try {
            // 通过国际化工具类处理
            // 操作类型
            operationLogBaseDto.setType(MessageUtil.getMessage(operationLog.type(), Locale.CHINA));
            operationLogBaseDto.setTypeEn(MessageUtil.getMessage(operationLog.type(), Locale.US));
            // 操作来源
            operationLogBaseDto.setResource(MessageUtil.getMessage(operationLog.resource(), Locale.CHINA));
            operationLogBaseDto.setResourceEn(MessageUtil.getMessage(operationLog.resource(), Locale.US));
            //操作资源名称
            if (StringUtils.isNotBlank(operationLog.objectName())) {
                Object objectName = parseExpression(operationLog.objectName(), context);
                operationLogBaseDto.setObjectName(
                        objectName instanceof String ? (String) objectName : JacksonUtils.toJsonString(objectName));

            }
            //操作资源id
            if (StringUtils.isNotBlank(operationLog.objectId())) {
                Object objectId = parseExpression(operationLog.objectId(), context);
                operationLogBaseDto.setObjectId(
                        objectId instanceof String ? (String) objectId : JacksonUtils.toJsonString(objectId));
            }
            //日志内容 若为实体则JSON序列化实体
            if (StringUtils.isNotBlank(operationLog.msg())) {
                String[] params = operationLog.msgParams();
                List<String> paramValue = new ArrayList<>();
                // 处理msg参数
                if (params != null && params.length > 0) {
                    for (String param : params) {
                        Object result = parseExpression(param, context);
                        String value = result instanceof String ? (String) result : JacksonUtils.toJsonString(result);
                        paramValue.add(value);
                    }
                }
                String msg = operationLog.msg();
                String message;
                String messageEn;
                if (CollectionUtil.isNotEmpty(paramValue)) {
                    message = MessageUtil.getMessage(msg, Locale.CHINA, paramValue.toArray());
                    messageEn = MessageUtil.getMessage(msg, Locale.US, paramValue.toArray());
                } else {
                    message = MessageUtil.getMessage(msg, Locale.CHINA);
                    messageEn = MessageUtil.getMessage(msg, Locale.US);
                }
                operationLogBaseDto.setMsg(message);
                operationLogBaseDto.setMsgEn(messageEn);
            }
        } catch (Exception e) {
            log.error("The operation log parsing expression failed", e);
        }
    }

    /**
     * 解析msg表达式
     * 解析规则：以单引号和拼接符号
     *
     * @param msg 消息表达式
     * @return 消息解析对象
     */
    private String parserMsg(String msg, StandardEvaluationContext context) {
        String val = msg;
        Pattern p = Pattern.compile("'([^']*)'");
        Matcher m = p.matcher(val);
        List<String> list = new ArrayList<>();
        while (m.find()) {
            list.add(m.group(0));
        }
        for (String s : list) {
            val = val.replace(s, "");
        }
        String replace = val.replace("+", "");
        String[] split = replace.split("#");
        Map<String, String> map = new HashMap<>();
        for (String s : split) {
            if (StrUtil.isBlank(s)) {
                continue;
            }
            String key = "#" + s;
            Object value = parseExpression(key, context);
            String parseValue = value instanceof String ? (String) value : JacksonUtils.toJsonString(value);
            map.put(key, parseValue);
        }
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String key = entry.getKey();
            String value = "'" + entry.getValue() + "'";
            msg = msg.replace(key, value);
        }
        return msg;
    }


    /**
     * 根据表达式解析
     *
     * @param expression 表达式
     * @param context    上下文入参
     * @return 解析结果
     */
    private Object parseExpression(String expression, StandardEvaluationContext context) {
        try {
            // 先判断是否为表达式，以#开头
            if (!expression.startsWith("#")) {
                return expression;
            }
            // 获取表达式中所有字段
            expression = expression.replace("#", "");
            String[] fields = expression.split("\\.");
            if (fields.length == 1) {
                return context.lookupVariable(fields[0]);
            }
            Object o = context.lookupVariable(fields[0]);
            if (o == null) {
                return null;
            }
            try {
                JSON parse = JSONUtil.parse(o);
                expression = expression.substring(expression.indexOf(".") + 1);
                return JSONUtil.getByPath(parse, expression);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return null;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return null;
        }
    }


    /**
     * 组装日志基本信息
     */
    private OperationLogBaseDto buildBaseOperationLogDto(JoinPoint point, AuthUser authUser) {
        String actionMethod = point.getSignature().toShortString().replaceAll("\\([^)]*\\)", "");
        HttpServletRequest request = WebUtil.getRequest();
        String remoteIp = IpAddressUtil.getRemoteHostIp(request);
        String userClient = request.getHeader("User-Agent");
        return OperationLogBaseDto.builder()
                //用户id
                .userId(ObjectUtils.isNotEmpty(authUser) ? authUser.getUserId() : null)
                //账号
                .account(ObjectUtils.isNotEmpty(authUser) ? authUser.getAccount() : null)
                //用户类型
                .userType(ObjectUtils.isNotEmpty(authUser) ? authUser.getUserType() : null)
                //操作人client
                .client(userClient)
                //路由ip
                .lbIp(remoteIp)
                //远程ip
                .remoteIp(remoteIp)
                //操作时间
                .time(Calendar.getInstance().getTime())
                //请求的路径
                .urlPath(request.getRequestURI())
                //操作的方法
                .method(actionMethod)
                //默认操作成功
                .httpMethod(request.getMethod())
                .success(Boolean.TRUE)
                .build();
    }

    /**
     * 校验当前登录用户为空或者当前登录账号为空
     */
    private Boolean checkAuthUser() {
        final AuthUser authUser = AuthUserHolderUtil.getAuthUser();
        return ObjectUtils.isEmpty(authUser) || StringUtils.isBlank(authUser.getAccount());
    }

    /**
     * 判断是否是feign调用
     *
     * @return {@link Boolean }
     */
    private Boolean isFeign() {
        // 来源是feign的接口不处理
        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes) ra;
        if (ObjectUtil.isNull(sra)) {
            return true;
        }
        HttpServletRequest request = sra.getRequest();
        String feignFlagHeader = request.getHeader(HeaderConstant.USE_FEIGN);
        return BooleanUtil.toBoolean(feignFlagHeader);
    }

}
