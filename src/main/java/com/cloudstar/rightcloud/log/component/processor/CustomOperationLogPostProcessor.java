package com.cloudstar.rightcloud.log.component.processor;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.pojo.user.AuthUser;
import com.cloudstar.rightcloud.common.utils.auth.AuthUserHolderUtil;
import com.cloudstar.rightcloud.log.component.processor.dto.OperationLogBaseDto;
import com.cloudstar.rightcloud.log.data.mapper.OperationLogUtilMapper;
import com.cloudstar.rightcloud.log.pojo.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 自定义 日志前后置处理器
 *
 * @author: zhangqiang
 * @date: 2022/11/3 14:55
 */
@Slf4j
public class CustomOperationLogPostProcessor implements OperationLogPostProcessor {

    /**
     * 操作日志工具 mapper
     */
    @Autowired
    private OperationLogUtilMapper logUtilMapper;

    @Override
    public OperationLogBaseDto beforeProcessor(OperationLogBaseDto baseDto) {
        return baseDto;
    }

    @Override
    public OperationLogBaseDto afterProcessor(OperationLogBaseDto baseDto, Object result) {
        //用户第一次登录 没有当前登录用户
        AuthUser authUser = AuthUserHolderUtil.getAuthUser();
        if (authUser == null) {
            supplementUserInfoWhenLogin(baseDto, baseDto.getResource());
        }
        // 解析接口错误信息
        if (result != null) {
            RightCloudResult<?> rightCloudResult = BeanUtil.toBean(result, RightCloudResult.class);
            if (!rightCloudResult.isSuccess()) {
                baseDto.setSuccess(false);
                baseDto.setErrorMsg("exception code【" + rightCloudResult.getCode() + "】，exception msg 【" + rightCloudResult.getMessage() + "】");
            } else {
                // 如果当前日志对象中没有objectId，且请求出参为Long类型，则默认取结果集中的数据为ObjectId
                if (StrUtil.isBlank(baseDto.getObjectId())) {
                    Object data = rightCloudResult.getData();
                    if (data instanceof Long) {
                        baseDto.setObjectId(String.valueOf(data));
                    } else if (data instanceof List) {
                        List list = (List) data;
                        if (CollectionUtil.isNotEmpty(list) && list.get(0) instanceof Long) {
                            baseDto.setObjectId(JSONObject.toJSONString(data));
                        }
                    }
                }
            }
        }
        return baseDto;
    }

    /**
     * 用户登陆时 如果登录成功 补充用户基础信息
     */
    private void supplementUserInfoWhenLogin(OperationLogBaseDto baseDto, String loginEnum) {
        //规定用户登录时 tagName作为他输入的账户名
        baseDto.setAccount(baseDto.getObjectName());
        //如果登录成功 获取登录用户的 userid,方便后续日志记录操作
        if (baseDto.getSuccess()) {
            SysUser sysUser = this.findUserByLoginEnum(baseDto.getAccount(), loginEnum);
            if (ObjectUtil.isNotEmpty(sysUser)) {
                baseDto.setUserId(sysUser.getUserId());
                baseDto.setUserType(sysUser.getUserType());
            }
        }
    }

    /**
     * 根据tagName 和登陆枚举获取用户
     */
    private SysUser findUserByLoginEnum(String tagName, String loginEnum) {
        //国际化处理后有中英文值需处理
        switch (loginEnum) {
            //账号密码
            case "Login":
                return logUtilMapper.findUserByAccount(tagName);
            case "用户密码登录":
                return logUtilMapper.findUserByAccount(tagName);
            //手机验证码
            case "Mobile login":
                return logUtilMapper.findUserByMobile(tagName);
            case "手机验证码登录":
                return logUtilMapper.findUserByMobile(tagName);
            default:
                log.warn("Unknown login method{}", loginEnum);
                return null;
        }
    }

}
