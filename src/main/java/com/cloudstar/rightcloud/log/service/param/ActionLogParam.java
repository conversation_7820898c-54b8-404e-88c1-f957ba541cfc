package com.cloudstar.rightcloud.log.service.param;

import cn.hutool.core.annotation.Alias;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 用户操作日志查询返回类
 *
 * <AUTHOR> Lesao
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ActionLogParam {

    /**
     * id
     */
    private Long id;
    /**
     * 组织id
     */
    private Long orgId;
    /**
     * 组织名称
     */
    private String orgName;
    /**
     * 操作用户id
     */
    private Long userId;
    /**
     * 操作用户名称
     */
    @Alias("account")
    private String username;
    /**
     * 操作对象
     */
    @Alias("objectName")
    private String object;
    /**
     * 操作对象
     */
    private String objectId;
    /**
     * 操作类型
     */
    private String type;
    /**
     * 操作类型英文
     */
    private String typeEn;
    /**
     * 操作结果
     */
    private Boolean success;
    /**
     * 操作详情
     */
    @Alias("msg")
    private String detail;
    /**
     * 操作详情英文
     */
    @Alias("msgEn")
    private String detailEn;
    /**
     * 日志来源
     */
    @Alias("resource")
    private String source;
    /**
     * 操作的资源类型英文
     */
    @Alias("resourceEn")
    private String sourceEn;
    /**
     * 日志来源ip地址
     */
    @Alias("remoteIp")
    private String sourceIp;
    /**
     * 访问的客户端标识
     */
    private String client;
    /**
     * 操作的路径
     */
    private String urlPath;
    /**
     * 请求的方式  Post，Put等
     */
    private String httpMethod;
    /**
     * 路由ip
     */
    private String lbIp;
    /**
     * 错误日志
     */
    private String errorMsg;
    /**
     * 变更前
     */
    private String changeBefore;
    /**
     * 变更后
     */
    private String changeAfter;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
    /**
     * 版本号
     */
    private Integer version;
}
