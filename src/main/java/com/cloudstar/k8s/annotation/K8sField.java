package com.cloudstar.k8s.annotation;


import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * K8sField
 *
 * <AUTHOR>
 * @date 2024/3/27 16:29
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface K8sField {

    /**
     * value
     */
    String value() default "";
}
