package com.cloudstar.k8s.config;

import com.cloudstar.k8s.service.facade.CrdService;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 初始化volcano CRD 只有初始化了volcano crd 才能使用volcano的各种自定义资源
 *
 * <AUTHOR>
 * @date 2024/3/27 16:29
 */
@Component
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class InitVolcanoCrd {

    CrdService crdService;

    private static final Map<String, String> CRD_MAP = new HashMap<>();

    static {
        CRD_MAP.put("jobs.batch.volcano.sh", "k8s/volcano-jobs_crd.yaml");
        CRD_MAP.put("notebooks.kubeflow.org", "k8s/notebook-crd.yaml");
        CRD_MAP.put("tensorboards.tensorboard.kubeflow.org", "k8s/tensorboards-crd.yaml");
    }

    /**
     * 初始化crd
     */
    @PostConstruct
    public void init() {
        if (CollectionUtil.isNotEmpty(CRD_MAP)) {
            CRD_MAP.entrySet().forEach((crd) -> {
                //初始化 job crd
                final boolean existCrd = crdService.isExistCrd(crd.getKey());
                if (existCrd) {
                    log.info("{} crd is exist", crd.getKey());
                } else {
                    final boolean flag = crdService.createCrd(crd.getValue());
                    log.info("{} crd create status:{}", crd.getKey(), flag);
                }
            });
        }
    }
}
