package com.cloudstar.k8s.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import cn.hutool.core.util.StrUtil;
import io.fabric8.kubernetes.client.Config;
import io.fabric8.kubernetes.client.ConfigBuilder;
import io.fabric8.kubernetes.client.DefaultKubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClient;

/**
 * k8s config
 *
 * <AUTHOR>
 * @date 2024/3/22 16:50
 */
@Configuration
public class K8sClientConfig {

    private static final String TOKEN = "eyJhbGciOiJSUzI1NiIsImtpZCI6IjgySUFDSWkzMFdoZjJuYmg2LXJPdDZPdWd2SXNnVDFOalF1bXNVNDN0UncifQ.eyJpc3MiOiJrd"
            + "WJlcm5ldGVzL3NlcnZpY2VhY2NvdW50Iiwia3ViZXJuZXRlcy5pby9zZXJ2aWNlYWNjb3VudC9uYW1lc3BhY2UiOiJkZWZhdWx0Iiwia3ViZXJuZXRlcy5pby9zZXJ2aWN"
            + "lYWNjb3VudC9zZWNyZXQubmFtZSI6ImFkbWluLXNlcnZpY2VhY2NvdW50LXRva2VuLTd0YnFyIiwia3ViZXJuZXRlcy5pby9zZXJ2aWNlYWNjb3VudC9zZXJ2aWNlLWFjY"
            + "291bnQubmFtZSI6ImFkbWluLXNlcnZpY2VhY2NvdW50Iiwia3ViZXJuZXRlcy5pby9zZXJ2aWNlYWNjb3VudC9zZXJ2aWNlLWFjY291bnQudWlkIjoiNjMxZDNjNWYtOTg"
            + "0My00Yzc3LWFhM2EtN2YzNTkyYzVlYzYzIiwic3ViIjoic3lzdGVtOnNlcnZpY2VhY2NvdW50OmRlZmF1bHQ6YWRtaW4tc2VydmljZWFjY291bnQifQ.lvrvqTgUGGAe2G"
            + "ZJrdVe_NXL-Q5scGkacslPFCV0A3jLyK4rgY91q69wGjD9jEQ_hlJ2xxU4r2XHMeHEokkqcGlR5LP3t9rWAMQ10iySKTFyFG3UDMNbOTsoVIRY9LxSnOnBF-sHUj8WyizA"
            + "YyKfEd-5MwwKUk5rve_2oK-3YPsAoTwY9VJTQOgsjYU69qx9IyJjBAdFZTM9Q9JNJEkcHXqrGnECdovdWNJP5F_mwo8fCsFXoGzH18syskxQNp6zNsUy6R53_JWvo_XlXi"
            + "2VWCt8VmptUGC43HaQbmHRUCP09z9G75W-2bPmslnCeGz4UBZ1W24fo2ems_okBkAuqg";

    private static final String BASE_URL = "https://10.106.3.111:6443";

    @Bean
    public KubernetesClient kubernetesClient() {
        //从环境变量获取k8s客户端地址
        String k8SClientUrl = System.getenv("K8S_CLIENT_URL");
        String token = System.getenv("K8S_CLIENT_TOKEN");
        if (StrUtil.isEmpty(token)) {
            token = TOKEN;
        }
        String url = StrUtil.isNotEmpty(k8SClientUrl) ? k8SClientUrl : BASE_URL;
        Config config = new ConfigBuilder()
                .withTrustCerts(true)
                .withMasterUrl(url)
                .withOauthToken(token)
                .build();
        KubernetesClient client = new DefaultKubernetesClient(config);
        return client;
    }
}
