package com.cloudstar.k8s.util;

import com.cloudstar.k8s.constant.K8sLabelConstants;
import com.cloudstar.k8s.pojo.dto.ReqsAndLimits;
import com.google.common.collect.Maps;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import io.fabric8.kubernetes.api.model.Container;
import io.fabric8.kubernetes.api.model.Node;
import io.fabric8.kubernetes.api.model.Pod;
import io.fabric8.kubernetes.api.model.Quantity;
import io.fabric8.kubernetes.client.Config;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.extern.slf4j.Slf4j;


/**
 * K8sUtils
 *
 * <AUTHOR>
 * @date 2024/3/22 16:50
 */
@Slf4j
@Component
public class K8sUtils {

    private static final String CPU_LOWER_CASE = "cpu";
    private static final String MEMORY_LOWER_CASE = "memory";
    public static final String CPU_REQUESTS = "cpuRequests";
    private static final String CPU_LIMITS = "cpuLimits";
    private static final String MEMORY_CAPACITY = "memoryCapacity";
    private static final String CPU_CAPACITY = "cpuCapacity";
    private static final String MEMORY_LIMITS = "memoryLimits";
    private static final String CPU_AVG_FRACTION = "cpuAvgFraction";
    public static final String MEMORY_REQUESTS = "memoryRequests";
    private static final String MEMORY_AVG_FRACTION = "memoryAvgFraction";
    private static final String MEMORY_LIMITS_FRACTION = "memoryLimitsFraction";
    public static final String MEMORY_REQUESTS_FRACTION = "memoryRequestsFraction";
    private static final String CPU_LIMITS_FRACTION = "cpuLimitsFraction";
    public static final String CPU_REQUESTS_FRACTION = "cpuRequestsFraction";

    @Autowired
    private KubernetesClient client;
    private Config config;

    public K8sUtils() {
    }


    /**
     * 获取client
     */
    public KubernetesClient getClient() {
        return client;
    }

    public Config getConfig() {
        return config;
    }

    public void setConfig(Config config) {
        this.config = config;
    }

    /**
     * podRequestsAndLimits
     *
     * @param pod pod
     */
    public ReqsAndLimits podRequestsAndLimits(Pod pod) {
        final Map<String, Double> reqs = Maps.newHashMap();
        final Map<String, Double> limits = Maps.newHashMap();
        final List<Container> containers = pod.getSpec().getContainers();
        for (Container container : containers) {
            addResourceList(reqs, container.getResources().getRequests());
            addResourceList(limits, container.getResources().getLimits());
        }
        final List<Container> initContainers = pod.getSpec().getInitContainers();
        // init containers define the minimum of any resource
        for (Container container : initContainers) {
            maxResourceList(reqs, container.getResources().getRequests());
            maxResourceList(limits, container.getResources().getLimits());
        }

        // Add overhead for running a pod to the sum of requests and to non-zero limits:
        final Map<String, Quantity> overhead = pod.getSpec().getOverhead();
        if (overhead != null) {
            addResourceList(reqs, overhead);
            addResourceList(limits, overhead);

        }
        return new ReqsAndLimits(reqs, limits);
    }

    /**
     * addResourceList
     *
     * @param multimap multimap
     * @param quantityMap quantityMap
     */
    private void addResourceList(Map<String, Double> multimap, Map<String, Quantity> quantityMap) {
        if (quantityMap == null) {
            return;
        }
        final Iterator<Map.Entry<String, Quantity>> iterator = quantityMap.entrySet().iterator();
        while (iterator.hasNext()) {
            final Map.Entry<String, Quantity> entry = iterator.next();
            final String name = entry.getKey();

            double request;
            if (name.equals(MEMORY_LOWER_CASE)) {
                request = this.convertToMB(entry.getValue());
            } else if (name.equals(CPU_LOWER_CASE)) {
                request = this.convertToM(entry.getValue());
            } else {
                continue;
            }
            multimap.merge(name, request, (a, b) -> b + a);
        }
    }

    /**
     * quantityMap
     *
     * @param multimap multimap
     * @param quantityMap multimap
     */
    private void maxResourceList(Map<String, Double> multimap, Map<String, Quantity> quantityMap) {
        if (quantityMap == null) {
            return;
        }
        for (Map.Entry<String, Quantity> entry : quantityMap.entrySet()) {
            final String name = entry.getKey();

            double request;
            switch (name) {
                case MEMORY_LOWER_CASE:
                    request = this.convertToMB(entry.getValue());
                    break;
                case CPU_LOWER_CASE:
                    request = this.convertToM(entry.getValue());
                    break;
                default:
                    continue;
            }
            final Double value = multimap.get(name);
            if (value == null) {
                multimap.put(name, request);
            } else if (value < request) {
                multimap.put(name, request);
            }
        }
    }

    /**
     * getPodResources
     *
     * @param pod pod
     */
    public Map<String, Double> getPodResources(Pod pod) {
        final Map<String, Double> reqs = Maps.newHashMap();
        final Map<String, Double> limits = Maps.newHashMap();

        final ReqsAndLimits reqsAndLimits = podRequestsAndLimits(pod);
        final Map<String, Double> podLimits = reqsAndLimits.getLimits();
        final Map<String, Double> podReqs = reqsAndLimits.getReqs();

        mergeMaps(reqs, podReqs);
        mergeMaps(limits, podLimits);

        Double cpuRequests = reqs.get(CPU_LOWER_CASE);
        if (cpuRequests == null) {
            cpuRequests = 0d;
        }
        Double cpuLimits = limits.get(CPU_LOWER_CASE);
        if (cpuLimits == null) {
            cpuLimits = 0d;
        }
        Double memoryRequests = reqs.get(MEMORY_LOWER_CASE);
        if (memoryRequests == null) {
            memoryRequests = 0d;
        }
        Double memoryLimits = limits.get(MEMORY_LOWER_CASE);
        if (memoryLimits == null) {
            memoryLimits = 0d;
        }

        double cpuRequestsFraction = 0;
        double memoryRequestsFraction = 0;
        //        if (cpuLimits > 0) {
        //            cpuRequestsFraction = cpuRequests / cpuLimits * 100;
        //        }
        //
        //        if (memoryLimits > 0) {
        //            memoryRequestsFraction = memoryRequests / memoryLimits * 100;
        //        }
        final Map<String, Double> allocatedResources = Maps.newHashMap();
        allocatedResources.put(CPU_REQUESTS, cpuRequests);
        allocatedResources.put(CPU_LIMITS, cpuLimits);
        allocatedResources.put(MEMORY_REQUESTS, memoryRequests);
        allocatedResources.put(MEMORY_LIMITS, memoryLimits);
        allocatedResources.put(CPU_REQUESTS_FRACTION, cpuRequestsFraction);
        allocatedResources.put(MEMORY_REQUESTS_FRACTION, memoryRequestsFraction);
        return allocatedResources;
    }


    /**
     * getNodeAllocatedResources
     *
     * @param node node
     * @param podList podList
     */
    public Map<String, Double> getNodeAllocatedResources(Node node, List<Pod> podList) {
        final Map<String, Double> reqs = Maps.newHashMap();
        final Map<String, Double> limits = Maps.newHashMap();

        for (Pod pod : podList) {
            final ReqsAndLimits reqsAndLimits = podRequestsAndLimits(pod);
            final Map<String, Double> podLimits = reqsAndLimits.getLimits();
            final Map<String, Double> podReqs = reqsAndLimits.getReqs();

            mergeMaps(reqs, podReqs);
            mergeMaps(limits, podLimits);
        }

        Double cpuRequests = reqs.get(CPU_LOWER_CASE);
        if (cpuRequests == null) {
            cpuRequests = 0d;
        }
        Double cpuLimits = limits.get(CPU_LOWER_CASE);
        if (cpuLimits == null) {
            cpuLimits = 0d;
        }
        Double memoryRequests = reqs.get(MEMORY_LOWER_CASE);
        if (memoryRequests == null) {
            memoryRequests = 0d;
        }
        Double memoryLimits = limits.get(MEMORY_LOWER_CASE);
        if (memoryLimits == null) {
            memoryLimits = 0d;
        }

        double cpuRequestsFraction = 0;
        double cpuLimitsFraction = 0;
        final double cpuCapacity = this.convertToM(node.getStatus().getCapacity().get(CPU_LOWER_CASE));
        if (cpuCapacity > 0) {
            cpuRequestsFraction = cpuRequests / cpuCapacity * 100 * 1000;
            cpuLimitsFraction = cpuLimits / cpuCapacity * 100 * 1000;
        }

        double memoryRequestsFraction = 0;
        double memoryLimitsFraction = 0;
        final double memoryCapacity = this.convertToMB(node.getStatus().getCapacity().get(MEMORY_LOWER_CASE));
        if (memoryCapacity > 0) {
            memoryRequestsFraction = memoryRequests / memoryCapacity * 100;
            memoryLimitsFraction = memoryLimits / memoryCapacity * 100;
        }
        final Map<String, Double> allocatedResources = Maps.newHashMap();
        allocatedResources.put(CPU_REQUESTS, cpuRequests);
        allocatedResources.put(CPU_LIMITS, cpuLimits);
        allocatedResources.put(CPU_CAPACITY, cpuCapacity * 1000);
        allocatedResources.put(MEMORY_REQUESTS, memoryRequests);
        allocatedResources.put(MEMORY_LIMITS, memoryLimits);
        allocatedResources.put(MEMORY_CAPACITY, memoryCapacity);
        allocatedResources.put(CPU_REQUESTS_FRACTION, cpuRequestsFraction);
        allocatedResources.put(CPU_LIMITS_FRACTION, cpuLimitsFraction);
        allocatedResources.put(MEMORY_REQUESTS_FRACTION, memoryRequestsFraction);
        allocatedResources.put(MEMORY_LIMITS_FRACTION, memoryLimitsFraction);
        return allocatedResources;
    }

    /**
     * mergeMaps
     *
     * @param mergedMap mergedMap
     * @param targetMap targetMap
     */
    private void mergeMaps(Map<String, Double> mergedMap, Map<String, Double> targetMap) {

        for (Map.Entry<String, Double> entry : targetMap.entrySet()) {
            final String name = entry.getKey();
            final Double value = mergedMap.get(name);
            if (value == null) {
                mergedMap.put(name, entry.getValue());
            } else {
                mergedMap.put(name, value + entry.getValue());
            }
        }
    }

    private double convertToM(Quantity bytes) {
        if (bytes.getFormat().equalsIgnoreCase("m")) {
            return new BigDecimal(bytes.getAmount()).doubleValue();
        }
        return new BigDecimal(bytes.getAmount()).multiply(new BigDecimal(1000)).doubleValue();
    }

    /**
     * convertToMB
     *
     * @param bytes bytes
     */
    private double convertToMB(Quantity bytes) {
        if (bytes.getFormat().equalsIgnoreCase("mi")) {
            return new BigDecimal(bytes.getAmount()).doubleValue();
        } else if (bytes.getFormat().equalsIgnoreCase("Gi")) {
            return new BigDecimal(bytes.getAmount()).multiply(new BigDecimal(1024)).doubleValue();
        }
        return new BigDecimal(bytes.getAmount()).divide(new BigDecimal(1024), 4).doubleValue();
    }


    /**
     * 获取gpu选择label
     *
     * @param gpuNum gpuNum
     */
    public static Map<String, String> gpuSelector(Integer gpuNum) {
        Map<String, String> gpuSelector = new HashMap<>(2);
        if (gpuNum != null && gpuNum > 0) {
            gpuSelector.put(K8sLabelConstants.NODE_GPU_LABEL_KEY, K8sLabelConstants.NODE_GPU_LABEL_VALUE);
        }
        return gpuSelector;
    }
}
