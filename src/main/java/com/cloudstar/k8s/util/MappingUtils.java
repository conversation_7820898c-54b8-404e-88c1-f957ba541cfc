package com.cloudstar.k8s.util;


import com.cloudstar.k8s.annotation.K8sField;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class MappingUtils {

    /**
     * fabric pojo 转换为 biz pojo 注意output类和input类中 不要使用基本类型，应使用封装类型
     **/
    public static <T, V> V mappingTo(T input, Class<V> outputClass) {
        //不判null的话，若T的属性有默认值，则会输出包含对应默认值的V实例
        if (null == input || outputClass == null) {
            return null;
        }
        //相同基本类的封装类直接输出 比如 String
        if (outputClass.isInstance(input)) {
            return outputClass.cast(input);
        }

        Field[] fields = outputClass.getDeclaredFields();
        V output = null;
        try {
            output = outputClass.newInstance();
            if (fields == null || fields.length < 1) {
                return output;
            }

            for (int i = 0; i < fields.length; i++) {
                Field field = fields[i];
                K8sField k8sField = field.getDeclaredAnnotation(K8sField.class);
                if (k8sField == null) {
                    continue;
                }
                String expression = k8sField.value();
                String[] splitExpression = expression.split(":");
                Object currentArg = input;

                int j = 0;
                while (j < splitExpression.length) {
                    if (currentArg == null) {
                        break;
                    }
                    String funcName = "get" + StrUtil.upperFirst(splitExpression[j]);
                    Method getMethod = currentArg.getClass().getDeclaredMethod(funcName, null);
                    currentArg = getMethod.invoke(currentArg, null);
                    j++;
                }

                if (currentArg == null) {
                    continue;
                }

                //List类型处理
                if (List.class.isAssignableFrom(field.getType())
                        && field.getGenericType() instanceof ParameterizedType
                        && List.class.isAssignableFrom(currentArg.getClass())) {
                    ParameterizedType pt = (ParameterizedType) field.getGenericType();
                    //泛型里的类型
                    Class<?> actualTypeArgument = (Class<?>) pt.getActualTypeArguments()[0];
                    List outputFieldList = new ArrayList();
                    List<?> inputArgList = ((List) currentArg);
                    for (Object inputArg : inputArgList) {
                        Object outputElm = mappingTo(inputArg, actualTypeArgument);
                        outputFieldList.add(outputElm);
                    }
                    currentArg = outputFieldList;
                } else if (Map.class.isAssignableFrom(field.getType()) && field.getGenericType() instanceof ParameterizedType
                        && Map.class.isAssignableFrom(currentArg.getClass())) {
                    Map map = (Map) currentArg;
                    map.forEach((key, value) -> {
                        map.put(mappingTo(key, getMapValueClass(field, 0)), mappingTo(value, getMapValueClass(field, 1)));
                    });
                } else if (!field.getType().isInstance(currentArg) && field.getType().getDeclaredFields().length > 0) {
                    //递归处理属性
                    currentArg = mappingTo(currentArg, field.getType());
                }

                //如果类型相同或是field的子类，赋值
                String setFuncName = "set" + StrUtil.upperFirst(field.getName());
                Method setMethod = outputClass.getDeclaredMethod(setFuncName, field.getType());
                setMethod.invoke(output, currentArg);
            }
        } catch (Exception e) {
            log.error("MappingUtils.mappingTo error, message{}", e.getMessage(), e);
            throw new RuntimeException("fabric to biz failed");
        }
        return output;
    }

    /**
     * 获取map 的key或value 的类型
     *
     * @param field 反射字段
     * @param fieldIndex 0获取key的Class 1获取Value的Class
     *
     * @return Class Class类对象
     */
    private static Class getMapValueClass(Field field, int fieldIndex) {
        try {
            if (Map.class.isAssignableFrom(field.getType())) {
                Type mapMainType = field.getGenericType();
                if (mapMainType instanceof ParameterizedType) {
                    // 执行强制类型转换
                    ParameterizedType parameterizedType = (ParameterizedType) mapMainType;
                    // 获取泛型类型的泛型参数
                    Type[] types = parameterizedType.getActualTypeArguments();
                    return Class.forName(types[fieldIndex].getTypeName());
                } else {
                    log.error("Error getting generic type {}", mapMainType.getTypeName());
                }
            }
        } catch (ClassNotFoundException e) {
            log.error("MappingUtils.getMapValueClass error, message {}", e.toString(), e);
        }
        return null;
    }
}
