package com.cloudstar.k8s.util;

import java.util.HashMap;
import java.util.Map;

import cn.hutool.core.codec.Base64;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;

/**
 * RegistryUtil
 *
 * <AUTHOR>
 * @date 2024/3/22 16:50
 */
public class RegistryUtil {

    /**
     * 默认参数
     */

    private static String httpUrl;
    private static String harbor2 = "/v2";
    private static String username;
    private static String password;


    public static RegistryUtil builder(String url, String username, String password) {
        return new RegistryUtil(url, username, password);
    }

    RegistryUtil(String url, String username, String password) {
        this.httpUrl = url;
        this.username = username;
        this.password = password;
    }

    /**
     * get
     *
     * @param methodUrl methodUrl
     * @param prams prams
     */
    public static HttpResponse get(String methodUrl, Map<String, Object> prams) {
        HttpRequest httpRequest = new HttpRequest(httpUrl + harbor2 + methodUrl).form(prams);
        httpRequest.addHeaders(createHeaders());
        return httpRequest.execute();
    }

    /**
     * Authorization Basic认证
     */
    private static Map<String, String> createHeaders() {
        String auth = username + ":" + password;
        Map<String, String> authMap = new HashMap<>();
        String authHeader = "Basic " + Base64.encode(auth);
        authMap.put("Authorization", authHeader);
        return authMap;
    }


}
