package com.cloudstar.k8s.service.facade;

import com.cloudstar.k8s.pojo.vo.requestvo.CreateImageSecretReq;

/**
 * Secret
 *
 * <AUTHOR>
 * @date 2024/7/16 10:28
 */
public interface SecretService {

    boolean createImageSecret(String nameSpace, CreateImageSecretReq req);

    boolean isExistSecret(String nameSpace, String secretName);

    boolean isExistWithNamespace(String nameSpace);

    boolean deleteSecret(String nameSpace, String secretName);
}
