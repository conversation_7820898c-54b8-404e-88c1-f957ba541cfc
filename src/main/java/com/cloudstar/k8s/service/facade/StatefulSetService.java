package com.cloudstar.k8s.service.facade;

import io.fabric8.kubernetes.api.model.apps.StatefulSet;

/**
 * StatefulSet
 *
 * <AUTHOR>
 * @date 2025/4/8 17:25
 */
public interface StatefulSetService {

    boolean createStatefulSet(String nameSpace, StatefulSet statefulSet);

    boolean isExistStatefulSet(String nameSpace, String statefulSetName);

    boolean deleteStatefulSet(String nameSpace, String statefulSetName);

    boolean updateReplicas(String nameSpace, String statefulSetName, int replicas);
}
