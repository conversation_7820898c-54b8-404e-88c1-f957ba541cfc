package com.cloudstar.k8s.service.crd.sh.volcano.scheduling.v1beta1.queuespec;

@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
@com.fasterxml.jackson.annotation.JsonPropertyOrder({"resource"})
@com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = com.fasterxml.jackson.databind.JsonDeserializer.None.class)
@javax.annotation.processing.Generated("io.fabric8.java.generator.CRGeneratorRunner")
public class Guarantee implements io.fabric8.kubernetes.api.model.KubernetesResource {

    /**
     * The amount of cluster resource reserved for queue. Just set either `percentage` or `resource`
     */
    @com.fasterxml.jackson.annotation.JsonProperty("resource")
    @com.fasterxml.jackson.annotation.JsonPropertyDescription("The amount of cluster resource reserved for queue. Just set either `percentage` or `resource`")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> resource;

    public java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> getResource() {
        return resource;
    }

    public void setResource(java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> resource) {
        this.resource = resource;
    }
}

