package com.cloudstar.k8s.service.crd.sh.volcano.scheduling.v1beta1.queuestatus;

@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
@com.fasterxml.jackson.annotation.JsonPropertyOrder({"nodes", "resource"})
@com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = com.fasterxml.jackson.databind.JsonDeserializer.None.class)
@javax.annotation.processing.Generated("io.fabric8.java.generator.CRGeneratorRunner")
public class Reservation implements io.fabric8.kubernetes.api.model.KubernetesResource {

    /**
     * Nodes are Locked nodes for queue
     */
    @com.fasterxml.jackson.annotation.JsonProperty("nodes")
    @com.fasterxml.jackson.annotation.JsonPropertyDescription("Nodes are Locked nodes for queue")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.List<String> nodes;

    public java.util.List<String> getNodes() {
        return nodes;
    }

    public void setNodes(java.util.List<String> nodes) {
        this.nodes = nodes;
    }

    /**
     * Resource is a list of total idle resource in locked nodes.
     */
    @com.fasterxml.jackson.annotation.JsonProperty("resource")
    @com.fasterxml.jackson.annotation.JsonPropertyDescription("Resource is a list of total idle resource in locked nodes.")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> resource;

    public java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> getResource() {
        return resource;
    }

    public void setResource(java.util.Map<java.lang.String, io.fabric8.kubernetes.api.model.IntOrString> resource) {
        this.resource = resource;
    }
}

