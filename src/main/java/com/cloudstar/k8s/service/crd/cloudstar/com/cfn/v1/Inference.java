package com.cloudstar.k8s.service.crd.cloudstar.com.cfn.v1;

@io.fabric8.kubernetes.model.annotation.Version(value = "v1", storage = true, served = true)
@io.fabric8.kubernetes.model.annotation.Group("cfn.com.cloudstar")
@io.fabric8.kubernetes.model.annotation.Singular("inference")
@io.fabric8.kubernetes.model.annotation.Plural("inferences")
@javax.annotation.processing.Generated("io.fabric8.java.generator.CRGeneratorRunner")
public class Inference extends
        io.fabric8.kubernetes.client.CustomResource<com.cloudstar.k8s.service.crd.cloudstar.com.cfn.v1.InferenceSpec, com.cloudstar.k8s.service.crd.cloudstar.com.cfn.v1.InferenceStatus> implements
        io.fabric8.kubernetes.api.model.Namespaced {

}

