package com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec;

@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
@com.fasterxml.jackson.annotation.JsonPropertyOrder({"spec"})
@com.fasterxml.jackson.databind.annotation.JsonDeserialize(using = com.fasterxml.jackson.databind.JsonDeserializer.None.class)
@javax.annotation.processing.Generated("io.fabric8.java.generator.CRGeneratorRunner")
public class Template implements io.fabric8.kubernetes.api.model.KubernetesResource {

    @com.fasterxml.jackson.annotation.JsonProperty("spec")
    @com.fasterxml.jackson.annotation.JsonSetter(nulls = com.fasterxml.jackson.annotation.Nulls.SKIP)
    private com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.Spec spec;

    public com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.Spec getSpec() {
        return spec;
    }

    public void setSpec(com.cloudstar.k8s.service.crd.notebook.org.kubeflow.v1.notebookspec.template.Spec spec) {
        this.spec = spec;
    }
}

