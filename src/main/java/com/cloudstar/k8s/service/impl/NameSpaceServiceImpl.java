package com.cloudstar.k8s.service.impl;

import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.k8s.annotation.K8sValidation;
import com.cloudstar.k8s.enums.K8sResponseEnum;
import com.cloudstar.k8s.enums.ValidationTypeEnum;
import com.cloudstar.k8s.pojo.PtBaseResult;
import com.cloudstar.k8s.pojo.resource.BizNamespace;
import com.cloudstar.k8s.pojo.vo.responsevo.DescribeNameSpaceResp;
import com.cloudstar.k8s.pojo.vo.responsevo.DescribeNameSpaceResp.LimitRangeItem;
import com.cloudstar.k8s.pojo.vo.responsevo.DescribeNameSpaceResp.Metadata;
import com.cloudstar.k8s.pojo.vo.responsevo.DescribeNameSpaceResp.ResourceQuota;
import com.cloudstar.k8s.service.facade.NameSpaceService;
import com.cloudstar.k8s.service.facade.ResourceQuotaService;
import com.cloudstar.k8s.util.BizConvertUtils;
import com.cloudstar.k8s.util.LabelUtils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import cn.hutool.core.date.BetweenFormatter.Level;
import cn.hutool.core.date.DateBetween;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.fabric8.kubernetes.api.model.LimitRange;
import io.fabric8.kubernetes.api.model.Namespace;
import io.fabric8.kubernetes.api.model.NamespaceBuilder;
import io.fabric8.kubernetes.api.model.NamespaceList;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.KubernetesClientException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;


/**
 * NameSpaceServiceImpl
 *
 * <AUTHOR>
 * @date 2024/3/22 16:50
 */
@Service
@Slf4j
public class NameSpaceServiceImpl implements NameSpaceService {

    private static final String CONSTANT_MEMORY = "memory";
    private static final String CONSTANT_CPU = "cpu";

    @Autowired
    private KubernetesClient client;

    @Value("${user.config.cpu-limit:16}")
    private Integer cpuLimit;

    @Value("${user.config.memory-limit:32}")
    private Integer memoryLimit;

    @Value("${user.config.gpu-limit:4}")
    private Integer gpuLimit;

    @Autowired
    private ResourceQuotaService resourceQuotaService;

    @SneakyThrows
    @Override
    public List<DescribeNameSpaceResp> getNameSpaceList(String clusterName) {
        List<DescribeNameSpaceResp> list = new ArrayList<>();
        client.namespaces().list().getItems().forEach(item -> {
            DescribeNameSpaceResp ns = new DescribeNameSpaceResp();
            ns.setApiVersion(item.getApiVersion());
            ns.setName(item.getMetadata().getName());
            ns.setLabels(item.getMetadata().getLabels());
            ns.setCreateTime(DateUtil.format(DateUtil.parseUTC(item.getMetadata().getCreationTimestamp()),
                                             DatePattern.NORM_DATETIME_PATTERN));
            ns.setStatus(item.getStatus().getPhase());
            String activeTime = DateBetween.create(DateUtil.parseUTC(item.getMetadata().getCreationTimestamp()),
                                                   new Date()).toString(Level.HOUR);
            ns.setActiveTime(activeTime);
            ns.setUid(item.getMetadata().getUid());
            DescribeNameSpaceResp.Metadata metadata = new Metadata();
            metadata.setAnnotations(item.getMetadata().getAnnotations());
            metadata.setLabels(item.getMetadata().getLabels());
            ns.setMetadata(metadata);

            //资源限制
            ns.setLimitRanges(limitRanges(item.getMetadata().getName(), clusterName));
            //资源配额
            ns.setResourceQuota(ResourceQuota(item.getMetadata().getName(), clusterName));
            //获取命名空间下event对象
            ns.setEventList(Event(item.getMetadata().getName(), clusterName));
            list.add(ns);

        });

        return list;

    }

    /**
     * 查询单个接口详情
     */
    @SneakyThrows
    @Override
    public DescribeNameSpaceResp getNameSpaces(String name, String clusterName) {
        DescribeNameSpaceResp ns = new DescribeNameSpaceResp();
        Namespace item = client.namespaces().withName(name).get();
        ns.setApiVersion(item.getApiVersion());
        ns.setName(item.getMetadata().getName());
        ns.setLabels(item.getMetadata().getLabels());
        ns.setCreateTime(DateUtil.format(DateUtil.parseUTC(item.getMetadata().getCreationTimestamp()),
                                         DatePattern.NORM_DATETIME_PATTERN));
        ns.setStatus(item.getStatus().getPhase());
        String activeTime = DateBetween.create(DateUtil.parseUTC(item.getMetadata().getCreationTimestamp()), new Date())
                                       .toString(Level.HOUR);
        ns.setActiveTime(activeTime);
        ns.setUid(item.getMetadata().getUid());
        DescribeNameSpaceResp.Metadata metadata = new Metadata();
        metadata.setAnnotations(item.getMetadata().getAnnotations());
        metadata.setLabels(item.getMetadata().getLabels());
        ns.setMetadata(metadata);

        //资源限制
        ns.setLimitRanges(limitRanges(item.getMetadata().getName(), clusterName));
        //资源配额
        ns.setResourceQuota(ResourceQuota(item.getMetadata().getName(), clusterName));
        //获取命名空间下event对象
        ns.setEventList(Event(item.getMetadata().getName(), clusterName));
        return ns;

    }

    @SneakyThrows
    @Override
    public Namespace getNameSpace(String name, String clusterName) {
        return client.namespaces().withName(name).get();
    }

    @SneakyThrows
    @Override
    public Object getResourceQuota(String namespace, String clusterName) {
        return client.resourceQuotas().inNamespace(namespace).list().getItems();
    }

    @SneakyThrows
    @Override
    public Object getLimitRange(String namespace, String clusterName) {
        return client.limitRanges().inNamespace(namespace).list().getItems();
    }

    @SneakyThrows
    @Override
    public void delNamespace(String name, String clusterName) {
        try {
            client.namespaces().withName(name).delete();
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("删除失败！");
        }
    }

    @SneakyThrows
    @Override
    public void putNamespace(Namespace namespace, String clusterName) {
        try {
            client.namespaces().create(namespace);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("创建失败！");
        }

    }

    @SneakyThrows
    @Override
    public void saveNamespace(Namespace namespace, String clusterName) {
        try {
            Namespace saveName = client.namespaces().withName(namespace.getMetadata().getName()).get();
            if (namespace.getMetadata().getAnnotations() != null) {
                saveName.getMetadata().setAnnotations(namespace.getMetadata().getAnnotations());
            }
            if (namespace.getMetadata().getLabels() != null) {
                saveName.getMetadata().setLabels(namespace.getMetadata().getLabels());
            }
            client.namespaces().createOrReplace(saveName);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("保存失败！");
        }

    }

    @SneakyThrows
    @Override
    public void putNamespaceResourceQuota(io.fabric8.kubernetes.api.model.ResourceQuota resourceQuota,
                                          String clusterName) {
        client.resourceQuotas().inNamespace(resourceQuota.getMetadata().getNamespace()).withName(resourceQuota.getMetadata().getName()).delete();
        try {
            client.resourceQuotas().inNamespace(resourceQuota.getMetadata().getNamespace()).create(resourceQuota);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("操作失败！");
        }

    }

    @SneakyThrows
    @Override
    public void putNamespaceLimitRange(LimitRange limitRange, String clusterName) {
        client.limitRanges().inNamespace(limitRange.getMetadata().getNamespace()).withName(limitRange.getMetadata().getName()).delete();

        try {
            client.limitRanges().inNamespace(limitRange.getMetadata().getNamespace()).create(limitRange);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("操作失败！");
        }

    }


    @SneakyThrows
    @Override
    @Deprecated
    public void saveNamespaceResourceQuota(io.fabric8.kubernetes.api.model.ResourceQuota resourceQuota,
                                           String clusterName) {
        try {
            client.resourceQuotas().updateStatus(resourceQuota);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("操作失败！");
        }

    }

    @SneakyThrows
    @Override
    @Deprecated
    public void saveNamespaceLimitRange(LimitRange limitRange, String clusterName) {
        try {
            client.limitRanges().updateStatus(limitRange);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BizException("操作失败！");
        }

    }

    @SneakyThrows
    @Override
    @Deprecated
    public void delResourceQuotaAndLimitRange(String namespace, String clusterName) {
        List<LimitRange> ranges = client.limitRanges().inAnyNamespace().list().getItems();
        for (LimitRange range :
                ranges) {
            if (range.getMetadata().getNamespace().equals(namespace)) {
                client.limitRanges().delete(range);
            }
        }

        List<io.fabric8.kubernetes.api.model.ResourceQuota> quotas = client.resourceQuotas()
                                                                           .inAnyNamespace()
                                                                           .list()
                                                                           .getItems();
        for (io.fabric8.kubernetes.api.model.ResourceQuota quota :
                quotas) {
            if (quota.getMetadata().getNamespace().equals(namespace)) {
                client.resourceQuotas().delete(quota);
            }
        }
    }

    @SuppressWarnings("checkstyle:MissingJavadocMethod")
    @SneakyThrows
    public List<DescribeNameSpaceResp.LimitRangeItem> limitRanges(String name, String clusterName) {
        List<DescribeNameSpaceResp.LimitRangeItem> list = new ArrayList<>();
        client.limitRanges().inNamespace(name).list().getItems().forEach(resource -> {
            resource.getSpec().getLimits().forEach(range -> {
                DescribeNameSpaceResp.LimitRangeItem cpu = new LimitRangeItem();
                cpu.setResourceName(CONSTANT_CPU);
                if (range.getDefault() != null) {
                    cpu.setDefaultLimit(range.getDefault().get(CONSTANT_CPU).toString());
                }
                if (range.getDefaultRequest() != null) {
                    cpu.setDefaultRequest(range.getDefaultRequest().get(CONSTANT_CPU).toString());
                }
                if (range.getMax() != null) {
                    cpu.setResourceMax(range.getMax().get(CONSTANT_CPU).toString());
                }
                cpu.setResourceType(range.getType());
                if (range.getMin() != null) {
                    cpu.setResourceMin(range.getMin().get(CONSTANT_CPU).toString());
                }
                list.add(cpu);
                DescribeNameSpaceResp.LimitRangeItem memory = new LimitRangeItem();
                memory.setResourceName(CONSTANT_MEMORY);
                if (range.getDefault() != null) {
                    memory.setDefaultLimit(range.getDefault().get(CONSTANT_MEMORY).toString());
                }
                if (range.getDefaultRequest() != null) {
                    memory.setDefaultRequest(range.getDefaultRequest().get(CONSTANT_MEMORY).toString());
                }
                if (range.getMax() != null) {
                    memory.setResourceMax(range.getMax().get(CONSTANT_MEMORY).toString());
                }
                memory.setResourceType(range.getType());
                if (range.getMin() != null) {
                    memory.setResourceMin(range.getMin().get(CONSTANT_MEMORY).toString());
                }
                list.add(memory);

            });
        });
        return list;
    }

    @SuppressWarnings({"checkstyle:MissingJavadocMethod", "checkstyle:MethodName"})
    @SneakyThrows
    public List<DescribeNameSpaceResp.ResourceQuota> ResourceQuota(String name, String clusterName) {
        List<DescribeNameSpaceResp.ResourceQuota> quotaList = new ArrayList<>();
        //资源配额
        client.resourceQuotas().inNamespace(name).list().getItems().forEach(resource -> {
            DescribeNameSpaceResp.ResourceQuota resourceQuota = new ResourceQuota();
            String newActiveTime = DateBetween.create(
                    DateUtil.parseUTC(resource.getMetadata().getCreationTimestamp()), new Date())
                                              .toString(Level.HOUR);
            resourceQuota.setActiveTime(newActiveTime);
            resourceQuota.setName(resource.getMetadata().getName());
            resourceQuota.setResourceQuotaStatus(resource.getStatus());
            quotaList.add(resourceQuota);

        });
        return quotaList;
    }

    @SuppressWarnings({"checkstyle:MissingJavadocMethod", "checkstyle:MethodName"})
    @SneakyThrows
    public List<DescribeNameSpaceResp.Event> Event(String name, String clusterName) {
        List<DescribeNameSpaceResp.Event> eventList = new ArrayList<>();
        client.v1().events().inNamespace(name).list().getItems().forEach(event -> {
            DescribeNameSpaceResp.Event addEvent = new DescribeNameSpaceResp.Event();
            addEvent.setUid(event.getMetadata().getUid());
            addEvent.setMessage(event.getMessage());
            addEvent.setName(event.getMetadata().getName());
            addEvent.setCount(event.getCount());
            addEvent.setInvolvedObject(event.getInvolvedObject());
            addEvent.setFirstTime(DateUtil.format(DateUtil.parseUTC(event.getFirstTimestamp()),
                                                  DatePattern.NORM_DATETIME_PATTERN));
            addEvent.setLastTime(DateUtil.format(DateUtil.parseUTC(event.getLastTimestamp()),
                                                 DatePattern.NORM_DATETIME_PATTERN));
            eventList.add(addEvent);

        });
        return eventList;
    }

    /**
     * 创建NamespaceLabels，为null则不添加标签
     *
     * @param namespace 命名空间
     * @param labels 标签Map
     *
     * @return BizNamespace Namespace 业务类
     */
    @Override
    public BizNamespace create(@K8sValidation(ValidationTypeEnum.K8S_RESOURCE_NAME) String namespace, Map<String, String> labels) {
        try {
            BizNamespace bizNamespace = get(namespace);
            if (bizNamespace != null) {
                return bizNamespace;
            }
            Namespace ns = new NamespaceBuilder().withNewMetadata()
                                                 .withName(namespace)
                                                 .addToLabels(LabelUtils.getBaseLabels(namespace, labels))
                                                 .endMetadata()
                                                 .build();
            Namespace res = client.namespaces().create(ns);
            resourceQuotaService.create(res.getMetadata().getName(), res.getMetadata().getName(), cpuLimit, memoryLimit, gpuLimit);
            return BizConvertUtils.toBizNamespace(res);
        } catch (KubernetesClientException e) {
            log.error("NamespaceApiImpl.create error, param:[namespace]={}, [labels]={},error:{}", namespace, labels, e);
            return new BizNamespace().error(String.valueOf(e.getCode()), e.getMessage());
        }
    }

    /**
     * 根据namespace查询BizNamespace
     *
     * @param namespace 命名空间
     *
     * @return BizNamespace Namespace 业务类
     */
    @Override
    public BizNamespace get(String namespace) {
        if (StrUtil.isEmpty(namespace)) {
            return new BizNamespace().baseErrorBadRequest();
        }
        Namespace namespaceEntity = client.namespaces().withName(namespace).get();
        return BizConvertUtils.toBizNamespace(namespaceEntity);
    }

    /**
     * 查询所有的BizNamespace
     */
    @Override
    public List<BizNamespace> listAll() {
        NamespaceList namespaceList = client.namespaces().list();
        if (namespaceList == null || CollectionUtils.isEmpty(namespaceList.getItems())) {
            return Collections.emptyList();
        }
        return namespaceList.getItems().parallelStream().map(obj -> BizConvertUtils.toBizNamespace(obj)).collect(Collectors.toList());
    }

    /**
     * 根据label标签查询所有的BizNamespace
     *
     * @param labelKey 标签的键
     */
    @Override
    public List<BizNamespace> list(String labelKey) {
        if (StrUtil.isEmpty(labelKey)) {
            return Collections.EMPTY_LIST;
        }
        NamespaceList namespaceList = client.namespaces().withLabel(labelKey, null).list();
        if (namespaceList == null || CollectionUtils.isEmpty(namespaceList.getItems())) {
            return Collections.EMPTY_LIST;
        }
        return namespaceList.getItems().parallelStream().map(obj -> BizConvertUtils.toBizNamespace(obj)).collect(Collectors.toList());
    }

    /**
     * 根据label标签集合查询所有的BizNamespace数据
     *
     * @param labels 标签键的集合
     */
    @Override
    public List<BizNamespace> list(Set<String> labels) {
        if (CollectionUtils.isEmpty(labels)) {
            return Collections.EMPTY_LIST;
        }
        Map<String, String> map = new HashMap<>();
        Iterator<String> it = labels.iterator();
        while (it.hasNext()) {
            //根据label的key查询,无需关系value值
            map.put(it.next(), null);
        }
        NamespaceList namespaceList = client.namespaces().withLabels(map).list();
        if (namespaceList == null || CollectionUtils.isEmpty(namespaceList.getItems())) {
            return Collections.EMPTY_LIST;
        }
        return namespaceList.getItems().parallelStream().map(obj -> BizConvertUtils.toBizNamespace(obj)).collect(Collectors.toList());
    }

    /**
     * 删除命名空间
     *
     * @param namespace 命名空间
     *
     * @return PtBaseResult 基础结果类
     */
    @Override
    public PtBaseResult delete(String namespace) {
        log.info("Param of delete namespace {}", namespace);
        if (StrUtil.isEmpty(namespace)) {
            return new PtBaseResult().baseErrorBadRequest();
        }
        try {
            if (client.namespaces().withName(namespace).delete()) {
                return new PtBaseResult();
            } else {
                return K8sResponseEnum.REPEAT.toPtBaseResult();
            }
        } catch (KubernetesClientException e) {
            log.error("NamespaceApiImpl.delete error, param:[namespace]={}, error:{}", namespace, e);
            return new PtBaseResult(String.valueOf(e.getCode()), e.getMessage());
        }
    }

    /**
     * 删除命名空间的标签
     *
     * @param namespace 命名空间
     * @param labelKey 标签的键
     *
     * @return PtBaseResult 基础结果类
     */
    @Override
    public PtBaseResult removeLabel(String namespace, String labelKey) {
        if (StrUtil.isEmpty(namespace) || StrUtil.isEmpty(labelKey)) {
            return new PtBaseResult().baseErrorBadRequest();
        }
        try {
            return new PtBaseResult();
        } catch (KubernetesClientException e) {
            log.error("NamespaceApiImpl.removeLabel error, param:[namespace]={}, [labelKey]={}, error:{}", namespace, labelKey,
                      e);
            return new PtBaseResult(String.valueOf(e.getCode()), e.getMessage());
        }
    }

    /**
     * 删除命名空间下的多个标签
     *
     * @param namespace 命名空间
     * @param labels 标签键的集合
     *
     * @return PtBaseResult 基础结果类
     */
    @Override
    public PtBaseResult removeLabels(String namespace, Set<String> labels) {
        if (StrUtil.isEmpty(namespace) || CollectionUtils.isEmpty(labels)) {
            return new PtBaseResult().baseErrorBadRequest();
        }
        try {
            Map<String, String> map = new HashMap<>();
            Iterator<String> it = labels.iterator();
            while (it.hasNext()) {
                //根据label的key查询,无需关系value值
                map.put(it.next(), null);
            }
            //TODO
            return new PtBaseResult();
        } catch (KubernetesClientException e) {
            log.error("NamespaceApiImpl.removeLabel error, param:[namespace]={}, [labels]={},error:{}", namespace, labels, e);
            return new PtBaseResult(String.valueOf(e.getCode()), e.getMessage());
        }
    }

    /**
     * 将labelKey和labelValue添加到指定的命名空间
     *
     * @param namespace 命名空间
     * @param labelKey 标签的键
     * @param labelValue 标签的值
     *
     * @return PtBaseResult 基础结果类
     */
    @Override
    public PtBaseResult addLabel(String namespace, String labelKey, String labelValue) {
        if (StrUtil.isEmpty(namespace) || StrUtil.isEmpty(labelKey)) {
            return new PtBaseResult().baseErrorBadRequest();
        }
        try {
            Map<String, String> labels = new HashMap<>();
            labels.put(labelKey, labelValue);
            client.namespaces().withName(namespace).edit().getMetadata().setLabels(labels);
            return new PtBaseResult();
        } catch (KubernetesClientException e) {
            log.error("NamespaceApiImpl.addLabel error, param:[namespace]={}, [labelKey]={}, [labelValue]={}, error:{}",
                      namespace, labelKey, labelValue, e);
            return new PtBaseResult(String.valueOf(e.getCode()), e.getMessage());
        }
    }

    /**
     * 将多个label标签添加到指定的命名空间
     *
     * @param namespace 命名空间
     * @param labels 标签
     *
     * @return PtBaseResult 基础结果类
     */
    @Override
    public PtBaseResult addLabels(String namespace, Map<String, String> labels) {
        if (StrUtil.isEmpty(namespace) || CollectionUtils.isEmpty(labels)) {
            return new PtBaseResult().baseErrorBadRequest();
        }
        try {
            client.namespaces().withName(namespace).edit().getMetadata().setLabels(labels);
            return new PtBaseResult();
        } catch (KubernetesClientException e) {
            log.error("NamespaceApiImpl.addLabels error, param:[namespace]={}, [labels]={},error:{}", namespace,
                      JSONUtil.toJsonStr(labels), e);
            return new PtBaseResult(String.valueOf(e.getCode()), e.getMessage());
        }
    }


    /**
     * 命名空间的资源限制
     *
     * @param namespace 命名空间
     * @param quota 资源限制参数类
     *
     * @return ResourceQuota 资源限制参数类
     */
    @Override
    public io.fabric8.kubernetes.api.model.ResourceQuota addResourceQuota(String namespace, io.fabric8.kubernetes.api.model.ResourceQuota quota) {
        return client.resourceQuotas().inNamespace(namespace).create(quota);
    }

    /**
     * 获得命名空间下的所有的资源限制
     *
     * @param namespace 命名空间
     */
    @Override
    public List<io.fabric8.kubernetes.api.model.ResourceQuota> listResourceQuotas(String namespace) {
        return client.resourceQuotas().inNamespace(namespace).list().getItems();
    }

    /**
     * 解除对命名空间的资源限制
     *
     * @param namespace 命名空间
     * @param quota 资源限制参数类
     *
     * @return boolean  true成功 false失败
     */
    @Override
    public boolean removeResourceQuota(String namespace, io.fabric8.kubernetes.api.model.ResourceQuota quota) {
        return client.resourceQuotas().inNamespace(namespace).delete(quota);
    }
}
