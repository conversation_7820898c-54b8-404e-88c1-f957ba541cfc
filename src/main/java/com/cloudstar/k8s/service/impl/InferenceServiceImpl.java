package com.cloudstar.k8s.service.impl;

import com.cloudstar.ConfigService;
import com.cloudstar.bean.enums.ConfigType;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.config.AgentSysEnvConfig;
import com.cloudstar.k8s.service.crd.cloudstar.com.cfn.v1.Inference;
import com.cloudstar.k8s.service.facade.InferenceService;

import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.fabric8.kubernetes.api.model.KubernetesResourceList;
import io.fabric8.kubernetes.api.model.PodList;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.dsl.MixedOperation;
import io.fabric8.kubernetes.client.dsl.Resource;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;


/**
 * 推理服务impl
 *
 * <AUTHOR>
 * @date 2024/08/27
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class InferenceServiceImpl implements InferenceService {

    private static final String STRING = "/";
    KubernetesClient kubernetesClient;

    ConfigService configService;

    private static final String INFERENCE_LABELS = "inference-name";

    private static final String POD_URL = "/inference/{}/{}";


    /**
     * 创建推理服务
     *
     * @param inference 创建请求
     *
     * @return 创建是否成功
     */
    @Override
    public boolean create(String namespace, Inference inference) {
        try {
            final MixedOperation<Inference, KubernetesResourceList<Inference>, Resource<Inference>> resourceClient =
                    kubernetesClient.resources(Inference.class);
            resourceClient.inNamespace(namespace).createOrReplace(inference);
            return true;
        } catch (Exception e) {
            log.error("create inference service error, createReq:{}", namespace, e);
            return false;
        }
    }

    /**
     * 停止推理服务
     *
     * @param name 服务名称
     * @param namespace 命名空间
     *
     * @return 停止是否成功
     */
    @Override
    public boolean stop(String name, String namespace) {
        try {
            final MixedOperation<Inference, KubernetesResourceList<Inference>, Resource<Inference>> resourceClient =
                    kubernetesClient.resources(Inference.class);
            Inference inference = resourceClient.inNamespace(namespace).withName(name).get();
            Map<String, String> annotations = inference.getMetadata().getAnnotations();
            if (ObjectUtil.isEmpty(annotations)) {
                annotations = new HashMap<>();
            }
            annotations.put("kubeflow-resource-stopped", "true");
            inference.getMetadata().setAnnotations(annotations);
            resourceClient.inNamespace(namespace).withName(name).replace(inference);
            return true;
        } catch (Exception e) {
            log.error("stop inference service error, name:{}, namespace:{}", name, namespace, e);
            return false;
        }
    }

    /**
     * 启动推理服务
     *
     * @param name 服务名称
     * @param namespace 命名空间
     *
     * @return 启动是否成功
     */
    @Override
    public boolean start(String name, String namespace) {
        try {
            final MixedOperation<Inference, KubernetesResourceList<Inference>, Resource<Inference>> resourceClient =
                    kubernetesClient.resources(Inference.class);
            Inference inference = resourceClient.inNamespace(namespace).withName(name).get();
            Map<String, String> annotations = inference.getMetadata().getAnnotations();
            if (ObjectUtil.isEmpty(annotations)) {
                annotations = new HashMap<>();
            }
            if (annotations.containsKey("kubeflow-resource-stopped")) {
                annotations.remove("kubeflow-resource-stopped");
            }
            inference.getMetadata().setAnnotations(annotations);
            resourceClient.inNamespace(namespace).withName(name).createOrReplace(inference);
            return true;
        } catch (Exception e) {
            log.error("start inference service error, name:{}, namespace:{}", name, namespace, e);
            return false;
        }
    }

    /**
     * 重启推理服务
     *
     * @param name 服务名称
     * @param namespace 命名空间
     *
     * @return 重启是否成功
     */
    @Override
    public boolean restart(String name, String namespace) {
        boolean stop = stop(name, namespace);
        boolean start = start(name, namespace);
        return stop && start;
    }

    /**
     * 删除推理服务
     *
     * @param name 服务名称
     * @param namespace 命名空间
     *
     * @return 删除是否成功
     */
    @Override
    public boolean delete(String name, String namespace) {
        try {
            final MixedOperation<Inference, KubernetesResourceList<Inference>, Resource<Inference>> resourceClient =
                    kubernetesClient.resources(Inference.class);
            return resourceClient.inNamespace(namespace).withName(name).delete();
        } catch (Exception e) {
            log.error("delete inference service error, name:{}, namespace:{}", name, namespace, e);
            return false;
        }
    }

    @Override
    public Inference getInference(String name, String namespace) {
        try {
            final MixedOperation<Inference, KubernetesResourceList<Inference>, Resource<Inference>> resourceClient =
                    kubernetesClient.resources(Inference.class);
            return resourceClient.inNamespace(namespace).withName(name).get();
        } catch (Exception e) {
            log.error("get inference service error, name:{}, namespace:{}", name, namespace, e);
            return null;
        }
    }

    @Override
    public String getInferencePort(String name, String namespace) {
        try {
            final io.fabric8.kubernetes.api.model.Service service = kubernetesClient.services()
                                                                                    .inNamespace(namespace)
                                                                                    .withName(name)
                                                                                    .get();
            if (ObjectUtil.isEmpty(service)) {
                throw new BizException("未找到Inference对应的service");
            }
            final Integer nodePort = service.getSpec().getPorts().get(0).getNodePort();
            return nodePort.toString();
        } catch (Exception e) {
            log.error("获取Inference{}端口:{}", name, e.getMessage());
        }
        return null;
    }

    @Override
    public String getInferenceUrl(String name, String namespace) {
        try {
            final Map<String, String> labels = new HashMap<>();
            labels.put(INFERENCE_LABELS, name);
            final PodList podList = kubernetesClient.pods().inNamespace(namespace).withLabels(labels).list();
            if (CollectionUtil.isEmpty(podList.getItems())) {
                throw new BizException("推理服务不存在");
            }
            AgentSysEnvConfig config = configService.getConfig(ConfigType.AGENT_SYS_ENV_CONFIG);
            if (ObjectUtil.isEmpty(config) || ObjectUtil.isEmpty(config.getNotebookPrefixUrl())) {
                throw new BizException("系统未配置推理服务地址前缀");
            }
            final String prefixUrl = config.getNotebookPrefixUrl().stringValue();
            return prefixUrl + StrUtil.format(POD_URL, namespace, name);
        } catch (Exception e) {
            log.error("获取推理服务{}地址失败:{}", name, e.getMessage());
        }
        return null;
    }
}
