package com.cloudstar.k8s.service.impl;

import com.cloudstar.k8s.service.facade.ServiceService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.extern.slf4j.Slf4j;

/**
 * Svc实现
 *
 * <AUTHOR>
 * @date 2024/12/27
 */
@Service
@Slf4j
public class ServiceServiceImpl implements ServiceService {

    @Resource
    private KubernetesClient client;

    @Override
    public boolean isExistService(String nameSpace, String serviceName) {
        try {
            final io.fabric8.kubernetes.api.model.Service service = client.services().inNamespace(nameSpace).withName(serviceName).get();
            if (ObjectUtil.isNotEmpty(service)) {
                return true;
            }
        } catch (Exception e) {
            log.error("创建Service失败", e);
        }
        return false;
    }

    @Override
    public boolean deleteService(String nameSpace, String serviceName) {
        try {
            client.services().inNamespace(nameSpace).withName(serviceName).delete();
            return true;
        } catch (Exception e) {
            log.error("删除Service失败", e);
        }
        return false;
    }

    @Override
    public boolean createService(String nameSpace, io.fabric8.kubernetes.api.model.Service service) {
        try {
            final boolean existService = isExistService(nameSpace, service.getMetadata().getName());
            if (existService) {
                client.services().inNamespace(nameSpace).createOrReplace(service);
            } else {
                client.services().inNamespace(nameSpace).create(service);
            }
            return true;
        } catch (Exception e) {
            log.error("创建Service失败", e);
        }
        return false;
    }

    @Override
    public io.fabric8.kubernetes.api.model.Service getService(String nameSpace, String serviceName) {
        return client.services().inNamespace(nameSpace).withName(serviceName).get();
    }
}
