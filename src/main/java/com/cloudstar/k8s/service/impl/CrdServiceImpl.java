package com.cloudstar.k8s.service.impl;

import com.cloudstar.k8s.service.facade.CrdService;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.InputStream;

import cn.hutool.core.util.ObjectUtil;
import io.fabric8.kubernetes.api.model.apiextensions.v1.CustomResourceDefinition;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * CrdServiceImpl
 *
 * <AUTHOR>
 * @date 2024/3/27 15:12
 */
@Service
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class CrdServiceImpl implements CrdService {

    KubernetesClient kubernetesClient;

    @Override
    public boolean createCrd(String yamlName) {
        // 创建crd
        InputStream inputStream = null;
        try {
            Resource templateResource = new ClassPathResource(yamlName, this.getClass().getClassLoader());
            inputStream = templateResource.getInputStream();
            kubernetesClient.apiextensions().v1().customResourceDefinitions().load(inputStream).createOrReplace();
            return true;
        } catch (Exception e) {
            log.error("创建crd错误:{}", e.getMessage());
            return false;
        }
    }

    @Override
    public boolean isExistCrd(String crdName) {
        CustomResourceDefinition crd = kubernetesClient.apiextensions()
                                                       .v1()
                                                       .customResourceDefinitions()
                                                       .withName(crdName)
                                                       .get();
        if (ObjectUtil.isNotEmpty(crd)) {
            return true;
        }
        return false;
    }
}
