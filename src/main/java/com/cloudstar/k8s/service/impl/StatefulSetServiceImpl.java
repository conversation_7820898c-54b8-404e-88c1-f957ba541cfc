package com.cloudstar.k8s.service.impl;

import com.cloudstar.k8s.service.facade.StatefulSetService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import io.fabric8.kubernetes.api.model.apps.StatefulSet;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.extern.slf4j.Slf4j;

/**
 * StatefulSetServiceImpl
 *
 * <AUTHOR>
 * @date 2025/4/8 17:25
 */
@Service
@Slf4j
public class StatefulSetServiceImpl implements StatefulSetService {

    @Resource
    private KubernetesClient client;

    @Override
    public boolean createStatefulSet(String nameSpace, StatefulSet statefulSet) {
        try {
            client.apps().statefulSets().inNamespace(nameSpace).create(statefulSet);
            return true;
        } catch (Exception e) {
            log.error("创建StatefulSet失败", e);
        }
        return false;
    }

    @Override
    public boolean isExistStatefulSet(String nameSpace, String statefulSetName) {
        try {
            final StatefulSet statefulSet = client.apps().statefulSets().inNamespace(nameSpace).withName(statefulSetName).get();
            if (ObjectUtil.isNotEmpty(statefulSet)) {
                log.error("StatefulSet:{}已存在", statefulSetName);
                return true;
            }
        } catch (Exception e) {
            log.error("查询StatefulSet失败", e);
        }
        return false;
    }

    @Override
    public boolean deleteStatefulSet(String nameSpace, String statefulSetName) {
        try {
            client.apps().statefulSets().inNamespace(nameSpace).withName(statefulSetName).delete();
            return true;
        } catch (Exception e) {
            log.error("删除StatefulSet失败", e);
        }
        return false;
    }

    @Override
    public boolean updateReplicas(String nameSpace, String statefulSetName, int replicas) {
        try {
            final StatefulSet statefulSet = client.apps().statefulSets().inNamespace(nameSpace).withName(statefulSetName).get();
            statefulSet.getSpec().setReplicas(replicas);
            client.apps().statefulSets().inNamespace(nameSpace).withName(statefulSetName).createOrReplace(statefulSet);
            return true;
        } catch (Exception e) {
            log.error("更新StatefulSet副本数失败", e);
        }
        return false;
    }
}
