package com.cloudstar.k8s.service.impl;

import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.k8s.pojo.vo.responsevo.DeploymentInfoResp;
import com.cloudstar.k8s.pojo.vo.responsevo.DeploymentResp;
import com.cloudstar.k8s.pojo.vo.responsevo.NodeListResp;
import com.cloudstar.k8s.pojo.vo.responsevo.PodListResp;
import com.cloudstar.k8s.pojo.vo.responsevo.PortResp;
import com.cloudstar.k8s.pojo.vo.responsevo.VolumeDeviceResp;
import com.cloudstar.k8s.pojo.vo.responsevo.VolumesResp;
import com.cloudstar.k8s.service.facade.DeploymentService;
import com.cloudstar.k8s.service.facade.NodeService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import io.fabric8.kubernetes.api.model.Container;
import io.fabric8.kubernetes.api.model.ContainerPort;
import io.fabric8.kubernetes.api.model.ContainerStatus;
import io.fabric8.kubernetes.api.model.EnvVar;
import io.fabric8.kubernetes.api.model.LabelSelector;
import io.fabric8.kubernetes.api.model.LocalObjectReference;
import io.fabric8.kubernetes.api.model.ObjectMeta;
import io.fabric8.kubernetes.api.model.Pod;
import io.fabric8.kubernetes.api.model.PodList;
import io.fabric8.kubernetes.api.model.PodSpec;
import io.fabric8.kubernetes.api.model.PodTemplateSpec;
import io.fabric8.kubernetes.api.model.Secret;
import io.fabric8.kubernetes.api.model.ServicePort;
import io.fabric8.kubernetes.api.model.ServiceSpec;
import io.fabric8.kubernetes.api.model.Volume;
import io.fabric8.kubernetes.api.model.VolumeMount;
import io.fabric8.kubernetes.api.model.apps.Deployment;
import io.fabric8.kubernetes.api.model.apps.DeploymentSpec;
import io.fabric8.kubernetes.client.KubernetesClient;
import lombok.extern.slf4j.Slf4j;

/**
 * DeploymentServiceImpl
 *
 * <AUTHOR>
 * @date 2024/3/22 16:50
 */
@Service
@Slf4j
public class DeploymentServiceImpl implements DeploymentService {

    @Autowired
    private KubernetesClient client;

    @Resource
    private NodeService nodeService;

    @Resource
    private PodServiceImpl podService;


    // 部署失败状态
    private static final Integer FAIL = 0;
    // 部署成功状态
    private static final Integer SUCCESS = 1;
    // 部署中状态
    private static final Integer UNDER = 2;

    /**
     * 创建deploy
     *
     * @param deploymentDO deploymentDO
     */
    @Transactional(rollbackFor = Exception.class)
    public void deployment(DeploymentResp deploymentDO) {
        Deployment deployment = initDeployment(deploymentDO, null);
        io.fabric8.kubernetes.api.model.Service service = initService(deploymentDO);
        try {
            //设置镜像secret
            //if (mirrorImage.getSource() != 2) {
            //    Secret secret = initSecret(deploymentDO);
            //    client.secrets().inNamespace(secret.getMetadata().getNamespace()).create(secret);
            // }
            client.services().inNamespace(service.getMetadata().getNamespace()).create(service);
            client.apps().deployments().inNamespace(deployment.getMetadata().getNamespace()).create(deployment);
            if (deploymentDO.getPortResponse().size() > 0) {
                deploymentDO.setPorts(JSONUtil.toJsonStr(deploymentDO.getPortResponse()));
            }
            if (deploymentDO.getVolumesResponses().size() > 0) {
                deploymentDO.setVolumes(JSONUtil.toJsonStr(deploymentDO.getVolumesResponses()));
            }
            if (deploymentDO.getVolumeDeviceResponses().size() > 0) {
                deploymentDO.setParams(JSONUtil.toJsonStr(deploymentDO.getVolumeDeviceResponses()));
            }
            deploymentDO.setType(0);
            deploymentDO.setStatus(UNDER);
        } catch (Exception e) {
            throw new BizException(e.getMessage());
        }
    }

    /**
     * 镜像名称中包含版本信息，如果存在则去掉
     **/
    public String getAppImageWithoutVersion(String appImage) {
        String appImageWithoutVersion = "";
        if (StrUtil.isNotEmpty(appImage) && appImage.lastIndexOf(":") != -1) {
            appImageWithoutVersion = appImage.substring(0, appImage.lastIndexOf(":"));
        } else if (StrUtil.isNotEmpty(appImage) && appImage.lastIndexOf("@") != -1) {
            appImageWithoutVersion = appImage.substring(0, appImage.lastIndexOf("@"));
        } else {
            appImageWithoutVersion = appImage;
        }
        log.info("提取没有版本号信息的镜像版本: {} -> {}", appImage, appImageWithoutVersion);
        return appImageWithoutVersion;
    }

    private Deployment initDeployment(DeploymentResp deploymentDO, Integer source) {
        if (source == 2) {
            // 获取内置镜像的镜像地址
            deploymentDO.setImageName("镜像名称");
        }
        Deployment deployment = new Deployment();
        deployment.setApiVersion("apps/v1");
        deployment.setKind("Deployment");
        ObjectMeta meta = new ObjectMeta();
        deployment.setMetadata(meta);
        DeploymentSpec deploymentSpec = new DeploymentSpec();
        deployment.setSpec(deploymentSpec);
        deployment.getMetadata().setName(deploymentDO.getApplicationName());
        deployment.getMetadata().setNamespace(deploymentDO.getNameSpace());
        LabelSelector labelSelector = new LabelSelector();
        Map<String, String> matchLabels = new HashMap<>();
        matchLabels.put("app", deploymentDO.getApplicationName());
        labelSelector.setMatchLabels(matchLabels);
        deployment.getSpec().setSelector(labelSelector);
        deployment.getSpec().setReplicas(deploymentDO.getReplicas());
        // 创建副本
        final PodTemplateSpec podTemplateSpec = new PodTemplateSpec();
        podTemplateSpec.setMetadata(new ObjectMeta());
        podTemplateSpec.getMetadata().setLabels(matchLabels);
        podTemplateSpec.setSpec(new PodSpec());
        // 创建容器
        final List<Container> containers = new ArrayList<>();
        Container container = new Container();
        container.setName(deploymentDO.getApplicationName());
        container.setImage(deploymentDO.getImageName() + ":" + deploymentDO.getTag());

        // 容器添加port
        List<ContainerPort> ports = new ArrayList<>();
        for (PortResp portResponse : deploymentDO.getPortResponse()) {
            ContainerPort containerPort = new ContainerPort();
            if (portResponse.getPort().getStrVal() != null) {
                //containerPort.setHostPort(Integer.parseInt(portResponse.getPort().getStrVal()));
                containerPort.setContainerPort(Integer.parseInt(portResponse.getPort().getStrVal()));
            } else {
                //containerPort.setHostPort(portResponse.getPort().getIntVal());
                containerPort.setContainerPort(portResponse.getPort().getIntVal());
            }
            containerPort.setProtocol(portResponse.getProtocol());
            ports.add(containerPort);
        }
        if (ports.size() > 0) {
            container.setPorts(ports);
        }

        // 容器添加VolumeMounts
        List<VolumeMount> volumeMounts = new ArrayList<>();
        List<Volume> volumeList = new ArrayList<>();
        for (VolumesResp volumesResponse : deploymentDO.getVolumesResponses()) {
            VolumeMount volumeMount = new VolumeMount();
            Volume volume = new Volume();
            volume.setName(volumesResponse.getName());
            volumeMount.setSubPath(volumesResponse.getSubPath());
            volumeMount.setMountPath(volumesResponse.getMountPath());
            volumeMount.setName(volumesResponse.getName());
            volumeMounts.add(volumeMount);
            volumeList.add(volume);
        }
        if (volumeList.size() > 0) {
            podTemplateSpec.getSpec().setVolumes(volumeList);
        }
        if (volumeMounts.size() > 0) {
            container.setVolumeMounts(volumeMounts);
        }

        //容器添加环境参数
        List<EnvVar> env = new ArrayList<>();
        for (VolumeDeviceResp volumeDeviceResponse : deploymentDO.getVolumeDeviceResponses()) {
            EnvVar envVar = new EnvVar();
            envVar.setName(volumeDeviceResponse.getName());
            envVar.setValue(volumeDeviceResponse.getPath());
            env.add(envVar);
        }
        if (env.size() > 0) {
            container.setEnv(env);
        }
        containers.add(container);

        if (source != 2) {
            // 添加secret
            List<LocalObjectReference> imagePullSecrets = new ArrayList<>();
            LocalObjectReference localObjectReference = new LocalObjectReference();
            localObjectReference.setName(deploymentDO.getApplicationName());
            imagePullSecrets.add(localObjectReference);
            podTemplateSpec.getSpec().setImagePullSecrets(imagePullSecrets);
        }
        podTemplateSpec.getSpec().setContainers(containers);
        podTemplateSpec.getMetadata().setName(deploymentDO.getApplicationName());
        podTemplateSpec.getMetadata().setNamespace(deploymentDO.getNameSpace());
        // 暂时不添加nodeName
        //podTemplateSpec.getSpec().setNodeName();
        deployment.getSpec().setTemplate(podTemplateSpec);
        return deployment;
    }

    private io.fabric8.kubernetes.api.model.Service initService(DeploymentResp deploymentDO) {
        io.fabric8.kubernetes.api.model.Service service = new io.fabric8.kubernetes.api.model.Service();
        service.setMetadata(new ObjectMeta());
        service.getMetadata().setName(deploymentDO.getApplicationName());
        service.getMetadata().setNamespace(deploymentDO.getNameSpace());
        Map<String, String> label = new HashMap<>();
        label.put("app", deploymentDO.getApplicationName());
        service.getMetadata().setLabels(label);
        service.setSpec(new ServiceSpec());
        // 这里必须和labelSelector label关联
        service.getSpec().setSelector(label);
        service.getSpec().setType("NodePort");
        List<ServicePort> servicePorts = new ArrayList<>();
        for (PortResp portResponse : deploymentDO.getPortResponse()) {
            ServicePort servicePort = new ServicePort();
            // 这里的端口和clusterIP(************)对应，即************:80,供内部访问。
            servicePort.setPort(80);
            servicePort.setProtocol(portResponse.getProtocol());
            // #端口一定要和container暴露出来的端口对应，nodejs暴露出来的端口是8081，所以这里也应是8081
            if (portResponse.getPort().getStrVal() != null) {
                portResponse.getPort().setIntVal(Integer.parseInt(portResponse.getPort().getStrVal()));
            } else {
                portResponse.getPort().setIntVal(portResponse.getPort().getIntVal());
            }
            portResponse.getPort().setStrVal(null);
            servicePort.setTargetPort(portResponse.getPort());
            if (!portResponse.getTargetPort().getStrVal().equalsIgnoreCase("auto")) {
                // # 所有的节点都会开放此端口，此端口供外部调用。
                servicePort.setNodePort(Integer.parseInt(portResponse.getTargetPort().getStrVal()));
            }
            servicePorts.add(servicePort);
        }
        if (servicePorts.size() > 0) {
            service.getSpec().setPorts(servicePorts);
        }
        return service;
    }

    private Secret initSecret(DeploymentResp deploymentDO) {
        Secret secret = new Secret();
        return secret;
    }

    @Override
    public DeploymentInfoResp deploymentInfo(String nameSpace, String name) {
        DeploymentInfoResp deploymentInfoResponse = new DeploymentInfoResp();
        Deployment deployment = client.apps().deployments().inNamespace(nameSpace).withName(name).get();
        if (deployment == null) {
            throw new BizException("集群中查询不到该:" + name + " 应用容器");
        }
        io.fabric8.kubernetes.api.model.Service service = client.services().inNamespace(nameSpace).withName(name).get();
        PodList podList = client.pods().inNamespace(nameSpace).withLabel("app", name).list();
        if (service != null) {
            deploymentInfoResponse.setClusterIP(service.getSpec().getClusterIP());
            deploymentInfoResponse.setServiceName(service.getMetadata().getName());
            StringBuilder nodePort = new StringBuilder();
            StringBuilder ip = new StringBuilder();
            for (ServicePort servicePort : service.getSpec().getPorts()) {
                nodePort.append(servicePort.getNodePort()).append(" ");
            }
            deploymentInfoResponse.setNodePort(nodePort.toString());
            deploymentInfoResponse.setType(service.getSpec().getType());
            StringBuilder nodeHost = new StringBuilder();
            List<NodeListResp> nodeListResponses = nodeService.listNode();
            for (NodeListResp nodeListResponse : nodeListResponses) {
                nodeHost.append(nodeListResponse.getInternalIp()).append(" ");
                ip.append(nodeListResponse.getInternalIp()).append(":").append(deploymentInfoResponse.getNodePort());
            }
            deploymentInfoResponse.setIp(ip.toString());
            deploymentInfoResponse.setNodeHost(nodeHost.toString());
        }

        deploymentInfoResponse.setDeploymentConditions(deployment.getStatus().getConditions());
        List<PodListResp> podListResponseList = new ArrayList<>();
        for (Pod pod : podList.getItems()) {
            PodListResp podListResponse = podService.formatPod(pod);
            List<ContainerStatus> containerStatuses = pod.getStatus().getContainerStatuses();
            for (ContainerStatus containerStatus : containerStatuses) {
                deploymentInfoResponse.getContainerStates().add(containerStatus.getState());
            }
            podListResponseList.add(podListResponse);
        }
        deploymentInfoResponse.setPodListResponses(podListResponseList);
        return deploymentInfoResponse;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDeployment(String nameSpace, String name) {
        Deployment deployment = client.apps().deployments().inNamespace(nameSpace).withName(name).get();
        client.apps().deployments().delete(deployment);
        io.fabric8.kubernetes.api.model.Service service = client.services().inNamespace(nameSpace).withName(name).get();
        client.services().delete(service);
        Secret secret = client.secrets().inNamespace(nameSpace).withName(name).get();
        client.secrets().delete(secret);
    }

    @Override
    public List<String> getDeploymentArgs(String nameSpace, String name) {
        Deployment deployment = client.apps().deployments().inNamespace(nameSpace).withName(name).get();
        if (ObjectUtil.isEmpty(deployment)) {
            log.error("未找到对应的deploy:{}", name);
            throw new BizException("未找到对应的deploy");
        }
        return deployment.getSpec().getTemplate().getSpec().getContainers().get(0).getArgs();
    }

    @Override
    public void updateDeploymentArgs(String nameSpace, String name, List<String> args) {
        Deployment deployment = client.apps().deployments().inNamespace(nameSpace).withName(name).get();
        if (ObjectUtil.isEmpty(deployment)) {
            log.error("未找到对应的deploy:{}", name);
            throw new BizException("未找到对应的deploy");
        }
        deployment.getSpec().getTemplate().getSpec().getContainers().get(0).setArgs(args);
        client.apps().deployments().inNamespace(nameSpace).createOrReplace(deployment);

    }

    /**
     * 更新Deployment的env
     * @param nameSpace 命名空间
     * @param name deployment名称
     * @param envVars 环境参数
     */
    public void updateDeploymentEnv(String nameSpace, String name, List<EnvVar> envVars) {
        // 获取指定命名空间和名称的 Deployment 对象
        Deployment deployment = client.apps().deployments().inNamespace(nameSpace).withName(name).get();

        // 检查 Deployment 是否存在
        if (ObjectUtil.isEmpty(deployment)) {
            log.error("未找到对应的deploy:{}", name);
            throw new BizException("未找到对应的deploy");
        }

        List<Container> containers = deployment.getSpec().getTemplate().getSpec().getContainers();
        if (containers.isEmpty()) {
            log.error("未找到对应的容器:{}", name);
            throw new BizException("未找到对应的容器");
        }

        // 更新容器的环境变量
        containers.get(0).setEnv(envVars);

        client.apps().deployments().inNamespace(nameSpace).createOrReplace(deployment);
    }

}
