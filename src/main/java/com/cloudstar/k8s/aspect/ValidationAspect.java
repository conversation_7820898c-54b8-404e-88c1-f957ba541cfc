package com.cloudstar.k8s.aspect;

import com.cloudstar.k8s.annotation.K8sValidation;
import com.cloudstar.k8s.enums.ValidationTypeEnum;
import com.cloudstar.k8s.pojo.PtBaseResult;
import com.cloudstar.k8s.util.ValidationUtils;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.json.JSONUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * ValidationAspect
 *
 * <AUTHOR>
 * @date 2024/3/27 16:29
 */
@Slf4j
@Component
@Aspect
public class ValidationAspect {

    /**
     * k8sResourceNameValidation
     *
     * @param point point
     *
     * @throws Throwable Throwable
     */
    @Order(1)
    @Around("execution(* com.cloudstar.k8s.service.impl.*.*(..)))")
    public Object k8sResourceNameValidation(JoinPoint point) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) point.getSignature();
        Annotation[][] parameterAnnotations = methodSignature.getMethod().getParameterAnnotations();
        Object[] args = point.getArgs();
        if (ArrayUtil.isNotEmpty(args)) {
            for (int i = 0; i < parameterAnnotations.length; i++) {
                if (null == args[i]) {
                    continue;
                }
                if (args[i].getClass().isPrimitive()) {
                    continue;
                }
                if (args[i] instanceof String) {
                    for (Annotation annotation : parameterAnnotations[i]) {
                        if (annotation instanceof K8sValidation && ValidationTypeEnum.K8S_RESOURCE_NAME.equals(
                                ((K8sValidation) annotation).value())) {
                            ValidateResourceNameResult validateResult = validateResourceName((String) args[i]);
                            if (!validateResult.isSuccess()) {
                                return getValidationResourceNameErrorReturn(methodSignature.getReturnType(), validateResult.getField());
                            }
                        }
                    }
                } else {
                    ValidateResourceNameResult validateResult = validateArgResourceName(args[i], args[i].getClass());
                    if (!validateResult.isSuccess()) {
                        return getValidationResourceNameErrorReturn(methodSignature.getReturnType(), validateResult.getField());
                    }
                }
            }
        }
        return ((ProceedingJoinPoint) point).proceed();
    }

    /**
     * 校验k8s资源对象名称是否合法
     *
     * @param resourceName 资源名称
     *
     * @return ValidateResourceNameResult 校验资源名称结果类
     */
    private ValidateResourceNameResult validateResourceName(String resourceName) {
        return new ValidateResourceNameResult(ValidationUtils.validateResourceName(resourceName), resourceName);
    }

    /**
     * 对参数内部字段做k8s资源对象名称是否合法校验
     *
     * @param arg 任意对象
     * @param argClass Class类对象
     *
     * @return ValidateResourceNameResult 校验资源名称结果类
     */
    private ValidateResourceNameResult validateArgResourceName(Object arg, Class argClass) {
        Field[] fields = argClass.getDeclaredFields();
        for (int i = 0; i < fields.length; i++) {
            Field field = fields[i];
            K8sValidation k8sValidation = field.getDeclaredAnnotation(K8sValidation.class);
            if (k8sValidation == null) {
                continue;
            }
            if (ValidationTypeEnum.K8S_RESOURCE_NAME.equals(k8sValidation.value()) && field.getType().equals(String.class)) {
                field.setAccessible(true);
                try {
                    String resourceName = (String) field.get(arg);
                    if (!validateResourceName(resourceName).isSuccess()) {
                        return new ValidateResourceNameResult(false, resourceName);
                    }
                } catch (IllegalAccessException e) {
                    log.error("ValidationAspect.validateArgResourceName exception, param:[arg]={}, [argClass]={}, exception:{}",
                              JSONUtil.toJsonStr(arg), JSONUtil.toJsonStr(argClass), e);
                }
            }
        }
        if (argClass.getSuperclass() != Object.class) {
            return validateArgResourceName(arg, argClass.getSuperclass());
        }
        return new ValidateResourceNameResult(true, null);
    }

    /**
     * 校验不通过获取返回值
     *
     * @param returnType Class类对象
     * @param fieldName 字段名称
     *
     * @return Object 任意对象
     */
    private Object getValidationResourceNameErrorReturn(Class<?> returnType, String fieldName) {
        try {
            if (PtBaseResult.class.isAssignableFrom(returnType)) {
                PtBaseResult validationReturn = (PtBaseResult) returnType.newInstance();
                return validationReturn.validationErrorRequest(fieldName);
            }
        } catch (InstantiationException e) {
            log.error("ValidationAspect.getValidationResourceNameErrorReturn exception, param:[returnType]={}, [fieldName]={}",
                      JSONUtil.toJsonStr(returnType), fieldName, e);
        } catch (IllegalAccessException e) {
            log.error("ValidationAspect.getValidationResourceNameErrorReturn exception, param:[returnType]={}, [fieldName]={}",
                      JSONUtil.toJsonStr(returnType), fieldName, e);
        }
        return null;
    }

    /**
     * 校验结果
     */
    @Data
    private class ValidateResourceNameResult {

        private boolean success;
        private String field;

        public ValidateResourceNameResult(boolean success, String field) {
            this.success = success;
            this.field = field;
        }
    }
}
