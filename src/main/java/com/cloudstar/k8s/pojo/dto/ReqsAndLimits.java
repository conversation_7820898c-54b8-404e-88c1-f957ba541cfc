package com.cloudstar.k8s.pojo.dto;

import com.google.common.collect.Maps;

import java.util.Map;

import lombok.Data;

@Data
public class ReqsAndLimits {

    Map<String, Double> reqs = Maps.newHashMap();
    Map<String, Double> limits = Maps.newHashMap();


    public ReqsAndLimits(Map<String, Double> reqs, Map<String, Double> limits) {
        this.reqs = reqs;
        this.limits = limits;


    }
}
