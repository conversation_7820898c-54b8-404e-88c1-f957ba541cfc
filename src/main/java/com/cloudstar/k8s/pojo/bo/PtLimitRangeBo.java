package com.cloudstar.k8s.pojo.bo;


import com.cloudstar.k8s.annotation.K8sValidation;
import com.cloudstar.k8s.enums.ValidationTypeEnum;
import com.cloudstar.k8s.pojo.resource.BizLimitRangeItem;

import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class PtLimitRangeBo {

    @K8sValidation(ValidationTypeEnum.K8S_RESOURCE_NAME)
    private String namespace;
    @K8sValidation(ValidationTypeEnum.K8S_RESOURCE_NAME)
    private String name;
    private List<BizLimitRangeItem> limits;
}
