package com.cloudstar.k8s.pojo.bo;


import com.cloudstar.k8s.util.YamlUtils;

import java.util.LinkedList;
import java.util.List;

import io.fabric8.kubernetes.api.model.HasMetadata;
import lombok.Data;


@Data
public class TaskYamlBo {

    private List<ResourceYamlBo> yamlList;

    public TaskYamlBo() {
        yamlList = new LinkedList();
    }

    public void append(HasMetadata resource) {
        String kind = resource.getKind();
        yamlList.add(new ResourceYamlBo(kind, YamlUtils.dumpAsYaml(resource)));
    }
}
