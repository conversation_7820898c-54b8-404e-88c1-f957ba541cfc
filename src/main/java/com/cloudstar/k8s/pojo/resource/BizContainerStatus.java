package com.cloudstar.k8s.pojo.resource;


import com.cloudstar.k8s.annotation.K8sField;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class BizContainerStatus {

    /**
     * Details about a terminated container
     */
    @K8sField("state:terminated")
    private BizContainerStateTerminated terminated;
    /**
     * Details about a waiting container
     */
    @K8sField("state:waiting")
    private BizContainerStateWaiting waiting;

    @K8sField("containerID")
    private String containerID;

    @K8sField("ready")
    private Boolean ready;

    @K8sField("restartCount")
    private Integer restartCount;
}
