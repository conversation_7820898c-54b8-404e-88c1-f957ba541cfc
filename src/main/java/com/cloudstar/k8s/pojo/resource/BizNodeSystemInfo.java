package com.cloudstar.k8s.pojo.resource;


import com.cloudstar.k8s.annotation.K8sField;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@SuppressWarnings("checkstyle:all")
public class BizNodeSystemInfo {

    @K8sField("architecture")
    private String architecture;
    @K8sField("bootID")
    private String bootID;
    @K8sField("containerRuntimeVersion")
    private String containerRuntimeVersion;
    @K8sField("kernelVersion")
    private String kernelVersion;
    @K8sField("kubeProxyVersion")
    private String kubeProxyVersion;
    @K8sField("kubeletVersion")
    private String kubeletVersion;
    @K8sField("machineID")
    private String machineID;
    @K8sField("operatingSystem")
    private String operatingSystem;
    @K8sField("osImage")
    private String osImage;
    @K8sField("systemUUID")
    private String systemUUID;
}
