package com.cloudstar.k8s.pojo.resource;

import com.cloudstar.k8s.annotation.K8sField;
import com.cloudstar.k8s.pojo.PtBaseResult;

import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class BizLimitRange extends PtBaseResult<BizLimitRange> {

    @K8sField("apiVersion")
    private String apiVersion;
    @K8sField("kind")
    private String kind;

    @K8sField("metadata:creationTimestamp")
    private String creationTimestamp;
    @K8sField("metadata:name")
    private String name;
    @K8sField("metadata:namespace")
    private String namespace;
    @K8sField("metadata:uid")
    private String uid;
    @K8sField("metadata:resourceVersion")
    private String resourceVersion;

    @K8sField("spec:limits")
    private List<BizLimitRangeItem> limits;
}
