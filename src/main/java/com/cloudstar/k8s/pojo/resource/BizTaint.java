package com.cloudstar.k8s.pojo.resource;


import com.cloudstar.k8s.annotation.K8sField;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class BizTaint {

    @K8sField("effect")
    private String effect;
    @K8sField("key")
    private String key;
    @K8sField("timeAdded")
    private String timeAdded;
    @K8sField("value")
    private String value;
}
