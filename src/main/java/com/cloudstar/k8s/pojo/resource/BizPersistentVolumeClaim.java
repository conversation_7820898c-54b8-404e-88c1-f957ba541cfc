package com.cloudstar.k8s.pojo.resource;

import com.cloudstar.k8s.annotation.K8sField;
import com.cloudstar.k8s.pojo.PtBaseResult;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class BizPersistentVolumeClaim extends PtBaseResult<BizPersistentVolumeClaim> {

    @K8sField("apiVersion")
    private String apiVersion;
    @K8sField("kind")
    private String kind;

    @K8sField("metadata:creationTimestamp")
    private String creationTimestamp;
    @K8sField("metadata:labels")
    private Map<String, String> labels = Maps.newHashMap();
    @K8sField("metadata:name")
    private String name;
    @K8sField("metadata:namespace")
    private String namespace;
    @K8sField("metadata:uid")
    private String uid;
    @K8sField("metadata:resourceVersion")
    private String resourceVersion;

    @K8sField("spec:accessModes")
    private List<String> accessModes;
    @K8sField("spec:resources:limits")
    private Map<String, BizQuantity> limits;
    @K8sField("spec:resources:requests")
    private Map<String, BizQuantity> requests;
    @K8sField("spec:storageClassName")
    private String storageClassName;
    @K8sField("spec:volumeMode")
    private String volumeMode;

    @K8sField("status:capacity")
    private Map<String, BizQuantity> capacity;
    @K8sField("status:conditions")
    private List<BizPersistentVolumeClaimCondition> conditions;
    @K8sField("status:phase")
    private String phase;

}
