package com.cloudstar.k8s.pojo.resource;


import com.cloudstar.k8s.annotation.K8sField;

import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class BizDeploymentCondition {

    @K8sField("lastTransitionTime")
    private String lastTransitionTime;
    @K8sField("message")
    private String message;
    @K8sField("reason")
    private String reason;
    @K8sField("status")
    private String status;
    @K8sField("type")
    private String type;
}
