package com.cloudstar.k8s.pojo.vo.responsevo;

import com.cloudstar.k8s.service.crd.cloudstar.com.cfn.v1.inferencespec.HealthCheckConfig;

import java.util.List;
import java.util.Map;

import lombok.Data;

@Data
public class InferenceCreateReq {

    /**
     * AccessKey 镜像仓库访问密钥
     */
    private String accessKey;

    /**
     * Args 容器启动参数
     */
    private List<String> args;

    /**
     * Command 容器启动命令
     */
    private List<String> command;

    /**
     * EnableHealthCheck 是否开启健康检查
     */
    private Boolean enableHealthCheck;

    /**
     * EnableHttps 是否开启https
     */
    private Boolean enableHttps;

    /**
     * Env 环境变量
     */
    private Map<String, String> env;

    /**
     * HealthCheckConfig 健康检查配置
     */
    private HealthCheckConfig healthCheckConfig;

    /**
     * ImageTag 镜像tag
     */
    private String imageTag;

    /**
     * LimitCount 限流数
     */
    private Integer limitCount;

    /**
     * Port 开放的端口
     */
    private Integer port;

    /**
     * Protocol 开放协议
     */
    private String protocol;

    /**
     * RecordLog 是否记录日志
     */
    private Boolean recordLog;

    /**
     * RegistryEndpoint 镜像仓库地址
     */
    private String registryEndpoint;

    /**
     * Replicas 副本数
     */
    private Integer replicas;


    /**
     * SecretKey 镜像仓库加密密钥
     */
    private String secretKey;

    /**
     * ServiceName 服务名称
     */
    private String serviceName;

    /**
     * ServiceNameSpace 服务命名空间
     */
    private String namespace;
}
