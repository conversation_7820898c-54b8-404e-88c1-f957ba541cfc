package com.cloudstar.k8s.enums;

/**
 * LackOfResourcesEnum
 *
 * <AUTHOR>
 * @date 2024/3/27 16:29
 */
public enum LackOfResourcesEnum {
    /**
     * 资源充足
     */
    ADEQUATE(0, "资源充足"),
    /**
     * cpu不足
     */
    LACK_OF_CPU(1, "cpu不足"),
    /**
     * 内存不足
     */
    LACK_OF_MEM(2, "内存不足"),
    /**
     * gpu不足
     */
    LACK_OF_GPU(3, "gpu不足"),
    /**
     * 没有可调度节点
     */
    LACK_OF_NODE(4, "没有可调度节点");

    LackOfResourcesEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    private int code;
    private String message;

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
