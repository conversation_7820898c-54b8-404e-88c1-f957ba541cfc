package com.cloudstar.k8s.enums;

/**
 * GraphicsCardTypeEnum
 *
 * <AUTHOR>
 * @date 2024/3/27 16:29
 */
public enum GraphicsCardTypeEnum {
    /**
     * 显卡型号 英伟达泰坦v
     */
    TITAN_V("Titan V", "英伟达泰坦v"),
    /**
     * 显卡型号 特斯拉v100
     */
    TESLA_V100("Tesla V100", "特斯拉v100");

    GraphicsCardTypeEnum(String type, String caption) {
        this.type = type;
        this.caption = caption;
    }

    private String type;
    private String caption;

    public String getType() {
        return type;
    }

    public String getCaption() {
        return caption;
    }

}
