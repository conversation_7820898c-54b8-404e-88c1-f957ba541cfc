package com.cloudstar.k8s.enums;


public enum ResObsTypeEnum {

    /**
     * 镜像
     */
    IMAGE("image", "镜像"),
    /**
     * 自使用
     */
    SELF_USE("self-use", "自使用"),
    /**
     * 共享存储
     */
    SHARE("share", "共享存储");

    private String code;

    private String desc;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    ResObsTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
