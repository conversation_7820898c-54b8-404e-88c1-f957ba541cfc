package com.cloudstar.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cloudstar.common.base.constant.RedisCacheKeyEnum;
import com.cloudstar.common.base.enums.TrainingJobAnnotationsEnum;
import com.cloudstar.common.base.enums.TrainingJobStatusEnum;
import com.cloudstar.common.base.pojo.BillCollectRedisDto;
import com.cloudstar.common.base.util.TimeStringUtil;
import com.cloudstar.common.component.redis.util.RedisUtil;
import com.cloudstar.dao.mapper.training.AgentJobsMapper;
import com.cloudstar.dao.model.training.AgentJobs;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.Job;
import com.cloudstar.k8s.service.crd.sh.volcano.batch.v1alpha1.jobstatus.Conditions;
import com.cloudstar.k8s.service.facade.PersistentVolumeClaimService;
import com.cloudstar.k8s.service.facade.VolcanoJobResourceService;
import com.cloudstar.service.grpc.AgentTrainingJobCallBackProto.TrainingJobCallBackRequest;
import com.cloudstar.service.grpcservice.facade.AgentBillCollectService;
import com.cloudstar.service.grpcservice.facade.AgentTrainingJobCallBackService;
import com.cloudstar.utils.VolcanoJobStatusChangeUtil;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.Date;
import java.util.List;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import io.fabric8.kubernetes.api.model.KubernetesResourceList;
import io.fabric8.kubernetes.client.KubernetesClient;
import io.fabric8.kubernetes.client.Watcher;
import io.fabric8.kubernetes.client.WatcherException;
import io.fabric8.kubernetes.client.dsl.MixedOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * volcano作业状态监听
 *
 * <AUTHOR>
 * @date 2024/7/5 15:10
 */
@Slf4j
@Component
public class VolcanoJobStatusWatchTask {


    @Resource
    private RedisUtil redisUtil;

    @Resource
    private KubernetesClient kubernetesClient;

    @Resource
    private AgentTrainingJobCallBackService agentTrainingJobCallBackService;

    @Resource
    private AgentBillCollectService agentBillCollectService;

    @Resource
    private AgentJobsMapper agentJobsMapper;

    @Resource
    private VolcanoJobResourceService volcanoJobResourceService;

    @Resource
    private PersistentVolumeClaimService persistentVolumeClaimService;

    private static final String PREFIX_KEY = "_JOB";


    @PostConstruct
    public void init() {
        redisUtil.delete(RedisCacheKeyEnum.K8S_POD_STATUS_WATCH_FLAG.getKey() + PREFIX_KEY);
    }

    /**
     * 容器状态变化定时任务
     */
    @Scheduled(cron = "0/10 * * * * ?")
    public void execute() {
        //标志为true的时候 重新获取监听，为false表示之前连接还没断开，不用去管
        final String flag = redisUtil.get(RedisCacheKeyEnum.K8S_POD_STATUS_WATCH_FLAG.getKey() + PREFIX_KEY);
        log.info("volcanoJob监听标志:{}", flag);
        if (ObjectUtil.isEmpty(flag) || "true".equals(flag)) {
            startListener();
        }
    }

    /**
     * 开始监听
     */
    private void startListener() {
        log.info("-----开始注册notebook监听器-------");
        redisUtil.set(RedisCacheKeyEnum.K8S_POD_STATUS_WATCH_FLAG.getKey() + PREFIX_KEY, "false");
        final MixedOperation<Job, KubernetesResourceList<Job>, io.fabric8.kubernetes.client.dsl.Resource<Job>> resourceClient =
                kubernetesClient.resources(Job.class);
        resourceClient.inAnyNamespace().watch(new Watcher<Job>() {
            @Override
            public void eventReceived(Action action, Job job) {
                syncJobStatus(job);
                saveVolcanoJobCollect(job);
            }

            @Override
            public void onClose(WatcherException e) {
                redisUtil.set(RedisCacheKeyEnum.K8S_POD_STATUS_WATCH_FLAG.getKey() + PREFIX_KEY, "true");
                log.info("notebook监听连接已经关闭:{}", e);
            }
        });
        log.info("-----注册notebook监听器完成-------");
    }

    /**
     * 推送作业状态
     *
     * @param job job
     */
    private void syncJobStatus(Job job) {
        try {
            if (ObjectUtil.isNotEmpty(job.getStatus()) && ObjectUtil.isNotEmpty(job.getStatus().getState())) {
                final String jobStatus = job.getStatus().getState().getPhase();
                log.info("volcano作业状态:{}", jobStatus);
                if (ObjectUtil.isNotEmpty(jobStatus)) {
                    final String cfnStatus = VolcanoJobStatusChangeUtil.changeCfnStatus(jobStatus);
                    if (ObjectUtil.isNotEmpty(cfnStatus)) {
                        final AgentJobs agentJobs = agentJobsMapper.selectOne(
                                new LambdaQueryWrapper<AgentJobs>().eq(AgentJobs::getUserId, job.getMetadata().getNamespace())
                                                                   .eq(AgentJobs::getJobName, job.getMetadata().getName()));
                        if (ObjectUtil.isEmpty(agentJobs)) {
                            log.error("agent数据库未找到对应作业:{}", job.getMetadata().getName());
                            return;
                        }
                        final String oldStatus = agentJobs.getJobSecPhase();
                        agentJobs.setJobId(job.getMetadata().getUid());
                        agentJobs.setJobPhase(cfnStatus);
                        agentJobs.setJobSecPhase(cfnStatus);
                        //如果作业是排队中 修改作业创建时间
                        if (cfnStatus.equals(TrainingJobStatusEnum.QUEUING.getType())) {
                            final ZonedDateTime lastTransitionTime = job.getStatus().getState().getLastTransitionTime();
                            agentJobs.setCreatedAt(Date.from(lastTransitionTime.toInstant()));
                            log.info("volcano作业创建时间:{}", lastTransitionTime);
                        }
                        //如果作业是运行状态 修改作业开始时间
                        if (cfnStatus.equals(TrainingJobStatusEnum.RUNNING.getType())) {
                            final ZonedDateTime lastTransitionTime = job.getStatus().getState().getLastTransitionTime();
                            agentJobs.setJobStartAt(Date.from(lastTransitionTime.toInstant()));
                            log.info("volcano作业开始时间:{}", lastTransitionTime);
                            final Date now = new Date();
                            final long duration = now.getTime() - Date.from(lastTransitionTime.toInstant()).getTime();
                            log.info("volcano作业运行时长:{}", duration);
                            agentJobs.setJobDuration(duration);
                        }
                        //如果作业是已完成状态 修改运行时间
                        if (cfnStatus.equals(TrainingJobStatusEnum.TERMINATED.getType())
                                || cfnStatus.equals(TrainingJobStatusEnum.COMPLETED.getType())
                                || cfnStatus.equals(TrainingJobStatusEnum.FAILED.getType())) {
                            final ZonedDateTime lastTransitionTime = job.getStatus().getState().getLastTransitionTime();
                            log.info("volcano作业完成时间:{}", lastTransitionTime);
                            agentJobs.setJobEndAt(Date.from(lastTransitionTime.toInstant()));
                            final String jobRunningDuration = job.getStatus().getRunningDuration();
                            if (ObjectUtil.isNotEmpty(jobRunningDuration)) {
                                agentJobs.setJobDuration(TimeStringUtil.strToLong(jobRunningDuration));
                                log.info("volcano作业运行时长:{}", agentJobs.getJobDuration());
                            }
                        }
                        //如果作业是已完成或者失败 则删掉pvc
                        if (cfnStatus.equals(TrainingJobStatusEnum.COMPLETED.getType())
                                || cfnStatus.equals(TrainingJobStatusEnum.FAILED.getType())) {
                            final String pvcName = job.getMetadata().getName() + "-pvc";
                            if (persistentVolumeClaimService.isExistPvc(job.getMetadata().getNamespace(), pvcName)) {
                                persistentVolumeClaimService.deletePvc(job.getMetadata().getNamespace(), pvcName);
                                log.info("删除作业:{}存储pvc成功", job.getMetadata().getName());
                            }
                        }
                        agentJobsMapper.updateById(agentJobs);
                        TrainingJobCallBackRequest.Builder builder = TrainingJobCallBackRequest.newBuilder();
                        builder.setCfnJobId(agentJobs.getCfnJobId());
                        builder.setJobId(agentJobs.getJobId());
                        builder.setStatus(cfnStatus);
                        if (ObjectUtil.isNotEmpty(agentJobs.getCreatedAt())) {
                            builder.setCreateTime(agentJobs.getCreatedAt().getTime());
                        }
                        if (ObjectUtil.isNotEmpty(agentJobs.getJobStartAt())) {
                            builder.setStartTime(agentJobs.getJobStartAt().getTime());
                        }
                        if (ObjectUtil.isNotEmpty(agentJobs.getJobDuration())) {
                            builder.setDuration(agentJobs.getJobDuration());
                        }
                        final String jobContainerId = volcanoJobResourceService.getJobContainerId(job.getMetadata().getNamespace(),
                                                                                                  job.getMetadata().getName());
                        if (ObjectUtil.isNotEmpty(jobContainerId)) {
                            builder.setContainerId(jobContainerId);
                        }
                        agentTrainingJobCallBackService.syncJob(builder.build());
                    }
                }
            }
        } catch (Exception e) {
            log.error("推送作业状态失败:{}", e);
        }

    }

    /**
     * 保存作业采集数据
     *
     * @param job job
     */
    private void saveVolcanoJobCollect(Job job) {
        try {
            String poolId = job.getMetadata().getAnnotations()
                    .getOrDefault(TrainingJobAnnotationsEnum.JOB_ID.getType(), "");
            // 非共享资源池的作业不写入计量
            if (!poolId.contains("share")) {
                return;
            }
            BillCollectRedisDto dto = new BillCollectRedisDto();
            dto.setName(job.getMetadata().getName());
            dto.setNamespace(job.getMetadata().getNamespace());
            dto.setType("job");
            dto.setId(job.getMetadata().getUid());
            if (ObjectUtil.isNotEmpty(job.getStatus()) && ObjectUtil.isNotEmpty(job.getStatus().getState())) {
                final String k8sStatus = job.getStatus().getState().getPhase();
                final String cfnStatus = VolcanoJobStatusChangeUtil.changeCfnStatus(k8sStatus);
                if (ObjectUtil.isNotEmpty(cfnStatus)) {
                    final AgentJobs agentJobs = agentJobsMapper.selectOne(
                            new LambdaQueryWrapper<AgentJobs>().eq(AgentJobs::getUserId, job.getMetadata().getNamespace())
                                                               .eq(AgentJobs::getJobName, job.getMetadata().getName()));
                    if (ObjectUtil.isEmpty(agentJobs)) {
                        log.error("agent数据库未找到对应作业:{}", job.getMetadata().getName());
                        return;
                    }
                    //状态是running状态 记录作业开始时间，并开始计量
                    if (cfnStatus.equals(TrainingJobStatusEnum.RUNNING.getType())) {
                        final ZonedDateTime lastTransitionTime = job.getStatus().getState().getLastTransitionTime();
                        dto.setStartTime(Date.from(lastTransitionTime.toInstant()));
                        //发送grpc消息 推送话单信息
                        agentBillCollectService.bmsBillCollect(dto);
                    }
                    //状态是Completed或者terminal状态 记录作业完成时间，并结束计量
                    if (cfnStatus.equals(TrainingJobStatusEnum.TERMINATED.getType())
                            || cfnStatus.equals(TrainingJobStatusEnum.COMPLETED.getType())
                            || cfnStatus.equals(TrainingJobStatusEnum.FAILED.getType())) {
                        //如果为空 补偿一下
                        if (ObjectUtil.isEmpty(dto.getStartTime())) {
                            final List<Conditions> conditionsList = job.getStatus().getConditions();
                            for (Conditions condition : conditionsList) {
                                final String conditionsStatus = condition.getStatus();
                                final String status = VolcanoJobStatusChangeUtil.changeCfnStatus(conditionsStatus);
                                //如果作业是运行状态 修改作业开始时间
                                if (status.equals(TrainingJobStatusEnum.RUNNING.getType())) {
                                    final ZonedDateTime lastTransitionTime = condition.getLastTransitionTime();
                                    dto.setStartTime(Date.from(lastTransitionTime.toInstant()));
                                }
                            }
                        }
                        final ZonedDateTime lastTransitionTime = job.getStatus().getState().getLastTransitionTime();
                        dto.setCompleteTime(Date.from(lastTransitionTime.toInstant()));
                        //发送grpc消息 推送话单信息
                        agentBillCollectService.bmsBillCollect(dto);
                    }
                }
                log.info("job状态State:{}", JSONUtil.toJsonStr(job.getStatus().getState()));
            }
        } catch (Exception e) {
            log.error("作业话单采集失败:{}", e);
        }

    }
}
