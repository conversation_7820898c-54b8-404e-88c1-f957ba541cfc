package com.cloudstar.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

/**
 * 登录枚举类
 *
 * <AUTHOR>
 * @date 2022/7/15 17:33
 */
@Getter
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
public enum LoginTypeEnum {


    ACCOUNT("ACCOUNT", "账号密码登录"),
    MOBILE("MOBILE", "手机验证码登录");

    /**
     * 缓存key
     */
    private String type;

    /**
     * 描述
     */
    private String desc;
}
