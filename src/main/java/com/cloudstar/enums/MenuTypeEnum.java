package com.cloudstar.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 菜单枚举类型
 *
 * <AUTHOR>
 * Created on 2022/8/10
 * @date 2022/08/12
 */
@Getter
@AllArgsConstructor
public enum MenuTypeEnum {
    NAVIGATE("navigate", "导航"),
    MENU("menu", "菜单");

    /**
     * 类型
     */
    private final String type;
    /**
     * 类型
     */
    private final String desc;

    public static String getDesc(String type) {
        for (MenuTypeEnum value : MenuTypeEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return null;
    }
}
