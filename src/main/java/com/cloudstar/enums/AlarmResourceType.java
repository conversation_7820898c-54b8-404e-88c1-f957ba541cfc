package com.cloudstar.enums;

import org.apache.commons.lang3.StringUtils;

import lombok.AllArgsConstructor;

/**
 * 告警资源类型
 *
 * @author: hjy
 * @date: 2025/05/26 16:09
 */
@AllArgsConstructor
public enum AlarmResourceType {
    /**
     * 集群
     */
    CLUSTER("Cluster", "集群", "Cluster"),
    /**
     * 裸金属服务器
     */
    BMS("BMS", "裸金属服务器", "BMS"),
    /**
     * 容器
     */
    CONTAINER("Container", "容器", "Container"),
    /**
     * NPU计算卡
     */
    NPU_COMPUTE_CARD("NPUComputeCard", "NPU计算卡", "NPU compute card");


    public final String code;
    public final String zhCn;
    public final String enUs;

    /**
     * 根据code获取枚举
     */
    public static AlarmResourceType buildResourceType(String code) {
        if (StringUtils.isNotEmpty(code)) {
            for (AlarmResourceType type : AlarmResourceType.values()) {
                if (StringUtils.equals(code, type.code)) {
                    return type;
                }
            }
        }
        return null;
    }

}
