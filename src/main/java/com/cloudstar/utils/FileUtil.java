package com.cloudstar.utils;

import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.utils.IOUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.GZIPInputStream;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FileUtil {

    /**
     * Tar文件解压方法
     *
     * @param tarGzFile 要解压的压缩文件名称（绝对路径名称）
     * @param destDir 解压后文件放置的路径名（绝对路径名称）当路径不存在，会自动创建
     */
    @SuppressFBWarnings("PATH_TRAVERSAL_IN")
    public static void deCompressGZipFile(String tarGzFile, String destDir) throws IOException {

        // 建立输出流，用于将从压缩文件中读出的文件流写入到磁盘
        TarArchiveEntry entry = null;
        TarArchiveEntry[] subEntries = null;
        File subEntryFile = null;
        FileInputStream fis = null;
        GZIPInputStream gis = null;
        TarArchiveInputStream taris = null;
        try {
            fis = new FileInputStream(tarGzFile);
            gis = new GZIPInputStream(fis);
            taris = new TarArchiveInputStream(gis);
            while ((entry = taris.getNextTarEntry()) != null) {
                StringBuilder entryFileName = new StringBuilder();
                entryFileName.append(destDir).append(File.separator).append(entry.getName());
                File entryFile = new File(entryFileName.toString());
                if (entry.isDirectory()) {
                    if (!entryFile.exists()) {
                        entryFile.mkdir();
                    }
                    subEntries = entry.getDirectoryEntries();
                    for (int i = 0; i < subEntries.length; i++) {
                        try (OutputStream out = new FileOutputStream(subEntryFile)) {
                            subEntryFile = new File(entryFileName + File.separator + subEntries[i].getName());
                            IOUtils.copy(taris, out);
                        } catch (Exception e) {
                            log.error("deCompressing file failed:" + subEntries[i].getName() + "in" + tarGzFile);
                        }
                    }
                } else {
                    checkFileExists(entryFile);
                    OutputStream out = new FileOutputStream(entryFile);
                    IOUtils.copy(taris, out);
                    out.close();
                    //如果是gz文件进行递归解压
                    if (entryFile.getName().endsWith(".gz")) {
                        deCompressGZipFile(entryFile.getPath(), destDir);
                    }
                }
            }
            //如果需要刪除之前解压的gz文件，在这里进行

        } catch (Exception e) {
            log.warn("decompress failed", e);
        } finally {
            if (fis != null) {
                fis.close();
            }
            if (gis != null) {
                gis.close();
            }
            if (taris != null) {
                taris.close();
            }
        }
    }
    @SuppressFBWarnings("PATH_TRAVERSAL_IN")
    private static void checkFileExists(File file) {
        //判断是否是目录
        if (file.isDirectory()) {
            if (!file.exists()) {
                file.mkdir();
            }
        } else {
            //判断父目录是否存在，如果不存在，则创建
            if (file.getParentFile() != null && !file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }
            try {
                file.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 获取到这个路径下的全部zip文件路径及名称
     *
     * @param hwZipPath 地址
     */
    @SuppressFBWarnings("PATH_TRAVERSAL_IN")
    public static Map<String, String> zipPaths(String hwZipPath) {
        File file = new File(hwZipPath);
        String fileName = null;
        String filePath = null;
        Map<String, String> fileMap = new HashMap<String, String>();
        if (file.isDirectory()) {
            for (File files : file.listFiles()) {
                if (files.isDirectory()) {
                    for (File f : files.listFiles()) {
                        if (f.isDirectory()) {
                            for (File ff : f.listFiles()) {
                                fileName = ff.getName();
                                filePath = ff.getPath();
                                fileMap.put(fileName, filePath);
                            }
                        } else {
                            fileName = f.getName();
                            filePath = f.getPath();
                            fileMap.put(fileName, filePath);
                        }
                    }
                } else {
                    fileName = files.getName();
                    filePath = files.getPath();
                    fileMap.put(fileName, filePath);
                }
            }
        }
        return fileMap;
    }


    /**
     * 删除文件
     *
     * @param filesPath 文件地址
     * @param filePath 文件地址
     */
    @SuppressFBWarnings("PATH_TRAVERSAL_IN")
    public static boolean deleteFile(String filesPath, String filePath) {
        // 删除文件
        File file1 = new File(filePath);
        file1.delete();
        // 删除文件夹
        File file = new File(filesPath);
        return deleteFile(file);
    }

    /**
     * 删除文件
     *
     * @param file 文件
     */
    @SuppressFBWarnings("PATH_TRAVERSAL_IN")
    private static boolean deleteFile(File file) {
        File[] files = file.listFiles();
        for (File deleteFile : files) {
            if (deleteFile.isDirectory()) {
                //如果是文件夹，则递归删除下面的文件后再删除该文件夹
                if (!deleteFile(deleteFile)) {
                    //如果失败则返回
                    return false;
                }
            } else {
                if (!deleteFile.delete()) {
                    //如果失败则返回
                    return false;
                }
            }
        }
        file.delete();
        return true;
    }


}