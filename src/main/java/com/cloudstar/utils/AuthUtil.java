package com.cloudstar.utils;

import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import javax.crypto.Cipher;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.security.SecureRandom;
import org.springframework.stereotype.Component;

@Component
public class AuthUtil {
    private static final String PRIVATE_KEY = System.getenv("RSA_PRIVATE_KEY");

    private static final int GCM_TAG_LENGTH = 128; // 128 bits for GCM tag length
    private static final int IV_LENGTH = 12; // 96 bits for GCM mode

    /**
     * 使用 RSA 算法解密字符串。
     *
     * @param ps 要解密的字符串
     * @return 解密后的字符串
     */
    public static String deEncrypt(String ps) {
        try {
            byte[] privateKeyBytes = Base64.getDecoder().decode(PRIVATE_KEY);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

            byte[] decodeString = Base64.getDecoder().decode(ps);
            Cipher cipher = Cipher.getInstance("RSA");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] decryptedBytes = cipher.doFinal(decodeString);
            return new String(decryptedBytes);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 使用 AES 算法加密字符串。
     *
     * @param ps 要加密的字符串
     * @return 加密后的字符串
     */
    public static String aesEncrypt(String ps) {
        try {
            // 从环境变量中读取密钥
            String key = System.getenv("ENCRYPTION_KEY");

            if (key == null || key.isEmpty()) {
                throw new IllegalStateException("Encryption key is not set in environment variables.");
            }

            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
            SecureRandom random = new SecureRandom();
            byte[] iv = new byte[IV_LENGTH];
            random.nextBytes(iv);

            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            GCMParameterSpec spec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, spec);
            byte[] encryptedBytes = cipher.doFinal(ps.getBytes());

            // 将 IV 和密文一起编码为 Base64 字符串返回
            byte[] combined = new byte[iv.length + encryptedBytes.length];
            System.arraycopy(iv, 0, combined, 0, iv.length);
            System.arraycopy(encryptedBytes, 0, combined, iv.length, encryptedBytes.length);
            return Base64.getEncoder().encodeToString(combined);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 使用 AES 算法解密字符串。
     *
     * @param ps 要解密的字符串
     * @return 解密后的字符串
     */
    public static String aesDeEncrypt(String ps) {
        try {
            byte[] decodedString = Base64.getDecoder().decode(ps);
            String key = System.getenv("ENCRYPTION_KEY");

            if (key == null || key.isEmpty()) {
                throw new IllegalStateException("Encryption key is not set in environment variables.");
            }

            SecretKeySpec secretKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");

            // 分离 IV 和密文
            byte[] iv = new byte[IV_LENGTH];
            byte[] ciphertext = new byte[decodedString.length - IV_LENGTH];
            System.arraycopy(decodedString, 0, iv, 0, IV_LENGTH);
            System.arraycopy(decodedString, IV_LENGTH, ciphertext, 0, ciphertext.length);

            Cipher cipher = Cipher.getInstance("AES/GCM/NoPadding");
            GCMParameterSpec spec = new GCMParameterSpec(GCM_TAG_LENGTH, iv);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, spec);
            byte[] decryptedBytes = cipher.doFinal(ciphertext);
            return new String(decryptedBytes);
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
