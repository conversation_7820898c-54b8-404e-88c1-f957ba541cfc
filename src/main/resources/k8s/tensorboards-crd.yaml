apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.8.0
  creationTimestamp: null
  name: tensorboards.tensorboard.kubeflow.org
spec:
  group: tensorboard.kubeflow.org
  names:
    kind: Tensorboard
    listKind: TensorboardList
    plural: tensorboards
    singular: tensorboard
  scope: Namespaced
  versions:
    - name: v1alpha1
      schema:
        openAPIV3Schema:
          description: Tensorboard is the Schema for the tensorboards API
          properties:
            apiVersion:
              description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
              type: string
            kind:
              description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
              type: string
            metadata:
              type: object
            spec:
              description: TensorboardSpec defines the desired state of Tensorboard
              properties:
                ak:
                  description: 'INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
                  Important: Run "make" to regenerate code after modifying this file'
                  type: string
                bucket:
                  description: 'bucket Important: Run "make" to regenerate code after
                  modifying this file'
                  type: string
                endpoint:
                  description: 'endpoint Important: Run "make" to regenerate code after
                  modifying this file'
                  type: string
                logspath:
                  description: 'prefix Important: Run "make" to regenerate code after
                  modifying this file'
                  type: string
                replicas:
                  description: 'INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
                  Important: Run "make" to regenerate code after modifying this file'
                  format: int32
                  type: integer
                sk:
                  description: 'INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
                  Important: Run "make" to regenerate code after modifying this file'
                  type: string
                type:
                  description: 'INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
                  Important: Run "make" to regenerate code after modifying this file'
                  type: string
              required:
                - logspath
                - ak
                - sk
                - type
                - bucket
                - endpoint
                - replicas
              type: object
            status:
              description: TensorboardStatus defines the observed state of Tensorboard
              properties:
                conditions:
                  description: Conditions is an array of current conditions
                  items:
                    description: TensorboardCondition defines the observed state of
                      Tensorboard
                    properties:
                      deploymentState:
                        description: Deployment status, 'Available', 'Progressing',
                          'ReplicaFailure' .
                        type: string
                      lastProbeTime:
                        description: Last time we probed the condition.
                        format: date-time
                        type: string
                    required:
                      - deploymentState
                    type: object
                  type: array
                readyReplicas:
                  description: ReadyReplicas defines the number of Tensorboard Servers
                    that are available to connect. The value of ReadyReplicas can be
                    either 0 or 1
                  format: int32
                  type: integer
              required:
                - conditions
                - readyReplicas
              type: object
          type: object
      served: true
      storage: true
      subresources:
        status: { }
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: [ ]
  storedVersions: [ ]
