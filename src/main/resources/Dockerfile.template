# 生成异构镜像DockerFile文件模板

# 构建基础镜像名称
FROM modelarts-job-dev-image/${BASE_IMAGE_FRAMEWORK}:train-${BASE_IMAGE_FRAMEWORK}-${BASE_IMAGE_CUDA}-py_3.9-${BASE_IMAGE_OS}-${BASE_IMAGE_ARCH}

# 设置 root 用户执行后续命令
USER root

# 动态设置用户名和用户组名
ARG USERNAME="yx-user"
ARG USER_GID=100
ARG USER_UID=1000

# 创建用户组和用户
RUN groupadd ${USERNAME}-group -g ${USER_GID} && \
    useradd -m -d /home/<USER>/bin/bash -g ${USER_GID} -u ${USER_UID} ${USERNAME}

# 修改镜像中相关文件权限，使得 ${USERNAME} 用户可读写
RUN chown -R ${USERNAME}:${USER_GID}

# 设置容器镜像预置环境变量
# 请务必设置 PYTHONUNBUFFERED=1, 以免日志丢失
ENV PYTHONUNBUFFERED=1

# 设置容器镜像默认用户与工作目录
USER ${USERNAME}
WORKDIR /home/<USER>

# 用户自定义
# ${USER_DEFINED}