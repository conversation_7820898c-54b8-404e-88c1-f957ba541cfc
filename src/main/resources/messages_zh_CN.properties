#\u5171\u540C\u90E8\u5206
common.info.handle.success=\u64CD\u4F5C\u6210\u529F
common.info.handle.login=\u767B\u5F55\u6210\u529F
common.info.handle.logout=\u767B\u51FA\u6210\u529F
common.warn.handle.partSuccess=\u90E8\u5206\u64CD\u4F5C\u6210\u529F
common.warn.handle.noPermission=\u6CA1\u6709\u64CD\u4F5C\u6743\u9650
common.error.handle.fail=\u64CD\u4F5C\u5931\u8D25
common.error.handle.invalidParameter=\u65E0\u6548\u53C2\u6570
common.error.handle.outputFail=\u5BFC\u51FA\u6587\u4EF6\u5931\u8D25
common.error.handle.connectFail=\u8FDE\u63A5\u5931\u8D25
common.error.handle.fileUploadFail=\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25
common.error.handle.internalServiceError=\u5185\u90E8\u670D\u52A1\u8C03\u7528\u5F02\u5E38
common.error.validation.constraints.cannotDeleteDefault=\u7CFB\u7EDF\u9ED8\u8BA4\u6570\u636E\u4E0D\u5141\u8BB8\u5220\u9664
common.error.validation.constraints.alreadyExists={0}\u6570\u636E\u5DF2\u7ECF\u5B58\u5728
common.error.validation.constraints.nonexistent={0}\u6570\u636E\u4E0D\u5B58\u5728
common.error.validation.constraints.cannotDelete={0}\u5B58\u5728{1}\u5173\u8054\u6570\u636E\uFF0C\u4E0D\u5141\u8BB8\u5220\u9664
common.error.validation.constraints.notEnabled={0}\u672A\u542F\u7528
common.error.validation.constraints.notEmpty={0}\u4E0D\u80FD\u4E3A\u7A7A
common.error.validation.constraints.outputLimit=\u5BFC\u51FA\u6570\u636E\u8D85\u51FA{0}\u6761\u9650\u5236\u6761\u6570
common.error.validation.constraints.fileUploadFileSize=\u6587\u4EF6\u8D85\u8FC7\u6700\u5927\u9650\u5236\uFF1A{0}MB
common.error.validation.constraints.fileUploadMax=\u9644\u4EF6\u4E0A\u4F20\u6570\u91CF\u6700\u5927\u652F\u6301{0}\u4E2A
common.error.validation.constraints.noData=\u6CA1\u6709\u7B26\u5408\u6761\u4EF6\u7684\u5BFC\u51FA\u6570\u636E
common.error.validation.constraints.noProxy=\u4EE3\u7406\u4E0D\u5B58\u5728,\u8BF7\u91CD\u65B0\u8BBE\u7F6E\u4EE3\u7406
common.error.validation.constraints.notTurnOnService=\u670D\u52A1\u672A\u5F00\u542F
common.error.validation.constraints.sortOrder=\u975E\u6CD5\u6392\u5E8F\u53C2\u6570






