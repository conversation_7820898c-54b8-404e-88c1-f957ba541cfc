<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.user.UserEntityGroupMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.user.UserEntityGroup">
        <id property="groupSid" column="group_sid" jdbcType="BIGINT"/>
        <id property="userSid" column="user_sid" jdbcType="BIGINT"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        group_sid
        ,user_sid,version,
        created_by,created_dt,updated_by,
        updated_dt
    </sql>
</mapper>
