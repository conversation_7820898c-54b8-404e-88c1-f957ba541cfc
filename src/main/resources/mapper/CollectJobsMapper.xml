<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.bigscreen.CollectJobsMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.bigscreen.CollectJobs">
        <result property="clusterId" column="cluster_id" jdbcType="BIGINT"/>
        <result property="time" column="time" jdbcType="TIMESTAMP"/>
        <result property="poolId" column="pool_id" jdbcType="VARCHAR"/>
        <result property="jobId" column="job_id" jdbcType="VARCHAR"/>
        <result property="jobStatus" column="job_status" jdbcType="VARCHAR"/>
        <result property="jobDuration" column="job_duration" jdbcType="BIGINT"/>
        <result property="waitedTime" column="waited_time" jdbcType="BIGINT"/>
    </resultMap>

    <resultMap id="ClusterJobCountMap" type="com.cloudstar.service.pojo.dto.bigscreen.ClusterJobStatisticDto">
        <result property="clusterId" column="cluster_id" jdbcType="BIGINT"/>
        <result property="jobCount" column="job_count" jdbcType="INTEGER"/>
        <result property="jobStatus" column="job_status" jdbcType="VARCHAR"/>
    </resultMap>


    <sql id="Base_Column_List">
        cluster_id
        ,time,pool_id,
        job_id,job_status,job_duration,
        waited_time
    </sql>


    <select id="getTotalJobStatistic" resultType="com.cloudstar.service.pojo.dto.bigscreen.ClusterJobStatisticDto">
        select coalesce(job_count,0) as job_count, time_section from (SELECT
        time_bucket_gapfill(
        <if test="summaryType == 'daily'">
            '1 day'
        </if>
        <if test="summaryType == 'monthly'">
            '1 month'
        </if>
        , time, (to_char(now(), 'yyyy-mm-dd')||' 00:00:00')::timestamp - INTERVAL
        <if test="summaryType == 'daily'">
            '7 days'
        </if>
        <if test="summaryType == 'monthly'">
            '3 months'
        </if>
        , (to_char(now() + INTERVAL
        <if test="summaryType == 'daily'">
            '1 day'
        </if>
        <if test="summaryType == 'monthly'">
            '0 month'
        </if>
        , 'yyyy-mm-dd')||' 00:00:00')::timestamp ) as time_section,
        <if test="isSmooth != null and isSmooth == 'true'">
            locf(
            count(distinct(job_id)),
            (select count from (SELECT count(distinct(job_id)) as count,
            time_bucket(
            <if test="summaryType == 'daily'">
                '1 day'
            </if>
            <if test="summaryType == 'monthly'">
                '1 month'
            </if>
            ,time) as time_bucket FROM collect_jobs cj WHERE cj.time &lt;
            (to_char(now(), 'yyyy-mm-dd')||' 00:00:00')::timestamp - INTERVAL
            <if test="summaryType == 'daily'">
                '7 days'
            </if>
            <if test="summaryType == 'monthly'">
                '3 months'
            </if>
            <if test="jobStatus != null ">
                and job_status = #{jobStatus}
            </if>
            group by time_bucket ORDER BY time_bucket DESC LIMIT 1) as t
            )) as job_count
        </if>
        <if test="isSmooth == null or isSmooth == 'false'">
            count(distinct(job_id)) as job_count
        </if>
        FROM
        (
        (SELECT
        job_id,
        TIME,
        job_status,
        cluster_id
        FROM
        collect_jobs
        WHERE
        job_status NOT IN ( 'COMPLETED', 'TERMINATED', 'FAILED' )
        AND time > (to_char(now(), 'yyyy-mm-dd')||' 00:00:00')::timestamp - INTERVAL
        <if test="summaryType == 'daily'">
            '7 days'
        </if>
        <if test="summaryType == 'monthly'">
            '3 months'
        </if>
        )
        UNION ALL
        (SELECT DISTINCT ON
        ( job_id ) job_id,
        TIME,
        job_status,
        cluster_id
        FROM
        collect_jobs
        WHERE
        job_status IN ( 'COMPLETED', 'TERMINATED', 'FAILED' )
        AND time > (to_char(now(), 'yyyy-mm-dd')||' 00:00:00')::timestamp - INTERVAL
        <if test="summaryType == 'daily'">
            '7 days'
        </if>
        <if test="summaryType == 'monthly'">
            '3 months'
        </if>
        ORDER BY
        job_id,
        TIME ASC)
        ) tmp
        WHERE
        cluster_id IN
        <foreach item="item" index="index" collection="clusterIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="jobStatus != null ">
            and job_status = #{jobStatus}
        </if>
        GROUP BY time_section
        ORDER BY time_section) as t;
    </select>

    <select id="getSevenDaysJobCount" resultType="java.lang.Integer">
        SELECT DISTINCT count(DISTINCT job_id)
        FROM collect_jobs
        WHERE
            time
            > (to_char(now()
            , 'yyyy-mm-dd')||' 00:00:00'):: timestamp - INTERVAL '6 days'
          AND job_id NOT IN
            ( SELECT DISTINCT job_id FROM collect_jobs
            <![CDATA[
            WHERE TIME
            > (to_char(now()
            , 'yyyy-mm-dd')||' 00:00:00'):: timestamp - INTERVAL '7 days'
          AND TIME
            < (to_char(now()
            , 'yyyy-mm-dd')||' 23:59:59'):: timestamp - INTERVAL '7 days'
          AND job_status IN ( 'COMPLETED'
            , 'TERMINATED'
            , 'FAILED' )
            ]]>
          )
    </select>

    <select id="getMaxJobCountAmongOneDay" resultType="java.lang.Integer">
        select max(job_count)
        from (select time_bucket('${dataCollect} minutes', time) as time_section, count(distinct (job_id)) as job_count
              from collect_jobs cj
              where time
                  > now() - interval '24 hours'
                and cluster_id = #{clusterId}
                and job_status = #{jobStatus}
              group by time_section
              order by time_section desc) as job_count_table;
    </select>

    <select id="getCurrentJobCountByStatus" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT job_id)
        FROM (SELECT job_id
              FROM collect_jobs
              WHERE job_status = #{jobStatus}
                AND cluster_id = #{clusterId}
                AND TIME
                  > now( ) - INTERVAL '1 minutes'
              ORDER BY TIME DESC) tmp
    </select>
    <select id="getRunAndQueueJobList" resultType="com.cloudstar.service.pojo.dto.monitor.PoolJobStatusDto">
        select distinct job_id       as jobId,
                        job_status   as jobStatus,
                        job_duration as jobDuration,
                        waited_time  as waitedTime,
                        job_name     as jobName,
                        create_time  as createTime,
                        flavor_id    as flavorId,
                        project_id   as projectId
        from collect_jobs
        where time = (select max (time)
            from collect_jobs
            where cluster_id = #{clusterId}
          and pool_id = #{poolId})
          and cluster_id = #{clusterId}
          and pool_id = #{poolId}
          and job_status in ('QUEUING'
            , 'RUNNING')
        order by create_time asc
    </select>
    <select id="getAllJobByCluster" resultType="com.cloudstar.dao.model.bigscreen.CollectJobs">
        select job.*
        from (
                 select job_id, max(time) as maxTime
                 from collect_jobs
                 where cluster_id = #{clusterId}
                 group by job_id
                 order by maxTime desc limit 50
             ) temp
                 left join collect_jobs job on temp.job_id = job.job_id and temp.maxTime = job.time
        where job.cluster_id = #{clusterId};
    </select>


</mapper>
