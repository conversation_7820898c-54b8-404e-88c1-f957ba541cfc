<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.access.UserMenuMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.access.UserMenu">
        <id property="menuSid" column="menu_sid" jdbcType="BIGINT"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="engName" column="eng_name" jdbcType="VARCHAR"/>
        <result property="menuType" column="menu_type" jdbcType="VARCHAR"/>
        <result property="ico" column="ico" jdbcType="VARCHAR"/>
        <result property="routeType" column="route_type" jdbcType="VARCHAR"/>
        <result property="routePath" column="route_path" jdbcType="VARCHAR"/>
        <result property="modulePath" column="module_path" jdbcType="VARCHAR"/>
        <result property="meta" column="meta" jdbcType="VARCHAR"/>
        <result property="display" column="display" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="BIGINT"/>
        <result property="pathIds" column="path_ids" jdbcType="VARCHAR"/>
        <result property="lowerPathIds" column="lower_path_ids" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="sort" column="sort" jdbcType="SMALLINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        menu_sid,code,name,
        eng_name,menu_type,ico,
        route_type,route_path,module_path,
        meta,display,status,
        description,parent_id,path_ids,
        lower_path_ids,type,sort
    </sql>
</mapper>
