<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.bill.BizBillUsageItemMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.bill.BizBillUsageItem">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="billNo" column="bill_no" jdbcType="VARCHAR"/>
            <result property="orgSid" column="org_sid" jdbcType="BIGINT"/>
            <result property="jobId" column="job_id" jdbcType="BIGINT"/>
            <result property="billingCycle" column="billing_cycle" jdbcType="VARCHAR"/>
            <result property="billType" column="bill_type" jdbcType="VARCHAR"/>
            <result property="ownerSid" column="owner_sid" jdbcType="BIGINT"/>
            <result property="product" column="product" jdbcType="VARCHAR"/>
            <result property="usageStartDate" column="usage_start_date" jdbcType="TIMESTAMP"/>
            <result property="usageEndDate" column="usage_end_date" jdbcType="TIMESTAMP"/>
            <result property="usageCount" column="usage_count" jdbcType="VARCHAR"/>
            <result property="summaryFlag" column="summary_flag" jdbcType="CHAR"/>
            <result property="configuration" column="configuration" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,bill_no,org_sid,
        job_id,billing_cycle,bill_type,
        owner_sid,product,usage_start_date,
        usage_end_date,usage_count,summary_flag,
        configuration,version,created_by,
        created_dt,updated_by,updated_dt
    </sql>
</mapper>
