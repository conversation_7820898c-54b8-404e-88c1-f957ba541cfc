<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.UserEntityMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.UserEntity">
            <id property="userSid" column="user_sid" jdbcType="OTHER"/>
            <result property="uuid" column="uuid" jdbcType="VARCHAR"/>
            <result property="account" column="account" jdbcType="VARCHAR"/>
            <result property="password" column="password" jdbcType="VARCHAR"/>
            <result property="realName" column="real_name" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="userType" column="user_type" jdbcType="SMALLINT"/>
            <result property="parentSid" column="parent_sid" jdbcType="BIGINT"/>
            <result property="authStatus" column="auth_status" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="lastLoginTime" column="last_login_time" jdbcType="TIMESTAMP"/>
            <result property="lastLoginIp" column="last_login_ip" jdbcType="VARCHAR"/>
            <result property="startTime" column="start_time" jdbcType="TIMESTAMP"/>
            <result property="endTime" column="end_time" jdbcType="TIMESTAMP"/>
            <result property="passwordExpiresAt" column="password_expires_at" jdbcType="TIMESTAMP"/>
            <result property="isResetPassword" column="is_reset_password" jdbcType="SMALLINT"/>
            <result property="version" column="version" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
            <result property="accountStatus" column="account_status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        user_sid,uuid,account,
        password,real_name,email,
        mobile,user_type,parent_sid,
        auth_status,status,last_login_time,
        last_login_ip,start_time,end_time,
        password_expires_at,is_reset_password,version,
        created_by,created_dt,updated_by,
        updated_dt
    </sql>

    <sql id="Base_Column_List_1">
        A.user_sid,A.uuid,A.account,
        A.password,A.real_name,A.email,
        A.mobile,A.user_type,A.parent_sid,
        A.auth_status,A.status,A.last_login_time,
        A.last_login_ip,A.start_time,A.end_time,
        A.password_expires_at,A.is_reset_password,A.version,
        A.created_by,A.created_dt,A.updated_by,
        A.updated_dt
    </sql>

    <select id="selectPermissionByUserSid" resultType="com.cloudstar.pojo.user.dto.PermissionDTO">
        SELECT e.url path, method method
        FROM user_entity A,
             sys_m_policy_user b,
             sys_m_policy_assertion C,
             user_assertion_permission d,
             user_permission e
        WHERE A.user_sid = b.user_sid
          AND b.policy_sid = C.policy_sid
          AND C.assertion_sid = d.assertion_sid
          AND d.permission_sid = e.permission_sid
          and a.user_sid = #{userSid}

        union

        SELECT e.url path, method method
        FROM
            user_entity_group b,
            sys_m_policy_group f ,
            sys_m_policy_assertion C,
            user_assertion_permission d,
            user_permission e
        WHERE
            b.group_sid = f.group_sid
          AND f.policy_sid = C.policy_sid
          AND C.assertion_sid = d.assertion_sid
          AND d.permission_sid = e.permission_sid
          and b.user_sid = #{userSid}
    </select>
    <select id="selectPublicPermission" resultType="com.cloudstar.pojo.user.dto.PermissionDTO">
        SELECT e.url    path,
               e.method method
        FROM
            user_permission e
        WHERE
            e.is_public = 'public'
    </select>
    <select id="selectAllPermission" resultType="com.cloudstar.pojo.user.dto.PermissionDTO">
        SELECT e.url    path,
               e.method method
        FROM
            user_permission e
    </select>
    <select id="selectByAccount" resultType="com.cloudstar.dao.model.UserEntity">
        select user_sid, user_type from user_entity A where A.account = #{account}
    </select>
</mapper>
