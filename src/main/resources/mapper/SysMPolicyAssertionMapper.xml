<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.access.SysMPolicyAssertionMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.access.SysMPolicyAssertion">
            <id property="assertionSid" column="assertion_sid" jdbcType="BIGINT"/>
            <id property="policySid" column="policy_sid" jdbcType="BIGINT"/>
            <result property="assertionExpression" column="assertion_expression" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        assertion_sid,policy_sid,assertion_expression,
        type,version,created_by,
        created_dt,updated_by,updated_dt
    </sql>
</mapper>
