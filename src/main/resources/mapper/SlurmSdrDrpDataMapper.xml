<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.sdr.SlurmSdrDrpDataMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.sdr.SlurmSdrDrpData">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="recordType" column="record_type" jdbcType="VARCHAR"/>
            <result property="timeStamp" column="time_stamp" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="regionCode" column="region_code" jdbcType="VARCHAR"/>
            <result property="azCode" column="az_code" jdbcType="VARCHAR"/>
            <result property="cloudServiceTypeCode" column="cloud_service_type_code" jdbcType="VARCHAR"/>
            <result property="resourceTypeCode" column="resource_type_code" jdbcType="VARCHAR"/>
            <result property="resourceSpecCode" column="resource_spec_code" jdbcType="VARCHAR"/>
            <result property="resourceId" column="resource_id" jdbcType="VARCHAR"/>
            <result property="csbparams" column="csbparams" jdbcType="VARCHAR"/>
            <result property="beginTime" column="begin_time" jdbcType="VARCHAR"/>
            <result property="endTime" column="end_time" jdbcType="VARCHAR"/>
            <result property="accumulateFactorName" column="accumulate_factor_name" jdbcType="VARCHAR"/>
            <result property="accumulateFactorValue" column="accumulate_factor_value" jdbcType="VARCHAR"/>
            <result property="extendParams" column="extend_params" jdbcType="VARCHAR"/>
            <result property="tag" column="tag" jdbcType="VARCHAR"/>
            <result property="enterprisePorjectId" column="enterprise_porject_id" jdbcType="VARCHAR"/>
            <result property="accountUuid" column="account_uuid" jdbcType="VARCHAR"/>
            <result property="billFlag" column="bill_flag" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
            <result property="adapterUuid" column="adapter_uuid" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,record_type,time_stamp,
        user_id,region_code,az_code,
        cloud_service_type_code,resource_type_code,resource_spec_code,
        resource_id,csbparams,begin_time,
        end_time,accumulate_factor_name,accumulate_factor_value,
        extend_params,tag,enterprise_porject_id,
        account_uuid,bill_flag,version,
        created_by,created_dt,updated_by,
        updated_dt,adapter_uuid
    </sql>
</mapper>
