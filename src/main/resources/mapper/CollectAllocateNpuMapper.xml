<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.bigscreen.CollectAllocateNpuMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.bigscreen.CollectAllocateNpu">
        <result property="clusterId" column="cluster_id" jdbcType="BIGINT"/>
        <result property="time" column="time" jdbcType="TIMESTAMP"/>
        <result property="poolId" column="pool_id" jdbcType="VARCHAR"/>
        <result property="poolAllocated" column="pool_allocated" jdbcType="OTHER"/>
        <result property="poolCapacity" column="pool_capacity" jdbcType="OTHER"/>
    </resultMap>

    <resultMap id="NpuResourceMap" type="com.cloudstar.service.pojo.dto.bigscreen.CollectCommonResourceDto">
        <result property="timeSection" column="time_section" jdbcType="DATE"/>
        <result property="clusterId" column="cluster_id" jdbcType="BIGINT"/>
        <result property="totalAllocated" column="total_allocated" jdbcType="INTEGER"/>
        <result property="totalFree" column="total_free" jdbcType="INTEGER"/>
        <result property="totalCapacity" column="total_capacity" jdbcType="INTEGER"/>
        <result property="allocatedRatio" column="allocated_ratio" jdbcType="DOUBLE"/>
    </resultMap>

    <sql id="Base_Column_List">
        cluster_id
        ,time,pool_id,
        pool_allocated,pool_capacity
    </sql>

    <select id="getNpuResource" resultType="com.cloudstar.service.pojo.dto.bigscreen.CollectCommonResourceDto">
        select time_bucket('${monitorCollect} minutes', "time") as time_section,
               coalesce(sum(pool_allocated), 0)                 as total_allocated,
               coalesce(sum(pool_capacity), 0)                  as total_capacity
        from collect_allocate_npu
        where cluster_id = #{clusterId}
        group by time_section
        order by time_section desc limit 1;
    </select>

    <select id="getPoolNpuResource" resultType="com.cloudstar.service.pojo.dto.bigscreen.CollectCommonResourceDto">
        select time_bucket('${monitorCollect} minutes', "time") as time_section,
               coalesce(sum(pool_allocated), 0)                 as total_allocated,
               coalesce(sum(pool_capacity), 0)                  as total_capacity
        from collect_allocate_npu
        where pool_id = #{poolId}
        group by time_section
        order by time_section desc limit 1;
    </select>

    <select id="getExclusiveNpuResource" resultType="com.cloudstar.service.pojo.dto.bigscreen.CollectCommonResourceDto">
        select time_bucket('${monitorCollect} minutes', "time") as time_section, coalesce(sum(pool_allocated),0) as total_allocated,
        coalesce(sum(pool_capacity),0) as total_capacity
        from collect_allocate_npu
        where cluster_id = #{clusterId}
        <if test="poolId != null">
            and pool_id &lt;&gt; #{poolId}
        </if>
        group by time_section order by time_section desc limit 1;
    </select>

    <select id="getNpuAllocatedResourceTrend"
        resultType="com.cloudstar.service.pojo.dto.bigscreen.CollectCommonResourceDto">
        select time_section,
        locf(case when pool_allocated1 &lt;=0 then 0 else cast(coalesce(pool_allocated1,0) as decimal (13,2)) end) as totalAllocated,
        locf( case when pool_capacity1 &lt;=0 or pool_allocated1 &lt;=0 then 0 else coalesce(cast(cast(pool_allocated1 as numeric) /
        cast(pool_capacity1 as numeric) as decimal(13,4)),0) end ) as allocatedRatio from
        (select
        case when max(pool_allocated) > max(pool_capacity) then max(pool_capacity) else max(pool_allocated) end as pool_allocated1,
        max(pool_capacity) as pool_capacity1,
        time_bucket_gapfill('${trendMonitorCollect} minutes', time1, now()-interval
        <if test="type == 'today'">
            '1 day'
        </if>
        <if test="type == 'sevenDay'">
            '6 day'
        </if>
        ,now()) as time_section from
        (select time_bucket_gapfill ('${monitorCollect} minutes', time, now() - interval
        <if test="type == 'today'">
            '1 day'
        </if>
        <if test="type == 'sevenDay'">
            '6 day'
        </if>
        , now()) as time1,
        sum(pool_allocated) + #{excluseiveNum} as pool_allocated ,
        GREATEST(sum(pool_capacity),#{capacity}) as pool_capacity
        from collect_allocate_npu can
        where cluster_id = #{clusterId}
        <if test="poolId != null">
            and pool_id = #{poolId}
        </if>
        and time > now() - interval
        <if test="type == 'today'">
            '1 day'
        </if>
        <if test="type == 'sevenDay'">
            '6 day'
        </if>
        group by time1 order by time1 asc) as t1
        group by time_section order by time_section asc) as t2;

    </select>
    <select id="getPoolByClusterId" resultType="java.lang.String">
        select pool_id
        from collect_allocate_npu
        where cluster_id = #{clusterId}
          and time >= now() - interval '1 h'
        group by pool_id
    </select>
</mapper>
