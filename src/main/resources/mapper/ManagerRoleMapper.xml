<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.ManagerRoleMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.ManagerRole">
        <result property="roleSid" column="role_sid" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createById" column="create_by_id" jdbcType="BIGINT"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedById" column="updated_by_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="status" column="status" jdbcType="VARCHAR"/>
        <result property="code" column="code" jdbcType="VARCHAR"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="presenceStatus" column="presence_status" jdbcType="CHAR"/>
        <result property="roleType" column="role_type" jdbcType="VARCHAR"/>
        <result property="defaultNavigate" column="default_navigate" jdbcType="VARCHAR"/>
        <collection property="permissions" ofType="java.lang.Long" select="selectPermissions" column="role_sid">
            <result column="permission_sid"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        role_sid
        ,name,description,
        create_by,create_by_id,updated_by,
        updated_by_id,create_time,update_time,
        status,code,type,
        presence_status,role_type
    </sql>

</mapper>
