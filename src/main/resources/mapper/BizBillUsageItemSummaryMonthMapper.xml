<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.bill.BizBillUsageItemSummaryMonthMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.bill.BizBillUsageItemSummaryMonth">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="billNo" column="bill_no" jdbcType="VARCHAR"/>
        <result property="billingCycle" column="billing_cycle" jdbcType="VARCHAR"/>
        <result property="billType" column="bill_type" jdbcType="VARCHAR"/>
        <result property="productCode" column="product_code" jdbcType="VARCHAR"/>
        <result property="orgSid" column="org_sid" jdbcType="BIGINT"/>
        <result property="ownerSid" column="owner_sid" jdbcType="BIGINT"/>
        <result property="usageCount" column="usage_count" jdbcType="NUMERIC"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,bill_no,billing_cycle,
        bill_type,product_code,org_sid,
        owner_sid,usage_count,version,
        created_by,created_dt,updated_by,
        updated_dt
    </sql>
    <sql id="UsageSummaryMonthList">
        SELECT
        bsm.billing_cycle as billingCycle,
        bsm.product_code as product,
        bsm.usage_count as usageCount,
        ue.account as ownerName,
        bsm.bill_type as billType
        FROM
        biz_bill_usage_item_summary_month bsm
        LEFT JOIN user_entity ue ON bsm.owner_sid = ue.user_sid
        <where>
            <if test="req.startDate != null and req.startDate!= ''">
                and bsm.billing_cycle &gt;= #{req.startDate}
            </if>
            <if test="req.endDate != null and req.endDate != ''">
                and bsm.billing_cycle &lt;= #{req.endDate}
            </if>
            <if test="req.billingCycle != null and req.billingCycle !=''">
                and bsm.billing_cycle = #{req.billingCycle}
            </if>
            <if test="req.product != null and req.product != ''">
                and bsm.product_code in
                <foreach collection="req.product" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.userSid != null and req.userSid != ''">
                and bsm.owner_sid = #{req.userSid}
            </if>
        </where>
        <if test="req.sortDataField != null and req.sortDataField != ''">
            order by ${req.sortDataField}
        </if>
        <!-- 默认时间降序 -->
        <if test="req.sortDataField == null or req.sortDataField == ''">
            order by bsm.billing_cycle desc
        </if>
    </sql>
    <select id="getBillUsageSummaryMonthList"
        resultType="com.cloudstar.service.pojo.vo.responsevo.bill.BizBillUsageItemSummaryPageResp">
        <include refid="UsageSummaryMonthList"></include>
    </select>
    <select id="getBillSummaryMonthExportList"
        resultType="com.cloudstar.service.pojo.vo.responsevo.bill.BizBillUsageItemSummaryExportResp">
        <include refid="UsageSummaryMonthList"></include>
    </select>
</mapper>
