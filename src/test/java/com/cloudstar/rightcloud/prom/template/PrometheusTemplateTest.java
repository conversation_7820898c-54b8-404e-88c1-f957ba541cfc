package com.cloudstar.rightcloud.prom.template;

import cn.hutool.core.collection.ListUtil;
import com.cloudstar.rightcloud.prom.datasource.AbstractPrometheusDatasource;
import com.cloudstar.rightcloud.prom.datasource.entity.DataSourceProperty;
import com.cloudstar.rightcloud.prom.datasource.enums.DatasourceType;
import com.cloudstar.rightcloud.prom.datasource.prometheus.DefaultAbstractPrometheusDatasource;
import com.cloudstar.rightcloud.prom.datasource.prometheus.DefaultPrometheusDatasource;
import com.cloudstar.rightcloud.prom.datasource.prometheus.PrometheusDatasource;
import com.cloudstar.rightcloud.prom.datasource.prometheus.entity.PrometheusQueryResult;
import com.cloudstar.rightcloud.prom.datasource.prometheus.entity.PrometheusRangeResult;
import com.cloudstar.rightcloud.prom.datasource.prometheus.entity.PrometheusResult;
import com.cloudstar.rightcloud.prom.datasource.prometheus.entity.PrometheusWriter;
import com.cloudstar.rightcloud.prom.datasource.prometheus.entity.PromqlLabelResult;
import com.cloudstar.rightcloud.prom.datasource.prometheus.enums.PrometheusExpr;
import com.cloudstar.rightcloud.prom.datasource.prometheus.enums.PrometheusTime;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.GropsName;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.Promqls;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.conditions.LambdaQueryBasePromql;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.util.StringUtils;
import com.cloudstar.rightcloud.prom.datasource.properties.DatasourceConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

class PrometheusTemplateTest {


    private PrometheusTemplate prometheusTemplate;

    private AbstractPrometheusDatasource prometheusDatasource;

    private PrometheusDatasource datasource;

    @BeforeEach
    public void init() {
        DatasourceConfig datasourceConfig = new DatasourceConfig();
        Map<String, List<DataSourceProperty>> map = new HashMap<>();
        List<DataSourceProperty> list = new ArrayList<>();
        DataSourceProperty dataSourceProperty = new DataSourceProperty();
        dataSourceProperty.setUrl("https://10.106.9.120:31102/select/0/prometheus");
        dataSourceProperty.setDefaults(true);
        dataSourceProperty.setWriteAddr("https://10.106.9.120:30511/insert/0/prometheus");
        dataSourceProperty.setUser("admin");
        dataSourceProperty.setPassword("Q_q5wiSx#IB5LNMW");
        list.add(dataSourceProperty);
        map.put(DatasourceType.PROMETHEUS.getType(), list);
        datasourceConfig.setDatasource(map);
        datasource = new DefaultPrometheusDatasource(datasourceConfig);
        prometheusDatasource = new DefaultAbstractPrometheusDatasource(datasource);
        prometheusTemplate = new PrometheusTemplate(prometheusDatasource);

    }


    @Test
    void testPromql() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusQueryResult> promql = prometheusTemplate.promql(
                wrapper
        );
        assert promql.getSuccess();
    }


    @Test
    void testPromqlOr() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .avgOverTime("cpu_usage_max")
                .or()
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusQueryResult> promql = prometheusTemplate.promql(
                wrapper
        );
        assert promql.getSuccess();
    }

    @Test
    void testPromqlAnd() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .avgOverTime("cpu_usage_max")
                .and()
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusQueryResult> promql = prometheusTemplate.promql(
                wrapper
        );
        assert promql.getSuccess();
    }

    @Test
    void testPromqlTime() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusQueryResult> promql = prometheusTemplate.promql(
                wrapper,
                new Date()
        );
        assert promql.getSuccess();

    }

    @Test
    void testPromqlTimeOr() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .avgOverTime("cpu_usage_max")
                .or()
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusQueryResult> promql = prometheusTemplate.promql(
                wrapper,
                new Date()
        );
        assert promql.getSuccess();

    }

    @Test
    void testPromqlTimeAnd() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .avgOverTime("cpu_usage_max")
                .and()
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusQueryResult> promql = prometheusTemplate.promql(
                wrapper,
                new Date()
        );
        assert promql.getSuccess();

    }

    @Test
    void testPromqlStep() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusQueryResult> promql = prometheusTemplate.promql(
                wrapper,
                new Date(),
                10
        );
        assert promql.getSuccess();
    }


    @Test
    void testPromqlStepOr() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .avgOverTime("cpu_usage_max")
                .or()
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusQueryResult> promql = prometheusTemplate.promql(
                wrapper,
                new Date(),
                10
        );
        assert promql.getSuccess();
    }

    @Test
    void testPromqlStepAnd() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .avgOverTime("cpu_usage_max")
                .and()
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusQueryResult> promql = prometheusTemplate.promql(
                wrapper,
                new Date(),
                10
        );
        assert promql.getSuccess();
    }


    @Test
    void testPromqlStarAndTime() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusRangeResult> promql = prometheusTemplate.promql(
                wrapper,
                new Date(),
                new Date(),
                10
        );
        assert promql.getSuccess();
    }

    @Test
    void testPromqlStarAndTimeOr() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .avgOverTime("cpu_usage_max")
                .or()
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusRangeResult> promql = prometheusTemplate.promql(
                wrapper,
                new Date(),
                new Date(),
                10
        );
        assert promql.getSuccess();
    }

    @Test
    void testPromqlStarAndTimeAnd() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .avgOverTime("cpu_usage_max")
                .and()
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        PrometheusResult<PrometheusRangeResult> promql = prometheusTemplate.promql(
                wrapper,
                new Date(),
                new Date(),
                10
        );
        assert promql.getSuccess();
    }

    @Test
    void checkPromql() {
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.DAY, "10")
                .avgOverTime("cpu_usage_max")
                .or()
                .countOverTime("cpu_usage_max", PrometheusExpr.GE, "1");
        Boolean result = prometheusTemplate.checkPromql(
                wrapper.getPromql()
        );
        assert result;
    }

    @Test
    void getLabel() {
        PrometheusResult<List<PromqlLabelResult>> nodeArpEntries = prometheusTemplate.getLabel("__name__");
        assert nodeArpEntries != null;
        assert nodeArpEntries.getSuccess();
        assert !nodeArpEntries.getData().isEmpty();
    }

    @Test
    void testWriter() {
        PrometheusWriter writer = new PrometheusWriter();
        writer.setValue(1.10);
        writer.setMetricName("cpu_usaegs");
        Map<String, String> labels = new HashMap<>();
        labels.put("policy", "hours");
        writer.setLabels(labels);
        final PrometheusResult<Boolean> result = prometheusTemplate.writer(writer);
        assert result.getData();
    }

    @Test
    void testWriters() {
        List<PrometheusWriter> writers = new ArrayList<>();
        Map<String, String> labels = new HashMap<>();
        labels.put("policy", "hours");
        PrometheusWriter cpuWriter = new PrometheusWriter();
        cpuWriter.setValue(2.10);
        cpuWriter.setMetricName("cpu_usaegs");
        cpuWriter.setLabels(labels);
        writers.add(cpuWriter);
        PrometheusWriter diskWriter = new PrometheusWriter();
        diskWriter.setValue(5.10);
        diskWriter.setMetricName("disk_usaegs");
        labels.put("policy", "hours");
        diskWriter.setLabels(labels);
        writers.add(diskWriter);
        final PrometheusResult<Boolean> result = prometheusTemplate.writer(writers);
        assert result.getData();
    }

    @Test
    void testNeq() {
        final LambdaQueryBasePromql<GropsName> count
                = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.SECOND, "10")
                .neq(GropsName::getName, "asdsa")
                .countOverTime("cpu_usage_max");
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .sum(count);
        PrometheusResult<PrometheusRangeResult> promql = prometheusTemplate.promql(
                wrapper,
                new Date(),
                new Date(),
                10
        );
        assert promql.getSuccess();
    }

    @Test
    void testOr() {
        List<LambdaQueryBasePromql<GropsName>> promqlList = new ArrayList<>();
        // 告警触发规则
        final LambdaQueryBasePromql<GropsName> promql =
                Promqls.<GropsName>lambdaQuery(GropsName.class);
        final LambdaQueryBasePromql<GropsName> opsAlarmRuleResultDtoLambdaQueryBasePromql =
                Promqls.<GropsName>lambdaQuery(GropsName.class);
        // 告警对象类型
        GropsName opsAlarmRuleResultDto=new GropsName();
        opsAlarmRuleResultDtoLambdaQueryBasePromql.eq(GropsName::getResourceType,
               "ECS");
        // 告警对象类型范围:指定云环境下的告警对象
        opsAlarmRuleResultDtoLambdaQueryBasePromql.in(GropsName::getEnvId,
                "21313131231");
        // 告警对象类型范围:所有告警对象
        //获取子集组织id
        opsAlarmRuleResultDtoLambdaQueryBasePromql.in(GropsName::getOrgId, List.of("12213","21312312"));

        final LambdaQueryBasePromql<GropsName> avgOverTimePromql1 =
                Promqls
                        .<GropsName>lambdaQuery(
                                GropsName.class)
                        .time(PrometheusTime.MONTH, "10")
                        .lastOverTime("memory_usage",
                                PrometheusExpr.GE, "90",
                                opsAlarmRuleResultDtoLambdaQueryBasePromql);
        promqlList.add(avgOverTimePromql1);

        final LambdaQueryBasePromql<GropsName> avgOverTimePromql2 =
                Promqls
                        .<GropsName>lambdaQuery(
                                GropsName.class)
                        .time(PrometheusTime.MONTH, "10")
                        .lastOverTime("cpu_usage",
                                PrometheusExpr.GE, "90",
                                opsAlarmRuleResultDtoLambdaQueryBasePromql);
        promqlList.add(avgOverTimePromql2);
        for (LambdaQueryBasePromql<GropsName> lambdaQueryBasePromql : promqlList) {
            final String sqlString = lambdaQueryBasePromql.getSqlString();
            promql
                    .and()
                    .sql(sqlString);
        }
        final LambdaQueryBasePromql<GropsName> count
                = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .time(PrometheusTime.SECOND, "10")
                .neq(GropsName::getName, "asdsa")
                .countOverTime("cpu_usage_max");
        final LambdaQueryBasePromql<GropsName> wrapper = Promqls.<GropsName>lambdaQuery(GropsName.class)
                .sum(count);


        System.out.println(wrapper.getSqlString());
        System.out.println(promql.getSqlString());
    }
}