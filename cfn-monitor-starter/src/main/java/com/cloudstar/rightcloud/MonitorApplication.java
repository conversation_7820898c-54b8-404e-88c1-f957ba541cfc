package com.cloudstar.rightcloud;

import com.cloudstar.common.jasypt.configuration.detector.CloudStarEncryptablePropertyDetector;
import com.cloudstar.common.jasypt.configuration.encryptor.DefaultEncryptor;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.Banner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.Map;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import cn.hutool.core.util.StrUtil;

/**
 * 运维监控服务启动类.
 * @date: 2022-12-1
 * <AUTHOR>
@MapperScan("com.cloudstar.rightcloud.monitor.data.**.mapper")
@SpringBootApplication(scanBasePackages = {"com.cloudstar"})
@EnableTransactionManagement
@EnableFeignClients
@EnableAsync
public class MonitorApplication {

    /**
     * 启动函数主入口
     *
     * @param args 入参
     */
    public static void main(String[] args) {
        System.out.println("[运维监控服务]开始启动...");
        initCerVerify();
        SpringApplication app = new SpringApplication(MonitorApplication.class);
        app.setBannerMode(Banner.Mode.OFF);
        // app.run(args);
        try {
            app.run(args);
        } catch (Throwable t) {
            // 打印完整堆栈跟踪
            t.printStackTrace();
        }
        System.out.println("[运维监控服务]启动完成...");
    }


    /**
     * CA证书验证
     */
    private static void initCerVerify() {
        String caTrustorePath = System.getenv("CMP_CA_TRUSTORE_PATH");
        String caTrustorePassword = System.getenv("CMP_CA_TRUSTORE_PASSWORD");
        // 验证 CA 是否合法
        if (StrUtil.isNotEmpty(caTrustorePath)) {
            System.setProperty("javax.net.ssl.trustStore", caTrustorePath);
            CloudStarEncryptablePropertyDetector propertyDetector = new CloudStarEncryptablePropertyDetector();
            DefaultEncryptor encryptor = new DefaultEncryptor(propertyDetector);
            if (propertyDetector.isEncrypted(caTrustorePassword)) {
                System.setProperty("javax.net.ssl.trustStorePassword", encryptor.decrypt(caTrustorePassword));
            } else {
                System.setProperty("javax.net.ssl.trustStorePassword", caTrustorePassword);
            }
        }
        // 验证 CRL
        Map<String, String> envMap = System.getenv();
        String sslCheckRevocation = envMap.getOrDefault("SSL_CHECK_REVOCATION", "false");
        if (StrUtil.isNotEmpty(sslCheckRevocation) && "true".equalsIgnoreCase(sslCheckRevocation)) {
            System.setProperty("com.sun.net.ssl.checkRevocation", "true");
            System.setProperty("com.sun.security.enableCRLDP", "true");
        }
    }

    /**
     * FindBugs#由于有的监控指标数据获取接口需要跳过证书验证
     */
    @edu.umd.cs.findbugs.annotations.SuppressFBWarnings("SSL_CONTEXT")
    @Bean
    public SSLSocketFactory skipSslFactory() throws NoSuchAlgorithmException, KeyManagementException {
        // 创建信任所有证书的TrustManager
        //FindBugs#由于有的监控指标数据获取接口需要跳过证书验证
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[0];
                    }
                    @Override
                    public void checkClientTrusted(X509Certificate[] certs, String authType) { }
                    @Override
                    public void checkServerTrusted(X509Certificate[] certs, String authType) { }
                }
        };

        // 初始化SSLContext
        SSLContext sslContext = SSLContext.getInstance("SSL");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

        // 获取SSLSocketFactory
        SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
        return sslSocketFactory;
    }

}
