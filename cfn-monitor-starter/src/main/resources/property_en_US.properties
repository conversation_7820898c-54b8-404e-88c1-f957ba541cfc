# Common
scheduleTask=Scheduled task
outputDataMax=10000
# Monitoring
id=ID
alarmBlocking=Alarm shielding data
notifyPolicy=Notification policy data
notifyUpgradePolicy=Alarm notification upgrade policy
alarmNotificationGroupAssociatedWithNotifier=Association between alarm notification group and notifier
notifyUpgradePolicyRule=Alarm upgrade rule data
notifyTarget=Notification target
alarmRule=Alarm rule
operationStatus=Operation and maintenance status
alarmStatus=Alarm status
objectScope=Alarm rule object scope
triggerRule=Alarm trigger rule
alarmRuleCondition=Alarm condition
detectionType=Alarm detection type
notifyTargetType=Notification type
groupName=Group name
notificationGroup=Notification group
alarmContacts=Alarm contacts
collectTask=Monitoring collection task
cloudEnvId=Cloud environment ID
cloudEnv=Cloud environment
collectTaskName=Collection task name
exporter=Monitoring collection exporter
exporterParam=Monitoring collection exporter parameters
exporterName=Exporter name
alarmRuleName=Alarm rule name
alarmObjectType=Alarm object type
alarmObjectCode=Alarm object code
alarmDuration=Alarm duration
alarmLevel=Alarm level
alarmContent=Alarm content
monitorMetric=Collection indicator
processingType=Alarm processing type
processingId=Alarm processing ID
monitorMetricId=Indicator ID
functionOperator=Function operator
operator=Operator
broadValue=Threshold
dataRangeTime=Data range time
userName=User name
userPhone=Mobile phone number
userEmail=Email address
monitorMetricName=Collection indicator name
monitorMetricStatus=Collection indicator status
monitorMetricUnifiedCoding=Collection indicator unified coding
monitorMetricOriginalCoding=Collection indicator original coding
monitorMetricEnvCode=Collection indicator cloud platform
monitorMetricEnvVersion=Collection indicator cloud platform version number
monitorMetricResTypeCode=Collection indicator cloud resource
monitorMetricUnit=Collection indicator unit
monitorMetricSortRank=Collection indicator sorting code
alarmDataName=Alarm name
alarmTarget=Alarm object
alarmTargetType=Alarm object type
alarmDataLevel=Alarm level
alarmDataStatus=Alarm status
alarmDataRecoverDate=Alarm recovery time
alarmCount=Alarm count
alarmNotifyPolicyName=Alarm notification policy
alarmDataStartTime=Start time
alarmDataOccurTime=Latest occurrence time
alarmDataDuration=Duration
alarmDataProcessingStatus=Alarm processing status
alarmDataProcessingUser=Alarm processing user
alarmDataNotifyStatus=Notification status
alarmDataOrgName=Owning organization
alarmDataConfirmTime=Confirmation time
alarmDataSource=Alarm source
exporterComponentName=Component name
exporterCollectInterval=Exporter collection frequency
exporterTimeout=Exporter timeout time
envCode=Cloud platform
envVersion=Cloud platform version number
collectorId=Collector ID
collectorName=Collector name
collectTaskInterval=Collection task period
collectTaskTimeout=Collection task timeout time
exporterId=Collection component ID
collectTaskStatus=Collection task status
userInfo=User information
resTypeCode=Resource type
collectRuleDimensionId=Collection rule dimension ID
collectRuleDimension=Collection rule dimension
collectRuleId=Collection rule ID
collectRule=Collection rule
collectRuleName=Collection rule name
monitorAccessEnv=Monitored access cloud environment
collectRuleDimensionCode=Collection rule dimension coding
collectRuleDimensionSortRank=Collection rule dimension sorting code
collectRuleDimensionCondition=Collection rule dimension condition
collectRuleDimensionConditionResTypeInstanceCode=Collection rule dimension condition resource instance coding
collectRuleDimensionConditionOperator=Collection rule dimension condition operator
collectRuleDimensionConditionAlignmentValue=Collection rule dimension condition comparison value
collectRuleDimensionConditionLogicOperator=Collection rule dimension condition logic operator
collectTaskParamName=Collection task parameter name
collectTaskFieldName=Collection task parameter field name
originAlarmMetric=Native alarm indicator
alarmSource=Alarm source
alarmClassification=Alarm classification
alarmProductComponent=Alarm product component
alarmCode=Alarm code
originAlarmMetricName=Original alarm name
originAlarmMetricCategory=Native alarm indicator category
originAlarmMetricCategoryCode=Native alarm indicator category coding
originAlarmMetricCategoryName=Native alarm indicator category name
originAlarmMetricCategorySortRank=Native alarm indicator category sorting code
originAlarmMetricCategoryParent=Parent native alarm indicator category
collectorPath=Collection path
originAlarm=Original alarm
originAlarmId=Original alarm ID
starTime=Start time
endTime=End time
categoryCode=Category coding
categoryInstanceId=Category instance ID
alarmDataTarget=Alarm object
alarmDataTargetType=Alarm object type
alarmDataCount=Alarm count
alarmDataNotifyPolicyName=Alarm notification policy
policy=Policy data
examineRuleStrategy=Assessment rule strategy
examineStrategyId=Assessment rule strategy ID
examineType=Assessment type
examineItem=Scoring item
examineRule=Assessment rule
examineRules=Assessment rule list
status=Status
envStatisticStrategy=Environment statistics strategy
performanceMetricIndicatorCode=Aggregated indicator type
usageBaselineEnable=Whether to enable usage rate data cleaning baseline
statisticDataPeak=Statistical data peak
statisticDataPeakSourceType=Statistical data peak source type
statisticDataPeakTargetType=Statistical data peak target type
hourStatisticMetricStrategy=Hourly performance aggregated indicator strategy
dayStatisticMetricStrategy=Daily performance aggregated indicator strategy
titleFiled=Table header attribute
morHourStatisticsLog=Hourly aggregation task log
monitorCollectMetricId=Monitoring collection indicator ID
monitorCollectMetricName=Monitoring collection indicator
mergeTime=Aggregation time
startTime=Start time
errorMsg=Error message
createBy=Operator
resPool=Resource pool
commonMonitorMetric=Common collection indicator
commonMonitorMetricCode=Common collection indicator coding
commonMonitorMetricName=Common collection indicator name

projectName=Project name
resUuid=Resource ID
resName=Resource name
resConfig=Resource configuration
usedTime=Resource usage duration
recommendConfig=Recommended configuration
cost=Resource cost
costDown=Cost difference after optimization
inquiryUnit=Inquiry unit
chargeType=Billing mode
optimizationType=Optimization type
optimizationDetail=Optimization details
strategyName=Strategy name
createdDt=Generation time
cloudEnvName=Cloud environment name
eventName=Security event name
eventType=Security event type
eventLevel=Security event level
reliabilityLevel=Security event credibility level
eventTime=Security event occurrence time
desIp=Destination IP of security event
desPort=Destination port of security event
srcIp=Source IP of security event
srcPort=Source port of security event
confirmUser=Handler
securityEventExportName=Security event
resourceName=Resource name
orgName=Organization name
recoveredTime=Recovery time
confirmTime=Handling time
productComponents=Product components
convertState=Conversion status
alarmDataOriginLastTime=Occurrence time
prometheusQuery=query
prometheusTime=time
time=time
