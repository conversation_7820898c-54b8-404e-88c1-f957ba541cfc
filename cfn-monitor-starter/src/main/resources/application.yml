common:
  excel:
    max-data-size: 100000
spring:
  config:
    activate:
      on-profile: local
  main:
    allow-bean-definition-overriding: true
  datasource:
    dynamic:
      primary: master #设置默认的数据源或者数据源组,默认值即为master
      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.zaxxer.hikari.HikariDataSource
          url: jdbc:mysql://${mysql.host}:${mysql.port:3306}/${mysql.dbname}?useUnicode=true&autoReconnect=true&failOverReadOnly=false&characterEncoding=UTF-8&allowMultiQueries=true&useSSL=false&serverTimezone=Asia/Shanghai
          username: ${mysql.username}
          password: ${mysql.password}
        clickhouse:
          driver-class-name: com.clickhouse.jdbc.ClickHouseDriver
          username: ${clickhouse.username}
          password: ${clickhouse.password}
          url: jdbc:clickhouse://${clickhouse.host}:${clickhouse.port}/${clickhouse.dbname}
          iscluster: false
          clustername:
  redis:
    host: ${redis.host}
    port: ${redis.port}
    password: ${redis.password}
    database: ${redis.dbnum}
    jedis:
      pool:
        max-active: 128
        max-idle: 32
        min-idle: 1
  rabbitmq:
    host: ${rabbitmq.host}
    port: ${rabbitmq.port}
    username: ${rabbitmq.username}
    password: ${rabbitmq.password}
    requested-heartbeat: 60s
    publisher-confirm-type: correlated
    publisher-returns: true
  prometheus:
    config:
      datasource:
        prometheus:
          - defaults: true
            name: prometheus_default
            password: Q_q5wiSx#IB5LNMW
            url: https://************:31102/select/0/prometheus
            user: admin
            write-addr: https://vminsert:8480/insert/0/prometheus
  influx:
    url: http://${base-influx-host}:${base-influx-port}
    user: ${base-influx-user}
    password: ${base-influx-password}
    database: ${base-influx-database}

server:
  port: 9004
feign:
  client:
    enabled: true
  url:
    #cfn-console-server: 127.0.0.1:9003
    cfn-schedule: 127.0.0.1:9002
    cfn-iam: 127.0.0.1:9010
    cfn-server: 127.0.0.1:9001
---
server:
  servlet:
    context-path: /api/v1/monitor
mybatis-plus:
  mapper-locations: classpath*:com/cloudstar/rightcloud/monitor/data/**/mysql/*.xml
  type-aliases-package: com.cloudstar.rightcloud.monitor.common.bean.**.dto
  type-enums-package: com.cloudstar.rightcloud.monitor.common.em
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
#    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl

feign:
  circuitbreaker:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
resilience4j.timelimiter:
  configs:
    default:
      timeoutDuration: 10s
      cancelRunningFuture: true
#upload:
#  base:
#    path: /opt/cmp/files
#  url:
#    prefix: /files/
spring:
  application:
    name: cfn-monitor
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  data:
    mongodb:
      authentication-database: admin
      database: ${base-mongo-database}
      username: ${base-mongo-username}
      password: ${base-mongo-password}
      host: ${base-mongo-host}
      port: ${base-mongo-port}
#  sql:
#    init:
#file:
#  storage:
#    active: minio
#    local:
#      #      path: /opt/data/cmp/
#      #      prefix: /cmp/
#      path: C:/other/
#      prefix: cmp
file:
  storage:
    active: local
    local:
      path: /opt/data/cfn/
      prefix: /cfn/
#    minio:
#      accessKey: ${base-minio-accessKey}
#      secretKey: ${base-minio-secretKey}
#      endPoint: ${base-minio-url}:${base-minio-port}
#      bucketName: cmp-system
#      interceptUrl: minio-oss-storage
jwt:
  header: authorization
  # 令牌前缀
  token-start-with: Bearer
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/分钟 ，默认30分钟，
  expired-time: 60

logging:
  level:
    root: info
    com.cloudstar.sdk: debug
    com.cloudstar.dao.mapper: debug
## 日志参数外置配置
#log:
#  # 日志级别
#  level: ${log-level:DEBUG}
#  # 日志保存路径
#  filePath: ${log-filePath:/cmplog}
#  # 单个文件大小
#  maxFileSize: ${log-maxFileSize:100MB}
#  # 文件个数
#  maxHistory: ${log-maxHistory:30}
#  # 日志文件总大小
#  totalSizeCap: ${log-totalSizeCap:2GB}

grafana:
  url: ${grafana-url:http://grafana}
  path: ${grafana-path:}

web:
  around:
    aop:
      interface-skip:
        - /api/v1/monitor/access/web/hook
        - /api/v1/monitor/access/cloud/env