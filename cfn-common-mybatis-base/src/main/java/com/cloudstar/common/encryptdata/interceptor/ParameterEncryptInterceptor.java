package com.cloudstar.common.encryptdata.interceptor;


import com.cloudstar.common.encryptdata.encrypt.EncryptDecryptUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.MapperMethod.ParamMap;
import org.apache.ibatis.executor.parameter.ParameterHandler;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.springframework.stereotype.Component;
import org.springframework.util.ClassUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.sql.PreparedStatement;
import java.util.Properties;

@Slf4j
@Component
@Intercepts({
        @Signature(type = ParameterHandler.class, method = "setParameters", args = PreparedStatement.class)
})
public class ParameterEncryptInterceptor implements Interceptor {
    
    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        //拦截 ParameterHandler 的 setParameters 方法 动态设置参数
        if (invocation.getTarget() instanceof ParameterHandler) {
            ParameterHandler parameterHandler = (ParameterHandler) invocation.getTarget();
            
            // 反射获取 参数对像
            Field fields = ClassUtils.getUserClass(parameterHandler).getDeclaredField("parameterObject");
            ReflectionUtils.makeAccessible(fields);
            Object parameterObject = fields.get(parameterHandler);
            if (ObjectUtils.isEmpty(parameterObject)) {
                return invocation.proceed();
            }
            Class<?> parameterObjectClass = ClassUtils.getUserClass(parameterObject);
            //updateById,or updateBatchById导致拦截不到，需转换成ParamMap.class
            if (parameterObjectClass.equals(ParamMap.class)) {
                ParamMap<Object> paramMap = (ParamMap<Object>) parameterObject;
                Object paramObj = paramMap.getOrDefault("param1", null);
                Object batchParamsObj = paramMap.getOrDefault("et", null);
                if (!ObjectUtils.isEmpty(paramObj) && EncryptDecryptUtil.needDecryptOrDecrypt(paramObj)) {
                    final Object encrypt = EncryptDecryptUtil.encrypt(paramObj, true);
                } else if (!ObjectUtils.isEmpty(batchParamsObj) && EncryptDecryptUtil.needDecryptOrDecrypt(batchParamsObj)) {
                    final Object encrypt = EncryptDecryptUtil.encrypt(batchParamsObj, true);
                }
            }
            if (EncryptDecryptUtil.needDecryptOrDecrypt(parameterObject)) {
                final Object encrypt = EncryptDecryptUtil.encrypt(parameterObject, true);
            }
        }
        return invocation.proceed();
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }
    
    @Override
    public void setProperties(Properties properties) {
    }
}
