FROM swr.cn-east-3.myhuaweicloud.com/distroless/openjdk:jre-11-alpine.3.17.0-stable-arm

COPY target/cfn-console-server.jar app.jar
COPY docker/entrypoint.sh entrypoint.sh


RUN set -ex \
      && sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories && apk update  \
      && chmod 500 /entrypoint.sh \
      && chown cloudstar:cloudstar /app.jar entrypoint.sh \
      && mkdir /opt/cloudstar/ \
      && mkdir /output \
      && chown cloudstar:cloudstar /output -R \
      && chown cloudstar:cloudstar /opt/cloudstar/ -R \
      && chown cloudstar:cloudstar /home/<USER>/ -R \
      && chown cloudstar:cloudstar /home/<USER>/ -R \
      && rm -rf /tmp/* /var/tmp/* \
      && rm -rf /root/.cache .build-deps \
      && ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
      && chmod 400 /app.jar

#hook

ARG DROPUSER="sync halt shutdown operator"
ARG DROPCOMMAND="readelf objdump mirror ld rpcgen netcat make strace gdb gcc cpp tcpdump nc perl lua nm mirror javac jdb "
# RUN  for delusered in $DROPUSER ;do deluser $delusered ||true ;done \
#    && find / -name *.crt  |xargs rm -rf  \
#    && find / -name *.pem |xargs rm -f \
#    && find / -name *.gitignore |xargs rm -rf \
#    && rm -rf /etc/ssl /etc/pki /usr/bin/crontab /etc/crontab \
#    && for command in $DROPCOMMAND ;do command_path=`which $command`  rm -rf $command_path ||true ;done \
#    &&df | awk '{if (NR!=1) print $6}' | xargs -I '{}' find -L '{}' -xdev -type l 2>/dev/null  | sort | grep -Ev '^/run|^/proc|^/sys|^/dev' |xargs -I '{}' rm -f '{}' || echo ok

## 演示环境临时kubectl 命令行环境创建
COPY docker/kubectl /usr/bin/kubectl
COPY docker/config /home/<USER>/config
COPY docker/tensorflow-mobile-net-fashion.yaml /tmp/tensorflow-mobile-net-fashion.yaml
RUN chmod +x /usr/bin/kubectl 
USER cloudstar
ENTRYPOINT ["/entrypoint.sh"]
