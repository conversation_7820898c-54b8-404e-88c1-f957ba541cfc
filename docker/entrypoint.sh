#!/bin/sh

#
# Copyright (c) 2022. CloudStar.Co.Ltd. All rights reserved.
#
umask 0027
EXEC_PARAM_COMMAND=" -XX:TieredStopAtLevel=1 -noverify -Dspring.jmx.enabled=false -Djava.security.egd=file:/dev/./urandom "
EXEC_END_COMMAND="-jar app.jar"

EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dspring.profiles.active=$PROFILES_ACTIVE "

if [ $CRYPTO_KEY_PATH ]; then
    EXEC_PARAM_COMMAND=${EXEC_PARAM_COMMAND}" -Dcloudstar.key.path=$CRYPTO_KEY_PATH"
fi


if [ $SHOW_PARAM ];then
    echo "EXEC COMMAND:java" $JVM_OPTS $CFN_SERVER_OPTS ${EXEC_PARAM_COMMAND} ${EXEC_END_COMMAND}
fi
exec tini -- java $JVM_OPTS $CFN_SERVER_OPTS ${EXEC_PARAM_COMMAND} ${EXEC_END_COMMAND}
