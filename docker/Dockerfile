ARG BUILD_IMAGE
ARG RUNTIME_IMAGE
FROM ${BUILD_IMAGE:-swr.cn-east-3.myhuaweicloud.com/distroless/rightcloud-lib:openjdk11-j<PERSON><PERSON>-arm64} AS compiler

LABEL maintainer="ShiWenQiang <<EMAIL>>"

ARG PROJECT_PATH
ARG PARENT

COPY ${PARENT} /usr/share/parent
COPY ${PROJECT_PATH} /usr/share/monitor

WORKDIR /usr/share

RUN set -ex \
      && cd /usr/share/parent \
      && mvn clean install -T 4C -s /usr/share/maven/ref/settings.xml  -Dmaven.test.skip=true \
      && cd /usr/share/monitor \
      && mvn clean install -X -T 4C -P k8s_env -s /usr/share/maven/ref/settings.xml  -Dmaven.test.skip=true 

ARG RUNTIME_IMAGE
FROM ${RUNTIME_IMAGE:-swr.cn-east-3.myhuaweicloud.com/distroless/alpine:3.19-user-arm64} as runtime

#hook

WORKDIR /pkg
COPY --from=compiler  /usr/share/monitor/cmp-monitor-starter/target/cmp-monitor-starter.jar /pkg/app.jar
COPY --from=compiler /usr/share/monitor/docker/entrypoint.sh  /pkg/entrypoint.sh
