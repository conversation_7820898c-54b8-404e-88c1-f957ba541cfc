<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cloudstar</groupId>
        <artifactId>cmp-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../cmp-parent/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cmp-common-component-operationlog</artifactId>
    <version>${cmp.common.operation.log.version}</version>

    <dependencies>
        <dependency>
            <groupId>com.cloudstar</groupId>
            <artifactId>cmp-common-base</artifactId>
            <version>${cmp.common.base.version}</version>
        </dependency>

        <dependency>
            <groupId>com.cloudstar</groupId>
            <artifactId>cmp-common-component-data</artifactId>
            <version>${cmp.common.data.version}</version>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
            <version>${mybatis-dynamic-datasource.version}</version>
        </dependency>

        <dependency>
            <groupId>com.clickhouse</groupId>
            <artifactId>clickhouse-jdbc</artifactId>
            <version>${clickhouse.version}</version>
        </dependency>

        <dependency>
            <groupId>org.lz4</groupId>
            <artifactId>lz4-java</artifactId>
            <version>${lz4.version}</version>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cloudstar</groupId>
            <artifactId>cmp-common-component-helper</artifactId>
            <version>${cmp.common.helper.version}</version>
        </dependency>


    </dependencies>

</project>
