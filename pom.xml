<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.cloudstar</groupId>
        <artifactId>cmp-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../cmp-parent/pom.xml</relativePath>
    </parent>


    <modelVersion>4.0.0</modelVersion>
    <name>CloudStar :: cmp-common-component-prometheus :: ${project.version}</name>
    <artifactId>cmp-common-component-prometheus</artifactId>
    <version>${cmp.common.prometheus.version}</version>

    <dependencies>
        <dependency>
            <groupId>com.cloudstar</groupId>
            <artifactId>cmp-common-base</artifactId>
            <version>${cmp.common.base.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cloudstar</groupId>
            <artifactId>cloudstar-jasypt-starter</artifactId>
            <version>${cloudstar-jasypt-starter.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.11.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>
    </dependencies>


</project>