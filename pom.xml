<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cloudstar</groupId>
        <artifactId>cmp-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../cmp-parent/pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cmp-common-component-schedule-facade</artifactId>
    <version>${cmp.common.schedule.facade.version}</version>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.cloudstar</groupId>
            <artifactId>cmp-common-base</artifactId>
            <version>${cmp.common.base.version}</version>
        </dependency>

        <dependency>
            <groupId>com.cloudstar</groupId>
            <artifactId>cmp-common-component-redis-starter</artifactId>
            <version>${cmp.common.redis.version}</version>
        </dependency>

        <dependency>
            <groupId>com.cloudstar</groupId>
            <artifactId>cmp-common-component-helper</artifactId>
            <version>${cmp.common.helper.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>
    </dependencies>

</project>
