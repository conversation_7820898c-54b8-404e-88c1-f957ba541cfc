<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cloudstar</groupId>
        <artifactId>cfn-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cfn-iam</artifactId>
    <name>CloudStar :: iam :: ${project.version}</name>

    <properties>
        <keycloak.version>10.0.1</keycloak.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-authz-client</artifactId>
            <version>${keycloak.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-admin-client</artifactId>
            <version>${keycloak.version}</version>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.keycloak</groupId>
            <artifactId>keycloak-adapter-core</artifactId>
            <version>${keycloak.version}</version>
            <optional>true</optional>
        </dependency>
        <!-- Maven 示例 -->
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.cloudstar.common.component.rabbitmq</groupId>
            <artifactId>cfn-common-component-rabbitmq-starter</artifactId>
            <version>${cfn.common.component.rabbitmq.starter}</version>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cloudstar.common.component.redis</groupId>
            <artifactId>cfn-common-component-redis-starter</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cloudstar.common.crypto</groupId>
            <artifactId>cfn-common-crypto</artifactId>
            <version>${cfn.common.crypto.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cloudstar.common.sysmconfig</groupId>
            <artifactId>cfn-common-component-sysmconfig-starter</artifactId>
            <version>${cfn.common.component.sysmconfig.starter}</version>
        </dependency>
        <dependency>
            <groupId>com.cloudstar.common.notification</groupId>
            <artifactId>cfn-common-component-notification-starter</artifactId>
            <version>${cfn.common.component.notification.starter}</version>
        </dependency>
        <dependency>
            <groupId>com.cloudstar.mybatis-base</groupId>
            <artifactId>cfn-common-mybatis-base</artifactId>
            <version>${cfn.common.mybatis-base.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cloudstar</groupId>
            <artifactId>cfn-sdk</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-kubernetes-fabric8-all</artifactId>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <id>k8s_env</id>
        </profile>
    </profiles>

    <build>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <configuration>
                    <dockerDirectory>${project.basedir}</dockerDirectory>
                    <dockerHost>http://**********:2375</dockerHost>
                    <imageName>${project.artifactId}_${project.version}</imageName>
                    <imageTags>
                        <!--suppress UnresolvedMavenProperty -->
                        <imageTag>${current.time}</imageTag>
                    </imageTags>
                    <resources>
                        <resource>
                            <directory>${project.build.directory}</directory>
                            <include>${project.build.finalName}.jar</include>
                            <targetPath>/</targetPath>
                        </resource>
                    </resources>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${springboot.dependencies.version}</version>
            </plugin>
        </plugins>
    </build>
</project>
