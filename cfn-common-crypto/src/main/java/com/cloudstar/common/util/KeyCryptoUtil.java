package com.cloudstar.common.util;

import cn.hutool.core.io.FileUtil;
import com.cloudstar.common.util.shamir.Scheme;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.util.encoders.Hex;

import javax.crypto.Cipher;
import java.io.File;
import java.io.FileNotFoundException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;


/**
 * 该类提供了一组静态方法，用于使用密钥对数据进行加密和解密
 *
 * <AUTHOR>
 * @date 2022/08/18
 */
@Slf4j
public class KeyCryptoUtil {
    
    private static final String KEY_PATH = "cloudstar.key.path";
    
    private static final KeyPair KEY_PAIR;
    
    private static final String PRIVATE_KEY;
    
    private static final String PUBLIC_KEY;
    
    /**
     * 指定加密算法RSA非对称加密
     */
    private static final String RSA = "key.rsa";
    
    /**
     * 指定RSA非对称算法/ECB模式/填充方式
     */
    private static final String RSA_CIPHER = "key.rsa.cipher";
    
    /**
     * RSA最大解密密文大小
     */
    private static final int MAX_DECRYPT_BLOCK = 56;
    
    /*
     * 初始化密码器
     */
    static {
        final String rootKey = getRootKey();
        PRIVATE_KEY = DataCryptoUtil.decrypt(getKeyStr("pri"), rootKey);
        PUBLIC_KEY = DataCryptoUtil.decrypt(getKeyStr("pub"), rootKey);
        KeyPairGenerator keyPairGenerator = null;
        try {
            keyPairGenerator = KeyPairGenerator.getInstance(SettingUtils.get(RSA));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        KEY_PAIR = keyPairGenerator == null ? null : keyPairGenerator.generateKeyPair();
    }
    
    public static String getPublicKey() {
        return PUBLIC_KEY;
    }
    
    static String getPrivateKey() {
        return PRIVATE_KEY;
    }
    
    /**
     * 它从密钥对中获取私钥，将其编码为字节数组，然后将该字节数组编码为字符串
     *
     * @return 字符串格式的私钥。
     */
    static String genPrivateKeyStr() {
        return Base64.getEncoder().encodeToString(KEY_PAIR.getPrivate().getEncoded());
    }
    
    /**
     * 从密钥对中获取公钥，将其编码为字节数组，然后将该字节数组编码为字符串
     *
     * @return 公钥作为字符串返回。
     */
    static String genPublicKeyStr() {
        return Base64.getEncoder().encodeToString(KEY_PAIR.getPublic().getEncoded());
    }
    
    /**
     * 从 base64 解码，然后使用 RSA 算法从解码的字符串生成公钥
     *
     * @param key 公钥字符串
     * @return 一个 PublicKey 对象。
     */
    static PublicKey toPublicKey(String key) throws NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory kf = KeyFactory.getInstance(SettingUtils.get(RSA));
        return kf.generatePublic(new X509EncodedKeySpec(Base64.getDecoder().decode(key)));
    }
    
    /**
     * 从 base64 解码，然后使用 RSA 算法从解码后的字符串生成私钥
     *
     * @param key 私钥字符串
     * @return 一个 PrivateKey 对象。
     */
    static PrivateKey toPrivateKey(String key) throws NoSuchAlgorithmException, InvalidKeySpecException {
        KeyFactory kf = KeyFactory.getInstance(SettingUtils.get(RSA));
        return kf.generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(key)));
    }
    
    /**
     * 使用公钥加密内容。
     *
     * @param content   要加密的内容
     * @param publicKey 用于加密数据的公钥。
     * @return 加密的字符串
     */
    static String encrypt(String content, String publicKey) {
        String result = null;
        try {
            byte[] dataByte = content.getBytes(StandardCharsets.UTF_8);
            Cipher cipher = Cipher.getInstance(SettingUtils.get(RSA_CIPHER));
            cipher.init(Cipher.ENCRYPT_MODE, toPublicKey(publicKey));
            result = Base64.getEncoder().encodeToString(cipher.doFinal(dataByte));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }
    
    /**
     * 使用私钥解密。
     *
     * @param cipherStr  加密的字符串
     * @param privateKey 用于解密密文的私钥。
     * @return 解密后的字符串。
     */
    static String decrypt(String cipherStr, String privateKey) {
        String result = null;
        try {
            Cipher cipher = Cipher.getInstance(SettingUtils.get(RSA_CIPHER));
            cipher.init(Cipher.DECRYPT_MODE, toPrivateKey(privateKey));
            result = new String(cipher.doFinal(Base64.getDecoder().decode(cipherStr)), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }
    
    /**
     * 使用 Shamir 的秘密共享算法来恢复根密钥
     *
     * @return 根密钥
     */
    @SneakyThrows
    static String getRootKey() {
        final Scheme scheme = new Scheme(new SecureRandom(), 5, 3);
        final String keyPath = System.getProperty(KEY_PATH, SettingBurstUtils.get(KEY_PATH));
        final File file = FileUtil.file(keyPath);
        final Map<Integer, byte[]> rootKeyMap = new HashMap<>();
        if (file.isDirectory()) {
            final File[] files = file.listFiles();
            if (files == null) {
                throw new FileNotFoundException("RootKey file not found.");
            }
            for (File f : files) {
                if (f.getName().matches("mk.[0-9]+")) {
                    rootKeyMap.put(Integer.parseInt(f.getName().split("\\.")[1]),
                            Hex.decode(FileUtil.readString(f, StandardCharsets.UTF_8)));
                }
            }
        }
        final byte[] recovered = scheme.join(rootKeyMap);
        return Base64.getEncoder().encodeToString(recovered);
    }
    
    /**
     * 获取Key
     *
     * @param fileName 文件名称
     * @return {@link String}
     */
    @SneakyThrows
    static String getKeyStr(String fileName) {
        final String keyPath = System.getProperty(KEY_PATH, SettingBurstUtils.get(KEY_PATH));
        final File file = FileUtil.file(keyPath);
        if (file.isDirectory()) {
            final File[] files = file.listFiles();
            if (files == null) {
                throw new FileNotFoundException("Key file not found.");
            }
            for (File f : files) {
                if (f.getName().equalsIgnoreCase(fileName)) {
                    return FileUtil.readString(f, StandardCharsets.UTF_8);
                }
            }
        }
        return null;
    }
    
}
