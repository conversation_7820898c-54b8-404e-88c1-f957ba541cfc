<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cloudstar.dao.mapper.user.SysMOrgMapper">
    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.user.SysMOrg">
        <id column="org_sid" property="orgSid" jdbcType="BIGINT"/>
        <result column="org_name" property="orgName" jdbcType="VARCHAR"/>
        <result column="org_code" property="orgCode" jdbcType="VARCHAR"/>
        <result column="org_type" property="orgType" jdbcType="VARCHAR"/>
        <result column="owner" property="owner" jdbcType="BIGINT"/>
        <result column="owner_name" property="owner" jdbcType="VARCHAR"/>
        <result column="tree_path" property="treePath" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="org_icon" property="orgIcon" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="VARCHAR"/>
        <result column="created_dt" property="createdDt" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="VARCHAR"/>
        <result column="updated_dt" property="updatedDt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="BIGINT"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.orgName != null">
                and org_name = #{condition.orgName}
            </if>
            <if test="condition.orgCode != null">
                and org_code = #{condition.orgCode}
            </if>
            <if test="condition.orgType != null">
                and org_type = #{condition.orgType}
            </if>
            <if test="condition.orgTypeNotIn != null">
                and org_type not in
                <foreach collection="condition.orgTypeNotIn" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orgIds != null">
                and org_sid in
                <foreach collection="condition.orgIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.owner != null">
                and owner = #{condition.owner}
            </if>
            <if test="condition.treePath != null">
                and tree_path = #{condition.treePath}
            </if>
            <if test="condition.parentId != null">
                and parent_id = #{condition.parentId}
            </if>
            <if test="condition.orgIcon != null">
                and org_icon = #{condition.orgIcon}
            </if>

            <if test="condition.status != null">
                and status = #{condition.status}
            </if>
            <if test="condition.createdBy != null">
                and created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and version = #{condition.version}
            </if>

        </trim>
    </sql>
    <sql id="Example_Where_Clause_Count">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.orgName != null">
                and A.org_name = #{condition.orgName}
            </if>
            <if test="condition.orgCode != null">
                and A.org_code = #{condition.orgCode}
            </if>
            <if test="condition.orgType != null">
                and A.org_type = #{condition.orgType}
            </if>
            <if test="condition.orgTypeNotEqual != null">
                and A.org_type != #{condition.orgTypeNotEqual}
            </if>
            <if test="condition.owner != null">
                and A.owner = #{condition.owner}
            </if>
            <if test="condition.treePath != null">
                and A.tree_path = #{condition.treePath}
            </if>
            <if test="condition.parentId != null">
                and A.parent_id = #{condition.parentId}
            </if>
            <if test="condition.orgIcon != null">
                and A.org_icon = #{condition.orgIcon}
            </if>

            <if test="condition.createdBy != null">
                and A.created_by = #{condition.createdBy}
            </if>
            <if test="condition.createdDt != null">
                and A.created_dt = #{condition.createdDt}
            </if>
            <if test="condition.updatedBy != null">
                and A.updated_by = #{condition.updatedBy}
            </if>
            <if test="condition.updatedDt != null">
                and A.updated_dt = #{condition.updatedDt}
            </if>
            <if test="condition.version != null">
                and A.version = #{condition.version}
            </if>

            <if test="condition.authOrgSid != null">
                and (EXISTS ( SELECT 1 FROM sys_m_org WHERE org_sid = #{condition.authOrgSid} AND org_sid = A.org_sid )
                OR A.org_sid = -1)
            </if>
            <if test="condition.ownerIds != null">
                and A.owner in
                <foreach collection="condition.ownerIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.orgIds != null and condition.orgIds.length > 0">
                and A.org_sid in
                <foreach collection="condition.orgIds" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
        </trim>
    </sql>
    <sql id="Base_Column_List">
        org_sid
        , org_name, org_code, org_type, owner, tree_path, parent_id, org_icon,created_by, created_dt, updated_by, updated_dt,
        version, address, contact_name, contact_phone, application_scenario, personnel_size, industry_type, remark
    </sql>
    <sql id="Base_Column_List_Alias">
        A.org_sid, A.org_name, A.org_code, A.org_type, A.owner, A.tree_path, A.parent_id, A.org_icon, A.created_by,
        A.created_dt, A.updated_by, A.updated_dt, A.version, A.address, A.contact_name, A.contact_phone, A.application_scenario,
        A.personnel_size, A.industry_type, A.remark
    </sql>
    <select id="selectAllOrg" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_m_org
    </select>
    <select id="getById" resultType="com.cloudstar.dao.model.user.SysMOrg">
        select
        <include refid="Base_Column_List"/>
        from sys_m_org where org_sid = #{orgSid}
    </select>
</mapper>
