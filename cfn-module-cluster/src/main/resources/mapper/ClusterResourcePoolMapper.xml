<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.cluster.ClusterResourcePoolMapper">
    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.cluster.ClusterResourcePool">
        <id column="id" property="id"/>
        <result column="cluster_id" property="clusterId"/>
        <result column="pool_name" property="poolName"/>
        <result column="pool_id" property="poolId"/>
        <result column="flavor_id" property="flavorId"/>
        <result column="pool_type" property="poolType"/>
        <result column="user_ids" property="userIds"/>
        <result column="account_ids" property="accountIds"/>
        <result column="version" property="version"/>
        <result column="created_by" property="createdBy"/>
        <result column="created_dt" property="createdDt"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="updated_dt" property="updatedDt"/>
        <result column="job_flavor_id" property="jobFlavorId"/>
        <result column="zone" property="zone"/>
        <result column="collaboration_agent_ip" property="collaborationAgentIp"/>
    </resultMap>

    <select id="selectOwnClusterIds" resultType="java.lang.Long">
        select crp.cluster_id from cluster_resource_pool as crp
        RIGHT JOIN user_resource_relation as urr
        on crp.id = urr.resource_id
        where urr.user_id = #{userSid}
        and crp.pool_type = 'SHARE'
        and crp.cluster_id in
        <foreach collection="clusterIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        or length(crp.user_ids) <![CDATA[ <= ]]> 0
    </select>
    <select id="selectUserPoolPage" resultType="com.cloudstar.dao.model.user.UserEntity">
        SELECT distinct u.user_sid, u.account
        FROM cluster_resource_pool p
        INNER JOIN
        user_entity u
        ON CAST(u.user_sid AS VARCHAR) = ANY (STRING_TO_ARRAY(p.user_ids, ','))
        where p.cluster_id = #{clusterId}
        <if test="account !=null and account!=''">
            and u.account like concat('%',#{account},'%');
        </if>
    </select>


    <!-- 查询当前用户已经映射的集群的共享资源池的资源汇总信息-->
    <select id="getComputeProducts"
            resultType="com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterComputeProductResp">
        SELECT cn.compute_product_name    AS computeProductName,
               SUM(cn.compute_card_count) AS computeCardCount
        FROM cluster_resource_pool crp
                 JOIN
             cluster_node cn ON crp.id = cn.pool_id
                 JOIN
             cluster_user_mapping cum ON crp.cluster_id = cum.cluster_id
        WHERE crp.pool_type = #{poolType}
          AND cum.user_sid = #{userSid}
          AND compute_card_count > 0
        GROUP BY cn.compute_product_name
    </select>

    <select id="getResourceTotals"
            resultType="com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterResourceTotalsResp">
        SELECT SUM(COALESCE(cn.cpu, 0))    AS totalCpu,
               SUM(COALESCE(cn.memory, 0)) AS totalMemory,
               SUM(COALESCE(cn.disk, 0))   AS totalDisk
        FROM cluster_resource_pool crp
                 JOIN
             cluster_node cn ON crp.id = cn.pool_id
                 JOIN
             cluster_user_mapping cum ON crp.cluster_id = cum.cluster_id
        WHERE crp.pool_type = #{poolType}
          AND cum.user_sid = #{userSid}
    </select>


    <!-- 查询当前用户在指定集群中已经映射的资源池的计算卡型号和数量 -->
    <select id="fetchComputeProductDetails"
        resultType="com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterComputeProductResp">
        SELECT cn.compute_product_name    AS computeProductName,
               SUM(cn.compute_card_count) AS computeCardCount
        FROM cluster_resource_pool crp
                 JOIN cluster_node cn ON crp.id = cn.pool_id
                 JOIN cluster_user_mapping cum ON crp.cluster_id = cum.cluster_id
        WHERE cum.user_sid = #{userSid}
          AND crp.cluster_id = #{clusterId}
        GROUP BY cn.compute_product_name
    </select>

    <!-- 查询当前用户在指定集群中已经映射的资源池的资源总量信息 -->
    <select id="fetchResourceTotals"
        resultType="com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterResourceTotalsResp">
        SELECT SUM(COALESCE(cn.cpu, 0))    AS totalCpu,
               SUM(COALESCE(cn.memory, 0)) AS totalMemory,
               SUM(COALESCE(cn.disk, 0))   AS totalDisk
        FROM cluster_resource_pool crp
                 JOIN cluster_node cn ON crp.id = cn.pool_id
                 JOIN cluster_user_mapping cum ON crp.cluster_id = cum.cluster_id
        WHERE cum.user_sid = #{userSid}
          AND crp.cluster_id = #{clusterId}
    </select>

    <select id="findResourceDetailsByUserIdAndClusterId"
        resultType="com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUserResourceInfoResp">
        SELECT p.id                     AS id,
               p.pool_id                AS poolId,
               p.pool_name              AS poolName,
               f.display_name              AS flavorId,
               f.flavor_type            AS flavorType,
               p.pool_type              AS poolType,
               (SELECT COUNT(*)
                FROM cluster_node n
                WHERE n.pool_id = p.id) AS node_count
        FROM cluster_resource_pool p
                 LEFT JOIN cluster_flavor f ON p.flavor_id = f.id
        WHERE p.user_ids ILIKE CONCAT('%', CAST(#{userId} AS VARCHAR)
            , '%')
          AND p.cluster_id = #{clusterId}
    </select>

    <select id="getMyQuotaInfo" resultType="com.cloudstar.service.pojo.dto.quota.QuotaDetailsDto">
        SELECT
            qd."id" AS id,
            qd.quota_id AS quotaId,
            qd."type" AS type,
            qd.training_quota AS trainingQuota,
            qd.v_training_quota AS virtualTrainingQuota,
            qd.develop_quota AS developQuota,
            qd.v_develop_quota AS virtualDevelopQuota,
            qd.inference_quota AS inferenceQuota,
            qd.v_inference_quota AS virtualInferenceQuota
        FROM quota q
                 LEFT JOIN quota_tenant qt ON qt.quota_id = q."id"
                 LEFT JOIN quota_details qd ON qd.quota_id = q."id"
        WHERE qt.tenant_id = #{userId}
           OR q.quota_type = 'default'
    </select>


    <select id="getUserResourceOccupy" resultType="com.cloudstar.service.pojo.dto.bigscreen.UserOccupyDto">
        SELECT
            SUM(CAST(COALESCE((cn.npu + cn.gpu), 0) AS NUMERIC)) AS resourceOccupy,
            ue.account AS userName,
            ue.real_name AS realName
        FROM cluster_resource_pool crp
                 LEFT JOIN user_entity ue
                           ON ue.user_sid = ANY(STRING_TO_ARRAY(crp.user_ids, ',')::NUMERIC[])
                 LEFT JOIN cluster_node cn
                           ON crp.id = cn.pool_id
        WHERE crp.pool_type = #{type}
          AND crp.cluster_id = #{clusterId}
        GROUP BY ue.account, ue.real_name
        ORDER BY resourceOccupy DESC;
    </select>

    <select id="getUserResourceOccupyFormFlavor" resultType="com.cloudstar.service.pojo.dto.bigscreen.UserOccupyDto">
        SELECT
            SUM(CAST(COALESCE((CAST(cn.npu_unit_num AS NUMERIC) + CAST(cn.gpu_unit_num AS NUMERIC)), 0) AS NUMERIC)) AS resourceOccupy,
            ue.account AS userName,
            ue.real_name AS realName
        FROM cluster_resource_pool crp
                 LEFT JOIN user_entity ue
                           ON ue.user_sid = ANY(STRING_TO_ARRAY(crp.user_ids, ',')::NUMERIC[])
                 LEFT JOIN cluster_flavor cn
                           ON crp.flavor_id = cn.id
        WHERE crp.pool_type = #{type}
          AND crp.cluster_id = #{clusterId}
          AND ue.account = #{userName}
        GROUP BY ue.account, ue.real_name
        ORDER BY resourceOccupy DESC;
    </select>

    <select id="getUsersResourceTend" resultType="com.cloudstar.service.pojo.dto.bigscreen.UserTendDto">
        SELECT
            ue.user_sid AS userSid,
            ue.account AS realName,
            TO_CHAR(bui.usage_start_date, 'YYYY-MM-DD') AS usageDate,
            ROUND(
                SUM(
                    CASE
                    WHEN cf.flavor_type = 'GPU' THEN bui.usage_count * cf.gpu_unit_num::NUMERIC
                    WHEN cf.flavor_type = 'NPU' THEN bui.usage_count * cf.npu_unit_num::NUMERIC
                    ELSE 0
                    END
                ) / 3600, 2) AS usageCount
        FROM cluster_resource_pool crp
                 LEFT JOIN user_entity ue
                           ON ue.user_sid = ANY(STRING_TO_ARRAY(crp.user_ids, ',')::NUMERIC[])
                 LEFT JOIN biz_bill_usage_item bui
                           ON bui.owner_sid = ANY(STRING_TO_ARRAY(crp.user_ids, ',')::NUMERIC[])
                 INNER JOIN cluster_flavor cf
                            ON bui.configuration = cf.flavor_id
                                AND cf.flavor_type IN ('GPU','NPU')
        WHERE crp.cluster_id = #{clusterId} and crp.pool_type = 'SHARE'
          AND bui.product IN ('bms_notebook', 'bms_job')
          AND bui.usage_start_date::DATE &gt;= #{startDate}
          AND bui.usage_end_date::DATE &lt;= #{endDate}
        GROUP BY ue.user_sid, ue.real_name, TO_CHAR(bui.usage_start_date, 'YYYY-MM-DD')
        ORDER BY usageDate, usageCount DESC;

    </select>

    <select id="selectPoolFlavorNum" resultType="java.util.HashMap">
        select p.pool_id, f.gpu_unit_num, f.npu_unit_num
        from cluster_resource_pool p
                 inner join cluster_flavor f on p.flavor_id = f.id
        where p.pool_type = 'EXCLUSIVE'
          and f.flavor_type = #{type}
    </select>
</mapper>
