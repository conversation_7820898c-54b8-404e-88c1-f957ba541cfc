package com.cloudstar.mq;


import com.cloudstar.common.base.constant.RabbitMqConstants;
import com.cloudstar.common.base.pojo.mq.BlockStorageEventMessage;
import com.cloudstar.common.base.pojo.mq.ClusterSdrCollectMessage;
import com.cloudstar.common.base.pojo.mq.ClusterSubAccountMessage;
import com.cloudstar.common.base.pojo.mq.ClusterSupplementaryCollectionMessage;
import com.cloudstar.common.base.pojo.mq.ClusterSyncMessage;
import com.cloudstar.common.base.pojo.mq.ConfigMapUpdateMessage;
import com.cloudstar.common.base.pojo.mq.DataStorageResourceMessage;
import com.cloudstar.common.base.pojo.mq.InferenceEventMessage;
import com.cloudstar.common.base.pojo.mq.LabelAnnotationMessage;
import com.cloudstar.common.base.pojo.mq.MirrorBuildMessage;
import com.cloudstar.common.base.pojo.mq.MirrorEventMessage;
import com.cloudstar.common.base.pojo.mq.ModelPushMessage;
import com.cloudstar.common.base.pojo.mq.NotebookAutoStopMessage;
import com.cloudstar.common.base.pojo.mq.NotebookEventMessage;
import com.cloudstar.common.base.pojo.mq.PrivateModelEventMessage;
import com.cloudstar.common.base.pojo.mq.ServiceAccountMessage;
import com.cloudstar.common.base.pojo.mq.TrainingJobEventMessage;
import com.cloudstar.common.base.pojo.mq.TrainingJobGroupEventMessage;
import com.cloudstar.common.base.pojo.mq.TrainingJobGroupMessage;
import com.cloudstar.common.base.pojo.mq.TrainingJobMessage;
import com.cloudstar.common.base.pojo.mq.VisualizedEventMessage;

import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessagePostProcessor;
import org.springframework.amqp.rabbit.core.RabbitTemplate;

import java.util.List;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * ScheduleHelper
 *
 * <AUTHOR>
 * @date 2018/01/26
 */
@Slf4j
public class ScheduleHelper {

    private static RabbitTemplate rabbitTemplate = SpringUtil.getBean(RabbitTemplate.class);

    /**
     * 数据存储资源信息
     *
     * @param dataStorageResourceMessage 数据存储资源信息
     */
    public static void dataStorageResourceMessage(DataStorageResourceMessage dataStorageResourceMessage) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SCHEDULE_EXCHANGE, RabbitMqConstants.DATA_STORAGE_ROUTING, dataStorageResourceMessage);
    }

    public static void addTrainingJobMessage(TrainingJobMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SCHEDULE_EXCHANGE, RabbitMqConstants.TRAINING_JOB_ROUTING, message);
    }

    public static void addTrainingJobEventMessage(TrainingJobEventMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SYS_LOGS_EXCHANGE, RabbitMqConstants.TRAINING_JOB_EVENT_ROUTING, message);
    }

    public static void trainingJobGroupMessage(TrainingJobGroupMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SCHEDULE_EXCHANGE, RabbitMqConstants.TRAINING_JOB_GROUP_ROUTING, message);
    }

    public static void trainingGroupJobEventMessage(TrainingJobGroupEventMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SYS_LOGS_EXCHANGE, RabbitMqConstants.TRAINING_JOB_GROUP_EVENT_ROUTING, message);
    }

    /**
     * 集群子账户信息
     *
     * @param clusterSubAccountMessage 集群子账户信息
     */
    public static void clusterSubAccountMessage(ClusterSubAccountMessage clusterSubAccountMessage) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SCHEDULE_EXCHANGE, RabbitMqConstants.CLUSTER_ACCOUNT_ROUTING,
                                      clusterSubAccountMessage);
    }

    /**
     * 同步集群信息mq
     *
     * @param message msg
     */
    public static void syncCluster(ClusterSyncMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.CLUSTER_EXCHANGE, RabbitMqConstants.CLUSTER_SYNC_ROUTING, message);
    }

    /**
     * 集群补充收集
     *
     * @param message 消息
     */
    public static void clusterSupplementaryCollection(ClusterSupplementaryCollectionMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SCHEDULE_EXCHANGE,
                                      RabbitMqConstants.CLUSTER_SUPPLEMENTARY_COLLECTION_ROUTING, message);
    }

    public static void clusterSdrCollect(ClusterSdrCollectMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.CLUSTER_SDR_COLLECT_ROUTING_EXCHANGE,
                                      RabbitMqConstants.CLUSTER_SDR_COLLECT_ROUTING_KEY, message);
    }

    public static void addNotebookEventMessage(NotebookEventMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SYS_LOGS_EXCHANGE, RabbitMqConstants.NOTEBOOK_EVENT_ROUTING_KEY, message);
    }

    /**
     * 自动停止notebook
     *
     * @param message msg
     * @param dlxTime dlx
     */
    public static void addNotebookAutoStopMessage(NotebookAutoStopMessage message, Integer dlxTime) {
        MessagePostProcessor postProcessor = new MessagePostProcessor() {
            @Override
            public Message postProcessMessage(Message message) throws AmqpException {
                message.getMessageProperties().setExpiration(1000 * 60 * 60 * dlxTime + "");
                return message;
            }
        };
        rabbitTemplate.convertAndSend(RabbitMqConstants.SYS_LOGS_EXCHANGE, RabbitMqConstants.NOTEBOOK_AUTO_STOP_ROUTING_KEY, message, postProcessor);
    }

    public static void addMirrorEventMessage(MirrorEventMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SCHEDULE_EXCHANGE, RabbitMqConstants.MIRROR_EVENT_ROUTING_KEY, message);
    }

    public static void addVisualizationJobMessage(VisualizedEventMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SCHEDULE_EXCHANGE, RabbitMqConstants.VISUALIZATION_JOB_ROUTING_KEY, message);
    }

    public static void addBlockStorageMessage(BlockStorageEventMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SCHEDULE_EXCHANGE, RabbitMqConstants.BLOCK_STORAGE_ROUTING_KEY, message);
    }

    public static void addInferenceServiceMessage(InferenceEventMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SCHEDULE_EXCHANGE, RabbitMqConstants.INFERENCE_SERVICE_ROUTING_KEY, message);
    }

    public static void addMirrorBuildMessage(MirrorBuildMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.MIRROR_BUILD_EXCHANGE, RabbitMqConstants.MIRROR_BUILD_ROUTING_KEY, message);
    }


    /**
     * 发送 ConfigMap 更新消息
     *
     * @param message 消息内容
     */
    public static void sendConfigMapUpdate(ConfigMapUpdateMessage message) {
        rabbitTemplate.convertAndSend(
                RabbitMqConstants.CONFIG_MAP_UPDATE_ROUTING_EXCHANGE,
                RabbitMqConstants.CONFIG_MAP_UPDATE_ROUTING_KEY,
                message
        );
    }

    /**
     * 发送节点标签和注释的消息到 RabbitMQ。
     *
     * @param messages 消息内容
     */
    public static void sendLabelAndAnnotation(List<LabelAnnotationMessage> messages) {
        rabbitTemplate.convertAndSend(
                RabbitMqConstants.LABEL_ANNOTATION_ROUTING_EXCHANGE,
                RabbitMqConstants.LABEL_ANNOTATION_ROUTING_KEY,
                messages
        );
    }


    /**
     * 发送访问密钥消息到 RabbitMQ。
     *
     * @param message 消息内容，包含访问密钥的具体信息
     */
    public static void sendDataStorageKey(ServiceAccountMessage message) {
        rabbitTemplate.convertAndSend(
                RabbitMqConstants.DATA_STORAGE_KEY_ROUTING_EXCHANGE,
                RabbitMqConstants.DATA_STORAGE_KEY_ROUTING_KEY,
                message
        );
    }

    /**
     * 模型发布消息
     */
    public static void addModelPushMessage(ModelPushMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.MODEL_PUSH_EXCHANGE, RabbitMqConstants.MODEL_PUSH_ROUTING_KEY, message);
    }

    /**
     * 私有模型消息发布
     */
    public static void addPrivateModelMessage(PrivateModelEventMessage message) {
        rabbitTemplate.convertAndSend(RabbitMqConstants.SCHEDULE_EXCHANGE, RabbitMqConstants.PRIVATE_MODEL_ROUTING_KEY, message);
    }
}



