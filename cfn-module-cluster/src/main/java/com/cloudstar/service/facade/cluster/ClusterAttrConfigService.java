package com.cloudstar.service.facade.cluster;

import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterAttrConfigResp;

import java.util.List;


/**
 * 集群属性配置表;(cluster_attr_config)表服务接口
 *
 * <AUTHOR> scx
 * @date : 2022-7-15
 */
public interface ClusterAttrConfigService {

    /**
     * 通过集群类型查询配置
     *
     * @param clusterType 集群类型
     *
     * @return 实例对象
     */
    List<ClusterAttrConfigResp> queryByClusterType(String clusterType);
}
