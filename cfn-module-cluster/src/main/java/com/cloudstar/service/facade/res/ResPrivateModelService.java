package com.cloudstar.service.facade.res;

import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.service.pojo.vo.requestvo.res.CreatePrivateModelReq;
import com.cloudstar.service.pojo.vo.requestvo.res.OperatePrivateModelReq;
import com.cloudstar.service.pojo.vo.requestvo.res.QueryPrivateModelReq;
import com.cloudstar.service.pojo.vo.responsevo.res.ClusterInfoResp;
import com.cloudstar.service.pojo.vo.responsevo.res.PrivateModelPageResp;

import java.util.List;

/**
 * 私有模型service
 *
 * <AUTHOR>
 * @date 2025/4/7 10:39
 */
public interface ResPrivateModelService {

    List<ClusterInfoResp> queryModelCluster(String account);

    Boolean createPrivateModel(CreatePrivateModelReq req);

    Boolean operatePrivateModel(OperatePrivateModelReq req);

    PageResult<PrivateModelPageResp> page(QueryPrivateModelReq req, PageForm pageForm);

}
