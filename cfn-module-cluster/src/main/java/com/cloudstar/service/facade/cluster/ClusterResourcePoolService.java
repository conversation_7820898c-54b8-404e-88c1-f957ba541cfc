package com.cloudstar.service.facade.cluster;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.model.cluster.ClusterResourcePool;
import com.cloudstar.service.pojo.dto.bigscreen.ModelArtsResourceDto;
import com.cloudstar.service.pojo.dto.bigscreen.UserOccupyDto;
import com.cloudstar.service.pojo.dto.bigscreen.UserTendDto;
import com.cloudstar.service.pojo.dto.cluster.ClusterResourcePoolDto;
import com.cloudstar.service.pojo.dto.cluster.ClusterResourceSummaryDto;
import com.cloudstar.service.pojo.dto.cluster.QueryClusterResourcePoolDto;
import com.cloudstar.service.pojo.dto.quota.QuotaDetailsDto;
import com.cloudstar.service.pojo.vo.requestvo.cluster.QueryPoolUserReq;
import com.cloudstar.service.pojo.vo.requestvo.cluster.UpdatePoolUserReq;
import com.cloudstar.service.pojo.vo.requestvo.res.ClusterResourceSummaryResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUserResourceInfoResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ExclusiveClusterResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.SelectAllPoolResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.UserDetailResourcePoolResponse;

import java.util.List;

/**
 * 集群资源池服务
 *
 * <AUTHOR>
 * @description 针对表【cluster_resource_pool(集群资源池表)】的数据库操作Service
 * @createDate 2022-12-21 14:20:52
 * @date 2022/12/22
 */
public interface ClusterResourcePoolService extends IService<ClusterResourcePool> {

    /**
     * 获得专属集群
     *
     * @return {@link List}<{@link ExclusiveClusterResp}>
     */
    List<ExclusiveClusterResp> getExclusiveCluster();


    boolean add(ClusterResourcePoolDto dto);

    boolean update(ClusterResourcePoolDto dto);

    Page<ClusterResourcePoolDto> page(QueryClusterResourcePoolDto dto, PageForm pageForm);

    ClusterResourcePoolDto queryById(Long id);

    List<SelectAllPoolResp> selectAll(Long clusterId);

    void syncByClusterId(Long clusterId);

    List<ModelArtsResourceDto> getPrivateUserResource(Long clusterId);

    List<UserDetailResourcePoolResponse> getResourcePool(String userSid);

    ClusterResourceSummaryDto getSharedResourceSummary(Long userId);

    boolean deletePool(Long id);


    ClusterResourceSummaryDto getResourceSummary(Long userId, Long clusterId);

    PageResult<ClusterUserResourceInfoResp> pageResourcesByUserIdAndClusterId(
            String userId, Long clusterId, PageForm pageForm);

    Page<QuotaDetailsDto> getMyQuotaInfo(Long userId, PageForm pageForm);

    List<ModelArtsResourceDto> getPublicUserResource(Long clusterId);

    List<UserOccupyDto> getPublicUserResourceOccupy(Long clusterId);

    List<UserOccupyDto> getPrivateUserResourceOccupy(Long clusterId);

    List<UserTendDto> getUsersResourceTend(Long clusterId, String startDate);

    ClusterResourceSummaryResp getMyResourceSummary(Long userId);

    Boolean updatePoolUser(UpdatePoolUserReq req);

    Boolean queryPoolUser(QueryPoolUserReq req);
}
