package com.cloudstar.service.pojo.dto.cluster;

import lombok.Data;

/**
 * 租户商汤话单
 *
 * <AUTHOR>
 * @date 2023/01/12
 */
@Data
public class ClusterUserMappingSdrDto {

    /**
     * 用户sid
     */
    private Long userSid;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;

    /**
     * 适配器uuid
     */
    private String adapterUuid;

    /**
     * 类型
     */
    private String poolName;
    /**
     * 规格id
     */
    private Long flavorId;
    /**
     * 组织id
     */
    private Long orgSid;
    /**
     * 资源池id
     */
    private String poolId;
    /**
     * 对象存储url
     */
    private String obsUrl;

    /**
     * 用户账号
     */
    private String userAccount;
}
