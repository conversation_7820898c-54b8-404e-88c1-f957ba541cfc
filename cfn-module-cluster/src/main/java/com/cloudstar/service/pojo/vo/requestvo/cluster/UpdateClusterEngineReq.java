package com.cloudstar.service.pojo.vo.requestvo.cluster;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 修改引擎
 *
 * <AUTHOR>
 * @date 2024/6/11 10:16
 */
@Data
public class UpdateClusterEngineReq {

    @NotNull
    private Long id;

    /**
     * 镜像
     */
    @NotEmpty
    private String imageTag;

    /**
     * 引擎名称
     */
    @NotEmpty
    private String engineName;
    /**
     * 引擎版本
     */
    @NotEmpty
    private String engineVersion;


    /**
     * 资源类型 cpu gpu npu
     */
    @NotEmpty
    private String resourceType;

    /**
     * 架构类型 arm或者x86
     */
    @NotEmpty
    private String archType;

    @NotNull
    private Long clusterId;
}
