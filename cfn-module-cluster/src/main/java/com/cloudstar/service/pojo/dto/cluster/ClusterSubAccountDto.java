package com.cloudstar.service.pojo.dto.cluster;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * iam子账号
 *
 * <AUTHOR>
 * @date 2022-11-16 14:12
 */
@Data
public class ClusterSubAccountDto {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 租户id
     */
    private Long userSid;

    /**
     * 适配器uuid
     */
    private String adapterUuid;

    /**
     * 主账号uuid
     */
    private String parentUuid;

    /**
     * 子账号uuid
     */
    private String accountUuid;

    /**
     * 子账号
     */
    private String account;

    /**
     * 子账号ak
     */
    private String accessKey;

    /**
     * 子账号sk
     */
    private String secretKey;

    /**
     * obs桶id
     */
    private String obsId;

    /**
     * 乐观锁
     */
    private String version;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 奥林匹克广播服务公司网址
     */
    private String obsUrl;

    /**
     * 集群id
     */
    private Long clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

}
