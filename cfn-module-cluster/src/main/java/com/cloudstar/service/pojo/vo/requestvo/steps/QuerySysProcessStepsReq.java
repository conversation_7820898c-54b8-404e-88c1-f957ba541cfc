package com.cloudstar.service.pojo.vo.requestvo.steps;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 查询步骤请求
 *
 * <AUTHOR>
 * @date 2022-11-15 11:11
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class QuerySysProcessStepsReq {


    /**
     * 步骤代码
     */
    private String stepsCode;
    /**
     * 集群id
     */
    private Long processId;

    /**
     * 所属人sid
     */
    private Long ownerSid;

}
