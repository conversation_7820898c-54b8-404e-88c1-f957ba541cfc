package com.cloudstar.service.pojo.dto.datastorage;

import lombok.Builder;
import lombok.Data;

/**
 * 数据资源缓存
 *
 * <AUTHOR>
 * @date 2022-09-14 11:32
 */
@Data
@Builder
public class DataStorageRedisDto {

    /**
     * 对象num
     */
    private Long objectNum;

    /**
     * 存储使用情况
     */
    private Long storageUsage;


    /**
     * 账号名称
     */
    private String ownerName;

    /**
     * 账号Id
     */
    private Long ownerId;

    /**
     * 所在集群类型
     */
    private String clusterType;

    /**
     * 集群id
     */
    private String clusterId;
}
