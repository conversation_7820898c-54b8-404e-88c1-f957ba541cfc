package com.cloudstar.service.pojo.vo.responsevo.res;

import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterGroupFlavorResp;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.List;

import lombok.Data;

/**
 * 集群资源池信息
 *
 * <AUTHOR>
 * @date 2025/4/7 11:19
 */
@Data
public class ClusterPoolResp {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    private String poolId;

    private String poolName;

    private String poolType;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long clusterId;

    /**
     * 资源规格列表
     */
    private List<ClusterGroupFlavorResp> flavorList;
}
