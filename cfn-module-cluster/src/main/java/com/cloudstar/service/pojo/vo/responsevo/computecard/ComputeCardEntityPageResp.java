package com.cloudstar.service.pojo.vo.responsevo.computecard;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import lombok.Data;

/**
 * 计算卡响应值
 */
@Data
public class ComputeCardEntityPageResp {

    /**
     * 主键
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;
    /**
     * 计算卡名称
     */
    private String cardName;
    /**
     * 计算卡ID
     */
    private String uuid;
    /**
     * 计算卡类型
     */
    private String cardType;
    /**
     * 计算卡hosts
     */
    private String cardHosts;
    /**
     * 计算卡序号
     */
    private String cardNumber;
    /**
     * 状态
     */
    private String status;
    /**
     * 属性详情
     */
    private String attrData;
    /**
     * 版本
     */
    private String version;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;

}
