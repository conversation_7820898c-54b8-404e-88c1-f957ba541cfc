package com.cloudstar.service.pojo.dto.monitor;

import java.util.Date;

import lombok.Data;

/**
 * 资源池作业dto
 *
 * <AUTHOR>
 * @date 2023/7/10 16:08
 */
@Data
public class PoolJobStatusDto {

    
    /**
     * 作业id;用来关联job表的作业id
     */
    private String jobId;

    /**
     * 作业状态
     */
    private String jobStatus;

    /**
     * 作业运行时长;毫秒
     */
    private Long jobDuration;

    /**
     * 作业等待运行时长
     */
    private Long waitedTime;

    /**
     * 作业名称
     */
    private String jobName;

    /**
     * 作业创建时间
     */
    private Date createTime;

    /**
     * 规格id
     */
    private String flavorId;

    /**
     * 项目id
     */
    private String projectId;


}
