package com.cloudstar.service.pojo.dto.quota;


import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * 新增配额详情
 *
 * <AUTHOR>
 * @date 2025/2/10 15:02
 */
@Data
public class QuotaDetailsDto {

    /**
     * 详情ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 配额Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long quotaId;

    /**
     * 卡类型 A100、p40等
     */
    private String type;

    /**
     * 训练实体配额
     */
    private Integer trainingQuota;

    /**
     * 训练虚拟配额
     */
    private Integer virtualTrainingQuota;


    /**
     * 开发实体配额
     */
    private Integer developQuota;

    /**
     * 开发虚拟配额
     */
    private Integer virtualDevelopQuota;


    /**
     * 推理实体配额
     */
    private Integer inferenceQuota;

    /**
     * 推理虚拟配额
     */
    private Integer virtualInferenceQuota;
}
