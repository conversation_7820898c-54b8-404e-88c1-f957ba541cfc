package com.cloudstar.service.pojo.vo.requestvo.cluster;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;

/**
 * 集群实体请求类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
@ToString
public class ClusterEntityReq {

    /**
     * 主键
     */
    private Long id;
    /**
     * 集群名称;管理员端展示
     */
    @Length(max = 64)
    @NotEmpty
    private String clusterName;
    /**
     * 集群昵称;租户端展示
     */
    @Length(max = 32)
    @NotEmpty
    private String clusterNickName;
    /**
     * 集群类型;HCSO,SLURM
     */
    @Length(max = 32)
    @NotEmpty
    private String clusterType;

    /**
     * 适配器地址
     */
    @Length(max = 64)
    @NotEmpty
    private String adapterAddress;
    /**
     * 适配器账号
     */
    @Length(max = 64)
    @NotEmpty
    private String adapterAccount;

    /**
     * 适配器密码
     */
    //@Length(max = 512)
    //@NotEmpty
    private String adapterPassword;
    /**
     * 集群标志;system:系统集群 calculate:算力集群
     */
    @Length(max = 32)
    @NotEmpty
    private String clusterFlag;
    /**
     * 集群地址
     */
    @Length(max = 32)
    private String clusterAddress;
    /**
     * 心跳检测频率;单位秒
     */
    @Max(999)
    @Min(0)
    private Integer heartCheck;
    /**
     * 数据采集频率;单位秒
     */
    @Max(999)
    @Min(0)
    private Integer dataCollect;
    /**
     * 监控采集频率;单位秒
     */
    @Max(999)
    @Min(0)
    private Integer monitorCollect;
    /**
     * 属性详情;属性详情json数据加密字段
     */
    @Length(max = 2048)
    private String attrData;
}
