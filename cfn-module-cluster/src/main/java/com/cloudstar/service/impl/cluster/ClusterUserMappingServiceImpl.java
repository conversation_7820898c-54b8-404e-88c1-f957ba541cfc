package com.cloudstar.service.impl.cluster;

import cn.hutool.core.util.BooleanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.ConfigService;
import com.cloudstar.bean.dto.SysMConfigDto;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.constant.ClusterAccountUseStatusEnum;
import com.cloudstar.common.base.constant.ClusterStatusEnum;
import com.cloudstar.common.base.constant.UserAuthStatusEnum;
import com.cloudstar.common.base.constant.UserStatusEnum;
import com.cloudstar.common.base.constant.UserTypeEnum;
import com.cloudstar.common.base.enums.ClusterFlagEnum;
import com.cloudstar.common.base.enums.ClusterTypeEnum;
import com.cloudstar.common.base.enums.SysEntityEnum;
import com.cloudstar.common.base.enums.SysProcessStepsEnum;
import com.cloudstar.common.base.exception.BizError;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.mq.ClusterSubAccountMessage;
import com.cloudstar.common.base.util.K8sNameUtils;
import com.cloudstar.common.base.util.UuidUtil;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.dao.mapper.cluster.ClusterAccountMapper;
import com.cloudstar.dao.mapper.tenantmapping.ClusterUserMappingMapper;
import com.cloudstar.dao.mapper.user.UserEntityMapper;
import com.cloudstar.dao.model.cluster.ClusterAccount;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.cluster.ClusterUserMappingLog;
import com.cloudstar.dao.model.steps.SysProcessSteps;
import com.cloudstar.dao.model.tenantmapping.ClusterUserMapping;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.mq.ScheduleHelper;
import com.cloudstar.service.facade.cluster.ClusterEntityService;
import com.cloudstar.service.facade.cluster.ClusterUserMappingLogService;
import com.cloudstar.service.facade.cluster.ClusterUserMappingService;
import com.cloudstar.service.facade.steps.SysProcessStepsService;
import com.cloudstar.service.pojo.dto.cluster.ClusterUserMappingBillDto;
import com.cloudstar.service.pojo.vo.requestvo.cluster.ClusterUserMappedReq;
import com.cloudstar.service.pojo.vo.requestvo.cluster.ClusterUserMappingSaveReq;
import com.cloudstar.service.pojo.vo.requestvo.cluster.QueryUserMappingReq;
import com.cloudstar.service.pojo.vo.requestvo.tenant.UserListSelectReq;
import com.cloudstar.service.pojo.vo.requestvo.tenant.UserListSelectRequset;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterEntityMappingResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUserMappingInfoResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUserMappingPageResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUserMappingResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterUserPriorityMappingResp;
import com.cloudstar.service.pojo.vo.responsevo.user.UserMappingResp;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicBoolean;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClusterUserMappingServiceImpl extends ServiceImpl<ClusterUserMappingMapper, ClusterUserMapping> implements
        ClusterUserMappingService {

    private final ClusterUserMappingMapper clusterUserMappingMapper;

    private final ClusterAccountMapper clusterAccountMapper;

    private final UserEntityMapper userEntityMapper;

    private final ClusterEntityService clusterEntityService;

    private final SysProcessStepsService stepsService;

    private final ClusterUserMappingLogService mappingLogService;

    private final ConfigService configService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ClusterUserMappingResp insertMappedClusters(ClusterUserMappingSaveReq clusterUserMappingSaveReq) {
        ClusterUserMappingResp resp = new ClusterUserMappingResp();
        Long userSid = clusterUserMappingSaveReq.getUserId();
        UserEntity userEntity = userEntityMapper.selectById(userSid);
        // 判断账号状态
        if (Objects.equals(userEntity.getStatus(), UserStatusEnum.DISABLE.getType())) {
            BizError.e(BizErrorEnum.MSG_1012_ACCOUNT_DISABLE);
        }
        // 判断是否实名认证成功
        log.info("用户实名认证状态：{}，SkipCheckAuth：{}", userEntity.getAuthStatus(),
                 clusterUserMappingSaveReq.getSkipCheckAuth());
        if (clusterUserMappingSaveReq.getSkipCheckAuth() || UserAuthStatusEnum.VERIFIED.getType()
                                                                                       .equals(userEntity.getAuthStatus())) {
            // 查询子账户
            List<Long> ids = new ArrayList<>(List.of(userSid));
            List<Long> clusterIdList = clusterUserMappingSaveReq.getClusterIds();
            getSubAccount(resp, userSid, userEntity, ids);

            ArrayList<ClusterEntityMappingResp> entityList = new ArrayList<>();
            for (Long clusterId : clusterIdList) {

                // 组装返回参数中的集群信息
                getClusterMapping(entityList, clusterId);
                ClusterEntity clusterEntity = clusterEntityService.getById(clusterId);
                boolean isBmsType = ClusterTypeEnum.BMS.getType().equalsIgnoreCase(clusterEntity.getClusterType());
                // 获取未使用的iam主账号
                ClusterAccount clusterAccount = getClusterAccount(ids, clusterId, clusterUserMappingSaveReq.getClusterUserId());
                if (Objects.isNull(clusterAccount) && isBmsType) {
                    // BMS新创建账号
                    clusterAccount = new ClusterAccount();
                    clusterAccount.setClusterId(clusterId);
                    clusterAccount.setAdapterUuid(K8sNameUtils.toK8sName(clusterEntity.getAdapterUuid()));
                    clusterAccount.setAccountUuid(K8sNameUtils.toK8sName(userEntity.getAccount()));
                    clusterAccount.setAccount(K8sNameUtils.toK8sName(userEntity.getAccount()));
                    clusterAccount.setUseStatus(ClusterAccountUseStatusEnum.USED.getStatus());
                    Date date = new Date();
                    clusterAccount.setCreatedDt(date);
                    clusterAccount.setUpdatedDt(date);
                    clusterAccountMapper.insert(clusterAccount);
                }

                //账号同步分两步，后面手动映射集群
                SysMConfigDto configByConfigKey = configService.getConfigByConfigKey("manual.mapping");
                log.info("manual.mapping {}", configByConfigKey.getConfigValue());
                boolean flag = BooleanUtil.toBoolean(configByConfigKey.getConfigValue());

                //如果是统一入口,保存系统集群固定映射日志
                String getenv = System.getenv("CFN_MQ_START");
                if ("true".equals(getenv)) {
                    if ((StrUtil.isNotBlank(clusterUserMappingSaveReq.getClusterUserId())
                            && ClusterFlagEnum.SYSTEM.getType().equals(clusterEntity.getClusterFlag())
                            && !ClusterTypeEnum.BMS.getType().equals(clusterEntity.getClusterType()))
                            || flag) {
                        LambdaQueryWrapper<ClusterUserMappingLog> queryWrapper = new LambdaQueryWrapper<>();
                        queryWrapper.eq(Objects.nonNull(clusterId), ClusterUserMappingLog::getClusterId, clusterId);
                        queryWrapper.eq(Objects.nonNull(clusterUserMappingSaveReq.getClusterUserId()),
                                ClusterUserMappingLog::getClusterAccount,
                                clusterUserMappingSaveReq.getClusterUserId());
                        queryWrapper.eq(Objects.nonNull(userEntity.getUserSid()), ClusterUserMappingLog::getUserSid, userEntity.getUserSid());
                        ClusterUserMappingLog userMappingLog = mappingLogService.getOne(queryWrapper);
                        //没操作记录 就新增一个
                        if (ObjectUtil.isEmpty(userMappingLog)) {
                            userMappingLog = new ClusterUserMappingLog();
                            userMappingLog.setClusterId(clusterId);
                            userMappingLog.setUserSid(userEntity.getUserSid());
                            userMappingLog.setClusterAccount(clusterUserMappingSaveReq.getClusterUserId());
                            if (StrUtil.isBlank(userMappingLog.getClusterAccount())) {
                                userMappingLog.setClusterAccount(K8sNameUtils.toK8sName(userEntity.getAccount()));
                            }
                            // 如果该集群没有可用主账号，就跳过
                            if (clusterAccount == null) {
                                userMappingLog.setStatus("FAIL");
                            } else {
                                userMappingLog.setStatus("SUCCESS");
                            }
                            userMappingLog.setCreatedDt(new Date());
                            userMappingLog.setHcsoUserInfo(clusterUserMappingSaveReq.getHcsoUserInfo());
                            mappingLogService.save(userMappingLog);
                        } else {
                            userMappingLog.setUpdatedDt(new Date());
                            mappingLogService.updateById(userMappingLog);
                        }
                        log.info("新增租户固定映射操作日志:{}", userMappingLog);
                    }
                }
                // 如果该集群没有可用主账号，就跳过
                if (clusterAccount == null) {
                    continue;
                }
                // 修改主账号的使用状态
                clusterAccount.setUseStatus(ClusterAccountUseStatusEnum.USED.getStatus());
                clusterAccountMapper.updateById(clusterAccount);
                // 发送映射MQ消息
                log.info("发送映射账号消息，userSid:{}<--->account:{}", userSid, clusterAccount.getAccount());
                sendMqMessage(userSid, userEntity, ids, clusterAccount, clusterUserMappingSaveReq.getHcsoUserInfo());
            }

            resp.setClusterList(entityList);
        }
        return resp;
    }

    /**
     * 获取uuid
     *
     * @return uuid
     */
    private String getShortUuid() {
        String shortUuid = UuidUtil.getShortUuid();
        if (StrUtil.isBlank(shortUuid)) {
            throw new BizException("生成集群账号uuid失败");
        }
        List<ClusterAccount> clusterAccounts = clusterAccountMapper
                .selectList(new LambdaQueryWrapper<ClusterAccount>().eq(ClusterAccount::getAccountUuid, shortUuid));
        if (CollectionUtils.isEmpty(clusterAccounts)) {
            return shortUuid;
        }
        return getShortUuid();
    }

    /**
     * 获取集群映射
     *
     * @param entityList 实体列表
     * @param clusterId 集群id
     */
    private void getClusterMapping(ArrayList<ClusterEntityMappingResp> entityList, Long clusterId) {
        ClusterEntity clusterEntity = clusterEntityService.getById(clusterId);
        ClusterEntityMappingResp build = ClusterEntityMappingResp.builder()
                                                                 .id(clusterEntity.getId())
                                                                 .clusterName(clusterEntity.getClusterName())
                                                                 .build();
        entityList.add(build);
    }

    /**
     * 获取子账户
     *
     * @param resp 分别地
     * @param userSid 用户sid
     * @param userEntity 用户实体
     * @param ids id
     */
    private void getSubAccount(ClusterUserMappingResp resp, Long userSid, UserEntity userEntity, List<Long> ids) {
        List<UserEntity> list = userEntityMapper.selectList(new QueryWrapper<UserEntity>().lambda()
                                                                                          .eq(UserEntity::getParentSid,
                                                                                              userSid)
                                                                                          .eq(UserEntity::getStatus,
                                                                                              UserStatusEnum.ENABLE.getType())
                                                                                          .eq(UserEntity::getAuthStatus,
                                                                                              UserAuthStatusEnum.VERIFIED.getType()));
        List<UserMappingResp> userList = new ArrayList<>();
        UserMappingResp usermapping = UserMappingResp.builder()
                                                     .id(userEntity.getUserSid())
                                                     .account(userEntity.getAccount())
                                                     .build();
        userList.add(usermapping);
        if (!CollectionUtils.isEmpty(list)) {
            for (UserEntity entity : list) {
                ids.add(entity.getUserSid());
                UserMappingResp user = UserMappingResp.builder()
                                                      .id(entity.getUserSid())
                                                      .account(entity.getAccount())
                                                      .build();
                userList.add(user);
            }
        }
        resp.setUserList(userList);
    }

    /**
     * 获取集群帐户
     *
     * @param ids id
     * @param clusterId 集群id
     * @param clusterUserId 集群用户id
     *
     * @return {@link ClusterAccount}
     */
    private ClusterAccount getClusterAccount(List<Long> ids, Long clusterId, String clusterUserId) {
        LambdaQueryWrapper<ClusterAccount> accountQueryWrapper = new LambdaQueryWrapper<>();
        accountQueryWrapper.select(ClusterAccount::getId, ClusterAccount::getAccountUuid,
                                   ClusterAccount::getClusterId, ClusterAccount::getAccount);
        accountQueryWrapper.eq(ClusterAccount::getUseStatus, ClusterAccountUseStatusEnum.UNUSED.getStatus());
        accountQueryWrapper.eq(ClusterAccount::getClusterId, clusterId);
        ClusterEntity clusterEntity = clusterEntityService.getById(clusterId);
        if (StrUtil.isNotBlank(clusterUserId) && ClusterFlagEnum.SYSTEM.getType().equals(clusterEntity.getClusterFlag())) {
            accountQueryWrapper.eq(ClusterAccount::getAccount, clusterUserId);
        }

        accountQueryWrapper.last("limit 1");
        List<ClusterAccount> clusterAccounts = clusterAccountMapper.selectList(accountQueryWrapper);
        if (ClusterTypeEnum.BMS.getType().equals(clusterEntity.getClusterType())) {
            for (Long id : ids) {
                saveSteps(id, clusterId, SysProcessStepsEnum.SUB_ACCOUNT_MAPPING.getStepCode()
                        + "_" + 0, Boolean.TRUE.toString());
            }
            return null;
        } else {
            if (CollectionUtils.isEmpty(clusterAccounts)) {
                for (Long id : ids) {
                    saveSteps(id, clusterId, SysProcessStepsEnum.SUB_ACCOUNT_MAPPING.getStepCode()
                            + "_" + 0, Boolean.FALSE.toString());
                }
                return null;
            } else {
                for (Long id : ids) {
                    saveSteps(id, clusterId, SysProcessStepsEnum.SUB_ACCOUNT_MAPPING.getStepCode()
                            + "_" + 0, Boolean.TRUE.toString());
                }
            }
        }

        return clusterAccounts.get(0);
    }

    /**
     * 提取
     *
     * @param userSid 用户sid
     * @param userEntity 用户实体
     * @param ids id
     * @param clusterAccount 集群账户
     */
    private void sendMqMessage(Long userSid, UserEntity userEntity, List<Long> ids, ClusterAccount clusterAccount) {
        ClusterSubAccountMessage clusterSubAccountMessage = new ClusterSubAccountMessage();
        clusterSubAccountMessage.setAccountUuid(clusterAccount.getAccountUuid());
        clusterSubAccountMessage.setUserSid(userSid);
        clusterSubAccountMessage.setParentUuid(clusterAccount.getAccountUuid());
        clusterSubAccountMessage.setClusterId(clusterAccount.getClusterId());
        clusterSubAccountMessage.setOrgSid(userEntity.getOrgSid());
        clusterSubAccountMessage.setUserName(userEntity.getAccount());
        clusterSubAccountMessage.setSubUserSids(ids);
        ScheduleHelper.clusterSubAccountMessage(clusterSubAccountMessage);
    }

    private void sendMqMessage(Long userSid, UserEntity userEntity, List<Long> ids, ClusterAccount clusterAccount, String hcsoUserInfo) {
        ClusterSubAccountMessage clusterSubAccountMessage = new ClusterSubAccountMessage();
        clusterSubAccountMessage.setAccountUuid(clusterAccount.getAccountUuid());
        clusterSubAccountMessage.setUserSid(userSid);
        clusterSubAccountMessage.setParentUuid(clusterAccount.getAccountUuid());
        clusterSubAccountMessage.setClusterId(clusterAccount.getClusterId());
        clusterSubAccountMessage.setOrgSid(userEntity.getOrgSid());
        clusterSubAccountMessage.setUserName(userEntity.getAccount());
        clusterSubAccountMessage.setSubUserSids(ids);
        clusterSubAccountMessage.setHcsoUserInfo(hcsoUserInfo);
        ScheduleHelper.clusterSubAccountMessage(clusterSubAccountMessage);
    }

    /**
     * 查询已映射租户
     */
    @Override
    public List<ClusterUserMappingInfoResp> queryUnmappedClusters(ClusterUserMappedReq clusterUserMappedReq) {
        Long userSid = clusterUserMappedReq.getUserId();
        if (Objects.isNull(userSid) || userSid.equals(0L)) {
            BizError.notFound();
        }
        List<ClusterUserMappingInfoResp> mappedClustersResp = clusterUserMappingMapper.queryMappedClusters(userSid);
        mappedClustersResp.forEach(response -> {
            response.setStatusName(ClusterStatusEnum.getDescByStatus(response.getStatus()));
        });
        //统一入口，当系统集群映射失败时，需要查询出失败记录，用户手动去做映射
        String getenv = System.getenv("CFN_MQ_START");
        if ("true".equals(getenv)) {
            ClusterEntity clusterEntity = clusterEntityService.getOne(
                    new LambdaQueryWrapper<ClusterEntity>().eq(ClusterEntity::getClusterFlag, ClusterFlagEnum.SYSTEM.getType()));
            AtomicBoolean flag = new AtomicBoolean(true);
            if (!CollectionUtils.isEmpty(mappedClustersResp)) {
                mappedClustersResp.stream().forEach(resp -> {
                    //租户映射包含系统集群的 则不用单独查询
                    if (resp.getClusterId().equals(clusterEntity.getId())) {
                        flag.set(false);
                    }
                });
            }
            if (flag.get()) {
                ClusterUserMappingInfoResp resp = mappingLogService.queryLogByUserId(clusterEntity.getId(), userSid);
                if (ObjectUtil.isNotEmpty(resp) && !"SUCCESS".equals(resp.getStatus())) {
                    resp.setStatus("MAPPING_FAIL");
                    resp.setStatusName("映射失败");
                }
                if (CollectionUtils.isEmpty(mappedClustersResp)) {
                    mappedClustersResp = new ArrayList<>();
                }
                mappedClustersResp.add(resp);
            }

        }
        return mappedClustersResp;
    }

    /**
     * 获取用户列表
     *
     * @param req 要求事情
     * @param pageForm 页面形式
     *
     * @return {@link PageResult}<{@link ClusterUserMappingPageResp}>
     */
    @Override
    public PageResult<ClusterUserMappingPageResp> getUserList(UserListSelectRequset req, PageForm pageForm) {
        UserListSelectReq userListSelectReq = BeanUtil.copyProperties(req, UserListSelectReq.class);
        BeanUtil.copyProperties(pageForm, userListSelectReq);
        userListSelectReq.setUserType(UserTypeEnum.ACCOUNT.getType());
        userListSelectReq.setAuthStatus(List.of(UserAuthStatusEnum.VERIFIED.getType()));
        Page<UserEntity> userList = getUserListServer(userListSelectReq);
        PageResult<ClusterUserMappingPageResp> result = PageResult.of(userList,
                                                                      ClusterUserMappingPageResp.class);
        result.getList().forEach(response -> {
            response.setStatusName(UserStatusEnum.getDesc(response.getStatus()));
            Integer clusterCount = clusterUserMappingMapper.queryMappedClustersNumber(response.getUserSid());
            response.setClusterMappingCount(clusterCount);
        });
        return result;
    }

    /**
     * 通过集群id 获取所有用户
     *
     * @param clusterId 集群id
     *
     * @return {@link List}<{@link ClusterUserMappingBillDto}>
     */
    @Override
    public List<ClusterUserMappingBillDto> getAllUserByClusterId(Long clusterId) {
        return baseMapper.getAllUser(clusterId);
    }

    /**
     * 保存步骤
     *
     * @param ownerSid 主人sid
     * @param clusterId 集群id
     * @param stepCode 步代码
     * @param stepResult 步结果
     */
    private void saveSteps(Long ownerSid, Long clusterId, String stepCode, String stepResult) {
        LambdaUpdateWrapper<SysProcessSteps> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysProcessSteps::getOwnerSid, ownerSid);
        updateWrapper.eq(SysProcessSteps::getProcessId, clusterId);
        updateWrapper.likeRight(SysProcessSteps::getStepCode, SysProcessStepsEnum.SUB_ACCOUNT_MAPPING.getStepCode());
        stepsService.remove(updateWrapper);
        SysProcessSteps steps = SysProcessSteps.builder()
                                               .ownerSid(ownerSid)
                                               .processId(clusterId)
                                               .stepCode(stepCode)
                                               .stepResult(stepResult)
                                               .stepName(SysProcessStepsEnum.SUB_ACCOUNT_MAPPING.getStepName())
                                               .createdDt(new Date())
                                               .type(SysEntityEnum.SUB_ACCOUNT.name()).build();
        stepsService.save(steps);
    }

    /**
     * 获取用户列表
     *
     * @param req 要求事情
     *
     * @return {@link Page}<{@link UserEntity}>
     */
    private Page<UserEntity> getUserListServer(UserListSelectReq req) {
        //TODO 先暂时全部查询出来
        QueryWrapper<UserEntity> queryWrapper = SpecWrapperUtil.filter(req);
        queryWrapper.eq("user_type", UserTypeEnum.ACCOUNT.getType());
        queryWrapper.ne("status", UserStatusEnum.DELETED.getType());
        return userEntityMapper.selectPage(
                ObjectUtils.isEmpty(req.getSortDataField()) ? req.pageRequest("status", "created_dt")
                        : req.pageRequest(), queryWrapper);
    }


    public ClusterUserPriorityMappingResp getMappingById(Long id) {
        return clusterUserMappingMapper.selectMappingById(id);
    }


    /**
     * 更新集群用户映射的优先级
     *
     * @param id 映射唯一标识符
     * @param priority 新的优先级值
     *
     * @return 更新操作是否成功
     */
    public boolean updatePriority(Long id, Integer priority) {
        LambdaUpdateWrapper<ClusterUserMapping> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ClusterUserMapping::getId, id)
                     .set(ClusterUserMapping::getPriority, priority);

        return clusterUserMappingMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 分页查询用户与集群的映射关系.
     *
     * @param req 查询条件封装对象
     * @param pageForm 分页信息.
     *
     * @return 分页对象
     */
    public PageResult<ClusterUserPriorityMappingResp> page(QueryUserMappingReq req, PageForm pageForm) {
        Long clusterId = req.getClusterId();
        if (clusterId == null) {
            throw new IllegalArgumentException("clusterId cannot be null");
        }
        Page<ClusterUserPriorityMappingResp> page = new Page<>(pageForm.getPageNo(), pageForm.getPageSize());
        LambdaQueryWrapper<ClusterUserMapping> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ClusterUserMapping::getClusterId, clusterId);
        Page<ClusterUserPriorityMappingResp> resultPage = clusterUserMappingMapper.pagePriority(page, queryWrapper);
        // 执行分页查询
        return PageResult.of(resultPage, ClusterUserPriorityMappingResp.class);
    }


}
