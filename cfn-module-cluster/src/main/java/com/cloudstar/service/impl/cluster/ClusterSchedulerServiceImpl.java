package com.cloudstar.service.impl.cluster;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.mapper.cluster.ClusterSchedulerMapper;
import com.cloudstar.dao.mapper.cluster.ClusterSchedulerResourceGroupMapper;
import com.cloudstar.dao.model.cluster.ClusterScheduler;
import com.cloudstar.service.facade.cluster.ClusterSchedulerService;
import com.cloudstar.service.pojo.dto.cluster.ClusterResourceGroupInfoDto;
import com.cloudstar.service.pojo.dto.cluster.ClusterSchedulerResourceGroupDto;
import com.cloudstar.service.pojo.vo.requestvo.cluster.QueryClusterSchedulerReq;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterSchedulerResp;
import com.cloudstar.service.pojo.vo.responsevo.cluster.ClusterSchedulerWithResourceGroupResp;

import org.springframework.stereotype.Service;

import java.util.List;

import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;

/**
 * 集群调度器服务实现
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
public class ClusterSchedulerServiceImpl extends ServiceImpl<ClusterSchedulerMapper, ClusterScheduler> implements ClusterSchedulerService {

    ClusterSchedulerMapper clusterSchedulerMapper;

    ClusterSchedulerResourceGroupMapper resourceGroupMapper;


    public ClusterSchedulerWithResourceGroupResp getSchedulersWithResourceGroups(Long schedulerId) {
        ClusterSchedulerWithResourceGroupResp schedulerInfo = clusterSchedulerMapper.selectSchedulerInfo(schedulerId);
        List<ClusterResourceGroupInfoDto> resourceGroups = clusterSchedulerMapper.selectResourceGroups(schedulerId);
        if (schedulerInfo != null) {
            schedulerInfo.setResourceGroups(resourceGroups);
        }
        return schedulerInfo;
    }

    public List<ClusterSchedulerResourceGroupDto> getResourceGroupsBySchedulerIds(List<Long> schedulerIds) {
        return resourceGroupMapper.selectResourceGroupsBySchedulerIds(schedulerIds);
    }

    /**
     * 分页查询
     *
     * @param req 筛选条件
     * @param pageForm 分页对象
     */
    @Override
    public PageResult<ClusterSchedulerResp> page(QueryClusterSchedulerReq req, PageForm pageForm) {
        Long clusterId = req.getClusterId();
        if (clusterId == null) {
            throw new IllegalArgumentException("clusterId cannot be null");
        }
        Page<ClusterSchedulerResp> page = new Page<>(pageForm.getPageNo(), pageForm.getPageSize());
        LambdaQueryWrapper<ClusterScheduler> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ClusterScheduler::getClusterId, req.getClusterId());
        Page<ClusterSchedulerResp> resultPage = clusterSchedulerMapper.page(page, queryWrapper);
        return PageResult.of(resultPage, ClusterSchedulerResp.class);
    }


}