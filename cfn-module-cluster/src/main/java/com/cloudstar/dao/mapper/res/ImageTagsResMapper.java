package com.cloudstar.dao.mapper.res;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cloudstar.dao.model.res.ImageTagsRes;

import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 镜像标签映射表
 *
 * <AUTHOR>
 * @date 2024/07/05
 */
public interface ImageTagsResMapper extends BaseMapper<ImageTagsRes> {

    /**
     * 保存批次
     *
     * @param imageTagsResList 镜像标签列表
     */
    void saveBatch(@Param("imageTagsResList") List<ImageTagsRes> imageTagsResList);

    /**
     * 根据id查询标签
     * @param id 镜像id或者算法id
     * @return 对象
     */
    List<ImageTagsRes> selectListById(Long id);
}
