package com.cloudstar.dao.model.cluster;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 集群账号
 *
 * <AUTHOR>
 * @since 2022/7/14 16:41:10
 */
@Data
public class ClusterAccount implements Serializable {

    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 集群
     */
    private Long clusterId;
    /**
     * 适配器uuid
     */
    private String adapterUuid;
    /**
     * 账号uuid
     */
    private String accountUuid;
    /**
     * 账号
     */
    private String account;
    /**
     * 使用状态
     */
    private String useStatus;
    /**
     * 乐观锁
     */
    private String version;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;

    private String obsId;

    private String projectId;
}
