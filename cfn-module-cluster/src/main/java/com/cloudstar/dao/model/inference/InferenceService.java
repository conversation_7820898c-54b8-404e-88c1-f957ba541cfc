package com.cloudstar.dao.model.inference;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("inference_service")
public class InferenceService {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 服务名称
     */
    private String name;
    /**
     * 服务状态
     */
    private String status;
    /**
     * 模型ID
     */
    private Long modelId;
    /**
     * 模型版本ID
     */
    private Long modelVersionId;
    /**
     * 数据源
     */
    private String source;
    /**
     * 部署类型
     */
    private String deployType;
    /**
     * 所有者ID
     */
    private Long ownerId;
    /**
     * 组织ID
     */
    private Long orgSid;
    /**
     * 集群ID
     */
    private Long clusterId;
    /**
     * 描述信息
     */
    private String description;
    /**
     * 是否开启日志
     */
    private Boolean logEnabled;
    /**
     * 公共API地址
     */
    private String publicApiUrl;
    /**
     * 调用指南
     */
    private String callGuide;
    /**
     * 配置ID
     */
    private Long configId;
    /**
     * 监控信息
     */
    private String monitoring;
    /**
     * 事件信息
     */
    private String events;
    /**
     * 标签信息
     */
    private String tags;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
    /**
     * 更新人
     */
    private String updatedBy;
}
