package com.cloudstar.dao.model.user;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.common.encryptdata.annotation.EncryptDecryptClass;
import com.cloudstar.common.encryptdata.annotation.EncryptDecryptField;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

@TableName(value = "user_entity")
@Data
@EncryptDecryptClass
public class UserEntity implements Serializable {

    @TableId
    private Long userSid;

    @TableField(value = "uuid")
    private String uuid;

    @TableField(value = "account")
    private String account;

    @TableField(value = "password")
    private String password;

    @TableField(value = "real_name")
    private String realName;

    @EncryptDecryptField
    @TableField(value = "email")
    private String email;

    @EncryptDecryptField
    @TableField(value = "mobile")
    private String mobile;

    @TableField(value = "user_type")
    private String userType;

    @TableField(value = "parent_sid")
    private Long parentSid;

    @TableField(value = "auth_status")
    private String authStatus;

    @TableField(value = "status")
    private String status;
    
    @TableField(value = "account_status")
    private String accountStatus;

    @TableField(value = "last_login_time")
    private Date lastLoginTime;

    @TableField(value = "last_login_ip")
    private String lastLoginIp;

    @TableField(value = "start_time")
    private Date startTime;

    @TableField(value = "end_time")
    private Date endTime;

    @TableField(value = "password_expires_at")
    private Date passwordExpiresAt;

    @TableField(value = "is_reset_password")
    private String isResetPassword;

    @TableField(value = "created_by")
    private String createdBy;

    @TableField(value = "created_dt")
    private Date createdDt;

    @TableField(value = "updated_by")
    private String updatedBy;

    @TableField(value = "updated_dt")
    private Date updatedDt;

    @TableField(value = "org_sid")
    private Long orgSid;

    @TableField(value = "version")
    private String version;


}
