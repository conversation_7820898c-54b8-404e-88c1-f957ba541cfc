package com.cloudstar.dao.model.res;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 对象存储信息表
 *
 * @TableName res_obs
 */
@TableName(value = "res_obs")
@Data
public class ResObs implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * 用户ID
     */
    private Long ownerId;
    /**
     * 组织ID
     */
    private Long orgSid;
    /**
     * 底层资源ID
     */
    private String uuid;
    /**
     * 桶名称
     */
    private String name;
    /**
     * 使用类型
     */
    private String useType;
    /**
     * 访问策略
     */
    private String accessPolicy;
    /**
     * 对象数
     */
    private String obsSum;
    /**
     * 总容量
     */
    private String totalCapacity;
    /**
     * 已使用容量
     */
    private String usedCapacity;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
    /**
     * 集群id
     */
    private Long clusterId;

}
