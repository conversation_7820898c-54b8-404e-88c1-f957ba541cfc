package com.cloudstar.dao.model.inference;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("inference_service_config")
public class InferenceServiceConfig {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 推理服务ID
     */
    private Long inferenceId;
    /**
     * 模型ID
     */
    private Long modelId;
    /**
     * 模型名称
     */
    private String modelName;
    /**
     * 模型版本
     */
    private String modelVersion;
    /**
     * 状态
     */
    private String status;
    /**
     * 规格ID
     */
    private Long specId;

    /**
     * 资源池ID
     */
    private Long poolId;
    /**
     * 规格信息
     */
    private String spec;
    /**
     * 调用限制
     */
    private Integer rateLimit;
    /**
     * 部署超时时间
     */
    private Integer deployTimeout;
    /**
     * 环境变量
     */
    private String environment;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 更新时间
     */
    private Date updatedDt;
    /**
     * 更新人
     */
    private String updatedBy;
}
