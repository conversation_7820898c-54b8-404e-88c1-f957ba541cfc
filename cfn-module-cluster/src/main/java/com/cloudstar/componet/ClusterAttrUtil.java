package com.cloudstar.componet;

import com.cloudstar.common.base.enums.ClusterAttrConfigEnum;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.service.facade.cluster.ClusterEntityService;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 集群配置获取工具
 *
 * <AUTHOR>
 * @date 2025/05/19
 */
@Component
@Slf4j
public class ClusterAttrUtil {

    @Lazy
    @Resource
    private ClusterEntityService clusterEntityService;

    /**
     * get cluster attr
     *
     * @param clusterId cluster id
     * @param attrName attr name
     *
     * @return {@link String}
     */
    private String getClusterAttr(Long clusterId, String attrName) {
        ClusterEntity clusterEntity = clusterEntityService.getBaseMapper().selectById(clusterId);
        JSONObject jsonObject = JSONUtil.parseObj(clusterEntity.getAttrData());
        if (jsonObject.containsKey(attrName)) {
            return jsonObject.get(attrName, String.class);
        }
        return "";
    }

    /**
     * get job monitor url prefix 获取集群作业监控url地址
     *
     * @param clusterId cluster id
     *
     * @return {@link String}
     */
    public String getJobMonitorUrlPrefix(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.workMonitoring.name());
    }

    /**
     * 获取节点监控url地址配置
     *
     * @param clusterId cluster id
     *
     * @return {@link String}
     */
    public String getNodeMonitorUrlPrefix(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.nodeMonitoring.name());
    }

    /**
     * 获取集群容器仓库地址
     *
     * @param clusterId cluster id
     *
     * @return {@link String}
     */
    public String getSwrUrl(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.selfSwrAddress.name());
    }

    /**
     * 获取集群容器仓库公共镜像账号
     *
     * @param clusterId cluster id
     *
     * @return {@link String}
     */
    public String getPublicSwrAccount(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.selfSwrAccount.name());
    }

    /**
     * 获取集群容器仓库公共镜像密码
     *
     * @param clusterId cluster id
     *
     * @return {@link String}
     */
    public String getPublicSwrPassword(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.selfSwrPassword.name());
    }

    /**
     * 获取集群话单采集ak
     *
     * @param clusterId cluster id
     *
     * @return {@link String}
     */
    public String getSdrAk(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.sdrAk.name());
    }

    /**
     * 获取集群话单采集SK
     *
     * @param clusterId cluster id
     *
     * @return {@link String}
     */
    public String getSdrSk(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.sdrSk.name());
    }

    /**
     * 获取集群话单桶地址
     *
     * @param clusterId cluster id
     *
     * @return {@link String}
     */
    public String getSdrAddress(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.sdrAddress.name());
    }

    /**
     * 获取集群话单桶名称
     *
     * @param clusterId cluster id
     *
     * @return {@link String}
     */
    public String getSdrBucketName(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.sdrBucketName.name());
    }

    /**
     * 获取集群话单桶名称
     *
     * @param clusterId cluster id
     *
     * @return {@link String}
     */
    public Boolean getActivateRefinedQuota(Long clusterId) {
        final String clusterAttr = clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.activateRefinedQuota.name());
        if (ObjectUtil.isEmpty(clusterAttr)) {
            return false;
        }
        if ("true".equals(clusterAttr)) {
            return true;
        }
        return false;
    }

    /**
     * 获取算力底座类型
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getCalculateBase(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.calculateBase.name());
    }

    /**
     * 获取集群obs类型
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getObsType(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.obs.name());
    }

    /**
     * 获取集群swr类型
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getSwrType(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.swr.name());
    }

    /**
     * 获取集群hcs地址
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getHcsAddress(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.hcsAddress.name());
    }

    /**
     * 获取集群hcs ak
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getAccessKey(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.accessKey.name());
    }

    /**
     * 获取集群hcs sk
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getSecretKey(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.secretKey.name());
    }

    /**
     * 获取集群hcs obs地址
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getHwObsAddress(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.hwObsAddress.name());
    }

    /**
     * 获取集群自建obs地址
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getSelfObsAddress(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.selfObsAddress.name());
    }

    /**
     * 获取集群自建obs账号
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getSelfObsAccount(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.selfObsAccount.name());
    }

    /**
     * 获取集群自建obs密码
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getSelfObsPassword(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.selfObsPassword.name());
    }

    /**
     * 获取集群hcs swr地址
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getHwSwrAddress(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.hwSwrAddress.name());
    }

    /**
     * 获取集群hcs swr公共镜像组织
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getHwPublicMirrorOrganization(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.hwPublicMirrorOrganization.name());
    }

    /**
     * 获取集群自建swr公共镜像组织
     * @param clusterId 集群id
     * @return {@link String}
     */
    public String getSelfPublicMirrorOrganization(Long clusterId) {
        return clusterEntityService.getClusterAttr(clusterId, ClusterAttrConfigEnum.selfPublicMirrorOrganization.name());
    }


}
