<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cloudstar</groupId>
        <artifactId>cfn-module-service</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.cloudstar.module.training.job</groupId>
    <artifactId>cfn-module-training-job</artifactId>
    <version>${cfn.module.training.job}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.cloudstar.module.cluster</groupId>
            <artifactId>cfn-module-cluster</artifactId>
            <version>${cfn.module.cluster}</version>
        </dependency>
        <dependency>
            <groupId>com.huaweicloud</groupId>
            <artifactId>esdk-obs-java-bundle</artifactId>
            <version>${esdk-obs-java-bundle.version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <version>${aws-sdk-version}</version>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>apache-client</artifactId>
            <version>${aws-sdk-version}</version>
        </dependency>
        <dependency>
            <groupId>com.cloudstar.common.sysmconfig</groupId>
            <artifactId>cfn-common-component-sysmconfig-starter</artifactId>
            <version>${cfn.common.component.sysmconfig.starter}</version>
        </dependency>
        <dependency>
            <groupId>com.cloudstar.common.async</groupId>
            <artifactId>cfn-common-component-async-starter</artifactId>
            <version>1.0.0-SHAPSHOT</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <properties>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

</project>
