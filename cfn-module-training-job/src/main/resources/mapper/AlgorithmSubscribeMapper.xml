<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.algorithm.AlgorithmSubscribeMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.algorithm.AlgorithmSubscribe">
            <result property="id" column="id" jdbcType="BIGINT"/>
            <result property="algorithmId" column="algorithm_id" jdbcType="BIGINT"/>
            <result property="ownerId" column="owner_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="unit" column="unit" jdbcType="VARCHAR"/>
            <result property="unitValue" column="unit_value" jdbcType="SMALLINT"/>
            <result property="algorithmVersionNum" column="algorithm_version_num" jdbcType="SMALLINT"/>
            <result property="subscribeStartTime" column="subscribe_start_time" jdbcType="TIMESTAMP"/>
            <result property="subscribeEndTime" column="subscribe_end_time" jdbcType="TIMESTAMP"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="version" column="version" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,algorithm_id,owner_id,
        name,unit,unit_value,
        algorithm_version_num,subscribe_start_time,subscribe_end_time,
        description,version,created_by,
        created_dt,updated_by,updated_dt
    </sql>
</mapper>
