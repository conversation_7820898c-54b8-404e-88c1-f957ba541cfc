package com.cloudstar.service.facade.training;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.dao.model.training.TrainingJobParams;
import com.cloudstar.service.pojo.dto.training.TrainingJobParamDto;
import com.cloudstar.service.pojo.vo.requestvo.training.AddOrUpdTrainingJobParamReq;

import java.util.List;

/**
 * 培训工作参数服务
 *
 * <AUTHOR>
 * @description 针对表【training_job_params(训练作业参数)】的数据库操作Service
 * @createDate 2022-08-24 18:01:07
 * @date 2022/08/25
 */
public interface TrainingJobParamsService extends IService<TrainingJobParams> {

    Long addOrUpdTrainingJobParam(AddOrUpdTrainingJobParamReq req);

    Object findOwnAll(String groupName);

    boolean delete(Long groupId);

    String selectLogPathsByParamsGroupId(Long paramsGroupId);

    /**
     * 根据组id查询参数列表
     * @param paramsGroupId 组id
     * @return 参数列表
     */
    List<TrainingJobParamDto> selectByGroupId(Long paramsGroupId);
}
