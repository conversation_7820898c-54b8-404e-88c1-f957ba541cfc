package com.cloudstar.service.facade.training;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cloudstar.common.base.pojo.JobPodInfo;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.dao.model.training.TrainingJobEntity;
import com.cloudstar.sdk.consoleserver.pojo.JobStatisticsRes;
import com.cloudstar.sdk.consoleserver.pojo.QueryJobStatisticsReq;
import com.cloudstar.service.pojo.dto.bigscreen.TrainingJobStatisticDto;
import com.cloudstar.service.pojo.dto.training.QueryTrainingJobEntityDto;
import com.cloudstar.service.pojo.dto.training.TrainingJobEntityDto;
import com.cloudstar.service.pojo.dto.training.TrainingJobStatisticsDto;
import com.cloudstar.service.pojo.dto.training.TrainingJobTrendDto;
import com.cloudstar.service.pojo.vo.requestvo.training.AddTrainingJobReq;
import com.cloudstar.service.pojo.vo.requestvo.training.BizDownloadReq;
import com.cloudstar.service.pojo.vo.requestvo.training.CreateCoordinationJobReq;
import com.cloudstar.service.pojo.vo.requestvo.training.DeleteJobReq;
import com.cloudstar.service.pojo.vo.requestvo.training.QueryCoordinationJobReq;
import com.cloudstar.service.pojo.vo.requestvo.training.QueryTrainingJobEntityListReq;
import com.cloudstar.service.pojo.vo.requestvo.training.UserDetailJobReq;
import com.cloudstar.service.pojo.vo.requestvo.training.UserJobExportReq;
import com.cloudstar.service.pojo.vo.responsevo.overview.OverviewClusterRes;
import com.cloudstar.service.pojo.vo.responsevo.overview.OverviewJobStaticsRes;
import com.cloudstar.service.pojo.vo.responsevo.training.BizUserResponse;
import com.cloudstar.service.pojo.vo.responsevo.training.CoordinationJobResp;
import com.cloudstar.service.pojo.vo.responsevo.training.TrainingJobDetailResp;
import com.cloudstar.service.pojo.vo.responsevo.training.UserDetailJobResponse;
import com.cloudstar.service.pojo.vo.responsevo.training.UserTrainingJobStatisticResp;

import java.util.List;

import javax.servlet.http.HttpServletResponse;

/**
 * 作业service
 *
 * <AUTHOR>
 * @createDate 2022-08-19 14:03:57
 */
public interface TrainingJobEntityService extends IService<TrainingJobEntity> {

    /**
     * 分页查询
     *
     * @param dto 筛选条件
     * @param pageForm 当前页码
     */
    Page<TrainingJobEntityDto> page(QueryTrainingJobEntityDto dto, PageForm pageForm);

    /**
     * 查询列表
     */
    List<TrainingJobEntityDto> getTrainingJobList(QueryTrainingJobEntityListReq dto);

    boolean addTrainingJob(AddTrainingJobReq req);

    /**
     * 根据id 获取作业详情
     *
     * @param id 作业id
     */
    TrainingJobDetailResp queryDetailById(Long id, String action);

    /**
     * 根据id 停止作业
     *
     * @param id 作业id
     */
    Boolean stop(Long id);

    /**
     * 根据id 删除作业
     */
    Boolean delete(DeleteJobReq req);


    /**
     * 创建协同作业
     *
     * @param req 作业参数
     *
     * @return {@link boolean}
     */
    boolean createCoordinationJob(CreateCoordinationJobReq req);

    /**
     * 根据id 取消作业
     *
     * @param id 作业id
     */
    Boolean cancel(Long id);

    JobStatisticsRes overviewStatistics(QueryJobStatisticsReq req);

    /**
     * 分页查询协同作业
     *
     * @param req 筛选条件
     * @param pageForm 当前页码
     *
     * @return 协同作业列表
     */
    PageResult<CoordinationJobResp> getPage(QueryCoordinationJobReq req, PageForm pageForm);

    /**
     * 分页查询
     *
     * @param dto 筛选条件
     * @param pageForm 当前页码
     */
    Page<TrainingJobEntityDto> pageServer(QueryTrainingJobEntityDto dto, PageForm pageForm);

    /**
     * 统计作业情况
     *
     * @return 响应值
     */
    List<TrainingJobStatisticsDto> statisticsTrainingJob(Boolean isHpc);

    UserTrainingJobStatisticResp getUserTrainingJobStatistic(Long userSid);

    List<TrainingJobStatisticDto> getTrainingjobList();

    Integer getUserCount(Long clusterId);

    /**
     * 获取作业执行趋势图
     *
     * @param clusterId 集群id
     * @param timeType 时间类型
     */
    List<TrainingJobTrendDto> getJobTrend(Long clusterId, String timeType);

    Integer getJobStatisticByClusterAndStatus(Long clusterId, String jobStatus);

    Integer getTotalJobNumByClusterId(Long clusterId);

    /**
     * 分页查询协同作业
     *
     * @param req 筛选条件
     * @param pageForm 当前页码
     */
    PageResult<CoordinationJobResp> getPageServer(QueryCoordinationJobReq req, PageForm pageForm);

    Integer getJobCount();

    Integer getSevenDaysJobCount();

    Integer getTotalJobNumByClusterIdAndStatus(Long clusterId, String status);

    /**
     * 集群概述
     *
     * @return {@link List}<{@link OverviewClusterRes}>
     */
    List<OverviewClusterRes> clusterOverview();


    OverviewJobStaticsRes jobStatics();


    /**
     * 获取集群作业使用趋势
     *
     * @param clusterId 集群id
     * @param timeType 时间类型
     */
    List<TrainingJobTrendDto> getClusterJobTrend(Long clusterId, String timeType);

    /**
     * 查询集群运行作业信息
     *
     * @param userDetailJobReq 查询参数
     * @param pageForm 分页参数
     */
    PageResult<UserDetailJobResponse> getJobList(UserDetailJobReq userDetailJobReq, PageForm pageForm);

    void exportJob(UserJobExportReq userJobExportReq);

    PageResult<BizUserResponse> downloadUserList(BizDownloadReq req);

    void downloadUser(Long downloadId, HttpServletResponse response);

    /**
     * 查询volcano作业的监控信息
     *
     * @param id id
     */
    String queryVolcanoJobMetrics(Long id, String podName);

    List<JobPodInfo> queryPodInfo(Long id);
}
