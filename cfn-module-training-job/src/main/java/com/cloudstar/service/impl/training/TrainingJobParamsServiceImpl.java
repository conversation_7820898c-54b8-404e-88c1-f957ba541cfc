package com.cloudstar.service.impl.training;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.enums.ClusterFlagEnum;
import com.cloudstar.common.base.enums.TrainingJobParamsEnum;
import com.cloudstar.common.base.exception.BizError;
import com.cloudstar.common.base.pojo.Criteria;
import com.cloudstar.common.base.pojo.mq.DataStorageResourceMessage;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.pojo.result.RestCodeEnum;
import com.cloudstar.common.base.util.WebUtil;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.dao.mapper.cluster.ClusterEntityMapper;
import com.cloudstar.dao.mapper.datastorage.DataStorageResourcesMapper;
import com.cloudstar.dao.mapper.training.TrainingJobParamsGroupMapper;
import com.cloudstar.dao.mapper.training.TrainingJobParamsMapper;
import com.cloudstar.dao.model.cluster.ClusterEntity;
import com.cloudstar.dao.model.training.TrainingJobParams;
import com.cloudstar.dao.model.training.TrainingJobParamsGroup;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.sdk.schedule.client.ScheduleClient;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountReqDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import com.cloudstar.service.facade.training.TrainingJobParamsGroupService;
import com.cloudstar.service.facade.training.TrainingJobParamsService;
import com.cloudstar.service.pojo.dto.training.TrainingJobParamDto;
import com.cloudstar.service.pojo.vo.requestvo.training.AddOrUpdTrainingJobParamReq;
import com.cloudstar.service.pojo.vo.requestvo.training.TrainingJobParamGroupDto;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * impl params服务培训工作
 *
 * <AUTHOR>
 * @description 针对表【training_job_params(训练作业参数)】的数据库操作Service实现
 * @createDate 2022-08-24 18:01:07
 * @date 2022/08/25
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class TrainingJobParamsServiceImpl extends ServiceImpl<TrainingJobParamsMapper, TrainingJobParams>
        implements TrainingJobParamsService {

    TrainingJobParamsGroupService trainingJobParamsGroupService;

    TrainingJobParamsGroupMapper trainingJobParamsGroupMapper;
    TrainingJobParamsMapper trainingJobParamsMapper;

    DataStorageResourcesMapper dataStorageResourcesMapper;

    ServerClient serverClient;

    ScheduleClient scheduleClient;

    ClusterEntityMapper clusterEntityMapper;


    /**
     * 添加参数
     *
     * @param req 要求事情
     *
     * @return Long 参数组id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addOrUpdTrainingJobParam(AddOrUpdTrainingJobParamReq req) {
        final AuthUser authUser = ThreadAuthUserHolder.getAuthUser();

        if (CollectionUtil.isEmpty(req.getTrainingJobParam())) {
            BizError.e(BizErrorEnum.MSG_1017_PARAMETER_ERROR);
        }
        final List<TrainingJobParams> trainingJobParams = BeanUtil.copyToList(req.getTrainingJobParam(), TrainingJobParams.class);

        //创建训练目录 代码目录直接前端选择传入，不需创建的
        //if (Objects.nonNull(req.getClusterId())) {
        //    createTrainUrl(req, trainingJobParams, authUser);
        //}

        //参数保存只需要保存参数组,对于需要持久化的参数组复制一份，不考虑修改的情况
        TrainingJobParamsGroup paramsGroup = new TrainingJobParamsGroup();
        paramsGroup.setName(req.getGroupName());
        paramsGroup.setDescription(req.getDescription());
        paramsGroup.setPresist(req.getPresist());
        paramsGroup.setEngineType(req.getEngineType());
        WebUtil.prepareInsertParams(paramsGroup, authUser);
        trainingJobParamsGroupService.save(paramsGroup);
        trainingJobParams.forEach(e -> {
            e.setId(null);
            e.setGroupId(paramsGroup.getId());
            WebUtil.prepareInsertParams(e, authUser);
        });
        this.saveBatch(trainingJobParams);
        //不能返回持久化保存的参数组，保存的参数组可能会被修改。重新复制份不持久化的参数返回
        if (Objects.equals(1, req.getPresist())) {
            paramsGroup.setPresist(0);
            paramsGroup.setDescription(req.getDescription());
            paramsGroup.setId(null);
            trainingJobParamsGroupService.save(paramsGroup);
            trainingJobParams.forEach(e -> {
                e.setId(null);
                e.setGroupId(paramsGroup.getId());
            });
            this.saveBatch(trainingJobParams);
        }
        return paramsGroup.getId();
    }

    @Override
    public Object findOwnAll(String groupName) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();

        Criteria criteria = new Criteria();
        criteria.put("ownAccount", authUser.getAccount());
        if (StrUtil.isNotBlank(groupName)) {
            criteria.put("groupName", groupName);
        }
        List<TrainingJobParamGroupDto> trainingJobParamGroups = trainingJobParamsGroupMapper.findOwnAll(criteria);

        return trainingJobParamGroups;
    }

    @Override
    public boolean delete(Long groupId) {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        int delete = trainingJobParamsGroupMapper.delete(
                SpecWrapperUtil.<TrainingJobParamsGroup>lambda()
                               .eq(TrainingJobParamsGroup::getId, groupId)
                               .eq(TrainingJobParamsGroup::getCreatedBy, authUser.getAccount()));
        return delete > 0;
    }

    /**
     * 按params组id选择日志路径
     *
     * @param paramsGroupId 参数组id
     */
    @Override
    public String selectLogPathsByParamsGroupId(Long paramsGroupId) {
        return Optional.ofNullable(trainingJobParamsMapper.selectLogPathsByParamsGroupId(paramsGroupId))
                       .orElse(new TrainingJobParamDto()).getValue();
    }

    @Override
    public List<TrainingJobParamDto> selectByGroupId(Long paramsGroupId) {
        LambdaQueryWrapper<TrainingJobParams> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TrainingJobParams::getGroupId, paramsGroupId);
        List<TrainingJobParams> paramsList = this.list(wrapper);
        return BeanUtil.copyToList(paramsList, TrainingJobParamDto.class);
    }


    private void createTrainUrl(AddOrUpdTrainingJobParamReq req, List<TrainingJobParams> trainingJobParams, AuthUser authUser) {
        //上传算法文件的子用户id 获取系统集群id
        final ClusterEntity clusterEntity = clusterEntityMapper.selectOne(
                new LambdaQueryWrapper<ClusterEntity>().eq(ClusterEntity::getClusterFlag, ClusterFlagEnum.SYSTEM.getType()));
        ClusterSubAccountReqDto clusterSubAccountReqDto = new ClusterSubAccountReqDto();
        clusterSubAccountReqDto.setClusterId(clusterEntity.getId());
        clusterSubAccountReqDto.setUserSid(authUser.getUserSid());
        log.info("上传算法文件的子用户id:{}", clusterSubAccountReqDto);
        Rest<ClusterSubAccountRespDto> response = serverClient.getClusterSubAccount(clusterSubAccountReqDto);

        final String trainUrl = "trainingJob/" + req.getJobId() + "/output/";
        final String logUrl = "trainingJob/" + req.getJobId() + "/log/";
        for (int i = 0; i < trainingJobParams.size(); i++) {
            TrainingJobParams p = trainingJobParams.get(i);
            if (StrUtil.isBlank(p.getName()) && StrUtil.isBlank(p.getValue())) {
                trainingJobParams.remove(i--);
                continue;
            }
            DataStorageResourceMessage dataStorageResourceMessage = new DataStorageResourceMessage();
            dataStorageResourceMessage.setId(req.getJobId());
            dataStorageResourceMessage.setClusterId(clusterEntity.getId());
            dataStorageResourceMessage.setBucketName(response.getData().getBucketName());
            dataStorageResourceMessage.setUserId(response.getData().getUserId());
            dataStorageResourceMessage.setType("createDir");
            if (TrainingJobParamsEnum.TRAIN_URL.getType().equals(p.getName())) {
                //创建训练输出位置目录
                dataStorageResourceMessage.setFileDir(trainUrl);
                long star = System.currentTimeMillis();
                Rest creat = scheduleClient.createDir(dataStorageResourceMessage);
                log.info("创建文件夹耗时【{}】", System.currentTimeMillis() - star);
                if (RestCodeEnum.SERVER_ERROR.getCode() == creat.getCode()) {
                    log.info("创建文件训练输出文件夹失败：{}", creat.getMessage());
                    BizError.e(BizErrorEnum.DEFAULT_EXCEPTION_MSG);
                }
                p.setValue(trainUrl);
            } else if (TrainingJobParamsEnum.LOG_URL.getType().equals(p.getName())) {
                //创建训练输出日志目录
                dataStorageResourceMessage.setFileDir(logUrl);
                long star = System.currentTimeMillis();
                Rest creat = scheduleClient.createDir(dataStorageResourceMessage);
                log.info("创建文件夹耗时【{}】", System.currentTimeMillis() - star);
                if (RestCodeEnum.SERVER_ERROR.getCode() == creat.getCode()) {
                    log.info("创建文件训练输出文件夹失败：{}", creat.getMessage());
                    BizError.e(BizErrorEnum.DEFAULT_EXCEPTION_MSG);
                }
                p.setValue(logUrl);
            }
            log.info("创建作业消息：{}", dataStorageResourceMessage);
        }
    }
}




