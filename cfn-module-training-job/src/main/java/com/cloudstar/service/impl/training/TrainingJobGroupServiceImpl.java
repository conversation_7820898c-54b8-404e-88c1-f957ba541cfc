package com.cloudstar.service.impl.training;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.constant.RedisCacheKeyEnum;
import com.cloudstar.common.base.constant.UserTypeEnum;
import com.cloudstar.common.base.enums.ClusterTypeEnum;
import com.cloudstar.common.base.enums.TrainingJobEventEnum;
import com.cloudstar.common.base.enums.TrainingJobGroupStatusEnum;
import com.cloudstar.common.base.enums.TrainingJobStatusEnum;
import com.cloudstar.common.base.exception.BizError;
import com.cloudstar.common.base.exception.BizException;
import com.cloudstar.common.base.pojo.mq.DataStorageResourceMessage;
import com.cloudstar.common.base.pojo.mq.TrainingJobGroupEventMessage;
import com.cloudstar.common.base.pojo.mq.TrainingJobGroupMessage;
import com.cloudstar.common.base.pojo.result.Rest;
import com.cloudstar.common.base.pojo.result.RestCodeEnum;
import com.cloudstar.common.base.util.UuidUtil;
import com.cloudstar.common.base.util.WebUtil;
import com.cloudstar.common.component.redis.util.RedisUtil;
import com.cloudstar.common.util.page.PageForm;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.dao.mapper.datastorage.DataStorageResourcesMapper;
import com.cloudstar.dao.mapper.training.TrainingJobEntityMapper;
import com.cloudstar.dao.mapper.training.TrainingJobGroupMapper;
import com.cloudstar.dao.mapper.user.SysMOrgMapper;
import com.cloudstar.dao.mapper.user.UserEntityMapper;
import com.cloudstar.dao.model.datastorage.DataStorageResources;
import com.cloudstar.dao.model.training.TrainingJobEntity;
import com.cloudstar.dao.model.training.TrainingJobGroup;
import com.cloudstar.dao.model.training.TrainingJobGroupTenant;
import com.cloudstar.dao.model.user.SysMOrg;
import com.cloudstar.dao.model.user.UserEntity;
import com.cloudstar.mq.ScheduleHelper;
import com.cloudstar.sdk.config.ThreadAuthUserHolder;
import com.cloudstar.sdk.iam.pojo.AuthUser;
import com.cloudstar.sdk.schedule.client.ScheduleClient;
import com.cloudstar.sdk.server.client.ServerClient;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountReqDto;
import com.cloudstar.sdk.server.pojo.ClusterSubAccountRespDto;
import com.cloudstar.service.facade.training.TrainingJobGroupService;
import com.cloudstar.service.facade.training.TrainingJobGroupTenantService;
import com.cloudstar.service.pojo.dto.training.DataResourcesDto;
import com.cloudstar.service.pojo.vo.requestvo.training.AddTrainingJobGroupReq;
import com.cloudstar.service.pojo.vo.requestvo.training.EditTrainingJobGroupReq;
import com.cloudstar.service.pojo.vo.requestvo.training.QueryTrainingJobGroupReq;
import com.cloudstar.service.pojo.vo.responsevo.training.AvailableGroupResp;
import com.cloudstar.service.pojo.vo.responsevo.training.TrainingJobEntityResp;
import com.cloudstar.service.pojo.vo.responsevo.training.TrainingJobGroupDetailResp;
import com.cloudstar.service.pojo.vo.responsevo.training.TrainingJobGroupResp;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;

/**
 * 培训工作小组服务impl
 *
 * <AUTHOR>
 * @description 针对表【training_job_group(作业组)】的数据库操作Service实现
 * @createDate 2022-09-19 14:44:59
 * @date 2022/09/19
 */
@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class TrainingJobGroupServiceImpl extends ServiceImpl<TrainingJobGroupMapper, TrainingJobGroup>
        implements TrainingJobGroupService {

    private static final String REBUILD = "rebuild";

    private static final String COPY_NAME = "-copy-";

    TrainingJobGroupMapper trainingJobGroupMapper;

    TrainingJobGroupTenantService trainingJobGroupTenantService;

    TrainingJobEntityMapper trainingJobEntityMapper;

    SysMOrgMapper orgMapper;

    DataStorageResourcesMapper dataStorageResourcesMapper;

    UserEntityMapper userEntityMapper;

    ScheduleClient scheduleClient;

    ServerClient serverClient;

    RedisUtil redisUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addTrainingJobGroup(AddTrainingJobGroupReq req) {
        checkParameter(req);
        TrainingJobGroup trainingJobGroup = BeanUtil.copyProperties(req, TrainingJobGroup.class);
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        trainingJobGroup.setStatus(TrainingJobGroupStatusEnum.NOT_DEFINED.getType());
        trainingJobGroup.setOrgSid(authUser.getOrgSid());
        trainingJobGroup.setUserSid(authUser.getUserSid());

        trainingJobGroup.setGroupVersion("1");
        WebUtil.prepareInsertParams(trainingJobGroup, authUser);
        //保存创建数据到training_job_group表
        this.save(trainingJobGroup);
        //创建输出日志和输出模型文件夹
        createLogUrl(trainingJobGroup);
        this.updateById(trainingJobGroup);
        //作业组所属用户暂时不需要.取当前登录用户
        TrainingJobGroupTenant trainingJobGroupTenant = new TrainingJobGroupTenant();
        trainingJobGroupTenant.setJobGroupId(trainingJobGroup.getId());
        trainingJobGroupTenant.setTenantId(authUser.getUserSid());
        //保存创建数据到training_job_group_tenant表
        trainingJobGroupTenantService.save(trainingJobGroupTenant);

        //记录事件
        recordJobGroupEvent(trainingJobGroup.getId(), TrainingJobGroupStatusEnum.NOT_DEFINED.getType());
        return true;
    }

    @Override
    public PageResult<TrainingJobGroupResp> selectPage(QueryTrainingJobGroupReq req, PageForm pageForm) {
        LambdaQueryWrapper<TrainingJobGroup> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ne(TrainingJobGroup::getStatus, TrainingJobGroupStatusEnum.DELETED.getType());
        if (ObjectUtil.isNotEmpty(req.getName())) {
            queryWrapper.like(TrainingJobGroup::getName, req.getName());
        }
        if (ObjectUtil.isNotEmpty(req.getStatus())) {
            queryWrapper.in(TrainingJobGroup::getStatus, req.getStatus());
        }
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("登录信息获取失败");
        }
        //若为租户管理员，则查询该组织下所有数据，否则只查询自己的
        UserEntity userEntity = userEntityMapper.selectOne(
                new QueryWrapper<UserEntity>().lambda().eq(UserEntity::getUserSid, authUser.getUserSid()));
        if (UserTypeEnum.ACCOUNT.getType().equals(userEntity.getUserType())) {
            queryWrapper.eq(TrainingJobGroup::getOrgSid, userEntity.getOrgSid());
        } else {
            queryWrapper.eq(TrainingJobGroup::getUserSid, userEntity.getUserSid());
        }
        queryWrapper.orderByDesc(TrainingJobGroup::getUpdatedDt);
        Page<TrainingJobGroup> trainingJobGroupPage = trainingJobGroupMapper.selectPage(pageForm.pageRequest(),
                                                                                        queryWrapper);

        if (ObjectUtil.isNotEmpty(trainingJobGroupPage)) {
            trainingJobGroupPage.getRecords().forEach(e -> {
                long integer = trainingJobEntityMapper.selectCount(Wrappers.<TrainingJobEntity>lambdaQuery()
                                                                           .eq(TrainingJobEntity::getJobGroupId,
                                                                               e.getId())
                                                                           .ne(TrainingJobEntity::getStatus,
                                                                               TrainingJobStatusEnum.DELETED.getType()));

                e.setHasJob(false);
                if (integer > 0) {
                    e.setHasJob(true);
                }
            });
        }
        //作业组列表需要返回协同作业所有当前状态、方便前端控制是否可以启动作业组
        if (ObjectUtil.isNotEmpty(trainingJobGroupPage)) {
            trainingJobGroupPage.getRecords().forEach(e -> {
                List<TrainingJobEntity> jobEntityList = trainingJobEntityMapper.selectList(
                        Wrappers.<TrainingJobEntity>lambdaQuery()
                                .eq(TrainingJobEntity::getJobGroupId, e.getId())
                                .ne(TrainingJobEntity::getStatus,
                                    TrainingJobStatusEnum.DELETED.getType()));
                BeanUtil.copyProperties(jobEntityList, TrainingJobEntityResp.class);
                e.setTaskList(jobEntityList);
            });
        }
        return PageResult.of(trainingJobGroupPage, TrainingJobGroupResp.class);
    }

    @Override
    public TrainingJobGroupDetailResp getGroupDetails(String groupId, String action) {
        final Long gId = Long.valueOf(groupId);
        //通过作业组id获取作业组
        TrainingJobGroup jobGroup = getJobGroupById(groupId);
        TrainingJobGroupDetailResp resp = BeanUtil.copyProperties(jobGroup, TrainingJobGroupDetailResp.class);
        //设置资源信息
        setDataResource(resp, jobGroup);
        if (StrUtil.equals(REBUILD, action)) {
            checkAndReset(resp);
        }
        SysMOrg sysMOrg = orgMapper.selectById(jobGroup.getOrgSid());
        resp.setTenant(sysMOrg.getOrgName());
        List<TrainingJobEntity> jobs = trainingJobEntityMapper.selectList(Wrappers.<TrainingJobEntity>lambdaQuery()
                                                                                  .eq(TrainingJobEntity::getJobGroupId,
                                                                                      gId)
                                                                                  .ne(TrainingJobEntity::getStatus,
                                                                                      TrainingJobStatusEnum.DELETED.getType()));
        resp.setTaskList(BeanUtil.copyToList(jobs, TrainingJobEntityResp.class));
        return resp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteGroup(String taskGroupId) {
        final Long groupId = Long.valueOf(taskGroupId);
        //通过作业组id获取作业组
        TrainingJobGroup jobGroup = getJobGroupById(taskGroupId);
        if (ObjectUtil.isEmpty(jobGroup)) {
            throw new BizException(BizErrorEnum.MSG_1002_NOT_FOUND);
        }
        //启动过的作业组都不能删除(只有未启动的能删除）
        if (!TrainingJobGroupStatusEnum.NOT_DEFINED.getType().equals(jobGroup.getStatus())) {
            BizError.e(BizErrorEnum.MSG_1046_JOB_GROUP_STATUS_CANNOT_DELETE);
        }
        //逻辑删除作业组
        jobGroup.setStatus(TrainingJobGroupStatusEnum.DELETED.getType());
        trainingJobGroupMapper.updateById(jobGroup);
        //删除作业
        List<TrainingJobEntity> jobEntityList = trainingJobEntityMapper.selectList(
                SpecWrapperUtil.<TrainingJobEntity>lambda()
                               .eq(TrainingJobEntity::getJobGroupId, groupId)
                               .isNotNull(TrainingJobEntity::getJobType));
        jobEntityList.forEach(item -> {
            item.setStatus(TrainingJobStatusEnum.DELETED.getType());
            trainingJobEntityMapper.updateById(item);
        });
        //删除作业组和用户的关联关系
        trainingJobGroupTenantService.remove(Wrappers.<TrainingJobGroupTenant>lambdaQuery()
                                                     .eq(TrainingJobGroupTenant::getJobGroupId, groupId));
        return true;
    }

    @Override
    public List<AvailableGroupResp> availableGroup() {
        AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        if (ObjectUtil.isEmpty(authUser)) {
            throw new BizException("登录信息获取失败");
        }
        //查询状态为未启动的作业组
        List<TrainingJobGroup> groups = list(Wrappers.<TrainingJobGroup>lambdaQuery()
                                                     .eq(TrainingJobGroup::getStatus,
                                                         TrainingJobGroupStatusEnum.NOT_DEFINED)
                                                     .eq(TrainingJobGroup::getUserSid, authUser.getUserSid()));
        //如果作业组下的子作业大于等于10则不显示
        List<TrainingJobGroup> collect = groups.stream().filter(item -> {
            long integer = trainingJobEntityMapper.selectCount(Wrappers.<TrainingJobEntity>lambdaQuery()
                                                                       .eq(TrainingJobEntity::getUserSid,
                                                                           authUser.getUserSid())
                                                                       .eq(TrainingJobEntity::getJobGroupId,
                                                                           item.getId())
                                                                       .ne(TrainingJobEntity::getStatus,
                                                                           TrainingJobStatusEnum.DELETED.getType()));
            if (integer >= 10) {
                return false;
            } else {
                return true;
            }
        }).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            return null;
        }
        List<AvailableGroupResp> resps = BeanUtil.copyToList(collect, AvailableGroupResp.class);
        //给每个可用作业组设置镜像名称
        resps.stream().filter(e ->
                                      ObjectUtil.isNotEmpty(dataStorageResourcesMapper.selectById(e.getImageClientId())));

        return resps;
    }

    @Override
    public Boolean start(String taskGroupId) {
        log.info("作业：{}启动开始", taskGroupId);
        final AuthUser authUser = ThreadAuthUserHolder.getAuthUser();
        //通过作业组id获取作业组
        TrainingJobGroup jobGroup = getJobGroupById(taskGroupId);
        if (!TrainingJobGroupStatusEnum.NOT_DEFINED.getType().equals(jobGroup.getStatus())) {
            BizError.e(BizErrorEnum.MSG_1052_JOB_GROUP_STATUS_CANNOT_STAR);
        }
        //判断运行中的作业组是否超过5个的限制
        List<String> asList = Arrays.asList(TrainingJobGroupStatusEnum.RUNNING.getType(),
                                            TrainingJobGroupStatusEnum.STARTING.getType());
        List<TrainingJobGroup> groups = this.list(Wrappers.<TrainingJobGroup>lambdaQuery()
                                                          .in(TrainingJobGroup::getStatus,
                                                              asList));
        if (CollectionUtil.isNotEmpty(groups) && groups.size() > 4) {
            log.info("运行中的作业组是否超过5个的限制,不可启动!");
            BizError.e(BizErrorEnum.MSG_1066_JOB_GROUP_RUNNING_TOO_MANY);
        }
        //判断作业组的协同作业是否全部就绪
        List<TrainingJobEntity> jobEntityList = trainingJobEntityMapper.selectList(
                Wrappers.<TrainingJobEntity>lambdaQuery()
                        .eq(TrainingJobEntity::getJobGroupId, taskGroupId)
                        .ne(TrainingJobEntity::getStatus, TrainingJobStatusEnum.DELETED.getType()));
        if (CollectionUtil.isNotEmpty(jobEntityList)) {
            long integer = trainingJobEntityMapper.selectCount(Wrappers.<TrainingJobEntity>lambdaQuery()
                                                                       .eq(TrainingJobEntity::getJobGroupId,
                                                                           taskGroupId)
                                                                       .eq(TrainingJobEntity::getStatus,
                                                                           TrainingJobStatusEnum.READY.getType()));

            if (jobEntityList.size() < 2 || jobEntityList.size() > 10) {
                log.info("作业组下的协同作业数得大于1小于11才能启动，当前数量为{},不可启动！", jobEntityList.size());
                BizError.e(BizErrorEnum.MSG_1052_JOB_GROUP_STATUS_CANNOT_STAR);
            }
            if (integer != jobEntityList.size()) {
                log.info("作业未全部就绪，启动作业组失败");
                BizError.e(BizErrorEnum.MSG_1052_JOB_GROUP_STATUS_CANNOT_STAR);
            }
        }

        jobGroup.setStartedBy(authUser.getAccount());
        jobGroup.setStartedDt(new Date());
        jobGroup.setStatus(TrainingJobGroupStatusEnum.STARTING.getType());
        TrainingJobGroupMessage message = new TrainingJobGroupMessage();
        message.setJobGroupId(jobGroup.getId());
        message.setEvent("CREATE");
        //发送mq消息
        ScheduleHelper.trainingJobGroupMessage(message);
        //更新作业组状态
        this.updateById(jobGroup);

        //记录事件
        recordJobGroupEvent(jobGroup.getId(), TrainingJobGroupStatusEnum.STARTING.getType());
        return true;
    }

    @Override
    public Boolean stop(String taskGroupId) {
        TrainingJobGroup jobGroup = trainingJobGroupMapper.selectById(Long.valueOf(taskGroupId));
        if (ObjectUtil.isEmpty(jobGroup)) {
            BizError.notFound();
        }
        //目前仅能停止运行中的作业组
        if (!TrainingJobGroupStatusEnum.RUNNING.getType().equals(jobGroup.getStatus())) {
            log.info("状态不为运行中的作业组{}不能停止", taskGroupId);
            throw new BizException(BizErrorEnum.DEFAULT_EXCEPTION_MSG);
        }
        String status = jobGroup.getStatus();
        jobGroup.setStatus(TrainingJobGroupStatusEnum.STOPPING.getType());
        int i = trainingJobGroupMapper.updateById(jobGroup);
        if (i > 0) {
            //将老的状态缓存到redis
            //保存作业组状态
            StringBuilder cacheKeyGroup = new StringBuilder(RedisCacheKeyEnum.TRAINING_JOB_GROUP_STATUS.getKey());
            cacheKeyGroup.append(RedisCacheKeyEnum.SEPARATOR.getKey()).append(jobGroup.getId());
            redisUtil.set(cacheKeyGroup.toString(), status);
            //保存子作业状态
            List<TrainingJobEntity> jobList = trainingJobEntityMapper.selectList(
                    SpecWrapperUtil.<TrainingJobEntity>lambda()
                                   .eq(TrainingJobEntity::getJobGroupId, jobGroup.getId()));
            jobList.forEach(job -> {
                StringBuilder cacheKeyJob = new StringBuilder(RedisCacheKeyEnum.TRAINING_JOB_STATUS.getKey());
                cacheKeyJob.append("::").append(job.getId());
                redisUtil.set(cacheKeyJob.toString(), job.getStatus());
            });

            TrainingJobGroupMessage message = new TrainingJobGroupMessage();
            message.setJobGroupId(jobGroup.getId());
            message.setEvent(TrainingJobEventEnum.STOP.getEvent());
            //发送mq消息
            ScheduleHelper.trainingJobGroupMessage(message);
            //记录事件
            recordJobGroupEvent(jobGroup.getId(), TrainingJobGroupStatusEnum.STOPPING.getType());
        }
        return true;
    }

    @Override
    public PageResult<TrainingJobGroupResp> selectPageServer(QueryTrainingJobGroupReq req, PageForm pageForm) {
        LambdaQueryWrapper<TrainingJobGroup> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.ne(TrainingJobGroup::getStatus, TrainingJobGroupStatusEnum.DELETED.getType());
        if (ObjectUtil.isNotEmpty(req.getName())) {
            queryWrapper.like(TrainingJobGroup::getName, req.getName());
        }
        if (ObjectUtil.isNotEmpty(req.getStatus())) {
            queryWrapper.in(TrainingJobGroup::getStatus, req.getStatus());
        }
        queryWrapper.orderByDesc(TrainingJobGroup::getUpdatedDt);
        Page<TrainingJobGroup> trainingJobGroupPage = trainingJobGroupMapper.selectPage(pageForm.pageRequest(),
                                                                                        queryWrapper);
        //判断作业组有无子作业
        if (ObjectUtil.isNotEmpty(trainingJobGroupPage)) {
            trainingJobGroupPage.getRecords().forEach(e -> {
                long integer = trainingJobEntityMapper.selectCount(Wrappers.<TrainingJobEntity>lambdaQuery()
                                                                           .eq(TrainingJobEntity::getJobGroupId,
                                                                               e.getId())
                                                                           .ne(TrainingJobEntity::getStatus,
                                                                               TrainingJobStatusEnum.DELETED.getType()));

                e.setHasJob(false);
                if (integer > 0) {
                    e.setHasJob(true);
                }
            });
        }
        //作业组列表需要返回协同作业所有当前状态、方便前端控制是否可以启动作业组
        if (ObjectUtil.isNotEmpty(trainingJobGroupPage)) {
            trainingJobGroupPage.getRecords().forEach(e -> {
                List<TrainingJobEntity> jobEntityList = trainingJobEntityMapper.selectList(
                        Wrappers.<TrainingJobEntity>lambdaQuery()
                                .eq(TrainingJobEntity::getJobGroupId, e.getId())
                                .ne(TrainingJobEntity::getStatus,
                                    TrainingJobStatusEnum.DELETED.getType()));
                BeanUtil.copyProperties(jobEntityList, TrainingJobEntityResp.class);
                e.setTaskList(jobEntityList);
            });
        }
        return PageResult.of(trainingJobGroupPage, TrainingJobGroupResp.class);
    }

    @Override
    public TrainingJobGroupDetailResp getGroupDetailsServer(String groupId) {
        Long gId = Long.valueOf(groupId);
        //通过作业组id获取作业组
        TrainingJobGroup jobGroup = getJobGroupById(groupId);
        TrainingJobGroupDetailResp resp = BeanUtil.copyProperties(jobGroup, TrainingJobGroupDetailResp.class);
        //设置资源信息
        setDataResource(resp, jobGroup);

        SysMOrg sysMOrg = orgMapper.selectById(jobGroup.getOrgSid());
        resp.setTenant(sysMOrg.getOrgName());
        List<TrainingJobEntity> jobs = trainingJobEntityMapper.selectList(Wrappers.<TrainingJobEntity>lambdaQuery()
                                                                                  .eq(TrainingJobEntity::getJobGroupId,
                                                                                      gId));
        resp.setTaskList(BeanUtil.copyToList(jobs, TrainingJobEntityResp.class));
        return resp;
    }

    @Override
    public Boolean updateTrainingJobGroupState(Long groupId, TrainingJobGroupStatusEnum jobGroupStatus) {
        TrainingJobGroup group = trainingJobGroupMapper.selectById(groupId);
        group.setStatus(jobGroupStatus.name());
        return trainingJobGroupMapper.updateById(group) > 0;
    }

    @Override
    public TrainingJobGroup getGroupById(Long groupId) {
        return trainingJobGroupMapper.selectById(groupId);
    }

    /**
     * 通过作业组id获取作业组
     *
     * @param taskGroupId 任务组id
     *
     * @return {@link TrainingJobGroup}
     */
    private TrainingJobGroup getJobGroupById(String taskGroupId) {
        Long groupId = Long.valueOf(taskGroupId);
        TrainingJobGroup jobGroup = trainingJobGroupMapper.selectOne1(groupId);
        if (ObjectUtil.isEmpty(jobGroup)) {
            BizError.notFound();
        }
        return jobGroup;
    }


    /**
     * 设置集数据资源
     *
     * @param resp 分别地
     * @param jobGroup 工作小组
     */
    private void setDataResource(TrainingJobGroupDetailResp resp, TrainingJobGroup jobGroup) {
        //融合节点算法
        resp.setAlgorithmServer(BeanUtil.copyProperties(
                dataStorageResourcesMapper.selectOne(Wrappers.<DataStorageResources>lambdaQuery()
                                                             .eq(DataStorageResources::getId,
                                                                 jobGroup.getImageServerId())),
                DataResourcesDto.class));
        //计算节点算法
        List<DataResourcesDto> algorithmClient = BeanUtil.copyToList(dataStorageResourcesMapper.selectList(
                Wrappers.<DataStorageResources>lambdaQuery()
                        .in(DataStorageResources::getId, jobGroup.getImageClientId())), DataResourcesDto.class);
        resp.setAlgorithmClient(algorithmClient);
        //日志输出
        if (ObjectUtil.isNotEmpty(jobGroup.getOutputLogResource())) {
            resp.setOutputLogResourceInfo(BeanUtil.copyProperties(
                    dataStorageResourcesMapper.selectOne(Wrappers.<DataStorageResources>lambdaQuery()
                                                                 .eq(DataStorageResources::getId,
                                                                     jobGroup.getOutputLogResource())),
                    DataResourcesDto.class));
        }
        //输出模型
        if (ObjectUtil.isNotEmpty(jobGroup.getOutputModelResource())) {
            resp.setOutputModelResourceInfo(BeanUtil.copyProperties(
                    dataStorageResourcesMapper.selectOne(Wrappers.<DataStorageResources>lambdaQuery()
                                                                 .eq(DataStorageResources::getId,
                                                                     jobGroup.getOutputModelResource())),
                    DataResourcesDto.class));
        }
    }

    /**
     * 检查参数
     *
     * @param req 要求事情
     */
    private void checkParameter(AddTrainingJobGroupReq req) {
        if (Objects.isNull(req.getName())) {
            BizError.e(BizErrorEnum.MSG_1017_PARAMETER_ERROR);
        }
        //检查同一组织的重名信息
        long count = this.count(
                Wrappers.<TrainingJobGroup>lambdaQuery()
                        .ne(TrainingJobGroup::getStatus, TrainingJobGroupStatusEnum.DELETED.getType())
                        .eq(TrainingJobGroup::getName, req.getName()));

        if (count > 0) {
            BizError.e(BizErrorEnum.MSG_1067_JOB_GROUP_EXIST);
        }
        //检查镜像是否存在
        if (Objects.isNull(dataStorageResourcesMapper.selectById(req.getImageServerId()))) {
            BizError.e(BizErrorEnum.MSG_1002_NOT_FOUND);
        }
        List<DataStorageResources> clientImages = dataStorageResourcesMapper.selectList(
                SpecWrapperUtil.<DataStorageResources>lambda().in(DataStorageResources::getId, req.getImageClientId()));
        if (Objects.isNull(clientImages)) {
            BizError.e(BizErrorEnum.MSG_1002_NOT_FOUND);
        }
        //华为和商汤的镜像只能同时存在一个
        AtomicInteger hcsoNm = new AtomicInteger(0);
        AtomicInteger slurmNm = new AtomicInteger(0);
        clientImages.forEach(image -> {
            if (ClusterTypeEnum.SLURM.getType().equals(image.getClusterType())) {
                hcsoNm.addAndGet(1);
            } else {
                slurmNm.addAndGet(1);
            }
        });
        if (hcsoNm.get() > 1 || slurmNm.get() > 1 || req.getImageClientId().length != clientImages.size()) {
            BizError.e(BizErrorEnum.MSG_1017_PARAMETER_ERROR);
        }
    }

    /**
     * 检查参数
     *
     * @param req 要求事情
     */
    private void checkEditParameter(EditTrainingJobGroupReq req) {
        //镜像文件必须存在
        if (Objects.isNull(req.getImageServerId()) && Objects.isNull(req.getImageClientId())) {
            BizError.e(BizErrorEnum.MSG_1017_PARAMETER_ERROR);
        } else {
            if (Objects.isNull(dataStorageResourcesMapper.selectById(req.getImageServerId())) && Objects.isNull(
                    dataStorageResourcesMapper.selectById(req.getImageClientId()))) {
                BizError.e(BizErrorEnum.MSG_1002_NOT_FOUND);
            }
        }
    }

    /**
     * 检查和调整
     *
     * @param resp 分别地
     */
    private void checkAndReset(TrainingJobGroupDetailResp resp) {
        //调整名称
        resp.setName(rebuildName(resp.getName()));
        //检查镜像
        if (Objects.isNull(dataStorageResourcesMapper.selectById(resp.getAlgorithmServer().getId()))) {
            resp.getAlgorithmServer().setId(null);
            resp.getAlgorithmServer().setName("");
        }
        Set<Long> collect = resp.getAlgorithmClient().stream().map(DataResourcesDto::getId).collect(Collectors.toSet());
        if (Objects.isNull(dataStorageResourcesMapper.selectList(
                SpecWrapperUtil.<DataStorageResources>lambda().in(DataStorageResources::getId, collect)))) {
            resp.setAlgorithmClient(null);
        }
    }

    /**
     * 重建名字
     *
     * @param name 名字
     *
     * @return {@link String}
     */
    private String rebuildName(String name) {
        if (name.length() > 10) {
            String substring = name.substring(name.length() - 10);
            if (substring.startsWith(COPY_NAME)) {
                name = name.substring(0, name.length() - 4) + UuidUtil.randomNumeric16(4);
                return name;
            }
        }
        name = name + COPY_NAME + UuidUtil.randomNumeric16(4);
        return name;
    }

    /**
     * 创建输出文件路径
     *
     * @param job 作业组
     */
    private void createLogUrl(TrainingJobGroup job) {
        //在算法文件的位置创建
        //上传镜像文件的子用户id
        DataStorageResources dataStorageImage = dataStorageResourcesMapper.selectById(job.getImageServerId());
        ClusterSubAccountReqDto clusterSubAccountReqDto = new ClusterSubAccountReqDto();
        clusterSubAccountReqDto.setClusterId(dataStorageImage.getClusterId());
        clusterSubAccountReqDto.setUserSid(dataStorageImage.getOwnerId());
        log.info("上传镜像文件的子用户id:{}", clusterSubAccountReqDto);
        Rest<ClusterSubAccountRespDto> response = serverClient.getClusterSubAccount(clusterSubAccountReqDto);

        final String trainUrl = "trainingJobGroup/" + job.getId() + "/output/";
        final String logUrl = "trainingJobGroup/" + job.getId() + "/log/";
        DataStorageResourceMessage dataStorageResourceMessage = new DataStorageResourceMessage();
        dataStorageResourceMessage.setId(job.getImageServerId());
        dataStorageResourceMessage.setClusterId(dataStorageImage.getClusterId());
        dataStorageResourceMessage.setBucketName(response.getData().getBucketName());
        dataStorageResourceMessage.setUserId(response.getData().getUserId());
        dataStorageResourceMessage.setType("createDir");
        //创建输出模型目录地址
        dataStorageResourceMessage.setFileDir(trainUrl);
        long star = System.currentTimeMillis();
        Rest creat = scheduleClient.createDir(dataStorageResourceMessage);
        log.info("创建文件夹耗时【{}】", System.currentTimeMillis() - star);
        if (RestCodeEnum.SERVER_ERROR.getCode() == creat.getCode()) {
            log.info("创建文件模型输出文件夹失败：{}", creat.getMessage());
            BizError.e(BizErrorEnum.DEFAULT_EXCEPTION_MSG);
        }
        job.setOutputModelUrl(trainUrl);
        //创建输出日志目录地址
        dataStorageResourceMessage.setFileDir(logUrl);
        long star1 = System.currentTimeMillis();
        Rest creat1 = scheduleClient.createDir(dataStorageResourceMessage);
        log.info("创建文件夹耗时【{}】", System.currentTimeMillis() - star1);
        if (RestCodeEnum.SERVER_ERROR.getCode() == creat1.getCode()) {
            log.info("创建文件日志输出文件夹失败：{}", creat1.getMessage());
            BizError.e(BizErrorEnum.DEFAULT_EXCEPTION_MSG);
        }
        job.setOutputLogUrl(logUrl);
        log.info("创建作业消息：{}", dataStorageResourceMessage);
    }


    /**
     * 记录作业组事件
     *
     * @param jobGroupId id
     * @param event 事件
     */
    private void recordJobGroupEvent(Long jobGroupId, String event) {
        TrainingJobGroupEventMessage eventMessage = new TrainingJobGroupEventMessage();
        eventMessage.setJobGroupId(jobGroupId);
        eventMessage.setEvent(event);
        eventMessage.setEventDate(new Date());
        ScheduleHelper.trainingGroupJobEventMessage(eventMessage);
    }
}




