package com.cloudstar.service.impl.training;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.dao.model.training.TrainingJobGroupTenant;
import com.cloudstar.service.facade.training.TrainingJobGroupTenantService;
import com.cloudstar.dao.mapper.training.TrainingJobGroupTenantMapper;
import org.springframework.stereotype.Service;

/**
 * 组租户服务impl培训工作
 *
 * <AUTHOR>
 * @description 针对表【training_job_group_tenant(作业组邀请租户)】的数据库操作Service实现
 * @createDate 2022-09-19 16:44:17
 * @date 2022/09/19
 */
@Service
public class TrainingJobGroupTenantServiceImpl extends ServiceImpl<TrainingJobGroupTenantMapper, TrainingJobGroupTenant>
        implements TrainingJobGroupTenantService {

}




