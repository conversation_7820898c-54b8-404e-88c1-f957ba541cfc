package com.cloudstar.service.pojo.vo.responsevo.datastorage;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

import lombok.Data;

/**
 * 运营分页
 *
 * <AUTHOR>
 * @date 2024/8/22 18:17
 */
@Data
public class DataStorageBssPageResp {


    /**
     * 数据集ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 数据集名称
     */
    private String name;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDt;


}
