package com.cloudstar.service.pojo.vo.requestvo.datastorage;

import com.cloudstar.common.util.page.PageForm;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 分页获取资源
 *
 * <AUTHOR>
 * @date 2022-08-22 14:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DataStorageResourcesPageReq extends PageForm {

    /**
     * 类别
     */
    private String category;

    /**
     * 名字
     */
    private String name;

    /**
     * 集群id
     */
    private Long clusterId;

}
