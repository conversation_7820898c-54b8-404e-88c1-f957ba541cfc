package com.cloudstar.service.pojo.vo.responsevo.datastorage;

import com.cloudstar.sdk.server.pojo.CreatJobParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 查询数据存储resp
 *
 * <AUTHOR>
 * Created on 2022/8/24
 * @date 2022/08/24
 */
@Data
public class QueryDataStorageResp {
    /**
     * id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 数据包名称
     */
    private String name;

    /**
     * 所属集群
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long clusterId;

    /**
     * 所属集群
     */
    private String path;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数据量
     */
    private Long storageUsage;

    /**
     * 集群参数
     */
    private CreatJobParam clusterInfo;

    /**
     * 数据包分类;algorithm dataset model
     */
    private String category;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 集群类型：HCSO,SLURM
     */
    private String clusterType;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDt;
}
