package com.cloudstar.service.pojo.vo.responsevo.datastorage;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 分页查询资源
 *
 * <AUTHOR>
 * @date 2022-08-22 14:59
 */
@Data
public class DataStorageResourcePageResp {

    /**
     * id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 数据包名称
     */
    private String name;

    /**
     * 桶名称
     */
    private String bucket;

    /**
     * 路径
     */
    private String path;

    /**
     * 数据包分类;algorithm dataset model
     */
    private String category;

    /**
     * 数据包分类名称
     */
    private String categoryName;


    /**
     * 所有人
     */
    private String ownerName;
    /**
     * 集群名称
     */
    private String clusterName;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDt;

    /**
     * 数据大小
     */
    private Long storageUsage;

    /**
     * 集群id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long clusterId;

    /**
     * 集群类型（HCSO、SLURM）
     */
    private String clusterType;

    /**
     * 集群类型名
     */
    private String clusterTypeName;
}
