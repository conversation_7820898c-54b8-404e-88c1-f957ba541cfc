package com.cloudstar.service.pojo.vo.requestvo.training;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import lombok.Data;

/**
 * 创建协同作业入参
 *
 * <AUTHOR> Created on 2022/8/24
 * @date 2022/08/24
 */
@Data
public class CreateCoordinationJobReq {

    /**
     * 作业名称
     */
    @Pattern(regexp = "^.{0,32}$", message = "作业名称长度不合法")
    @NotBlank
    private String name;

    /**
     * 作业描述
     */
    private String description;

    /**
     * data_storage_resources:数据集id
     */
    @NotNull
    private Long inputDataResource;

    /**
     * 调度信息:资源感知、数据优先、指定计算集群策略
     */
    private String scheduleInfo;

    /**
     * AI训练引擎
     */
    private Long engineId;

    /**
     * AI训练引擎版本（调度排队需填写）
     */
    private String engineVersion;

    /**
     * 底层资源规格id（调度排队需填写）
     */
    private String flavorId;

    /**
     * data_storage_resources:引用的算法文件包ID
     */
    private Long inputAlgorithmResource;

    /**
     * data_storage_resources:引用的镜像文件ID
     */
    private Long inputImageId;

    /**
     * 启动脚本
     */
    private String lanchScript;

    /**
     * 启动命令
     */
    private String command;

    /**
     * 超参组id
     */
    @Valid
    private AddOrUpdTrainingJobParamReq trainingJobParamReq;

    /**
     * 资源池：专属、共享
     */
    private Long clusterId;

    /**
     * 资源池id(用户选中专属资源池，需填写)
     */
    @NotNull
    private Long poolId;

    /**
     * 资源类型：GPU、CPU
     */
    private String resourceType;

    /**
     * 集群规格id(用户选中共享资源池，需填写)
     */
    private String spec;

    /**
     * 计算节点数量
     */
    @NotNull
    private Integer specNum;
    /**
     * 资源池类型
     */
    private String jobPoolType;
    /**
     * 作业类型：null-普通作业，COORDINATION-协同作业
     */
    private String jobType;
    /**
     * 作业组id
     */
    private String jobGroupId;
    /**
     * 调度策略
     */
    private String policyCode;

}
