package com.cloudstar.service.pojo.vo.requestvo.training;

import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 编辑作业组入参
 *
 * @author: zeng<PERSON>
 * @date: 2022/11/10 18:38
 */
@Data
public class EditTrainingJobGroupReq {

    /**
     * 作业组id
     */
    @NotBlank
    private String id;

    /**
     * 服务端算法文件
     */
    @NotNull
    private Long imageServerId;

    /**
     * 客户端算法文件
     */
    @NotNull
    private Long imageClientId;

    /**
     * 作业描述
     */
    @Length(min = 0, max = 512, message = "描述长度不能超过512")
    private String description;

}
