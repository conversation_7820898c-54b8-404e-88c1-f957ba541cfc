package com.cloudstar.service.pojo.vo.requestvo.training;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 作业训练入参
 *
 * <AUTHOR>
 * @date 2025/6/3 16:50
 */
@Data
public class QueryTrainingJobEntityListReq {

    /**
     * 作业名称
     */
    private String name;

    /**
     * 作业状态
     */
    private String status;

    /**
     * 开始创建时间
     */
    private Date startCreatedDt;

    /**
     * 结束创建时间
     */
    private Date endCreatedDt;

    /**
     * 集群id
     */
    private Long clusterId;

    /**
     * 集群名称
     */
    private String clusterName;

    /**
     * 是否只显示自己
     */
    private Boolean isShowSelf = false;

    /**
     * 作业类型
     */
    private String jobType;

    /**
     * 租户账号
     */
    private String account;


    /**
     * 是否是hpc作业
     */
    private Boolean isHpc = false;


    /**
     * 容器pod name
     */
    private String containerPodName;

    /**
     * 容器ids模糊查询
     */
    private List<String> containerIdsLike;
}
