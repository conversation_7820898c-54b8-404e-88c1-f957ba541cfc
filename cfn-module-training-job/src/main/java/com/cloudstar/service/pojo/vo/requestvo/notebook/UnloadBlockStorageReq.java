package com.cloudstar.service.pojo.vo.requestvo.notebook;

import java.util.List;

import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 卸载存储
 *
 * <AUTHOR>
 * @date 2024/8/7 14:17
 */
@Data
public class UnloadBlockStorageReq {

    /**
     * notebook id
     */
    @NotNull
    private Long notebookId;

    /**
     * 存储id
     */
    @NotNull
    private List<Long> storageIds;
}
