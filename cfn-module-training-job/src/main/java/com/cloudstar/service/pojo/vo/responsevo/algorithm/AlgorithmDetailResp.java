package com.cloudstar.service.pojo.vo.responsevo.algorithm;

import com.cloudstar.service.pojo.dto.training.TrainingJobParamDto;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;
import java.util.List;

import lombok.Data;

/**
 * 算法详情
 *
 * <AUTHOR>
 * @date 2022/8/19 16:54
 */
@Data
public class AlgorithmDetailResp {

    /**
     * 算法id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long id;

    /**
     * 用户id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long userSid;

    /**
     * 组织id
     */
    private Long orgSid;

    /**
     * 算法名称
     */
    private String name;

    /**
     * 算法类型（保留）
     */
    private String algorithmType;

    /**
     * 算法标签（保留）
     */
    private String algorithmTag;

    /**
     * 描述
     */
    private String description;

    /**
     * 启动方式
     */
    private String startType;

    /**
     * 引用的镜像文件ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long inputImageId;


    /**
     * 训练引擎Id
     */
    private String engineId;

    /**
     * 训练引擎名称
     */
    private String engineName;

    /**
     * 引擎id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long clusterEngineId;
    /**
     * 镜像名称
     */
    private String imageName;

    /**
     * 训练引擎版本
     */
    private String engineVersion;

    /**
     * 启动脚本
     */
    private String lanchScript;

    /**
     * 参数组id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paramsGroupId;

    /**
     * 参数
     */
    List<TrainingJobParamDto> paramsGroup;


    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDt;

    /**
     * 算法目录
     */
    private String codeDir;

    /**
     * 启动命令
     */
    private String command;

}
