package com.cloudstar.service.pojo.vo.requestvo.obs;

import javax.validation.constraints.NotNull;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ObsBucketPolicyReq {

    /**
     * 集群id
     */
    @NotNull(message = "clusterId cannot be null")
    private Long clusterId;

    /**
     * 文件路径，用于过滤桶中对象的访问权限
     */
    private String fileDir;

    /**
     * 账户UUID
     */
    private String accountUuid;
}