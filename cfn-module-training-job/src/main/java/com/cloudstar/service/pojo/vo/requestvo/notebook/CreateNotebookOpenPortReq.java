package com.cloudstar.service.pojo.vo.requestvo.notebook;


import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import lombok.Data;

/**
 * 创建prot req
 *
 * <AUTHOR>
 * @date 2024/7/1 10:36
 */
@Data
public class CreateNotebookOpenPortReq {


    /**
     * notebookId
     */
    @NotNull
    private Long notebookId;
    /**
     * 名称
     */
    @NotEmpty
    @Size(max = 32, message = "名称长度超长")
    private String portName;
    /**
     * 描述
     */
    @Min(value = 1)
    @Max(value = 65535)
    @NotNull
    private Long insidePort;

}
