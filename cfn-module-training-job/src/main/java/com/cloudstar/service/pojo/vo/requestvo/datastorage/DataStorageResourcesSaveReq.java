package com.cloudstar.service.pojo.vo.requestvo.datastorage;

import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

import lombok.Data;

/**
 * 添加数据资源
 *
 * <AUTHOR>
 * @date 2022-08-22 15:36
 */
@Data
public class DataStorageResourcesSaveReq {

    /**
     * 名字3
     */
    @NotBlank(message = "名称不能为空")
    @Length(max = 64, message = "长度不能超过64")
    private String name;

    /**
     * 类别名称
     */
    @NotBlank(message = "资源类型不能为空")
    private String category;


    /**
     * 集群id
     */
    private Long clusterId;

    /**
     * 备注
     */
    @Length(max = 500, message = "长度不能超过500")
    private String remark;
    /**
     * 调度类型
     */
    @NotBlank(message = "调度类型不能为空")
    private String scheduleType;
    /**
     * 集群类型：HCSO,SLURM
     */
    private String clusterType;

    /**
     * 训练框架
     */
    private String trainIframe;

    /**
     * 文件源路径
     */
    private String fileSourcePath;
}
