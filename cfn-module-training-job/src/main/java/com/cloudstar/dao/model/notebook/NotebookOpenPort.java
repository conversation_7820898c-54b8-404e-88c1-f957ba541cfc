package com.cloudstar.dao.model.notebook;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.util.Date;

import lombok.Data;

/**
 * 开放端口
 *
 * <AUTHOR>
 * @date 2025/5/14 14:24
 */
@Data
@TableName(value = "notebook_open_port")
public class NotebookOpenPort {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    /**
     * notebookId
     */
    private Long notebookId;

    private String k8sSvcName;

    private String k8sSvcId;
    /**
     * 端口名称
     */
    private String portName;
    /**
     * 容器内部端口
     */
    private Long insidePort;
    /**
     * 容器外部端口
     */
    private Long externalPort;

    private String endpoint;
    /**
     * 乐观锁
     */
    private String version;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private Date createdDt;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private Date updatedDt;

}
