package com.cloudstar.rightcloud.monitor.service.moroptimization.model;

import lombok.Data;

import java.util.Date;

/**
 * 监控指标查询结果集
 *
 * <AUTHOR> Lesao
 */
@Data
public class OptimizationMonitorData {
    /**
     * 时间
     */
    private Date time;

    /**
     * 资源实例id
     */
    private String resourceInstanceId;

    /**
     * 监控数据类型
     */
    private String type;

    /**
     * 监控指标项
     */
    private String metric;

    /**
     * 采集指标值（平均值）
     */
    private Object value;

    /**
     * 最小值
     */
    private Object minValue;

    /**
     * 最大值
     */
    private Object maxValue;

    /**
     * 95分位值
     */
    private Object percentValue;
}
