package com.cloudstar.rightcloud.monitor.service.opsexporter;

import com.cloudstar.rightcloud.api.resource.defineenv.form.DefineEnvGetDetailsFeignForm;
import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.exception.BizException;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.LocaleLanguageContextUtil;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.common.utils.message.MessageUtil;
import com.cloudstar.rightcloud.data.util.page.PageHelperUtil;
import com.cloudstar.rightcloud.monitor.client.opsaccess.param.OpsMonitorEnvCheckParam;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryGetListResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsEnvGetListResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.service.OpsCmdbService;
import com.cloudstar.rightcloud.monitor.client.opsexporter.param.OpsCollectExporterCheckEnvParam;
import com.cloudstar.rightcloud.monitor.client.opsexporter.param.OpsExporterCreateParam;
import com.cloudstar.rightcloud.monitor.client.opsexporter.param.OpsExporterCreateParamsParam;
import com.cloudstar.rightcloud.monitor.client.opsexporter.param.OpsExporterGetPageParam;
import com.cloudstar.rightcloud.monitor.client.opsexporter.param.OpsExporterUpdateParam;
import com.cloudstar.rightcloud.monitor.client.opsexporter.param.OpsExporterUpdateParamsParam;
import com.cloudstar.rightcloud.monitor.client.opsexporter.result.OpsExporterByParamGetListResult;
import com.cloudstar.rightcloud.monitor.client.opsexporter.result.OpsExporterGetDetailsParamsResult;
import com.cloudstar.rightcloud.monitor.client.opsexporter.result.OpsExporterGetDetailsResult;
import com.cloudstar.rightcloud.monitor.client.opsexporter.result.OpsExporterGetListResult;
import com.cloudstar.rightcloud.monitor.client.opsexporter.result.OpsExporterGetPageResult;
import com.cloudstar.rightcloud.monitor.client.opsexporter.result.OpsExporterParamsGetListResult;
import com.cloudstar.rightcloud.monitor.client.opsexporter.service.OpsExporterService;
import com.cloudstar.rightcloud.monitor.common.constant.StringPool;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.common.constant.msg.OpsMonitorMsgConstant;
import com.cloudstar.rightcloud.monitor.common.util.StringUtils;
import com.cloudstar.rightcloud.monitor.data.opscollectrule.dao.OpsCollectRuleDao;
import com.cloudstar.rightcloud.monitor.data.opscollectrule.dto.OpsTableCollectRuleResultDto;
import com.cloudstar.rightcloud.monitor.data.opscollectrule.dto.query.OpsCollectRuleQueryDto;
import com.cloudstar.rightcloud.monitor.data.opscollecttask.dao.OpsCollectTaskDao;
import com.cloudstar.rightcloud.monitor.data.opscollecttask.dto.OpsTableCollectTaskResultDto;
import com.cloudstar.rightcloud.monitor.data.opscollecttask.dto.query.OpsCollectTaskQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsexporter.dao.OpsExporterDao;
import com.cloudstar.rightcloud.monitor.data.opsexporter.dto.OpsTableExporterResultDto;
import com.cloudstar.rightcloud.monitor.data.opsexporter.dto.query.OpsExporterQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsexporterparam.dao.OpsExporterParamDao;
import com.cloudstar.rightcloud.monitor.data.opsexporterparam.dto.OpsTableExporterParamResultDto;
import com.cloudstar.rightcloud.monitor.data.opsexporterparam.dto.query.OpsExporterParamQueryDto;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * exporter service
 *
 * @author: wanglang
 * @date: 2023/2/15 11:15 AM
 */
@Service
@AllArgsConstructor
@Slf4j
public class OpsExporterServiceImpl implements OpsExporterService {

    private final OpsExporterDao opsExporterDao;

    private final OpsExporterParamDao opsExporterParamDao;

    private final OpsCollectTaskDao opsCollectTaskDao;

    private final OpsCollectRuleDao opsCollectRuleDao;

    private final OpsCmdbService opsCmdbService;

    @Transactional
    @Override
    public RightCloudResult<Long> createExporter(OpsExporterCreateParam opsExporterCreateParam) {
        BizAssertUtils.notNull(opsExporterCreateParam,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.EXPORTER_PARAM);
        // 校验名称
        // 创建
        final OpsTableExporterResultDto opsTableExporterResultDto =
                BeanHelperUtil.copyForBean(OpsTableExporterResultDto::new, opsExporterCreateParam);
        // 校验参数
        this.checkoutCollectExporter(opsTableExporterResultDto, opsExporterCreateParam.getResTypeCodes());
        opsTableExporterResultDto.setNameEn(opsTableExporterResultDto.getName());
        opsTableExporterResultDto.setExporterNameEn(opsTableExporterResultDto.getExporterName());
        opsTableExporterResultDto.setDescriptionEn(opsTableExporterResultDto.getDescription());
        Boolean result = true;
        result = opsExporterDao.insert(opsTableExporterResultDto);
        // 参数
        final List<OpsExporterCreateParamsParam> exporterCreateParamsParams = opsExporterCreateParam.getExporterCreateParamsParams();
        if (CollectionUtil.isNotEmpty(exporterCreateParamsParams)) {
            final long count = exporterCreateParamsParams.stream()
                    .filter(Objects::nonNull)
                    .map(OpsExporterCreateParamsParam::getParamValue)
                    .count();
            BizAssertUtils.isFalse(
                    exporterCreateParamsParams.size() > count,
                    CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                    MonitorFieldKeyConstant.EXPORTER_PARAM
            );
            final List<OpsTableExporterParamResultDto> opsTableExporterParamResultDtoList = exporterCreateParamsParams.stream()
                    .filter(Objects::nonNull)
                    .map(opsExporterCreateParamsParam -> {
                        final OpsTableExporterParamResultDto opsTableExporterParamResultDto =
                                BeanHelperUtil.copyForBean(OpsTableExporterParamResultDto::new, opsExporterCreateParamsParam);
                        opsTableExporterParamResultDto.setExporterId(opsTableExporterResultDto.getId());
                        opsTableExporterParamResultDto.setParamNameEn(opsTableExporterResultDto.getName());
                        opsTableExporterParamResultDto.setDescriptionEn(opsTableExporterResultDto.getDescription());
                        return opsTableExporterParamResultDto;
                    }).collect(Collectors.toList());
            result = opsExporterParamDao.insertBatch(opsTableExporterParamResultDtoList);
        }
        BizAssertUtils.isTrue(result, CommonMsgConstant.COMMON_ERROR_HANDLE_FAIL, MonitorFieldKeyConstant.EXPORTER);
        return RightCloudResult.success(opsTableExporterResultDto.getId());
    }

    @Transactional
    @Override
    public RightCloudResult<Boolean> updateExporter(OpsExporterUpdateParam opsExporterUpdateParam) {
        BizAssertUtils.notNull(opsExporterUpdateParam,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.EXPORTER_PARAM);
        BizAssertUtils.notNull(opsExporterUpdateParam.getId(),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.ID);
        // 校验名称是否重复
        // 更新
        final OpsTableExporterResultDto opsTableExporterResultDto =
                BeanHelperUtil.copyForBean(OpsTableExporterResultDto::new, opsExporterUpdateParam);
        this.checkoutCollectExporter(opsTableExporterResultDto, opsExporterUpdateParam.getResTypeCodes());
        opsTableExporterResultDto.setNameEn(opsTableExporterResultDto.getName());
        opsTableExporterResultDto.setExporterNameEn(opsTableExporterResultDto.getExporterName());
        opsTableExporterResultDto.setDescriptionEn(opsTableExporterResultDto.getDescription());
        Boolean result = true;
        result = opsExporterDao.update(opsTableExporterResultDto);
        // 参数
        final List<OpsExporterUpdateParamsParam> exporterUpdateParamsParams =
                opsExporterUpdateParam.getExporterUpdateParamsParams();
        if (CollectionUtil.isNotEmpty(exporterUpdateParamsParams)) {
            final long count = exporterUpdateParamsParams.stream()
                    .filter(Objects::nonNull)
                    .map(OpsExporterUpdateParamsParam::getParamValue)
                    .count();
            BizAssertUtils.isFalse(
                    exporterUpdateParamsParams.size() > count,
                    CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                    MonitorFieldKeyConstant.EXPORTER_PARAM
            );
            result = opsExporterParamDao.delete(
                    OpsExporterParamQueryDto.builder()
                            .exporterIds(List.of(opsTableExporterResultDto.getId()))
                            .build()
            );
            if (result) {
                final List<OpsTableExporterParamResultDto> opsTableExporterParamUpdateResult = exporterUpdateParamsParams.stream()
                        .filter(Objects::nonNull)
                        .map(opsExporterUpdateParamsParam -> {
                            final OpsTableExporterParamResultDto opsTableExporterParamResultDto =
                                    BeanHelperUtil.copyForBean(OpsTableExporterParamResultDto::new, opsExporterUpdateParamsParam);
                            opsTableExporterParamResultDto.setExporterId(opsExporterUpdateParam.getId());
                            opsTableExporterParamResultDto.setParamNameEn(opsTableExporterResultDto.getName());
                            opsTableExporterParamResultDto.setDescriptionEn(opsTableExporterResultDto.getDescription());
                            return opsTableExporterParamResultDto;
                        }).collect(Collectors.toList());
                // 更新
                if (CollectionUtil.isNotEmpty(opsTableExporterParamUpdateResult)) {
                    result = opsExporterParamDao.insertBatch(opsTableExporterParamUpdateResult);
                }
            }
        }
        return RightCloudResult.success(result);
    }

    @Override
    public RightCloudResult<OpsExporterGetDetailsResult> getExporterDetails(Long id) {
        BizAssertUtils.notNull(id, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY, MonitorFieldKeyConstant.ID);
        // 获取数据
        final OpsTableExporterResultDto opsTableExporterResultDto = opsExporterDao.selectOne(
                OpsExporterQueryDto.builder()
                        .id(id)
                        .build()
        );
        BizAssertUtils.notNull(opsTableExporterResultDto, CommonMsgConstant.COMMON_ERROR_HANDLE_INVALIDPARAMETER);
        final OpsExporterGetDetailsResult opsExporterGetDetailsResult =
                BeanHelperUtil.copyForBean(OpsExporterGetDetailsResult::new, opsTableExporterResultDto);
        if (StringUtils.isNotEmpty(opsTableExporterResultDto.getResTypeCodes())) {
            final String[] split = opsTableExporterResultDto.getResTypeCodes().split(StringPool.COMMA);
            if (ArrayUtil.isNotEmpty(split)) {
                final List<String> resTypeCodes = List.of(split);
                opsExporterGetDetailsResult.setResTypeCodes(resTypeCodes);
            }
        }
        // 获取参数
        final List<OpsTableExporterParamResultDto> opsTableExporterParamResultDtos = opsExporterParamDao.selectList(
                OpsExporterParamQueryDto.builder()
                        .exporterId(opsExporterGetDetailsResult.getId())
                        .build()
        );
        List<OpsExporterGetDetailsParamsResult> opsExporterGetDetailsParamsResults = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(opsTableExporterParamResultDtos)) {
            opsExporterGetDetailsParamsResults = BeanHelperUtil.copyForList(OpsExporterGetDetailsParamsResult::new, opsTableExporterParamResultDtos);
        }
        opsExporterGetDetailsResult.setExporterGetDetailsParamsResults(opsExporterGetDetailsParamsResults);
        return RightCloudResult.success(opsExporterGetDetailsResult);
    }

    @Override
    public RightCloudResult<PageResult<OpsExporterGetPageResult>> getExporterPage(OpsExporterGetPageParam opsExporterGetPageParam) {
        // 构造条件
        final OpsExporterQueryDto opsExporterQueryDto = OpsExporterQueryDto.builder().build();
        // 组件名称
        Boolean en = LocaleLanguageContextUtil.isEn();
        if (StringUtils.isNotEmpty(opsExporterGetPageParam.getName())) {
            if (en) {
                opsExporterQueryDto.setNameEn(opsExporterGetPageParam.getName());
            } else {
                opsExporterQueryDto.setName(opsExporterGetPageParam.getName());
            }
        }
        // exporter名称
        if (StringUtils.isNotEmpty(opsExporterGetPageParam.getExporterName())) {
            if (en) {
                opsExporterQueryDto.setExporterNameEn(opsExporterGetPageParam.getExporterName());
            } else {
                opsExporterQueryDto.setExporterName(opsExporterGetPageParam.getExporterName());
            }
        }
        // 云平台
        if (StringUtils.isNotEmpty(opsExporterGetPageParam.getEnvType())) {
            final String[] envTypeArray = opsExporterGetPageParam.getEnvType().split(StringPool.COMMA);
            if (ArrayUtil.isNotEmpty(envTypeArray) && envTypeArray.length > 1) {
                String envCode = envTypeArray[0];
                String envVersion = envTypeArray[1];
                opsExporterQueryDto.setEnvCode(envCode);
                opsExporterQueryDto.setEnvVersion(envVersion);
            }
        }
        final PageResult<OpsTableExporterResultDto> tableExporterResultDtoPageResult =
                PageHelperUtil.doSelectPageResult(opsExporterGetPageParam, OpsTableExporterResultDto.class, () -> {
                    opsExporterDao.selectList(opsExporterQueryDto);
                });
        final PageResult<OpsExporterGetPageResult> opsExporterGetPageResultPageResult = new PageResult<>();
        opsExporterGetPageResultPageResult.setPageNo(tableExporterResultDtoPageResult.getPageNo());
        opsExporterGetPageResultPageResult.setTotalPages(tableExporterResultDtoPageResult.getTotalPages());
        opsExporterGetPageResultPageResult.setPageSize(tableExporterResultDtoPageResult.getPageSize());
        opsExporterGetPageResultPageResult.setTotal(tableExporterResultDtoPageResult.getTotal());
        List<OpsExporterGetPageResult> collect = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(tableExporterResultDtoPageResult.getList())) {
            final List<OpsTableExporterResultDto> opsTableExporterResultDtoList = tableExporterResultDtoPageResult.getList();
            collect = opsTableExporterResultDtoList.stream()
                    .filter(Objects::nonNull)
                    .map(opsTableExporterResultDto -> {
                        final OpsExporterGetPageResult opsExporterGetPageResult =
                                BeanHelperUtil.copyForBean(OpsExporterGetPageResult::new, opsTableExporterResultDto);
                        if (StringUtils.isNotEmpty(opsTableExporterResultDto.getResTypeCodes())) {
                            final String[] split = opsTableExporterResultDto.getResTypeCodes().split(StringPool.COMMA);
                            if (ArrayUtil.isNotEmpty(split)) {
                                final List<String> resTypeCodes = List.of(split);
                                opsExporterGetPageResult.setResTypeCodes(resTypeCodes);
                            }
                        }
                        return opsExporterGetPageResult;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            // 查询云平台数据
            // 查询云平台数据
            Map<DefineEnvGetDetailsFeignForm, OpsEnvGetListResult> detailsFeignFormDefineEnvDetailFeignResultMap = null;
            final RightCloudResult<List<OpsEnvGetListResult>> defineEnvGetDetailsList = opsCmdbService.getEnvList();
            if (Objects.nonNull(defineEnvGetDetailsList) && CollectionUtil.isNotEmpty(defineEnvGetDetailsList.getData())) {
                final List<OpsEnvGetListResult> defineEnvDetailFeignResults = defineEnvGetDetailsList.getData();
                detailsFeignFormDefineEnvDetailFeignResultMap = defineEnvDetailFeignResults.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(
                                defineEnvDetailFeignResult -> {
                                    DefineEnvGetDetailsFeignForm defineEnvGetDetailsFeignForm = new DefineEnvGetDetailsFeignForm();
                                    defineEnvGetDetailsFeignForm.setEnvCode(defineEnvDetailFeignResult.getEnvCode());
                                    defineEnvGetDetailsFeignForm.setEnvVersion(defineEnvDetailFeignResult.getVersionNumber());
                                    return defineEnvGetDetailsFeignForm;
                                },
                                defineEnvDetailFeignResult -> defineEnvDetailFeignResult
                        ));
            }
            if (Objects.nonNull(defineEnvGetDetailsList) && CollectionUtil.isNotEmpty(defineEnvGetDetailsList.getData())) {
                final List<OpsEnvGetListResult> defineEnvDetailFeignResults = defineEnvGetDetailsList.getData();
                detailsFeignFormDefineEnvDetailFeignResultMap = defineEnvDetailFeignResults.stream()
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(
                                defineEnvDetailFeignResult -> {
                                    DefineEnvGetDetailsFeignForm defineEnvGetDetailsFeignForm = new DefineEnvGetDetailsFeignForm();
                                    defineEnvGetDetailsFeignForm.setEnvCode(defineEnvDetailFeignResult.getEnvCode());
                                    defineEnvGetDetailsFeignForm.setEnvVersion(defineEnvDetailFeignResult.getVersionNumber());
                                    return defineEnvGetDetailsFeignForm;
                                },
                                defineEnvDetailFeignResult -> defineEnvDetailFeignResult
                        ));
            }
            if (CollectionUtil.isNotEmpty(detailsFeignFormDefineEnvDetailFeignResultMap)) {
                for (OpsExporterGetPageResult opsExporterGetPageResult : collect) {
                    DefineEnvGetDetailsFeignForm defineEnvGetDetailsFeignForm = new DefineEnvGetDetailsFeignForm();
                    defineEnvGetDetailsFeignForm.setEnvCode(opsExporterGetPageResult.getEnvCode());
                    defineEnvGetDetailsFeignForm.setEnvVersion(opsExporterGetPageResult.getEnvVersion());
                    final OpsEnvGetListResult defineEnvDetailFeignResult =
                            detailsFeignFormDefineEnvDetailFeignResultMap.get(defineEnvGetDetailsFeignForm);
                    if (Objects.nonNull(defineEnvDetailFeignResult)) {
                        opsExporterGetPageResult.setEnvNameEn(defineEnvDetailFeignResult.getNameEn());
                        opsExporterGetPageResult.setEnvName(defineEnvDetailFeignResult.getName());
                        opsExporterGetPageResult.setEnvIcon(defineEnvDetailFeignResult.getIcon());
                    }
                }
            }
            // 查询资源数据
            Map<String, String> cmdbResCategoryGetListFeignResultMap = null;
            List<String> resTypeCodeList = new ArrayList<>();
            final List<List<String>> reTypeCodes = collect.stream()
                    .filter(Objects::nonNull)
                    .map(OpsExporterGetPageResult::getResTypeCodes)
                    .filter(CollectionUtil::isNotEmpty)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(reTypeCodes)) {
                for (List<String> reTypeCode : reTypeCodes) {
                    resTypeCodeList.addAll(reTypeCode);
                }
            }
            resTypeCodeList = resTypeCodeList.stream().distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(resTypeCodeList)) {
                final RightCloudResult<List<OpsCategoryGetListResult>> categoryDataList =
                        opsCmdbService.getCategoryDataList(resTypeCodeList.stream().distinct().collect(Collectors.toList()));
                if (Objects.nonNull(categoryDataList) && CollectionUtil.isNotEmpty(categoryDataList.getData())) {
                    final List<OpsCategoryGetListResult> cmdbResCategoryGetListFeignResults = categoryDataList.getData();
                    cmdbResCategoryGetListFeignResultMap = cmdbResCategoryGetListFeignResults.stream()
                            .filter(Objects::nonNull)
                            .collect(Collectors.toMap(OpsCategoryGetListResult::getCode,
                                    OpsCategoryGetListResult::getName));
                }
            }
            if (CollectionUtil.isNotEmpty(cmdbResCategoryGetListFeignResultMap)) {
                for (OpsExporterGetPageResult opsExporterGetPageResult : collect) {
                    final List<String> resTypeCodes = opsExporterGetPageResult.getResTypeCodes();

                    if (CollectionUtil.isNotEmpty(resTypeCodes)) {
                        final List<String> resTypeNames = new ArrayList<>();
                        for (String resTypeCode : resTypeCodes) {
                            final String categoryName = cmdbResCategoryGetListFeignResultMap.get(resTypeCode);
                            resTypeNames.add(categoryName);
                        }
                        opsExporterGetPageResult.setResTypeNames(resTypeNames);
                    }

                }
            }

        }
        opsExporterGetPageResultPageResult.setList(collect);


        return RightCloudResult.success(opsExporterGetPageResultPageResult);
    }

    @Transactional
    @Override
    public RightCloudResult<Boolean> deleteExporter(List<Long> ids) {
        BizAssertUtils.notEmpty(ids,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY, MonitorFieldKeyConstant.ID);

        // 校验数据是否有效
        OpsExporterQueryDto opsExporterQueryDto = OpsExporterQueryDto.builder().build();
        opsExporterQueryDto.setIds(ids);
        long count = opsExporterDao.selectCount(opsExporterQueryDto);
        if (count - ids.size() != 0) {
            throw new BizException(MessageUtil.getMessage(CommonMsgConstant.COMMON_ERROR_HANDLE_INVALIDPARAMETER));
        }

        // 校验当前exporter有没有正在使用
        final List<OpsTableCollectTaskResultDto> opsTableCollectTaskResultDto = opsCollectTaskDao.selectList(
                OpsCollectTaskQueryDto.builder()
                        .opsExporterIds(ids)
                        .build()
        );
        BizAssertUtils.isEmpty(opsTableCollectTaskResultDto,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_CANNOTDELETE,
                MonitorFieldKeyConstant.EXPORTER,
                MonitorFieldKeyConstant.COLLECT_TASK);
        // 校验采集规则没有在使用
        final List<OpsTableCollectRuleResultDto> opsTableCollectRuleResultDtoList = opsCollectRuleDao.selectListNoDataFilter(
                OpsCollectRuleQueryDto.builder()
                        .opsExporterIds(ids)
                        .build()
        );
        BizAssertUtils.isEmpty(opsTableCollectRuleResultDtoList,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_CANNOTDELETE,
                MonitorFieldKeyConstant.EXPORTER,
                MonitorFieldKeyConstant.COLLECT_RULE
        );
        // 删除exporter
        Boolean result = true;
        result = opsExporterDao.deleteByIds(ids);
        // 删除exporter params
        result = opsExporterParamDao.delete(
                OpsExporterParamQueryDto.builder()
                        .exporterIds(ids)
                        .build()
        );
        return RightCloudResult.success(result);
    }

    @Override
    public RightCloudResult<List<OpsExporterByParamGetListResult>> getExporterByParams() {
        final List<OpsTableExporterResultDto> exporterResultDtoList = opsExporterDao.selectList(
                null
        );
        List<OpsExporterByParamGetListResult> opsExporterResultList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(exporterResultDtoList)) {
            // 转换类型
            opsExporterResultList = BeanHelperUtil.copyForList(OpsExporterByParamGetListResult::new, exporterResultDtoList);
            if (CollectionUtil.isNotEmpty(opsExporterResultList)) {
                // 获取exporter参数列表
                final List<Long> exporterIdList = opsExporterResultList
                        .stream()
                        .filter(Objects::nonNull).map(OpsExporterByParamGetListResult::getId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(exporterIdList)) {
                    final List<OpsTableExporterParamResultDto> opsTableExporterParamResultDtoList = opsExporterParamDao.selectList(
                            OpsExporterParamQueryDto.builder()
                                    .exporterIds(exporterIdList)
                                    .build()
                    );
                    if (CollectionUtil.isNotEmpty(opsTableExporterParamResultDtoList)) {
                        final Map<Long, List<OpsTableExporterParamResultDto>> opsTableExporterParamResultMap =
                                opsTableExporterParamResultDtoList
                                        .stream()
                                        .filter(Objects::nonNull).collect(Collectors.groupingBy(OpsTableExporterParamResultDto::getExporterId));
                        for (OpsExporterByParamGetListResult opsExporterResult : opsExporterResultList) {
                            // 获取参数列表
                            final List<OpsTableExporterParamResultDto> opsTableExporterParamResultList =
                                    opsTableExporterParamResultMap.get(opsExporterResult.getId());
                            if (CollectionUtil.isNotEmpty(opsTableExporterParamResultList)) {
                                opsExporterResult.setOpsExporterParamsGetListResults(
                                        BeanHelperUtil.copyForList(OpsExporterParamsGetListResult::new,
                                                opsTableExporterParamResultList));
                            }
                        }
                    }

                }
            }
        }
        return RightCloudResult.success(opsExporterResultList);
    }

    @Override
    public RightCloudResult<List<OpsExporterGetListResult>> getExporterList() {
        final List<OpsTableExporterResultDto> exporterResultDtoList = opsExporterDao.selectList(
                null
        );
        final List<OpsExporterGetListResult> opsExporterGetListResults =
                BeanHelperUtil.copyForList(OpsExporterGetListResult::new, exporterResultDtoList);
        return RightCloudResult.success(opsExporterGetListResults);
    }

    @Override
    public RightCloudResult<Boolean> checkEnvExporterConfiguration(OpsCollectExporterCheckEnvParam param) {
        return null;
    }


    /**
     * 校验采集组件
     *
     * @param exporterResultDto 采集组件
     * @param resTypeCodes      资源类型
     */
    private void checkoutCollectExporter(OpsTableExporterResultDto exporterResultDto,
                                         List<String> resTypeCodes) {
        // 校验名称
        OpsTableExporterResultDto checkNameExporterResultDto = null;
        OpsExporterQueryDto build = OpsExporterQueryDto.builder().build();
        if (LocaleLanguageContextUtil.isEn()) {
            build.setNameEn(exporterResultDto.getName());
        } else {
            build.setName(exporterResultDto.getName());
        }
        if (StringUtils.isNotEmpty(exporterResultDto.getName()) && Objects.nonNull(exporterResultDto.getId())) {
            build.setNotInIds(List.of(exporterResultDto.getId()));
            checkNameExporterResultDto = opsExporterDao.selectOne(build);
        } else {
            checkNameExporterResultDto = opsExporterDao.selectOne(build);
        }
        BizAssertUtils.isNull(checkNameExporterResultDto,
                OpsMonitorMsgConstant.MONITOR_ERROR_VALIDATION_CONSTRAINT_NAME,
                MonitorFieldKeyConstant.EXPORTER_NAME);

        // 校验采集地址
        this.checkoutCollectMetricPath(exporterResultDto.getCollectorPath());
        if (CollectionUtil.isNotEmpty(resTypeCodes)) {
            final String resTypeCods = String.join(StringPool.COMMA, resTypeCodes);
            exporterResultDto.setResTypeCodes(resTypeCods);
        }

    }

    /**
     * 校验采集路径是否正确
     *
     * @param collectMetricPath 采集路径
     */
    private void checkoutCollectMetricPath(String collectMetricPath) {
        // 构建targets
        try {
            URL url = new URL(collectMetricPath);
        } catch (MalformedURLException e) {
            log.error(e.getMessage());
            BizAssertUtils.fail(
                    OpsMonitorMsgConstant.MONITOR_ERROR_HANDLE_INVALID_PARAMETER,
                    MonitorFieldKeyConstant.COLLECTOR_PATH
            );
        }
    }

    @Override
    public RightCloudResult<Boolean> checkMonitorConfiguration(OpsMonitorEnvCheckParam param) {
        boolean result = true;
        // 构造条件
        final OpsExporterQueryDto opsExporterQueryDto = OpsExporterQueryDto.builder().build();
        // 云平台
        if (StringUtils.isNotEmpty(param.getEnvCode()) && StringUtils.isNotEmpty(param.getEnvVersion())) {
            opsExporterQueryDto.setEnvCode(param.getEnvCode());
            opsExporterQueryDto.setEnvVersion(param.getEnvVersion());
        } else {
            result = false;
        }
        if (result) {
            final Long count = opsExporterDao.selectCount(opsExporterQueryDto);
            result = count > 0;
        }
        return RightCloudResult.success(result);
    }
}
