package com.cloudstar.rightcloud.monitor.service.mortasklog;

import cn.hutool.core.bean.BeanUtil;
import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.monitor.client.mortasklog.param.MorCollectTaskLogCreateParam;
import com.cloudstar.rightcloud.monitor.client.mortasklog.param.MorCollectTaskLogParams;
import com.cloudstar.rightcloud.monitor.client.mortasklog.result.MorCollectTaskLogListResult;
import com.cloudstar.rightcloud.monitor.client.mortasklog.service.MorCollectTaskLogService;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.data.mortasklog.dao.MorCollectTaskLogDao;
import com.cloudstar.rightcloud.monitor.data.mortasklog.dto.MorCollectTaskLogTableDto;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@AllArgsConstructor
@Slf4j
@Service
public class MorCollectTaskLogServiceImpl implements MorCollectTaskLogService {

    private MorCollectTaskLogDao dao;

    @Override
    public RightCloudResult<Boolean> createBatch(List<MorCollectTaskLogCreateParam> list) {
        List<MorCollectTaskLogTableDto> dtos = BeanUtil.copyToList(list, MorCollectTaskLogTableDto.class);
        return RightCloudResult.success(dao.insertBatch(dtos));
    }

    @Override
    public RightCloudResult<MorCollectTaskLogListResult> getLastExecuteLogs(MorCollectTaskLogParams params) {
        BizAssertUtils.notNull(params, CommonMsgConstant.COMMON_ERROR_HANDLE_INVALIDPARAMETER);
        BizAssertUtils.notNull(params, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                MonitorFieldKeyConstant.COLLECT_TASK_PARAM_ID);
        MorCollectTaskLogTableDto morCollectTaskLogTableDto = dao.selectLastExecuteFirstLog(params.getTaskId());
        return RightCloudResult.success(BeanHelperUtil.copyForBean(MorCollectTaskLogListResult::new, morCollectTaskLogTableDto));
    }

}
