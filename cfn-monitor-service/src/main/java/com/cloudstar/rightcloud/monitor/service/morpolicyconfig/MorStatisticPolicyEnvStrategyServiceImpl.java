package com.cloudstar.rightcloud.monitor.service.morpolicyconfig;

import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.exception.BizException;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.common.utils.message.MessageUtil;
import com.cloudstar.rightcloud.data.util.page.PageHelperUtil;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorEnvStatisticStrategyCreateParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorEnvStatisticStrategyPageParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorEnvStatisticStrategyUpdateParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorEnvStatisticStrategyUpdateStatusParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorEnvStatisticStrategyInfoResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorEnvStatisticStrategyPageResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.service.MorStatisticEnvStrategyService;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsEnvGetListResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.service.OpsCmdbService;
import com.cloudstar.rightcloud.monitor.common.constant.StringPool;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.common.constant.msg.OpsMonitorMsgConstant;
import com.cloudstar.rightcloud.monitor.common.em.OperationStatus;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dao.MorStatisticEnvStrategyDao;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorTableStatisticEnvStrategyDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.query.MorStatisticEnvStrategyQueryDto;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ArrayUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import static com.cloudstar.rightcloud.common.utils.web.CrudHelpUtil.prepareInsertParams;
import static com.cloudstar.rightcloud.common.utils.web.CrudHelpUtil.prepareUpdateParams;

/**
 * 环境策略配置统计规则
 *
 * @author: wanglang
 * @date: 2023/11/3 16:07
 */
@Service
@AllArgsConstructor
@Slf4j
public class MorStatisticPolicyEnvStrategyServiceImpl implements MorStatisticEnvStrategyService {

    private final MorStatisticEnvStrategyDao morStatisticEnvStrategyDao;
    private final OpsCmdbService opsCmdbService;

    /**
     * 分页查询环境策略配置统计规则
     *
     * @param param 筛选条件
     */
    @Override
    public RightCloudResult<PageResult<MorEnvStatisticStrategyPageResult>> getPage(
            MorEnvStatisticStrategyPageParam param) {
        MorStatisticEnvStrategyQueryDto dto = BeanUtil.copyProperties(param, MorStatisticEnvStrategyQueryDto.class);
        // 云平台
        if (StringUtils.isNotEmpty(param.getEnvType())) {
            final String[] envTypeArray = param.getEnvType().split(StringPool.COMMA);
            if (ArrayUtil.isNotEmpty(envTypeArray) && envTypeArray.length > 1) {
                String envCode = envTypeArray[0];
                String envVersion = envTypeArray[1];
                dto.setEnvCode(envCode);
                dto.setEnvVersion(envVersion);
            }
        }
        // 分页查询环境策略配置统计规则
        PageResult<MorTableStatisticEnvStrategyDto> pageResult =
                PageHelperUtil.doSelectPageResult(param, MorTableStatisticEnvStrategyDto.class, () -> {
                    morStatisticEnvStrategyDao.selectList(dto);
                });
        PageResult<MorEnvStatisticStrategyPageResult> result = PageHelperUtil.of(pageResult,
                                                                                 MorEnvStatisticStrategyPageResult
                                                                                         .class);
        RightCloudResult<List<OpsEnvGetListResult>> envListResult = opsCmdbService.getEnvList();
        List<OpsEnvGetListResult> envList = envListResult.getData();
        Map<String, String> envTypeMap = envList.stream()
                                                .filter(Objects::nonNull)
                                                .collect(Collectors.toMap(
                                                        defineEnvDetailFeignResult -> {
                                                            String envTye = defineEnvDetailFeignResult.getEnvCode()
                                                                    + StringPool.HASH
                                                                    + defineEnvDetailFeignResult.getVersionNumber();
                                                            return envTye;
                                                        },
                                                        OpsEnvGetListResult::getName,
                                                        (t1, t2) -> t1));
        for (MorEnvStatisticStrategyPageResult envStatisticStrategyResult : result.getList()) {
            envStatisticStrategyResult.setEnvType(envStatisticStrategyResult.getEnvCode()
                                                              + StringPool.HASH
                                                              + envStatisticStrategyResult.getEnvVersion());
            envStatisticStrategyResult.setEnvTypeName(envTypeMap.get(envStatisticStrategyResult.getEnvType()));
        }
        return RightCloudResult.success(result);
    }

    /**
     * 根据id查询环境策略配置统计规则
     *
     * @param id id
     */
    @Override
    public RightCloudResult<MorEnvStatisticStrategyInfoResult> getDetail(Long id) {
        MorTableStatisticEnvStrategyDto dto = this.checkEnvStatisticStrategyExist(id);
        MorEnvStatisticStrategyInfoResult result = BeanUtil.copyProperties(dto,
                                                                           MorEnvStatisticStrategyInfoResult.class);
        return RightCloudResult.success(result);
    }

    /**
     * 创建环境策略配置统计规则
     *
     * @param param 新增参数
     */
    @Override
    @Transactional
    public RightCloudResult<Long> create(MorEnvStatisticStrategyCreateParam param) {
        //1.校验参数
        String message = MessageUtil.getMessage(OpsMonitorMsgConstant.MONITOR_INDICATOR_ALREADY_EXISTS);
        this.checkMetricType(param.getEnvCode(), param.getEnvVersion(),
                             param.getResTypeCode(), param.getPerformanceMetricIndicatorCode(), message);

        //2.创建环境策略配置统计规则
        MorTableStatisticEnvStrategyDto dto = BeanUtil.copyProperties(param, MorTableStatisticEnvStrategyDto.class);
        dto.setStatus(OperationStatus.ENABLE.status);
        prepareInsertParams(dto);

        int result = morStatisticEnvStrategyDao.insert(dto);

        return result > 0 ? RightCloudResult.success(dto.getId()) : RightCloudResult.fail();
    }

    /**
     * 修改环境策略配置统计规则
     *
     * @param param 修改参数
     */
    @Override
    @Transactional
    public RightCloudResult<Void> update(MorEnvStatisticStrategyUpdateParam param) {
        //1.校验环境策略配置统计规则是否存在
        MorTableStatisticEnvStrategyDto envStatisticStrategyDto = this.checkEnvStatisticStrategyExist(param.getId());

        //2.校验当前状态下是否支持修改
        String message = MessageUtil.getMessage(OpsMonitorMsgConstant.MONITOR_DISABLE_STATUS_CAN_MODIFY);
        if (StringUtils.equalsIgnoreCase(OperationStatus.ENABLE.status, envStatisticStrategyDto.getStatus())) {
            throw new BizException(message);
        }

        //3.修改环境策略配置统计规则
        MorTableStatisticEnvStrategyDto dto = BeanUtil.copyProperties(param, MorTableStatisticEnvStrategyDto.class);
        prepareUpdateParams(dto);
        int result = morStatisticEnvStrategyDao.update(dto);

        return result > 0 ? RightCloudResult.success() : RightCloudResult.fail();
    }

    @Override
    @Transactional
    public RightCloudResult<Void> updateStatus(MorEnvStatisticStrategyUpdateStatusParam param) {
        //1.校验环境策略配置统计规则是否存在
        MorTableStatisticEnvStrategyDto dto = this.checkEnvStatisticStrategyExist(param.getId());
        MorTableStatisticEnvStrategyDto updateStatisticStrategy = BeanUtil.copyProperties(param, MorTableStatisticEnvStrategyDto.class);

        //2.修改状态
        updateStatisticStrategy.setStatus(param.getStatus());
        prepareUpdateParams(updateStatisticStrategy);

        int result = morStatisticEnvStrategyDao.update(updateStatisticStrategy);
        return result > 0  ? RightCloudResult.success() : RightCloudResult.fail();
    }

    @Override
    @Transactional
    public RightCloudResult<Void> delete(Long id) {
        //1.校验环境策略配置统计规则是否存在
        MorTableStatisticEnvStrategyDto strategy = this.checkEnvStatisticStrategyExist(id);
        Boolean result = morStatisticEnvStrategyDao.deleteById(id);
        return result ? RightCloudResult.success() : RightCloudResult.fail();
    }

    /**
     * 校验环境策略配置统计规则是否存在
     *
     * @param id 环境策略配置统计规则id
     */
    private MorTableStatisticEnvStrategyDto checkEnvStatisticStrategyExist(Long id) {
        MorTableStatisticEnvStrategyDto morTableStatisticEnvStrategyDto = morStatisticEnvStrategyDao.selectById(id);
        BizAssertUtils.notNull(morTableStatisticEnvStrategyDto,
                               CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                               MonitorFieldKeyConstant.ENV_STATISTIC_STRATEGY);
        return morTableStatisticEnvStrategyDto;
    }

    /**
     * 判断是否有相同指标类型
     * @param envCode 云平台
     * @param envVersion 云平台版本号
     * @param resTypeCode 资源类型
     * @param metricIndicatorCode 统计指标项code
     * @param message 消息
     */
    private void checkMetricType(String envCode, String envVersion, String resTypeCode,
                                 String metricIndicatorCode, String message) {

        MorStatisticEnvStrategyQueryDto param = new MorStatisticEnvStrategyQueryDto();
        param.setEnvCode(envCode);
        param.setEnvVersion(envVersion);
        param.setResTypeCode(resTypeCode);
        param.setPerformanceMetricIndicatorCode(metricIndicatorCode);
        param.setStatus(OperationStatus.ENABLE.status);
        Long count = morStatisticEnvStrategyDao.selectCount(param);
        if (count > 0) {
            throw new BizException(message);
        }
    }

}
