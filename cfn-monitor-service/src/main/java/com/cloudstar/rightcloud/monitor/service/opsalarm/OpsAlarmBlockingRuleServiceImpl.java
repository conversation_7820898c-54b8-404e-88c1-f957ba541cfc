package com.cloudstar.rightcloud.monitor.service.opsalarm;

import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.data.util.page.PageHelperUtil;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsAlarmBlockingCreateParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsAlarmBlockingQueryParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsAlarmBlockingUpdateParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsAlarmBlockingDetailResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsAlarmBlockingRulePageResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.service.OpsAlarmBlockingRuleService;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.common.util.StringUtils;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsAlarmBlockingRuleDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableAlarmBlockingRuleResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsAlarmBlockingRuleQueryDto;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 告警屏蔽策略
 *
 * @author: 卢泳舟
 * @date: 2023/6/7 9:35
 */
@Service
@AllArgsConstructor
public class OpsAlarmBlockingRuleServiceImpl implements OpsAlarmBlockingRuleService {

    private final OpsAlarmBlockingRuleDao opsAlarmBlockingRuleDao;

    @Override
    public RightCloudResult<PageResult<OpsAlarmBlockingRulePageResult>> getBlockingRulePage(OpsAlarmBlockingQueryParam param) {

        // 查询条件
        final OpsAlarmBlockingRuleQueryDto query = OpsAlarmBlockingRuleQueryDto.builder().build();
        if (StringUtils.isNotEmpty(param.getName())) {
            query.setName(param.getName());
        }
        if (StringUtils.isNotEmpty(param.getShieldingPeriod())) {
            query.setShieldingPeriod(param.getShieldingPeriod());
        }
        // 分页查询告警屏蔽规则
        final PageResult<OpsTableAlarmBlockingRuleResultDto> resultDtoPageResult =
                PageHelperUtil.doSelectPageResult(param, OpsTableAlarmBlockingRuleResultDto.class, () -> {
                    opsAlarmBlockingRuleDao.selectList(query);
                });
        // 转换类型
        List<OpsAlarmBlockingRulePageResult> resultList =
                BeanHelperUtil.copyForList(OpsAlarmBlockingRulePageResult::new, resultDtoPageResult.getList());
        PageResult<OpsAlarmBlockingRulePageResult> pageResult = new PageResult();
        pageResult.setList(resultList);
        pageResult.setPageNo(resultDtoPageResult.getPageNo());
        pageResult.setPageSize(resultDtoPageResult.getPageSize());
        pageResult.setTotal(resultDtoPageResult.getTotal());
        pageResult.setTotalPages(resultDtoPageResult.getTotalPages());

        return RightCloudResult.success(pageResult);
    }

    @Override
    public RightCloudResult<Boolean> updateBlockingStatus(String id) {

        final String enable = "enable";
        final String disable = "disable";

        // 判断id是否为空
        BizAssertUtils.notNull(id, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.ID);
        // 根据id获得告警屏蔽数据
        OpsTableAlarmBlockingRuleResultDto itemResult = opsAlarmBlockingRuleDao.selectOne(
                OpsAlarmBlockingRuleQueryDto.builder()
                        .id(id)
                        .build()
        );
        BizAssertUtils.notNull(itemResult, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                MonitorFieldKeyConstant.ALARM_BLOCKING);
        // 修改状态
        if (enable.equals(itemResult.getStatus())) {
            itemResult.setStatus(disable);
        } else if (disable.equals(itemResult.getStatus())) {
            itemResult.setStatus(enable);
        }
        // 更新数据
        return RightCloudResult.success(opsAlarmBlockingRuleDao.updateBlockingStatus(itemResult));
    }

    @Override
    public RightCloudResult<Boolean> deleteBlockingRule(String ids) {
        return RightCloudResult.success(false);
    }

    @Override
    public RightCloudResult<OpsAlarmBlockingDetailResult> selectBlockingDetails(String id) {
        return RightCloudResult.success();
    }

    @Override
    public RightCloudResult<Boolean> updateBlockingDetails(OpsAlarmBlockingUpdateParam opsAlarmBlockingUpdateParam) {
        return RightCloudResult.success(false);
    }

    @Override
    public RightCloudResult<String> addBlockingRule(OpsAlarmBlockingCreateParam opsAlarmBlockingCreateParam) {
        return RightCloudResult.success();
    }


}
