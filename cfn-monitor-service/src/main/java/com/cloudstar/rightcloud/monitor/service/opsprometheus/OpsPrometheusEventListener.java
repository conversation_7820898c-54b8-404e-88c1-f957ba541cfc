package com.cloudstar.rightcloud.monitor.service.opsprometheus;

import com.cloudstar.rightcloud.monitor.client.opsaccess.service.OpsAccessService;
import com.cloudstar.rightcloud.monitor.common.event.PrometheusEvent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * prometheus事件监听
 *
 * @author: wanglang
 * @date: 2023/8/11 18:14
 */
@Component
@Slf4j
@AllArgsConstructor
public class OpsPrometheusEventListener implements ApplicationListener<PrometheusEvent> {

    private final OpsAccessService opsAccessService;

    @Override
    public void onApplicationEvent(PrometheusEvent event) {
        if (Objects.nonNull(event)) {
            switch (event.getType()) {
                // 告警规则
                case ALARM_RULE:
                    log.info(
                            "Start writing to the Prometheus configuration alarm rule"
                    );
                    opsAccessService.updatePrometheusAlertRuleConfig();
                    break;
                default:
                    log.error(
                            "The corresponding Prometheus event type could not be found,{}",
                            event
                    );
            }
        }
    }
}
