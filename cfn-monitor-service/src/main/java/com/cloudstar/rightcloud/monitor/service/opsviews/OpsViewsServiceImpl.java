package com.cloudstar.rightcloud.monitor.service.opsviews;

import cn.hutool.core.collection.CollectionUtil;
import com.cloudstar.rightcloud.api.system.org.SysOrgClient;
import com.cloudstar.rightcloud.api.system.org.form.SysOrgListQueryFeignForm;
import com.cloudstar.rightcloud.api.system.org.result.SysOrgListQueryFeignResult;
import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.LocaleLanguageContextUtil;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.data.util.page.PageHelperUtil;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MonitorCollectMetricsData;
import com.cloudstar.rightcloud.monitor.client.opscmdb.param.OpsCategoryInstanceGetPageParam;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryGetListResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryInstanceGetPropertiesPageResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCategoryPropertiesGetListResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.result.OpsCloudEnvGetInstanceResult;
import com.cloudstar.rightcloud.monitor.client.opscmdb.service.OpsCmdbService;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.OpsMonitoringViewsAlarmDataGetPageParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.OpsMonitoringViewsAlarmRuleGetPageParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.OpsMonitoringViewsCategoryGetParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.OpsMonitoringViewsCategoryInstanceGetDetailsParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.OpsViewsMetricDataGetParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsMonitoringViewsAlarmCountGetResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsMonitoringViewsAlarmDataGetPageResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsMonitoringViewsAlarmRuleGetPageResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsViewsMetricDataGetListResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.OpsViewsMetricGetListResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.service.OpsViewsCategoryService;
import com.cloudstar.rightcloud.monitor.client.opsviews.service.OpsViewsService;
import com.cloudstar.rightcloud.monitor.common.constant.OpsMonitorConstant;
import com.cloudstar.rightcloud.monitor.common.constant.PrometheusResTypeMetricTag;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.common.em.AlarmStatus;
import com.cloudstar.rightcloud.monitor.common.em.ExporterEnvType;
import com.cloudstar.rightcloud.monitor.common.em.ExporterTenantEnvTypes;
import com.cloudstar.rightcloud.monitor.common.em.OperationStatus;
import com.cloudstar.rightcloud.monitor.common.em.OpsViewsCategory;
import com.cloudstar.rightcloud.monitor.common.util.AviatorEvaluatorUtil;
import com.cloudstar.rightcloud.monitor.common.util.DateUtil;
import com.cloudstar.rightcloud.monitor.common.util.StringUtils;
import com.cloudstar.rightcloud.monitor.data.morcomonmetric.dao.MorCommonMetricDao;
import com.cloudstar.rightcloud.monitor.data.morcomonmetric.dto.MorTableCommonMetricResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsAlarmDataDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsAlarmRuleDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsAlarmRuleTargetDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsNotifyPolicyDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsNotifyPolicyByRuleNameResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableAlarmDataResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableAlarmRuleResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableAlarmRuleTargetResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsAlarmDataQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsAlarmRuleQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsAlarmRuleTargetQueryDto;
import com.cloudstar.rightcloud.monitor.data.opscollectrule.dao.OpsCollectMetricDao;
import com.cloudstar.rightcloud.monitor.data.opscollectrule.dao.OpsCollectRuleDao;
import com.cloudstar.rightcloud.monitor.data.opscollectrule.dto.OpsTableCollectMetricResultDto;
import com.cloudstar.rightcloud.monitor.data.opscollectrule.dto.OpsTableCollectRuleResultDto;
import com.cloudstar.rightcloud.monitor.data.opscollectrule.dto.query.OpsCollectMetricQueryDto;
import com.cloudstar.rightcloud.monitor.data.opscollectrule.dto.query.OpsCollectRuleQueryDto;
import com.cloudstar.rightcloud.prom.datasource.prometheus.entity.PrometheusRangeResult;
import com.cloudstar.rightcloud.prom.datasource.prometheus.entity.PrometheusResult;
import com.cloudstar.rightcloud.prom.datasource.prometheus.promql.Promqls;
import com.cloudstar.rightcloud.prom.template.PrometheusTemplate;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 视图接口
 *
 * @author: wanglang
 * @date: 2023/8/18 19:00
 */
@Service
@AllArgsConstructor
@Slf4j
public class OpsViewsServiceImpl implements OpsViewsService {

    private final List<OpsViewsCategoryService> opsViewsCategoryServices;

    private final OpsCmdbService opsCmdbService;

    private final OpsAlarmDataDao opsAlarmDataDao;

    private final OpsNotifyPolicyDao notifyPolicyDao;

    private final SysOrgClient sysOrgClient;

    private final OpsAlarmRuleDao opsAlarmRuleDao;

    private final OpsAlarmRuleTargetDao opsAlarmRuleTargetDao;

    private final MorCommonMetricDao morCommonMetricDao;

    private final PrometheusTemplate prometheusTemplate;

    private final OpsCollectRuleDao opsCollectRuleDao;

    private final OpsCollectMetricDao collectMetricDao;

    @Override
    public RightCloudResult<PageResult<OpsViewsMetricGetListResult>> getViewsMetricListResult(
            OpsMonitoringViewsCategoryGetParam param) {
        if (Objects.isNull(param.getViewsCategory())) {
            param.setViewsCategory(OpsViewsCategory.DEFAULT_CATEGORY);
        }
        PageResult<OpsViewsMetricGetListResult> result = new PageResult<>();
        final List<OpsViewsCategoryService> opsViewsCategoryServiceList = opsViewsCategoryServices.stream()
                .filter(opsViewsCategoryService -> opsViewsCategoryService
                        .isViewMetricCategory(param.getViewsCategory()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(opsViewsCategoryServiceList)) {
            for (OpsViewsCategoryService opsViewsCategoryService : opsViewsCategoryServiceList) {
                RightCloudResult<PageResult<OpsViewsMetricGetListResult>> viewsMetricListResult = opsViewsCategoryService
                        .getViewsMetricListResult(param);
                if (Objects.nonNull(viewsMetricListResult) && Objects.nonNull(viewsMetricListResult.getData())) {
                    result = viewsMetricListResult.getData();
                    break;
                }
            }
        }
        return RightCloudResult.success(result);
    }

    @Override
    public RightCloudResult<Map<String, String>> getAlarmCount(OpsMonitoringViewsCategoryGetParam param) {
        Map<String, String> alarmCoutMap = new HashMap<>();
        final List<OpsViewsCategoryService> opsViewsCategoryServiceList = opsViewsCategoryServices.stream()
                .filter(opsViewsCategoryService -> opsViewsCategoryService
                        .isViewMetricCategory(param.getViewsCategory()))
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(opsViewsCategoryServiceList)) {
            for (OpsViewsCategoryService opsViewsCategoryService : opsViewsCategoryServiceList) {
                final RightCloudResult<Map<String, String>> alarmCount = opsViewsCategoryService.getAlarmCount(param);
                if (Objects.nonNull(alarmCount) && CollectionUtil.isNotEmpty(alarmCount.getData())) {
                    alarmCoutMap.putAll(
                            alarmCount.getData());
                }
            }
        }
        return RightCloudResult.success(alarmCoutMap);
    }

    @Override
    public RightCloudResult<Map<String, Object>> getAlarmCount(String categoryCode) {
        final List<OpsTableAlarmDataResultDto> opsTableAlarmDataResultDtoList = opsAlarmDataDao.selectList(
                OpsAlarmDataQueryDto.builder()
                        .objectType(categoryCode)
                        .status(AlarmStatus.NOT_RECOVERED.status)
                        .build());
        final Map<String, Object> alarmCount = this.countLevelAlarmCount(opsTableAlarmDataResultDtoList);
        return RightCloudResult.success(alarmCount);
    }

    @Override
    public RightCloudResult<List<OpsMonitoringViewsAlarmCountGetResult>> getAlarmCount(
            List<OpsMonitoringViewsCategoryGetParam> params) {
        List<OpsMonitoringViewsAlarmCountGetResult> alarmCountData = new ArrayList<>();
        for (OpsMonitoringViewsCategoryGetParam param : params) {
            final List<OpsViewsCategoryService> opsViewsCategoryServiceList = opsViewsCategoryServices.stream()
                    .filter(opsViewsCategoryService -> opsViewsCategoryService
                            .isViewMetricCategory(param.getViewsCategory()))

                    .collect(Collectors.toList());
            for (OpsViewsCategoryService opsViewsCategoryService : opsViewsCategoryServiceList) {
                final RightCloudResult<List<OpsMonitoringViewsAlarmCountGetResult>> alarmCount = opsViewsCategoryService
                        .getAlarmCount(params);
                if (Objects.nonNull(alarmCount) && CollectionUtil.isNotEmpty(alarmCount.getData())) {
                    alarmCountData = alarmCount.getData();
                    break;
                }
            }
        }
        return RightCloudResult.success(alarmCountData);
    }

    /**
     * 统计等级等告警数量
     *
     * @param opsTableAlarmDataResultDtoList 告警数据
     * @return Map 告警统计数量
     */
    private Map<String, Object> countLevelAlarmCount(List<OpsTableAlarmDataResultDto> opsTableAlarmDataResultDtoList) {
        Map<String, Object> alarmMap = new HashMap<>();
        alarmMap.put(AlarmStatus.MINOR.status, "0");
        alarmMap.put(AlarmStatus.POINT_OUT.status, "0");
        alarmMap.put(AlarmStatus.SIGNIFICANCE.status, "0");
        alarmMap.put(AlarmStatus.URGENCY.status, "0");
        if (CollectionUtil.isNotEmpty(opsTableAlarmDataResultDtoList)) {
            final Map<String, List<OpsTableAlarmDataResultDto>> levelStatusMap = opsTableAlarmDataResultDtoList.stream()
                    .filter(Objects::nonNull)
                    .collect(
                            Collectors.groupingBy(OpsTableAlarmDataResultDto::getAlarmLevelStatus));
            final Set<String> levelStatusKeyList = levelStatusMap.keySet();
            if (CollectionUtil.isNotEmpty(levelStatusKeyList)) {
                for (String levelStatusKey : levelStatusKeyList) {
                    final List<OpsTableAlarmDataResultDto> opsTableAlarmDataResult = levelStatusMap.get(levelStatusKey);
                    String count = "0";
                    if (CollectionUtil.isNotEmpty(opsTableAlarmDataResult)) {
                        count = String.valueOf(opsTableAlarmDataResult.stream()
                                .filter(Objects::nonNull)
                                .count());
                    }
                    alarmMap.put(levelStatusKey, count);
                }
            }
        }
        return alarmMap;
    }

    @Override
    public RightCloudResult<List<OpsCategoryPropertiesGetListResult>> getCategoryMonitorViewsPropertiesList(
            String categoryCode) {
        List<OpsCategoryPropertiesGetListResult> opsCategoryPropertiesGetListResults = new ArrayList<>();
        final RightCloudResult<List<OpsCategoryPropertiesGetListResult>> resTypePropertiesList = opsCmdbService
                .getResTypePropertiesList(categoryCode);
        if (Objects.nonNull(resTypePropertiesList) && CollectionUtil.isNotEmpty(resTypePropertiesList.getData())) {
            final List<OpsCategoryPropertiesGetListResult> propertiesListData = resTypePropertiesList.getData();
            // 自定义添加字段显示
            opsCategoryPropertiesGetListResults.addAll(propertiesListData);
        }
        if (CollectionUtil.isNotEmpty(opsCategoryPropertiesGetListResults)) {
            final List<String> codeList = opsCategoryPropertiesGetListResults.stream()
                    .filter(Objects::nonNull)
                    .map(OpsCategoryPropertiesGetListResult::getCode)
                    .collect(Collectors.toList());
            final List<OpsCategoryPropertiesGetListResult> categoryPropertiesGetListDisplayResults = opsCategoryPropertiesGetListResults
                    .stream()
                    .filter(categoryProperties -> categoryProperties.getPropType().contains("display"))
                    .collect(Collectors.toList());
            final List<OpsCategoryPropertiesGetListResult> categoryPropertiesGetListNotDisplayResults = opsCategoryPropertiesGetListResults
                    .stream()
                    .filter(categoryProperties -> !categoryProperties.getPropType().contains("display"))
                    .collect(Collectors.toList());
            final Integer envIndex = categoryPropertiesGetListDisplayResults.size() >= 2
                    ? 2
                    : null;
            final Integer envNameIndex = categoryPropertiesGetListDisplayResults.size() >= 3
                    ? 3
                    : null;
            if (CollectionUtil.isNotEmpty(codeList)) {
                // 云平台
                if (codeList.contains(OpsMonitorConstant.MONITOR_ENV_ID_FIELD_NAME)) {
                    OpsCategoryPropertiesGetListResult opsCategoryPropertiesGetListResult = new OpsCategoryPropertiesGetListResult();
                    opsCategoryPropertiesGetListResult.setCode(OpsMonitorConstant.MONITOR_ENV_FIELD_NAME);
                    opsCategoryPropertiesGetListResult.setName(OpsMonitorConstant.MONITOR_ENV_TYPE_NAME);
                    opsCategoryPropertiesGetListResult.setNameEn(OpsMonitorConstant.MONITOR_ENV_TYPE_EN_NAME);
                    opsCategoryPropertiesGetListResult.setPropType(List.of("display"));
                    if (Objects.nonNull(envIndex)) {
                        categoryPropertiesGetListDisplayResults.add(envIndex, opsCategoryPropertiesGetListResult);
                    } else {
                        categoryPropertiesGetListDisplayResults.add(opsCategoryPropertiesGetListResult);
                    }
                }
                // 云环境
                if (codeList.contains(OpsMonitorConstant.MONITOR_ENV_ID_FIELD_NAME)) {
                    OpsCategoryPropertiesGetListResult opsCategoryPropertiesGetListResult = new OpsCategoryPropertiesGetListResult();
                    opsCategoryPropertiesGetListResult.setCode(OpsMonitorConstant.MONITOR_ENV_NAME_FIELD_NAME);
                    opsCategoryPropertiesGetListResult.setName(OpsMonitorConstant.MONITOR_ENV_NAME);
                    opsCategoryPropertiesGetListResult.setNameEn(OpsMonitorConstant.MONITOR_ENV_EN_NAME);
                    opsCategoryPropertiesGetListResult.setPropType(List.of("display"));
                    if (Objects.nonNull(envNameIndex)) {
                        categoryPropertiesGetListDisplayResults.add(envNameIndex, opsCategoryPropertiesGetListResult);
                    } else {
                        categoryPropertiesGetListDisplayResults.add(opsCategoryPropertiesGetListResult);
                    }
                }
            }
            // 告警
            final Integer alarmCountIndex = categoryPropertiesGetListDisplayResults.size() >= 4
                    ? 4
                    : null;
            OpsCategoryPropertiesGetListResult opsCategoryPropertiesGetListResult = new OpsCategoryPropertiesGetListResult();
            opsCategoryPropertiesGetListResult.setCode(OpsMonitorConstant.MONITOR_ALARM_FIELD_NAME);
            opsCategoryPropertiesGetListResult.setName(OpsMonitorConstant.MONITOR_ALARM_NAME);
            opsCategoryPropertiesGetListResult.setNameEn(OpsMonitorConstant.MONITOR_ALARM_EN_NAME);
            opsCategoryPropertiesGetListResult.setPropType(List.of("display"));
            if (Objects.nonNull(alarmCountIndex)) {
                categoryPropertiesGetListDisplayResults.add(alarmCountIndex, opsCategoryPropertiesGetListResult);
            } else {
                categoryPropertiesGetListDisplayResults.add(opsCategoryPropertiesGetListResult);
            }
            opsCategoryPropertiesGetListResults.addAll(categoryPropertiesGetListDisplayResults);
            opsCategoryPropertiesGetListResults.addAll(categoryPropertiesGetListNotDisplayResults);

        }
        return RightCloudResult
                .success(opsCategoryPropertiesGetListResults.stream().distinct().collect(Collectors.toList()));
    }

    @Override
    public RightCloudResult<PageResult<OpsCategoryInstanceGetPropertiesPageResult>> getCategoryMonitorViewsInstancePageList(
            OpsCategoryInstanceGetPageParam param) {
        if (Objects.nonNull(param) && StringUtils.isNotEmpty(param.getCategoryCode())) {
            param.setDatafilter(true);
        }
        final RightCloudResult<PageResult<OpsCategoryInstanceGetPropertiesPageResult>> resInstancePageList = opsCmdbService
                .getCategoryInstancePropertiesPageList(param);
        if (Objects.nonNull(resInstancePageList)
                && Objects.nonNull(resInstancePageList.getData())
                && CollectionUtil.isNotEmpty(resInstancePageList.getData().getList())) {
            final PageResult<OpsCategoryInstanceGetPropertiesPageResult> pageListData = resInstancePageList.getData();
            List<OpsCategoryInstanceGetPropertiesPageResult> categoryInstanceGetPageResults = pageListData
                    .getList();
            if (CollectionUtil.isNotEmpty(categoryInstanceGetPageResults)) {
                categoryInstanceGetPageResults = categoryInstanceGetPageResults.stream()
                        .filter(Objects::nonNull)
                        .filter(categoryProperties ->
                                Objects.nonNull(categoryProperties.getOpsResInstancePropertiesResults()))
                        .collect(Collectors.toList());
                pageListData.setList(categoryInstanceGetPageResults);
            }
            List<String> cloudEnvIds = new ArrayList<>();
            List<OpsMonitoringViewsCategoryGetParam> monitoringViewsMetricGetParams = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(categoryInstanceGetPageResults)) {
                // 云环境数据
                for (OpsCategoryInstanceGetPropertiesPageResult categoryInstanceGetPageResult : categoryInstanceGetPageResults) {
                    // 云环境
                    final Map<String, Object> opsResInstancePropertiesResults = categoryInstanceGetPageResult
                            .getOpsResInstancePropertiesResults();
                    final Object cloudEnvId = opsResInstancePropertiesResults
                            .get(OpsMonitorConstant.MONITOR_ENV_ID_FIELD_NAME);
                    if (Objects.nonNull(cloudEnvId)) {
                        cloudEnvIds.add(cloudEnvId.toString());
                    }
                    // 告警数量
                    OpsMonitoringViewsCategoryGetParam opsMonitoringViewsCategoryGetParam = new OpsMonitoringViewsCategoryGetParam();
                    opsMonitoringViewsCategoryGetParam.setCategoryCode(param.getCategoryCode());
                    opsMonitoringViewsCategoryGetParam.setViewsCategory(OpsViewsCategory.DEFAULT_CATEGORY);
                    final Object id = opsResInstancePropertiesResults.get(OpsMonitorConstant.MONITOR_ID_FIELD_NAME);
                    if (Objects.nonNull(cloudEnvId) && Objects.nonNull(id)) {
                        opsMonitoringViewsCategoryGetParam.setCategoryInstanceId(id.toString());
                        opsMonitoringViewsCategoryGetParam.setCloudEnvId(cloudEnvId.toString());
                        opsMonitoringViewsCategoryGetParam.setViewsCategory(OpsViewsCategory.ENV_TYPE_CATEGORY);
                    }
                    monitoringViewsMetricGetParams.add(opsMonitoringViewsCategoryGetParam);
                }
                // 云环境
                RightCloudResult<List<OpsCloudEnvGetInstanceResult>> cloudEnvInstance = null;
                try {
                    cloudEnvInstance = opsCmdbService.getCloudEnvInstance(cloudEnvIds);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
                Map<String, OpsCloudEnvGetInstanceResult> cloudEnvGetInstanceResultMap = new HashMap<>();
                if (Objects.nonNull(cloudEnvInstance) && CollectionUtil.isNotEmpty(cloudEnvInstance.getData())) {
                    cloudEnvGetInstanceResultMap = cloudEnvInstance.getData().stream()
                            .filter(Objects::nonNull)
                            .collect(Collectors.toMap(OpsCloudEnvGetInstanceResult::getCloudEnvId,
                                    opsCloudEnvGetInstanceResult -> opsCloudEnvGetInstanceResult));
                }
                // 告警数量
                final RightCloudResult<List<OpsMonitoringViewsAlarmCountGetResult>> alarmCount = this
                        .getAlarmCount(monitoringViewsMetricGetParams);
                Map<String, Map<String, String>> alarmDataMap = new HashMap<>();
                if (Objects.nonNull(alarmCount) && CollectionUtil.isNotEmpty(alarmCount.getData())) {
                    final List<OpsMonitoringViewsAlarmCountGetResult> alarmCountData = alarmCount.getData();
                    alarmDataMap = alarmCountData.stream()
                            .filter(Objects::nonNull)
                            .collect(Collectors.toMap(OpsMonitoringViewsAlarmCountGetResult::getCategoryInstanceId,
                                    OpsMonitoringViewsAlarmCountGetResult::getAlarmCount));
                }
                for (OpsCategoryInstanceGetPropertiesPageResult categoryInstanceGetPageResult : categoryInstanceGetPageResults) {
                    // 自定义字段
                    // 云环境
                    final Map<String, Object> opsResInstancePropertiesResults = categoryInstanceGetPageResult
                            .getOpsResInstancePropertiesResults();
                    final Object cloudEnvId = opsResInstancePropertiesResults
                            .get(OpsMonitorConstant.MONITOR_ENV_ID_FIELD_NAME);
                    OpsCloudEnvGetInstanceResult opsCloudEnvGetInstanceResult = null;
                    if (Objects.nonNull(cloudEnvId)) {
                        opsCloudEnvGetInstanceResult = cloudEnvGetInstanceResultMap.get(cloudEnvId.toString());
                    }
                    if (Objects.nonNull(opsCloudEnvGetInstanceResult)) {
                        final String defineEnvName = opsCloudEnvGetInstanceResult.getDefineEnvName();
                        Map<String, String> envMap = new HashMap<>();
                        if (StringUtils.isNotEmpty(defineEnvName)) {
                            envMap.put("defineEnvName", defineEnvName);
                        }
                        final String defineEnvNameEn = opsCloudEnvGetInstanceResult.getDefineEnvNameEn();
                        if (StringUtils.isNotEmpty(defineEnvNameEn)) {
                            envMap.put("defineEnvNameEn", defineEnvNameEn);
                        }
                        final String envVersion = opsCloudEnvGetInstanceResult.getEnvVersion();
                        if (StringUtils.isNotEmpty(envVersion)) {
                            envMap.put("envVersion", envVersion);
                        }
                        final String envIcon = opsCloudEnvGetInstanceResult.getEnvIcon();
                        if (StringUtils.isNotEmpty(envIcon)) {
                            envMap.put("envIcon", envIcon);
                        }
                        final String envCode = opsCloudEnvGetInstanceResult.getEnvCode();
                        if (StringUtils.isNotEmpty(envCode)) {
                            envMap.put("envCode", envCode);
                        }
                        // 云平台
                        if (CollectionUtil.isNotEmpty(envMap)) {
                            opsResInstancePropertiesResults.put(
                                    OpsMonitorConstant.MONITOR_ENV_FIELD_NAME,
                                    envMap);
                        }
                        // 云环境
                        final String cloudEnvName = opsCloudEnvGetInstanceResult.getCloudEnvName();
                        if (StringUtils.isNotEmpty(cloudEnvName)) {
                            opsResInstancePropertiesResults.put(
                                    OpsMonitorConstant.MONITOR_ENV_NAME_FIELD_NAME,
                                    cloudEnvName);
                        }
                    }
                    // 告警
                    final Object alarm = opsResInstancePropertiesResults.get(OpsMonitorConstant.MONITOR_ALARM_FIELD_NAME);
                    if (Objects.isNull(alarm)) {
                        Map<String, String> alarmMap = new HashMap<>();
                        final Object id = opsResInstancePropertiesResults.get(OpsMonitorConstant.MONITOR_ID_FIELD_NAME);
                        if (Objects.nonNull(id)) {
                            final Map<String, String> alarmCountMap = alarmDataMap.get(id.toString());
                            if (CollectionUtil.isEmpty(alarmCountMap)) {
                                alarmMap.put(AlarmStatus.MINOR.status, "0");
                                alarmMap.put(AlarmStatus.POINT_OUT.status, "0");
                                alarmMap.put(AlarmStatus.SIGNIFICANCE.status, "0");
                                alarmMap.put(AlarmStatus.URGENCY.status, "0");
                            }
                            if (CollectionUtil.isNotEmpty(alarmCountMap)) {
                                alarmMap = alarmCountMap;
                            }
                        }
                        if (CollectionUtil.isNotEmpty(alarmMap)) {
                            opsResInstancePropertiesResults.put(
                                    OpsMonitorConstant.MONITOR_ALARM_FIELD_NAME,
                                    alarmMap);
                        }
                    }
                }
            }
        }
        return resInstancePageList;
    }


    @Override
    public RightCloudResult<OpsCategoryInstanceGetPropertiesPageResult> getCategoryMonitorViewsInstanceDetails(
            OpsMonitoringViewsCategoryInstanceGetDetailsParam param) {
        BizAssertUtils.notNull(
                param,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.VIEW_METRIC_QUERY);
        BizAssertUtils.notBlank(
                param.getInstanceId(),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.CATEGORY_INSTANCE_ID);
        final RightCloudResult<OpsCategoryInstanceGetPropertiesPageResult> categoryInstancePropertiesDetails = opsCmdbService
                .getCategoryInstancePropertiesDetails(param.getInstanceId());
        if (Objects.nonNull(categoryInstancePropertiesDetails)
                && Objects.nonNull(categoryInstancePropertiesDetails.getData())) {
            final OpsCategoryInstanceGetPropertiesPageResult categoryInstanceGetPropertiesPageResult = categoryInstancePropertiesDetails
                    .getData();
            // 自定义字段
            // 云环境
            final Map<String, Object> opsResInstancePropertiesResults = categoryInstanceGetPropertiesPageResult
                    .getOpsResInstancePropertiesResults();
            final Object cloudEnvId = opsResInstancePropertiesResults.get(OpsMonitorConstant.MONITOR_ENV_ID_FIELD_NAME);
            OpsCloudEnvGetInstanceResult opsCloudEnvGetInstanceResult = null;
            if (Objects.nonNull(cloudEnvId)) {
                final RightCloudResult<OpsCloudEnvGetInstanceResult> cloudEnvInstance = opsCmdbService
                        .getCloudEnvInstance(cloudEnvId.toString());
                if (Objects.nonNull(cloudEnvInstance) && Objects.nonNull(cloudEnvInstance.getData())) {
                    opsCloudEnvGetInstanceResult = cloudEnvInstance.getData();
                }
            }
            if (Objects.nonNull(opsCloudEnvGetInstanceResult)) {
                final String defineEnvName = opsCloudEnvGetInstanceResult.getDefineEnvName();
                Map<String, String> envMap = new HashMap<>();
                if (StringUtils.isNotEmpty(defineEnvName)) {
                    envMap.put("defineEnvName", defineEnvName);
                }
                final String defineEnvNameEn = opsCloudEnvGetInstanceResult.getDefineEnvNameEn();
                if (StringUtils.isNotEmpty(defineEnvNameEn)) {
                    envMap.put("defineEnvNameEn", defineEnvNameEn);
                }
                final String envVersion = opsCloudEnvGetInstanceResult.getEnvVersion();
                if (StringUtils.isNotEmpty(envVersion)) {
                    envMap.put("envVersion", envVersion);
                }
                final String envIcon = opsCloudEnvGetInstanceResult.getEnvIcon();
                if (StringUtils.isNotEmpty(envIcon)) {
                    envMap.put("envIcon", envIcon);
                }
                final String envCode = opsCloudEnvGetInstanceResult.getEnvCode();
                if (StringUtils.isNotEmpty(envCode)) {
                    envMap.put("envCode", envCode);
                }
                if (CollectionUtil.isNotEmpty(envMap)) {
                    opsResInstancePropertiesResults.put(
                            OpsMonitorConstant.MONITOR_ENV_FIELD_NAME,
                            envMap);
                }
                // 云环境
                final String cloudEnvName = opsCloudEnvGetInstanceResult.getCloudEnvName();
                if (StringUtils.isNotEmpty(cloudEnvName)) {
                    opsResInstancePropertiesResults.put(
                            OpsMonitorConstant.MONITOR_ENV_NAME_FIELD_NAME,
                            cloudEnvName);
                }
            }
            // 告警
            final Object alarm = opsResInstancePropertiesResults.get(OpsMonitorConstant.MONITOR_ALARM_FIELD_NAME);
            if (Objects.isNull(alarm)) {
                Map<String, String> alarmMap = new HashMap<>();
                final Object id = opsResInstancePropertiesResults.get(OpsMonitorConstant.MONITOR_ID_FIELD_NAME);
                if (Objects.nonNull(id)) {
                    // 告警数量
                    OpsMonitoringViewsCategoryGetParam opsMonitoringViewsCategoryGetParam = new OpsMonitoringViewsCategoryGetParam();
                    opsMonitoringViewsCategoryGetParam.setCategoryCode(param.getCategoryCode());
                    opsMonitoringViewsCategoryGetParam.setViewsCategory(OpsViewsCategory.DEFAULT_CATEGORY);
                    if (Objects.nonNull(cloudEnvId) && Objects.nonNull(id)) {
                        opsMonitoringViewsCategoryGetParam.setCategoryInstanceId(id.toString());
                        opsMonitoringViewsCategoryGetParam.setCloudEnvId(cloudEnvId.toString());
                        opsMonitoringViewsCategoryGetParam.setViewsCategory(OpsViewsCategory.ENV_TYPE_CATEGORY);
                    }
                    final RightCloudResult<Map<String, String>> alarmCount = this
                            .getAlarmCount(opsMonitoringViewsCategoryGetParam);
                    if (Objects.nonNull(alarmCount) && CollectionUtil.isNotEmpty(alarmCount.getData())) {
                        final Map<String, String> alarmCountMap = alarmCount.getData();
                        if (CollectionUtil.isEmpty(alarmCountMap)) {
                            alarmMap.put(AlarmStatus.MINOR.status, "0");
                            alarmMap.put(AlarmStatus.POINT_OUT.status, "0");
                            alarmMap.put(AlarmStatus.SIGNIFICANCE.status, "0");
                            alarmMap.put(AlarmStatus.URGENCY.status, "0");
                        }
                        if (CollectionUtil.isNotEmpty(alarmCountMap)) {
                            alarmMap = alarmCountMap;
                        }
                    } else {
                        alarmMap.put(AlarmStatus.MINOR.status, "0");
                        alarmMap.put(AlarmStatus.POINT_OUT.status, "0");
                        alarmMap.put(AlarmStatus.SIGNIFICANCE.status, "0");
                        alarmMap.put(AlarmStatus.URGENCY.status, "0");
                    }
                }
                if (CollectionUtil.isNotEmpty(alarmMap)) {
                    opsResInstancePropertiesResults.put(
                            OpsMonitorConstant.MONITOR_ALARM_FIELD_NAME,
                            alarmMap);
                }
            }
        }
        return categoryInstancePropertiesDetails;
    }

    @Override
    public RightCloudResult<PageResult<OpsMonitoringViewsAlarmDataGetPageResult>> getCategoryMonitorViewsAlarmData(
            OpsMonitoringViewsAlarmDataGetPageParam param) {
        BizAssertUtils.notNull(
                param,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.VIEW_METRIC_QUERY);
        BizAssertUtils.notBlank(
                param.getInstanceId(),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.CATEGORY_INSTANCE_ID);
        final PageResult<OpsTableAlarmDataResultDto> opsTableAlarmDataResultDtoIPage = PageHelperUtil
                .doSelectPageResult(param, OpsTableAlarmDataResultDto.class, () -> {
                    opsAlarmDataDao.selectList(
                            OpsAlarmDataQueryDto.builder()
                                    .status(AlarmStatus.NOT_RECOVERED.status)
                                    .objectInstanceId(param.getInstanceId())
                                    .build());
                });
        PageResult<OpsMonitoringViewsAlarmDataGetPageResult> pageResultPageResult = new PageResult<>();
        List<OpsMonitoringViewsAlarmDataGetPageResult> opsAlarmDataPageResultList = new ArrayList<>();
        if (Objects.nonNull(opsTableAlarmDataResultDtoIPage)) {
            final List<OpsTableAlarmDataResultDto> records = opsTableAlarmDataResultDtoIPage.getList();
            pageResultPageResult.setTotalPages(opsTableAlarmDataResultDtoIPage.getTotalPages());
            pageResultPageResult.setTotal(opsTableAlarmDataResultDtoIPage.getTotal());
            // 持续时长 告警通知策略
            List<Long> policyRuleIds = new ArrayList<>();
            List<String> orgIds = new ArrayList<>();
            List<String> categoryCodeList = new ArrayList<>();
            opsAlarmDataPageResultList = records.stream().filter(Objects::nonNull)
                    .map(opsTableAlarmDataPageResult -> {
                        final OpsMonitoringViewsAlarmDataGetPageResult opsAlarmDataPageResult = BeanHelperUtil
                                .copyForBean(OpsMonitoringViewsAlarmDataGetPageResult::new,
                                        opsTableAlarmDataPageResult);
                        final Date date = new Date();
                        final String duration = DateUtil
                                .getDateStr(date.getTime() - opsAlarmDataPageResult.getOccurTime().getTime());
                        opsAlarmDataPageResult.setDuration(duration);
                        // 角色id
                        if (StringUtils.isNotEmpty(opsAlarmDataPageResult.getNotifyPolicyName())) {
                            policyRuleIds.add(opsAlarmDataPageResult.getOpsAlarmRuleId());
                        }
                        if (Objects.nonNull(opsTableAlarmDataPageResult.getOrgId())) {
                            orgIds.add(opsTableAlarmDataPageResult.getOrgId().toString());
                        }
                        if (Objects.nonNull(opsTableAlarmDataPageResult.getObjectType())) {
                            categoryCodeList.add(opsTableAlarmDataPageResult.getObjectType());
                        }
                        // 处理中
                        if (StringUtils.equals(AlarmStatus.PROCESSING.status,
                                opsAlarmDataPageResult.getProcessingStatus())) {
                            opsAlarmDataPageResult.setProcessingUser(opsTableAlarmDataPageResult.getConfirmUser());
                        }
                        // 已解决
                        if (StringUtils.equals(AlarmStatus.RECOVERED.status,
                                opsAlarmDataPageResult.getProcessingStatus())) {
                            opsAlarmDataPageResult.setProcessingUser(opsTableAlarmDataPageResult.getResolveUser());
                        }
                        return opsAlarmDataPageResult;
                    }).collect(Collectors.toList());
            // 查询通知策略数据
            if (CollectionUtil.isNotEmpty(policyRuleIds)) {
                final RightCloudResult<List<OpsNotifyPolicyByRuleNameResultDto>> listRightCloudResult = notifyPolicyDao
                        .selectPolicyByRuleNameList(policyRuleIds);
                if (Objects.nonNull(listRightCloudResult) && listRightCloudResult.isSuccess()
                        && CollectionUtil.isNotEmpty(listRightCloudResult.getData())) {
                    final List<OpsNotifyPolicyByRuleNameResultDto> opsNotifyPolicyByRuleNameResultDtos = listRightCloudResult
                            .getData();
                    final Map<Long, OpsNotifyPolicyByRuleNameResultDto> ruleIdResultMap = opsNotifyPolicyByRuleNameResultDtos
                            .stream()
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(OpsNotifyPolicyByRuleNameResultDto::getRuleId,
                                            opsNotifyPolicyByRuleNameResultDto -> opsNotifyPolicyByRuleNameResultDto));
                    // 组装告警策略数据
                    for (OpsMonitoringViewsAlarmDataGetPageResult opsAlarmDataPageResult : opsAlarmDataPageResultList) {
                        final OpsNotifyPolicyByRuleNameResultDto opsNotifyPolicyByRuleNameResultDto = ruleIdResultMap
                                .get(opsAlarmDataPageResult.getOpsAlarmRuleId());
                        if (Objects.nonNull(opsNotifyPolicyByRuleNameResultDto)) {
                            opsAlarmDataPageResult.setNotifyPolicyName(opsNotifyPolicyByRuleNameResultDto.getPolicyName());
                        }
                    }
                }
            }
            // 查询组织信息
            if (CollectionUtil.isNotEmpty(orgIds)) {
                final String orgIdsJoin = String.join(",", orgIds);
                SysOrgListQueryFeignForm sysOrgListQueryFeignForm = new SysOrgListQueryFeignForm();
                sysOrgListQueryFeignForm.setIds(orgIdsJoin);
                final RightCloudResult<List<SysOrgListQueryFeignResult>> orgResult = sysOrgClient
                        .list(sysOrgListQueryFeignForm);
                if (Objects.nonNull(orgResult) && orgResult.isSuccess()
                        && CollectionUtil.isNotEmpty(orgResult.getData())) {
                    final Map<Long, SysOrgListQueryFeignResult> orgListQueryFeignResultMap = orgResult.getData()
                            .stream()
                            .filter(Objects::nonNull)
                            .collect(
                                    Collectors.toMap(SysOrgListQueryFeignResult::getId,
                                            sysOrgListQueryFeignResult -> sysOrgListQueryFeignResult));
                    if (CollectionUtil.isNotEmpty(orgListQueryFeignResultMap)) {
                        for (OpsMonitoringViewsAlarmDataGetPageResult opsAlarmDataPageResult : opsAlarmDataPageResultList) {
                            if (StringUtils.isNotEmpty(opsAlarmDataPageResult.getOrgName())) {
                                final SysOrgListQueryFeignResult sysOrgListQueryFeignResult = orgListQueryFeignResultMap
                                        .get(opsAlarmDataPageResult.getOrgName());
                                if (Objects.nonNull(sysOrgListQueryFeignResult)) {
                                    opsAlarmDataPageResult.setOrgName(sysOrgListQueryFeignResult.getName());
                                }
                            }
                        }
                    }
                }
            }
            // 查询告警对象
            if (CollectionUtil.isNotEmpty(categoryCodeList)) {
                final RightCloudResult<List<OpsCategoryGetListResult>> categoryDataList = opsCmdbService
                        .getCategoryDataList(categoryCodeList.stream().distinct().collect(Collectors.toList()));
                if (Objects.nonNull(categoryDataList) && CollectionUtil.isNotEmpty(categoryDataList.getData())) {
                    final List<OpsCategoryGetListResult> categoryGetListResultList = categoryDataList.getData();
                    final Map<String, String> categoryGetMap = categoryGetListResultList.stream()
                            .filter(Objects::nonNull)
                            .collect(Collectors.toMap(OpsCategoryGetListResult::getCode,
                                    OpsCategoryGetListResult::getName));
                    if (CollectionUtil.isNotEmpty(categoryGetMap)) {
                        for (OpsMonitoringViewsAlarmDataGetPageResult opsAlarmDataPageResult : opsAlarmDataPageResultList) {
                            if (StringUtils.isNotEmpty(opsAlarmDataPageResult.getTargetType())) {
                                final String name = categoryGetMap.get(opsAlarmDataPageResult.getTargetType());
                                if (StringUtils.isNotEmpty(name)) {
                                    opsAlarmDataPageResult.setTarget(name);
                                }
                            }
                        }
                    }
                }
            }
        }
        pageResultPageResult.setPageNo(opsTableAlarmDataResultDtoIPage.getPageNo());
        pageResultPageResult.setPageSize(opsTableAlarmDataResultDtoIPage.getPageSize());
        pageResultPageResult.setList(opsAlarmDataPageResultList);
        return RightCloudResult.success(pageResultPageResult);
    }

    @Override
    public RightCloudResult<PageResult<OpsMonitoringViewsAlarmRuleGetPageResult>> getCategoryMonitorViewsAlarRule(
            OpsMonitoringViewsAlarmRuleGetPageParam param) {
        BizAssertUtils.notNull(
                param,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.VIEW_METRIC_QUERY);
        BizAssertUtils.notBlank(
                param.getInstanceId(),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.CATEGORY_INSTANCE_ID);
        final List<Long> alarmRuleIdList = new ArrayList<>();
        // 查询告警规则id
        final List<OpsTableAlarmDataResultDto> opsTableAlarmDataResultDtoList = opsAlarmDataDao.selectList(
                OpsAlarmDataQueryDto.builder()
                        .status(AlarmStatus.NOT_RECOVERED.status)
                        .objectInstanceId(param.getInstanceId())
                        .build()
        );
        if (CollectionUtil.isNotEmpty(opsTableAlarmDataResultDtoList)) {
            alarmRuleIdList.addAll(opsTableAlarmDataResultDtoList.stream()
                    .filter(Objects::nonNull)
                    .map(OpsTableAlarmDataResultDto::getOpsAlarmRuleId)
                    .collect(Collectors.toList()));
        }
        // 查询告警规则id 所有告警对象
        final OpsAlarmRuleQueryDto ruleQueryDto = OpsAlarmRuleQueryDto.builder()
                .alarmTargetScope(AlarmStatus.ALL_ALARM_OBJECT.status)
                .build();
        if (StringUtils.isNotEmpty(param.getCategoryCode())) {
            ruleQueryDto.setAlarmTargetType(param.getCategoryCode());
        }
        final List<OpsTableAlarmRuleResultDto> opsAlarmTargetScopeRuleList = opsAlarmRuleDao.selectList(
                ruleQueryDto
        );
        if (CollectionUtil.isNotEmpty(opsAlarmTargetScopeRuleList)) {
            alarmRuleIdList.addAll(opsAlarmTargetScopeRuleList.stream()
                    .filter(Objects::nonNull)
                    .map(OpsTableAlarmRuleResultDto::getId)
                    .collect(Collectors.toList()));
        }
        // 查询告警规则id 指定告警对象
        final List<OpsTableAlarmRuleTargetResultDto> opsAlarmObjectRuleTargetList = opsAlarmRuleTargetDao.selectList(
                OpsAlarmRuleTargetQueryDto.builder()
                        .alarmTargetScope(AlarmStatus.ALARM_OBJECT.status)
                        .alarmTargetScopeId(param.getInstanceId())
                        .build()
        );
        if (CollectionUtil.isNotEmpty(opsAlarmObjectRuleTargetList)) {
            alarmRuleIdList.addAll(opsAlarmObjectRuleTargetList.stream()
                    .filter(Objects::nonNull)
                    .map(OpsTableAlarmRuleTargetResultDto::getOpsAlarmRuleId)
                    .collect(Collectors.toList()));
        }
        // 查询告警规则id 指定云环境
        if (StringUtils.isNotEmpty(param.getCloudEnvId())) {
            final List<OpsTableAlarmRuleTargetResultDto> opsCloudEnvAlarmDataList = opsAlarmRuleTargetDao.selectList(
                    OpsAlarmRuleTargetQueryDto.builder()
                            .alarmTargetScope(AlarmStatus.CLOUD_ENV_ALARM_OBJECT.status)
                            .alarmTargetScopeId(param.getCloudEnvId())
                            .build()
            );
            if (CollectionUtil.isNotEmpty(opsCloudEnvAlarmDataList)) {
                alarmRuleIdList.addAll(opsCloudEnvAlarmDataList.stream()
                        .filter(Objects::nonNull)
                        .map(OpsTableAlarmRuleTargetResultDto::getOpsAlarmRuleId)
                        .collect(Collectors.toList()));
            }
        }
        PageResult<OpsMonitoringViewsAlarmRuleGetPageResult> pageResult = new PageResult<>();
        // 查询告警规则数据
        if (CollectionUtil.isNotEmpty(opsTableAlarmDataResultDtoList)) {
            final OpsAlarmRuleQueryDto rule = OpsAlarmRuleQueryDto.builder()
                    .ids(alarmRuleIdList)
                    .build();
            if (StringUtils.isNotEmpty(param.getCategoryCode())) {
                rule.setAlarmTargetType(param.getCategoryCode());
            }
            pageResult = PageHelperUtil.doSelectPageResult(param, OpsMonitoringViewsAlarmRuleGetPageResult.class,
                    () -> {
                        opsAlarmRuleDao.selectList(
                                OpsMonitoringViewsAlarmRuleGetPageResult::new,
                                rule);
                    });

        } else {
            pageResult.setList(new ArrayList<>());
            pageResult.setPageNo(pageResult.getPageNo());
            pageResult.setPageSize(0);
            pageResult.setTotalPages(0);
            pageResult.setTotal(0L);
        }
        return RightCloudResult.success(pageResult);
    }

    @Override
    public RightCloudResult<List<OpsViewsMetricDataGetListResult>> getCategoryMonitorViewsMetricData(
            OpsViewsMetricDataGetParam param) {
        BizAssertUtils.notNull(
                param,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.VIEW_METRIC_QUERY);
        BizAssertUtils.notEmpty(
                param.getMetricIds(),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.VIEW_METRIC_QUERY);
        BizAssertUtils.notBlank(
                param.getCategoryCode(),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.CATEGORY_CODE);
        BizAssertUtils.notBlank(
                param.getCategoryInstanceId(),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.CATEGORY_INSTANCE_ID);
        BizAssertUtils.notNull(
                param.getStartTime(),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.STAR_TIME);
        BizAssertUtils.notNull(
                param.getEndTime(),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.END_TIME);
        List<OpsViewsMetricDataGetListResult> opsViewsMetricDataGetListResults = new ArrayList<>();
        // 查询指标数据
        if (CollectionUtil.isNotEmpty(param.getMetricIds())) {
            List<MorTableCommonMetricResultDto> opsTableCollectMetricResultDtoList = morCommonMetricDao.selectByIds(param.getMetricIds());

            BizAssertUtils.notEmpty(
                    opsTableCollectMetricResultDtoList,
                    CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                    MonitorFieldKeyConstant.MONITOR_METRIC);
            opsTableCollectMetricResultDtoList = groupFilterCommonMetric(param.getCategoryCode(),
                    param.getEnvTypeCode(),
                    param.getEnvTypeVersion(),
                    opsTableCollectMetricResultDtoList);
            // 获取指标名称
            final List<String> codeList = opsTableCollectMetricResultDtoList.stream()
                    .filter(Objects::nonNull)
                    .map(MorTableCommonMetricResultDto::getCode)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(codeList)) {
                // 获取指标数据
                int step = 10;
                if (Objects.nonNull(param.getStep())) {
                    step = getMetricStep(param.getStartTime(), param.getEndTime());
                }
                List<PrometheusRangeResult> prometheusResults = new ArrayList<>();
                for (MorTableCommonMetricResultDto opsTableCollectMetricResultDto : opsTableCollectMetricResultDtoList) {
                    final PrometheusResult<PrometheusRangeResult> prometheusResult = prometheusTemplate.promql(
                            Promqls
                                    .<MonitorCollectMetricsData>lambdaQuery(MonitorCollectMetricsData.class)
                                    .set(opsTableCollectMetricResultDto.getCode())
                                    .eq(MonitorCollectMetricsData::getResourceInstanceId,
                                            param.getCategoryInstanceId())
                                    .neq(MonitorCollectMetricsData::getPolicy, OpsMonitorConstant.HOURLY_RETENTION_POLICY),
                            param.getStartTime(),
                            param.getEndTime(),
                            step);
                    if (prometheusResult.getSuccess()) {
                        prometheusResults.add(prometheusResult.getData());
                    }
                }
                // 封装数据
                List<PrometheusRangeResult.PrometheusResultData> resultData = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(prometheusResults)) {
                    for (PrometheusRangeResult prometheusResult : prometheusResults) {
                        if (CollectionUtil.isNotEmpty(prometheusResult.getResult())) {
                            resultData.addAll(prometheusResult.getResult());
                        }
                    }
                }
                for (MorTableCommonMetricResultDto opsTableCollectMetricResultDto : opsTableCollectMetricResultDtoList) {
                    OpsViewsMetricDataGetListResult opsViewsMetricDataGetListResult = new OpsViewsMetricDataGetListResult();
                    opsViewsMetricDataGetListResult.setId(opsTableCollectMetricResultDto.getId());
                    opsViewsMetricDataGetListResult.setUnit(LocaleLanguageContextUtil.isEn()
                            ? opsTableCollectMetricResultDto.getUnitEn() : opsTableCollectMetricResultDto.getUnit());
                    opsViewsMetricDataGetListResult.setName(LocaleLanguageContextUtil.isEn() ? opsTableCollectMetricResultDto.getNameEn()
                            : opsTableCollectMetricResultDto.getName());
                    // 统计数据
                    List<OpsViewsMetricDataGetListResult.OpsMetricDataResult> metricDataResults = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(resultData)) {
                        for (PrometheusRangeResult.PrometheusResultData resultDatum : resultData) {
                            if (Objects.nonNull(resultDatum.getMetric())) {
                                Map<String, String> metric = resultDatum.getMetric();
                                if (StringUtils.equals(metric.get(PrometheusResTypeMetricTag.METRIC_NAME),
                                        opsTableCollectMetricResultDto.getCode())) {
                                    final List<List<String>> values = resultDatum.getValues();
                                    if (CollectionUtil.isNotEmpty(values)) {
                                        List<OpsViewsMetricDataGetListResult.OpsMetricDataResult> opsMetricDataResultList = new ArrayList<>();
                                        for (List<String> timestamp : values) {
                                            if (timestamp.size() >= 1) {
                                                final String timeStr = timestamp.get(0);
                                                String value = timestamp.get(1);

                                                final BigDecimal time = new BigDecimal(timeStr)
                                                        .multiply(new BigDecimal(1000));
                                                Date timestampDate = null;
                                                try {
                                                    BigDecimal number = new BigDecimal(value);
                                                    BigDecimal roundedNumber = number.setScale(2, RoundingMode.DOWN);
                                                    value = roundedNumber.toString();
                                                    timestampDate = new Date(time.longValue());
                                                    OpsViewsMetricDataGetListResult.OpsMetricDataResult opsMetricDataResult =
                                                            new OpsViewsMetricDataGetListResult.OpsMetricDataResult();
                                                    opsMetricDataResult.setTime(timestampDate);
                                                    opsMetricDataResult.setValue(value);
                                                    opsMetricDataResultList.add(opsMetricDataResult);
                                                } catch (Exception e) {
                                                    log.error(
                                                            "Monitor view to get last execution time conversion error, error message: {}",
                                                            e.getMessage());
                                                }
                                            }
                                        }
                                        if (CollectionUtil.isNotEmpty(opsMetricDataResultList)) {
                                            final List<OpsViewsMetricDataGetListResult.OpsMetricDataResult> collect = opsMetricDataResultList.stream()
                                                    .sorted(Comparator.comparing(
                                                            OpsViewsMetricDataGetListResult.OpsMetricDataResult::getTime))
                                                    .collect(Collectors.toList());
                                            if (CollectionUtil.isNotEmpty(collect)) {
                                                metricDataResults.addAll(collect);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    opsViewsMetricDataGetListResult.setValues(metricDataResults.stream()
                            .sorted(Comparator.comparing(OpsViewsMetricDataGetListResult.OpsMetricDataResult::getTime))
                            .collect(Collectors.toList()));
                    opsViewsMetricDataGetListResults.add(opsViewsMetricDataGetListResult);
                }
            }
        }
        return RightCloudResult.success(opsViewsMetricDataGetListResults);
    }


    /**
     * 过滤指标
     *
     * @param resTypeCode    资源类型
     * @param envTypeCode    云平台编码
     * @param envTypeVersion 云平台版本
     */
    private List<MorTableCommonMetricResultDto> groupFilterCommonMetric(String resTypeCode,
                                                                        String envTypeCode,
                                                                        String envTypeVersion,
                                                                        List<MorTableCommonMetricResultDto> commonMetricGetPageResults) {
        List<OpsTableCollectMetricResultDto> commonMetricList = this.getCommonMetricId(resTypeCode,
                envTypeCode, envTypeVersion);
        List<MorTableCommonMetricResultDto> resultDtoList = new ArrayList<>();
        if (StringUtils.isEmpty(envTypeCode) && StringUtils.isEmpty(envTypeVersion)) {
            resultDtoList.addAll(commonMetricGetPageResults);
        }
        Map<Long, List<OpsTableCollectMetricResultDto>> commonMetricMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(commonMetricList)) {
            commonMetricMap = commonMetricList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.groupingBy(OpsTableCollectMetricResultDto::getMonitorCommonMetricId));
        }
        if (CollectionUtil.isNotEmpty(commonMetricGetPageResults)) {
            for (MorTableCommonMetricResultDto commonMetricGetListResult : commonMetricGetPageResults) {
                final List<OpsTableCollectMetricResultDto> opsTableCollectMetricResultDtoList
                        = commonMetricMap.get(commonMetricGetListResult.getId());
                if (CollectionUtil.isNotEmpty(opsTableCollectMetricResultDtoList)) {
                    final OpsTableCollectMetricResultDto opsTableCollectMetricResultDto = opsTableCollectMetricResultDtoList.stream()
                            .filter(Objects::nonNull)
                            .findFirst()
                            .orElse(null);
                    if (Objects.nonNull(opsTableCollectMetricResultDto)) {
                        if (StringUtils.isNotEmpty(opsTableCollectMetricResultDto.getName())) {
                            commonMetricGetListResult.setName(opsTableCollectMetricResultDto.getName());
                        }
                        if (StringUtils.isNotEmpty(opsTableCollectMetricResultDto.getNameEn())) {
                            commonMetricGetListResult.setNameEn(opsTableCollectMetricResultDto.getNameEn());
                        }
                        if (StringUtils.isNotEmpty(opsTableCollectMetricResultDto.getUnit())) {
                            commonMetricGetListResult.setUnit(opsTableCollectMetricResultDto.getUnit());
                        }
                        resultDtoList.add(commonMetricGetListResult);
                    }
                }
            }
        }
        return resultDtoList;
    }

    /**
     * 通过云平台查询通用指标id
     *
     * @param envTypeCode    云平台编码
     * @param envTypeVersion 云平台版本
     * @return 通用指标id
     */
    private List<OpsTableCollectMetricResultDto> getCommonMetricId(String resTypeCode, String envTypeCode, String envTypeVersion) {
        List<OpsTableCollectMetricResultDto> commonMetricList = new ArrayList<>();
        if (StringUtils.isNotEmpty(envTypeCode) && StringUtils.isNotEmpty(envTypeVersion)) {
            // 通过查询采集规则再去查询采集规则下的指标
            List<OpsTableCollectRuleResultDto> opsTableCollectRuleResultList = new ArrayList<>();
            ExporterTenantEnvTypes exporterEnvType = ExporterTenantEnvTypes.getExporterEnvType(envTypeCode);
            if (Objects.isNull(exporterEnvType)) {
                opsTableCollectRuleResultList = opsCollectRuleDao.selectListNoDataFilter(
                        OpsCollectRuleQueryDto.builder()
                                .envCode(envTypeCode)
                                .envVersion(envTypeVersion)
                                .resTypeCode(resTypeCode)
                                .status(OperationStatus.ENABLE.status)
                                .build()
                );
            }

            if (Objects.nonNull(exporterEnvType)) {
                // 通过查询采集规则再去查询采集规则下的指标
                ExporterEnvType envType = exporterEnvType.getExporterEnvType();
                opsTableCollectRuleResultList = opsCollectRuleDao.selectListNoDataFilter(
                        OpsCollectRuleQueryDto.builder()
                                .envCode(envType.getCode())
                                .envVersion(envType.getVersion())
                                .resTypeCode(resTypeCode)
                                .status(OperationStatus.ENABLE.status)
                                .build()
                );
            }

            if (CollectionUtil.isNotEmpty(opsTableCollectRuleResultList)) {
                List<Long> collectRuleIdList = opsTableCollectRuleResultList.stream()
                        .filter(Objects::nonNull)
                        .map(OpsTableCollectRuleResultDto::getId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(collectRuleIdList)) {
                    List<OpsTableCollectMetricResultDto> opsTableCollectMetricResultList = collectMetricDao.selectListNoDataFilter(
                            OpsCollectMetricQueryDto.builder()
                                    .opsCollectRuleIds(collectRuleIdList)
                                    .status(OperationStatus.ENABLE.status)
                                    .resTypeCode(resTypeCode)
                                    .build()
                    );
                    if (CollectionUtil.isNotEmpty(opsTableCollectMetricResultList)) {
                        commonMetricList = opsTableCollectMetricResultList;
                    }
                }
            }
        }

        return commonMetricList;
    }

    /**
     * 获取监控数据间隔时间
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    private Integer getMetricStep(Date startTime, Date endTime) {
        // 获取相差毫秒数
        long dayM = 1000 * 24 * 60 * 60;
        long hourM = 1000 * 60 * 60;
        long differ = endTime.getTime() - startTime.getTime();
        long hour = differ % dayM / hourM + 24 * (differ / dayM);
        Map<String, Object> datasource = new HashMap<>();
        datasource.put("time", hour);
        final Map<String, String> metricStepExpressionMap = getMetricStepExpression();
        final Set<String> expressions = metricStepExpressionMap.keySet();
        String step = "1200";
        for (String expression : expressions) {
            step = metricStepExpressionMap.get(expression);
            try {
                final Boolean result = (Boolean) AviatorEvaluatorUtil.execute(expression, datasource);
                if (result) {
                    break;
                }
            } catch (Exception e) {
                log.error(
                        "The step rule condition execution error, check the condition:error:{}",
                        e.getMessage()
                );
            }
        }
        return Integer.valueOf(step);
    }


    /**
     * 获取指标数据间隔时间计算表达式
     * 一个小时：
     * 1、小于或者等于一个小时 5m time<=1
     * 三个小时
     * 1、等于三小时 10m time=3
     * 8个小时
     * 1、大于三小时并且小于或者等于8小时 20m time>3&&<=8
     * 24小时（1天）：
     * 1、大于8个小时并且小于或者等于24小时 1h time>8&&<=24
     * 120小时（5小时）：
     * 1、大于24个小时并且小于或者等于120小时 1h time>24&&<=120
     * 720小时（30天）：
     * 1、大于120小时并且小于或者等于720小时 1d time>120&&<=720
     * 2160小时（90天）：
     * 1、大于720小时并且小于或者等于2160小时 1d time>720&&<=2160
     * 8760小时（365天、一年）：
     * 1、大于2160小时并且小于或者等于8760小时 30d time>2160&&<=8760
     */
    private Map<String, String> getMetricStepExpression() {
        Map<String, String> expressionMap = new HashMap<>();
        // 一个小时：1、小于或者等于一个小时time<=1  5m
        expressionMap.put("time<=1", "300");
        // 三个小时：1、等于三小时 time=3 10m
        expressionMap.put("time=3", "600");
        // 8个小时：1、大于三小时并且小于或者等于8小时 time>3&&<=8 20m
        expressionMap.put("time>3 && time<=8", "1200");
        // 24小时（1天）：1、大于8个小时并且小于或者等于24小时 time>8&&<=24 1h
        expressionMap.put("time>8 && time<=24", "3600");
        // 120小时（5小时）：1、大于24个小时并且小于或者等于120小时 time>24&&<=120 1h
        expressionMap.put("time>24 && time<=120", "3600");
        // 720小时（30天）：1、大于120小时并且小于或者等于720小时 time>120&&<=720 1d
        expressionMap.put("time>120 && time<=720", "86400");
        // 2160小时（90天）：1、大于720小时并且小于或者等于2160小时 time>720&&<=2160 1d
        expressionMap.put("time>720 && time<=2160", "86400");
        // 8760小时（365天、一年）：1、大于2160小时并且小于或者等于8760小时 time>2160&&<=8760 30d
        expressionMap.put("time>2160 && time<=8760", "2592000");
        return expressionMap;
    }
}
