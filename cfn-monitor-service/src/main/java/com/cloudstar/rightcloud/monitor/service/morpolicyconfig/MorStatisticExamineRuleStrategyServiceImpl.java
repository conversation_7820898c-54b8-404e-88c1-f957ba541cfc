package com.cloudstar.rightcloud.monitor.service.morpolicyconfig;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.cloudstar.rightcloud.api.system.code.SysCodeClient;
import com.cloudstar.rightcloud.api.system.code.form.SysCodeQueryFeignForm;
import com.cloudstar.rightcloud.api.system.code.reult.SysCodeQueryFeignResult;
import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.exception.BizException;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.common.utils.message.MessageUtil;
import com.cloudstar.rightcloud.data.util.page.PageHelperUtil;
import com.cloudstar.rightcloud.log.common.utils.ActionLogUtil;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorExamineConfigParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorExamineRuleStrategyCreateParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorExamineRuleStrategyPageParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorExamineRuleStrategyUpdateParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorExamineRuleStrategyUpdateStatusParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorExamineConfigResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorExamineRuleResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorExamineRuleStrategyInfoResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorExamineRuleStrategyPageResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorExamineRuleStrategyResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.service.MorStatisticExamineRuleStrategyService;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.common.constant.msg.OpsMonitorMsgConstant;
import com.cloudstar.rightcloud.monitor.common.em.OperationStatus;
import com.cloudstar.rightcloud.monitor.data.morcode.MorEcsSqlCodeDto;
import com.cloudstar.rightcloud.monitor.data.morcode.MorMetricConfigDto;
import com.cloudstar.rightcloud.monitor.data.morcode.MorScoreDto;
import com.cloudstar.rightcloud.monitor.data.morcode.MorSpecConfigDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dao.MorStatisticsExamineRuleStrategyDao;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorExamineRuleDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.MorTableStatisticsExamineRuleStrategyDto;
import com.cloudstar.rightcloud.monitor.data.morpolicyconfig.dto.query.MorStatisticsExamineRuleStrategyQueryDto;
import com.google.common.collect.Lists;
import io.netty.util.internal.StringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import static com.cloudstar.rightcloud.common.utils.web.CrudHelpUtil.prepareInsertParams;
import static com.cloudstar.rightcloud.common.utils.web.CrudHelpUtil.prepareUpdateParams;

/**
 * 考核规则策略配置实现类
 *
 * @author: hjy
 * @date: 2023/10/31 17:22
 */
@Service
@Slf4j
@AllArgsConstructor
public class MorStatisticExamineRuleStrategyServiceImpl implements MorStatisticExamineRuleStrategyService {

    private final MorStatisticsExamineRuleStrategyDao morStatisticsExamineRuleStrategyDao;

    private final SysCodeClient sysCodeClient;

    /**
     * 分页查询考核规则策略
     *
     * @param param 筛选条件
     */
    @Override
    public RightCloudResult<PageResult<MorExamineRuleStrategyPageResult>> getExamineRuleStrategyPage(
            MorExamineRuleStrategyPageParam param) {
        MorStatisticsExamineRuleStrategyQueryDto dto = BeanUtil.copyProperties(param, MorStatisticsExamineRuleStrategyQueryDto.class);
        // 分页查询考核规则策略
        PageResult<MorTableStatisticsExamineRuleStrategyDto> pageResult =
                PageHelperUtil.doSelectPageResult(param, MorTableStatisticsExamineRuleStrategyDto.class, () -> {
                    morStatisticsExamineRuleStrategyDao.selectList(dto);
                });
        PageResult<MorExamineRuleStrategyPageResult> result = PageHelperUtil.of(pageResult,
                MorExamineRuleStrategyPageResult.class);
        Comparator examineType = Comparator.comparing(MorExamineRuleStrategyPageResult::getExamineType);
        Comparator examineItem = Comparator.comparing(MorExamineRuleStrategyPageResult::getExamineItem);
        Collections.sort(result.getList(), examineType.thenComparing(examineItem));
        for (MorExamineRuleStrategyPageResult morExamineRuleStrategyPageResult : result.getList()) {
            morExamineRuleStrategyPageResult.setExamineRules(
                    JSON.parseArray(morExamineRuleStrategyPageResult.getExamineRule(), MorExamineRuleResult.class));
        }
        return RightCloudResult.success(result);
    }

    /**
     * 根据id查询查询考核规则策略
     *
     * @param id id
     */
    @Override
    public RightCloudResult<MorExamineRuleStrategyInfoResult> getExamineRuleStrategyDetail(Long id) {
        MorTableStatisticsExamineRuleStrategyDto examineRuleStrategy = this.checkExamineRuleExit(id);
        MorExamineRuleStrategyInfoResult result = BeanUtil.copyProperties(examineRuleStrategy,
                MorExamineRuleStrategyInfoResult.class);
        result.setExamineRules(
                JSON.parseArray(result.getExamineRule(), MorExamineRuleResult.class));
        return RightCloudResult.success(result);
    }

    /**
     * 创建考核规则策略
     *
     * @param param 新增参数
     */
    @Override
    @Transactional
    public RightCloudResult<Long> create(MorExamineRuleStrategyCreateParam param) {
        //1.校验参数
        String message = MessageUtil.getMessage(OpsMonitorMsgConstant.DISABLE_SAME_EXAMINE_ITEM_BEFORE_CREATE);
        this.checkExamineItem(param.getExamineType(), param.getExamineItem(), message);

        //2.创建考核规则策略
        MorTableStatisticsExamineRuleStrategyDto dto = BeanUtil.copyProperties(param, MorTableStatisticsExamineRuleStrategyDto.class);
        dto.setExamineRule(JSON.toJSONString(param.getExamineRules()));
        dto.setStatus(OperationStatus.ENABLE.status);
        prepareInsertParams(dto);

        Boolean result = morStatisticsExamineRuleStrategyDao.insert(dto);
        ActionLogUtil.logParam(param.getExamineType() + "-" + param.getExamineItem(), "");

        return result ? RightCloudResult.success(dto.getId()) : RightCloudResult.fail();
    }

    /**
     * 修改考核规则策略
     *
     * @param param 修改参数
     */
    @Override
    @Transactional
    public RightCloudResult<Void> update(MorExamineRuleStrategyUpdateParam param) {
        //1.校验考核规则策略是否存在
        MorTableStatisticsExamineRuleStrategyDto examineRuleStrategy = this.checkExamineRuleExit(param.getId());

        //2.校验是否存在相同评分项影响修改
        String message = MessageUtil.getMessage(OpsMonitorMsgConstant.DISABLE_SAME_EXAMINE_ITEM_BEFORE_UPDATE);
        if (!StringUtils.equalsIgnoreCase(examineRuleStrategy.getExamineItem(), param.getExamineItem())) {
            if (StringUtils.equalsIgnoreCase(examineRuleStrategy.getStatus(), OperationStatus.ENABLE.status)) {
                checkExamineItem(examineRuleStrategy.getExamineType(), param.getExamineItem(), message);
            }
        }

        //3.修改考核规则策略
        examineRuleStrategy.setExamineItem(param.getExamineItem());
        examineRuleStrategy.setExamineRule(JSON.toJSONString(param.getExamineRules()));
        examineRuleStrategy.setStatisticType(param.getStatisticType());
        prepareUpdateParams(examineRuleStrategy);

        Boolean result = morStatisticsExamineRuleStrategyDao.update(examineRuleStrategy);

        return result ? RightCloudResult.success() : RightCloudResult.fail();
    }

    @Override
    @Transactional
    public RightCloudResult<Void> updateStatus(MorExamineRuleStrategyUpdateStatusParam param) {
        //1.校验考核规则策略是否存在
        MorTableStatisticsExamineRuleStrategyDto examineRuleStrategy = this.checkExamineRuleExit(param.getId());
        MorTableStatisticsExamineRuleStrategyDto updateExamineRuleStrategy =
                BeanUtil.copyProperties(param, MorTableStatisticsExamineRuleStrategyDto.class);

        //2.校验是否存在相同评分项影响修改状态
        String message = MessageUtil.getMessage(OpsMonitorMsgConstant.DISABLE_SAME_EXAMINE_ITEM_BEFORE_ENABLE);
        if (StringUtils.equalsIgnoreCase(param.getStatus(), OperationStatus.ENABLE.status)) {
            checkExamineItem(examineRuleStrategy.getExamineType(), examineRuleStrategy.getExamineItem(), message);

            updateExamineRuleStrategy.setStatus(OperationStatus.ENABLE.status);
        } else {
            updateExamineRuleStrategy.setStatus(param.getStatus());
        }
        prepareUpdateParams(updateExamineRuleStrategy);
        Boolean result = morStatisticsExamineRuleStrategyDao.update(updateExamineRuleStrategy);
        return result ? RightCloudResult.success() : RightCloudResult.fail();
    }

    @Override
    @Transactional
    public RightCloudResult<Void> delete(Long id) {
        //1.校验考核规则策略是否存在
        MorTableStatisticsExamineRuleStrategyDto examineRuleStrategy = this.checkExamineRuleExit(id);
        Boolean result = morStatisticsExamineRuleStrategyDao.deleteById(id);
        return result ? RightCloudResult.success() : RightCloudResult.fail();
    }

    /**
     * 查询考核项配置
     *
     * @param param 筛选条件
     */
    @Override
    public RightCloudResult<List<MorExamineConfigResult>> getExamineConfigQuery(MorExamineConfigParam param) {
        MorEcsSqlCodeDto sqlCodeDto = getDynamicSqlCode(param.getResTypeCodeCategory());
        List<MorMetricConfigDto> metricConfigs = sqlCodeDto.getMetricConfig();
        List<MorExamineConfigResult> examineConfigList = new ArrayList<>();
        for (MorMetricConfigDto morMetricConfigDto : metricConfigs) {
            MorExamineConfigResult examineConfigResult = new MorExamineConfigResult();
            examineConfigResult.setResTypeCode(sqlCodeDto.getResTypeCode());
            examineConfigResult.setResTypeCodeCategory(sqlCodeDto.getResTypeCodeCategory());
            examineConfigResult.setExamineMetricCode(morMetricConfigDto.getCode());
            examineConfigResult.setExamineMetricName(morMetricConfigDto.getName());
            examineConfigResult.setExamineMetricEnName(morMetricConfigDto.getEnName());
            examineConfigList.add(examineConfigResult);
        }

        for (MorSpecConfigDto specConfigDto : sqlCodeDto.getSpecConfig()) {
            MorExamineConfigResult examineConfigResult = new MorExamineConfigResult();
            examineConfigResult.setResTypeCode(sqlCodeDto.getResTypeCode());
            examineConfigResult.setResTypeCodeCategory(sqlCodeDto.getResTypeCodeCategory());
            examineConfigResult.setExamineMetricCode(specConfigDto.getCode());
            examineConfigResult.setExamineMetricName(specConfigDto.getName());
            examineConfigResult.setExamineMetricEnName(specConfigDto.getEnName());
            examineConfigList.add(examineConfigResult);
        }

        return RightCloudResult.success(examineConfigList);
    }

    @Override
    public void calculateTagCheckScore(Map<String, Object> statistic,
                                       List<MorExamineRuleStrategyResult> rules,
                                       String resTypeCode) {
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        Map<String, List<MorExamineRuleStrategyResult>> ruleMap = rules.stream()
                .collect(Collectors.groupingBy(
                        MorExamineRuleStrategyResult
                                ::getExamineItem));

        BigDecimal score = BigDecimal.ZERO;
        List<String> adviceList = Lists.newArrayList();
        Consumer<MorExamineRuleDto> adviceHandler = (item) -> {
            if (StringUtil.isNullOrEmpty(item.getAdvice())) {
                return;
            }
            adviceList.add(item.getAdvice());
        };

        MorEcsSqlCodeDto dynamicCode = getDynamicSqlCode(resTypeCode);
        List<MorScoreDto> scores = dynamicCode.getScore();
        for (MorScoreDto scoreDto : scores) {
            if (Objects.nonNull(statistic.get(scoreDto.getCondition()))
                    && ruleMap.containsKey(scoreDto.getType())) {
                List<MorExamineRuleStrategyResult> typeRules = ruleMap.get(scoreDto.getType());
                Double condition = Double.parseDouble(statistic.get(scoreDto.getCondition()).toString());
                MorExamineRuleDto item = resourceScoreItem(typeRules, BigDecimal.valueOf(condition));
                if (Objects.nonNull(item) && Objects.nonNull(item.getScore())) {
                    BigDecimal typeScore = item.getScore();
                    statistic.put(scoreDto.getKey(), typeScore);
                    score = score.add(typeScore);
                    adviceHandler.accept(item);
                }
            }
        }
        statistic.put("score", score);
        statistic.put("adviceList", adviceList);
    }

    @Override
    public void calculatePoolTopCheckScore(Map<String, Object> statistic,
                                           List<MorExamineRuleStrategyResult> rules,
                                           String resTypeCode) {
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        Map<String, List<MorExamineRuleStrategyResult>> ruleMap = rules.stream()
                .collect(Collectors.groupingBy(
                        MorExamineRuleStrategyResult
                                ::getExamineItem));

        BigDecimal score = BigDecimal.ZERO;
        List<String> adviceList = Lists.newArrayList();

        MorEcsSqlCodeDto dynamicCode = getDynamicSqlCode(resTypeCode);
        List<MorScoreDto> scores = dynamicCode.getScore();
        for (MorScoreDto scoreDto : scores) {
            if (Objects.nonNull(statistic.get(scoreDto.getCondition()))
                    && ruleMap.containsKey(scoreDto.getType())) {
                List<MorExamineRuleStrategyResult> typeRules = ruleMap.get(scoreDto.getType());
                Double condition = Double.parseDouble(statistic.get(scoreDto.getCondition()).toString());
                BigDecimal typeScore = resourceScore(typeRules, BigDecimal.valueOf(condition));
                if (Objects.nonNull(typeScore)) {
                    statistic.put(scoreDto.getKey(), typeScore);
                    score = score.add(typeScore);
                }
            }
        }

        statistic.put("score", score);
        statistic.put("adviceList", adviceList);
    }

    private BigDecimal resourceScore(List<MorExamineRuleStrategyResult> rules, BigDecimal value) {
        MorExamineRuleDto item = resourceScoreItem(rules, value);
        if (Objects.nonNull(item)) {
            return item.getScore();
        }

        return null;
    }


    /**
     * 判断是否有相同评分项
     *
     * @param examineType 考核类型
     * @param examineItem 评分项
     * @param message     消息
     */
    private void checkExamineItem(String examineType, String examineItem, String message) {

        MorStatisticsExamineRuleStrategyQueryDto ruleParam = new MorStatisticsExamineRuleStrategyQueryDto();
        ruleParam.setExamineType(examineType);
        ruleParam.setStatus(OperationStatus.ENABLE.status);
        List<MorTableStatisticsExamineRuleStrategyDto> rules = morStatisticsExamineRuleStrategyDao.selectList(ruleParam);
        Map<String, MorTableStatisticsExamineRuleStrategyDto> ruleMap = rules.stream()
                .collect(Collectors.toMap(
                        MorTableStatisticsExamineRuleStrategyDto::getExamineItem,
                        a -> a,
                        (a, b) -> a));
        if (ruleMap.containsKey(examineItem)) {
            throw new BizException(message);
        }
    }

    /**
     * 校验考核规则策略是否存在
     *
     * @param id 考核规则策略id
     */
    private MorTableStatisticsExamineRuleStrategyDto checkExamineRuleExit(Long id) {
        MorTableStatisticsExamineRuleStrategyDto morTableStatisticsExamineRuleStrategyDto = morStatisticsExamineRuleStrategyDao.selectById(id);
        BizAssertUtils.notNull(morTableStatisticsExamineRuleStrategyDto,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                MonitorFieldKeyConstant.EXAMINE_RULE_STRATEGY);
        return morTableStatisticsExamineRuleStrategyDto;
    }

    /**
     * 查询code表中动态sql变量的配置
     *
     * @param resTypeCodeCategory 考核配置
     * @return 返回结果
     */
    private MorEcsSqlCodeDto getDynamicSqlCode(String resTypeCodeCategory) {
        SysCodeQueryFeignForm codeQueryFeignForm = new SysCodeQueryFeignForm();
        codeQueryFeignForm.setCodeCategory(resTypeCodeCategory);
        codeQueryFeignForm.setStatus(1);
        RightCloudResult<List<SysCodeQueryFeignResult>> codeResult = sysCodeClient.list(codeQueryFeignForm);
        BizAssertUtils.isTrue(codeResult.isSuccess() && codeResult.getCode() == 200, codeResult.getMessage());
        List<SysCodeQueryFeignResult> datas = codeResult.getData();
        MorEcsSqlCodeDto sqlCode = new MorEcsSqlCodeDto();
        if (CollectionUtil.isNotEmpty(datas)) {
            List<SysCodeQueryFeignResult> data = datas.stream()
                    .filter(t -> com.cloudstar.rightcloud.monitor.common.util.StringUtils
                            .equalsIgnoreCase("analysis",
                                    t.getAttribute1()))
                    .collect(Collectors.toList());
            if (data.size() > 0) {
                SysCodeQueryFeignResult code = data.get(0);
                sqlCode = JSONUtil.toBean(code.getCodeValue(), MorEcsSqlCodeDto.class);
            }
        }

        return sqlCode;
    }

    private MorExamineRuleDto resourceScoreItem(List<MorExamineRuleStrategyResult> rules, BigDecimal value) {
        if (CollectionUtils.isEmpty(rules) || Objects.isNull(value)) {
            return null;
        }

        for (MorExamineRuleStrategyResult rule : rules) {
            List<MorExamineRuleDto> ranges = JSON.parseArray(rule.getExamineRule(), MorExamineRuleDto.class);

            for (MorExamineRuleDto rangeItem : ranges) {
                if (!conditionCompare(rangeItem.getStartOperate(), value.doubleValue(), rangeItem.getStartValue())) {
                    continue;
                }
                if (StringUtils.isNotEmpty(rangeItem.getEndOperate())) {
                    if (!conditionCompare(rangeItem.getEndOperate(), value.doubleValue(), rangeItem.getEndValue())) {
                        continue;
                    }
                }


                return rangeItem;
            }
        }

        return null;
    }

    /**
     * 条件比较
     *
     * @param operator      比较符
     * @param value         比较的值
     * @param boundaryValue 边界值
     */
    private boolean conditionCompare(String operator, Double value, Double boundaryValue) {
        if (Objects.isNull(value) || Objects.isNull(boundaryValue)) {
            return false;
        }

        if ("lt".equals(operator)) {
            return value < boundaryValue;
        } else if ("lte".equals(operator) || "le".equals(operator) || "ltet".equals(operator)) {
            return value <= boundaryValue;
        } else if ("eq".equals(operator) || "et".equals(operator)) {
            return value.equals(boundaryValue);
        } else if ("neq".equals(operator)) {
            return !value.equals(boundaryValue);
        } else if ("gt".equals(operator)) {
            return value > boundaryValue;
        } else if ("gte".equals(operator) || "ge".equals(operator) || "gtet".equals(operator)) {
            return value >= boundaryValue;
        }

        return false;
    }


}
