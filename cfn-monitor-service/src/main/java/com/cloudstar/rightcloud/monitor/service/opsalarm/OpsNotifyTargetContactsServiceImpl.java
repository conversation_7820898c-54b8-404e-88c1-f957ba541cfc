package com.cloudstar.rightcloud.monitor.service.opsalarm;


import com.cloudstar.rightcloud.api.system.user.SysUserClient;
import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.common.utils.exception.BizAssertUtils;
import com.cloudstar.rightcloud.data.util.page.PageHelperUtil;
import com.cloudstar.rightcloud.log.common.utils.ActionLogUtil;
import com.cloudstar.rightcloud.monitor.client.feign.server.ServerClient;
import com.cloudstar.rightcloud.monitor.client.feign.server.request.ManagerListSelectReq;
import com.cloudstar.rightcloud.monitor.client.feign.server.result.ManagerDetailResponse;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsAlarmContactsCreateParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsAlarmContactsListParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsAlarmContactsPageParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsAlarmContactsUpdateParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsAlarmContactsUserCheckParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsNotifyTargetContactUserPageParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsAlarmContactInfoResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsAlarmContactsListResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsAlarmContactsResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsAlarmNotifyContactsTelEmailResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsNotifyTargetContactUserPageResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.service.OpsNotifyTargetContactsService;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationMessageConstant;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.common.feign.result.Rest;
import com.cloudstar.rightcloud.monitor.common.util.StringUtils;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsNotifyPolicyDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsNotifyTargetContactsDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsNotifyTargetContactsGroupDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dao.OpsNotifyTargetGroupContactsRelDao;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsContactsResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsNotifyContactsGroupResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableNotifyTargetContactsGroupResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableNotifyTargetContactsResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.OpsTableNotifyTargetGroupContactsRelResultDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsNotifyTargetContactsGroupQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsNotifyTargetContactsQueryDto;
import com.cloudstar.rightcloud.monitor.data.opsalarm.dto.query.OpsNotifyTargetGroupContactsRelQueryDto;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollectionUtil;
import lombok.AllArgsConstructor;

/**
 * 通知对象联系人接口
 *
 * @author: 卢泳舟
 * @date: 2023/5/19 12:04
 */
@AllArgsConstructor
@Service
public class OpsNotifyTargetContactsServiceImpl implements OpsNotifyTargetContactsService {

    private final OpsNotifyTargetContactsDao opsNotifyTargetContactsDao;

    private final OpsNotifyTargetContactsGroupDao opsNotifyTargetContactsGroupDao;

    private final OpsNotifyTargetGroupContactsRelDao opsNotifyTargetGroupContactsRelDao;

    private final SysUserClient sysUserClient;

    private final OpsNotifyPolicyDao opsNotifyPolicyDao;

    private final ServerClient serverClient;

    @Override
    public RightCloudResult<PageResult<OpsAlarmContactsResult>> getAlarmContactsPage(OpsAlarmContactsPageParam param) {
        // 分页查询通知人信息
        final PageResult<OpsTableNotifyTargetContactsResultDto> resultDtoPageResult =
                PageHelperUtil.doSelectPageResult(param, OpsTableNotifyTargetContactsResultDto.class, () -> {
                    opsNotifyTargetContactsDao.selectList(
                            OpsNotifyTargetContactsQueryDto.builder()
                                    .userName(param.getUserName())
                                    .build()
                    );
                });
        List<OpsTableNotifyTargetContactsResultDto> records = resultDtoPageResult.getList();

        //获取需要查询关联组名信息的id列表
        List<Long> idList = records.stream()
                .map(OpsTableNotifyTargetContactsResultDto::getId)
                .collect(Collectors.toList());
        // 通过id列表查询对应的关联组名信息
        List<OpsNotifyContactsGroupResultDto> relList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(idList)) {
            relList =
                    opsNotifyTargetContactsDao.selectRelGroupName(idList);
        }
        // 构造id和groupName的对应关系

        Map<Long, List<String>> idGroupMap = relList.stream()
                .collect(Collectors.groupingBy(OpsNotifyContactsGroupResultDto::getId,
                        Collectors.mapping(OpsNotifyContactsGroupResultDto::getGroupName, Collectors.toList())));
        // 拼接用户信息
        List<OpsAlarmContactsResult> userList = new ArrayList<>();
        for (OpsTableNotifyTargetContactsResultDto opsTableNotifyTargetContactsResultDto : records) {
            OpsAlarmContactsResult opsAlarmContactsResult = new OpsAlarmContactsResult();
            opsAlarmContactsResult.setId(opsTableNotifyTargetContactsResultDto.getId());
            opsAlarmContactsResult.setUserName(opsTableNotifyTargetContactsResultDto.getUserName());
            opsAlarmContactsResult.setUserPhone(opsTableNotifyTargetContactsResultDto.getUserPhone());
            opsAlarmContactsResult.setUserEmail(opsTableNotifyTargetContactsResultDto.getUserEmail());
            opsAlarmContactsResult.setDescription(opsTableNotifyTargetContactsResultDto.getDescription());
            final List<String> groupNameList = idGroupMap.get(opsTableNotifyTargetContactsResultDto.getId());
            if (CollectionUtil.isNotEmpty(groupNameList)) {
                opsAlarmContactsResult.setGroupNameList(idGroupMap.get(opsTableNotifyTargetContactsResultDto.getId()));
            }
            userList.add(opsAlarmContactsResult);
        }
        // 拼接返回结果
        PageResult<OpsAlarmContactsResult> pageResult = new PageResult<>();
        pageResult.setPageNo(resultDtoPageResult.getPageNo());
        pageResult.setPageSize(resultDtoPageResult.getPageSize());
        pageResult.setTotal(resultDtoPageResult.getTotal());
        pageResult.setTotalPages(resultDtoPageResult.getTotalPages());
        pageResult.setList(userList);

        return RightCloudResult.success(pageResult);
    }

    @Override
    public RightCloudResult<List<OpsAlarmContactsListResult>> getAlarmContactsList(OpsAlarmContactsListParam param) {
        // 分页查询通知人信息
        List<OpsTableNotifyTargetContactsResultDto> records = opsNotifyTargetContactsDao
                .selectList(OpsNotifyTargetContactsQueryDto.builder()
                                               .userName(param.getUserName()).build()
        );

        //获取需要查询关联组名信息的id列表
        List<Long> idList = records.stream()
                                   .map(OpsTableNotifyTargetContactsResultDto::getId)
                                   .collect(Collectors.toList());
        // 通过id列表查询对应的关联组名信息
        List<OpsNotifyContactsGroupResultDto> relList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(idList)) {
            relList =
                    opsNotifyTargetContactsDao.selectRelGroupName(idList);
        }
        // 构造id和groupName的对应关系

        Map<Long, List<String>> idGroupMap = relList
                .stream()
                .collect(Collectors.groupingBy(OpsNotifyContactsGroupResultDto::getId,
                                               Collectors.mapping(OpsNotifyContactsGroupResultDto::getGroupName,
                                                                  Collectors.toList())));
        // 拼接用户信息
        List<OpsAlarmContactsListResult> userList = new ArrayList<>();
        for (OpsTableNotifyTargetContactsResultDto opsTableNotifyTargetContactsResultDto : records) {
            OpsAlarmContactsListResult opsAlarmContactsResult = new OpsAlarmContactsListResult();
            opsAlarmContactsResult.setId(opsTableNotifyTargetContactsResultDto.getId());
            opsAlarmContactsResult.setUserName(opsTableNotifyTargetContactsResultDto.getUserName());
            opsAlarmContactsResult.setUserPhone(opsTableNotifyTargetContactsResultDto.getUserPhone());
            opsAlarmContactsResult.setUserEmail(opsTableNotifyTargetContactsResultDto.getUserEmail());
            opsAlarmContactsResult.setDescription(opsTableNotifyTargetContactsResultDto.getDescription());
            final List<String> groupNameList = idGroupMap.get(opsTableNotifyTargetContactsResultDto.getId());
            if (CollectionUtil.isNotEmpty(groupNameList)) {
                opsAlarmContactsResult.setGroupNameList(idGroupMap.get(opsTableNotifyTargetContactsResultDto.getId()));
            }
            userList.add(opsAlarmContactsResult);
        }


        return RightCloudResult.success(userList);
    }



    @Override
    public RightCloudResult<OpsAlarmContactInfoResult> getDetails(Long id) {
        // 验证是否为空
        BizAssertUtils.notNull(id, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.ID);
        // 查询联系人信息
        OpsTableNotifyTargetContactsResultDto resultDto = opsNotifyTargetContactsDao.selectOne(
                OpsNotifyTargetContactsQueryDto.builder()
                        .id(id)
                        .build()
        );
        // 查询联系人关联组id
        List<OpsTableNotifyTargetGroupContactsRelResultDto> groupIdresult = opsNotifyTargetGroupContactsRelDao.selectList(
                OpsNotifyTargetGroupContactsRelQueryDto.builder()
                        .opsNotifyTargetContactsId(id)
                        .build()
        );
        // 判断是否有关联组
        if (CollectionUtil.isNotEmpty(groupIdresult)) {
            List<Long> groupId = groupIdresult
                    .stream()
                    .map(OpsTableNotifyTargetGroupContactsRelResultDto::getOpsNotifyTargetGroupId)
                    .collect(Collectors.toList());
            // 根据组id查询组名
            List<OpsTableNotifyTargetContactsGroupResultDto> groupResultDto = opsNotifyTargetContactsGroupDao.selectList(
                    OpsNotifyTargetContactsGroupQueryDto.builder()
                            .ids(groupId)
                            .build()
            );
            List<Long> groupIds = groupResultDto
                    .stream()
                    .map(OpsTableNotifyTargetContactsGroupResultDto::getId)
                    .collect(Collectors.toList());
            List<String> groupNames = groupResultDto
                    .stream()
                    .map(OpsTableNotifyTargetContactsGroupResultDto::getGroupName)
                    .collect(Collectors.toList());

            // 拼接需要的数据
            OpsAlarmContactInfoResult result = BeanHelperUtil.copyForBean(OpsAlarmContactInfoResult::new, resultDto);
            result.setGroupIds(groupIds);
            if (CollectionUtil.isNotEmpty(groupNames)) {
                result.setGroupNameList(groupNames);
            }
            return RightCloudResult.success(result);
        }
        OpsAlarmContactInfoResult result = BeanHelperUtil.copyForBean(OpsAlarmContactInfoResult::new, resultDto);
        return RightCloudResult.success(result);
    }

    @Override
    public RightCloudResult<OpsAlarmNotifyContactsTelEmailResult> getPhoneEmailOrName(Long id) {
        // 验证是否为空
        BizAssertUtils.notNull(id, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.ID);
        // 查询联系人信息
        OpsTableNotifyTargetContactsResultDto resultDto = opsNotifyTargetContactsDao.selectOne(
                OpsNotifyTargetContactsQueryDto.builder()
                        .id(id)
                        .build()
        );
        BizAssertUtils.notNull(resultDto, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                MonitorFieldKeyConstant.ALARM_CONTACTS);
        OpsAlarmNotifyContactsTelEmailResult result = BeanHelperUtil.copyForBean(OpsAlarmNotifyContactsTelEmailResult::new, resultDto);
        // 拼接需要的数据
        return RightCloudResult.success(result);
    }

    @Override
    public RightCloudResult<Long> createAlarmContacts(OpsAlarmContactsCreateParam alarmContactsParam) {
        BizAssertUtils.notNull(
                alarmContactsParam,
                CommonMsgConstant.COMMON_ERROR_HANDLE_INVALIDPARAMETER
        );
        // 校验联系人邮箱和手机号
        BizAssertUtils.notBlank(
                alarmContactsParam.getUserEmail(),
                CommonMsgConstant.COMMON_ERROR_HANDLE_INVALIDPARAMETER
        );
        BizAssertUtils.notBlank(
                alarmContactsParam.getUserPhone(),
                CommonMsgConstant.COMMON_ERROR_HANDLE_INVALIDPARAMETER
        );
        // 校验联系人邮箱
        final List<OpsTableNotifyTargetContactsResultDto> checkEmailList = opsNotifyTargetContactsDao.selectList(
                OpsNotifyTargetContactsQueryDto.builder()
                        .checkUserEmail(alarmContactsParam.getUserEmail())
                        .build()
        );
        BizAssertUtils.isEmpty(
                checkEmailList,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_ALREADYEXISTS,
                MonitorFieldKeyConstant.USER_EMAIL
        );
        // 校验联系人手机号
        final List<OpsTableNotifyTargetContactsResultDto> checkPhonelList = opsNotifyTargetContactsDao.selectList(
                OpsNotifyTargetContactsQueryDto.builder()
                        .checkUserPhone(alarmContactsParam.getUserPhone())
                        .build()
        );
        BizAssertUtils.isEmpty(
                checkPhonelList,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_ALREADYEXISTS,
                MonitorFieldKeyConstant.USER_PHONE
        );
        // 插入数据转型
        OpsTableNotifyTargetContactsResultDto result = BeanHelperUtil.copyForBean(
                OpsTableNotifyTargetContactsResultDto::new, alarmContactsParam);
        opsNotifyTargetContactsDao.insert(result);
        // 返回新增数据的id
        return RightCloudResult.success(result.getId());
    }

    @Transactional
    @Override
    public RightCloudResult<Boolean> createAlarmContactsExistUser(List<Long> userIds) {
        // 根据id查询用户信息
        ManagerListSelectReq managerListSelectReq = new  ManagerListSelectReq();
        managerListSelectReq.setUserSids(userIds);
        final Rest<List<ManagerDetailResponse>> managerInfoList = serverClient.getManagers(managerListSelectReq);
        BizAssertUtils.isTrue(Objects.nonNull(managerInfoList) && managerInfoList.isSuccess()
                                      && CollectionUtil.isNotEmpty(managerInfoList.getData()),
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT,
                MonitorFieldKeyConstant.USER_INFO);
        String accounts = managerInfoList.getData().stream().map(item -> item.getAccount()).collect(Collectors.joining(","));
        String idStrs = managerInfoList.getData().stream().map(item -> item.getUserSid().toString()).collect(Collectors.joining(","));
        ActionLogUtil.logParam(accounts, idStrs,
                               MonitorOperationMessageConstant.ACTION_NOTIFY_CONTACTS_ADD_USER,
                               idStrs, accounts);
        // 拼接结果
        final List<ManagerDetailResponse> sysUserResultDtoList = managerInfoList.getData();
        List<OpsAlarmContactsResult> userParam = new ArrayList<>();
        for (ManagerDetailResponse sysUserResultDto : sysUserResultDtoList) {
            OpsAlarmContactsResult opsAlarmContactsResult = new OpsAlarmContactsResult();
            opsAlarmContactsResult.setUserName(sysUserResultDto.getRealName());
            opsAlarmContactsResult.setUserPhone(sysUserResultDto.getMobile());
            opsAlarmContactsResult.setUserEmail(sysUserResultDto.getEmail());
            opsAlarmContactsResult.setImportUserId(sysUserResultDto.getUserSid().toString());
            userParam.add(opsAlarmContactsResult);
        }

        return RightCloudResult.success(
                opsNotifyTargetContactsDao.insertList(BeanHelperUtil.copyForList(OpsContactsResultDto::new, userParam)));
    }

    @Transactional
    @Override
    public RightCloudResult<Boolean> updateAlarmContacts(OpsAlarmContactsUpdateParam opsAlarmContactsUpdateParam) {
        BizAssertUtils.notNull(opsAlarmContactsUpdateParam, CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.ALARM_CONTACTS);
        BizAssertUtils.notNull(opsAlarmContactsUpdateParam.getId(), CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY,
                MonitorFieldKeyConstant.ID);
        // 校验联系人邮箱
        if (StringUtils.isNotBlank(opsAlarmContactsUpdateParam.getUserEmail())) {
            final List<OpsTableNotifyTargetContactsResultDto> checkEmailList = opsNotifyTargetContactsDao.selectList(
                    OpsNotifyTargetContactsQueryDto.builder()
                            .checkUserEmail(opsAlarmContactsUpdateParam.getUserEmail())
                            .build()
            );
            BizAssertUtils.isEmpty(
                    checkEmailList,
                    CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_ALREADYEXISTS,
                    MonitorFieldKeyConstant.USER_EMAIL
            );
        }
        // 校验联系人手机号
        if (StringUtils.isNotBlank(opsAlarmContactsUpdateParam.getUserPhone())) {
            final List<OpsTableNotifyTargetContactsResultDto> checkPhonelList = opsNotifyTargetContactsDao.selectList(
                    OpsNotifyTargetContactsQueryDto.builder()
                            .checkUserPhone(opsAlarmContactsUpdateParam.getUserPhone())
                            .build()
            );
            BizAssertUtils.isEmpty(
                    checkPhonelList,
                    CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_ALREADYEXISTS,
                    MonitorFieldKeyConstant.USER_PHONE
            );
        }

        OpsTableNotifyTargetContactsResultDto opsTableNotifyTargetContactsResultDto = new OpsTableNotifyTargetContactsResultDto();
        // 名称
        if (StringUtils.isNotEmpty(opsAlarmContactsUpdateParam.getUserName())) {
            opsTableNotifyTargetContactsResultDto.setUserName(opsAlarmContactsUpdateParam.getUserName());
        }
        // 手机号
        if (StringUtils.isNotEmpty(opsAlarmContactsUpdateParam.getUserPhone())) {
            opsTableNotifyTargetContactsResultDto.setUserPhone(opsAlarmContactsUpdateParam.getUserPhone());
        }
        // 邮箱
        if (StringUtils.isNotEmpty(opsAlarmContactsUpdateParam.getUserEmail())) {
            opsTableNotifyTargetContactsResultDto.setUserEmail(opsAlarmContactsUpdateParam.getUserEmail());
        }
        // 描述
        if (StringUtils.isNotEmpty(opsAlarmContactsUpdateParam.getDescription())) {
            opsTableNotifyTargetContactsResultDto.setDescription(opsAlarmContactsUpdateParam.getDescription());
        }
        Boolean result = false;
        if (CollectionUtil.isNotEmpty(opsAlarmContactsUpdateParam.getGroupIds())) {
            //重新设置联系人组
            result = opsNotifyTargetGroupContactsRelDao.delete(
                    OpsNotifyTargetGroupContactsRelQueryDto.builder()
                            .opsNotifyTargetContactsId(opsAlarmContactsUpdateParam.getId())
                            .build()
            );
            List<OpsTableNotifyTargetGroupContactsRelResultDto> notifyTargetGroupContactsRelResultDtos = new ArrayList<>();
            for (Long groupId : opsAlarmContactsUpdateParam.getGroupIds()) {
                OpsTableNotifyTargetGroupContactsRelResultDto opsTableNotifyTargetGroupContactsRelResultDto =
                        new OpsTableNotifyTargetGroupContactsRelResultDto();
                opsTableNotifyTargetGroupContactsRelResultDto.setOpsNotifyTargetGroupId(groupId);
                opsTableNotifyTargetGroupContactsRelResultDto.setOpsNotifyTargetContactsId(opsAlarmContactsUpdateParam.getId());
                notifyTargetGroupContactsRelResultDtos.add(opsTableNotifyTargetGroupContactsRelResultDto);
            }
            if (CollectionUtil.isNotEmpty(notifyTargetGroupContactsRelResultDtos)) {
                result = opsNotifyTargetGroupContactsRelDao.createList(notifyTargetGroupContactsRelResultDtos);
            }
        } else {
            // 删除联系人组和联系人的关系
            result = opsNotifyTargetGroupContactsRelDao.delete(
                    OpsNotifyTargetGroupContactsRelQueryDto.builder()
                                                           .opsNotifyTargetContactsId(opsAlarmContactsUpdateParam.getId())
                                                           .build()
            );
        }
        result = opsNotifyTargetContactsDao.updateByPrimaryKey(
                opsTableNotifyTargetContactsResultDto,
                OpsNotifyTargetContactsQueryDto.builder()
                        .id(opsAlarmContactsUpdateParam.getId())
                        .build()
        );
        return RightCloudResult.success(result);
    }

    @Transactional
    @Override
    public RightCloudResult<Boolean> deleteAlarmContacts(Long id) {
        //添加日志
        OpsTableNotifyTargetContactsResultDto entity = opsNotifyTargetContactsDao.selectOne(OpsNotifyTargetContactsQueryDto.builder().id(id).build());
        if (entity != null) {
            ActionLogUtil.logParam(
                    entity.getUserName(), null,
                    MonitorOperationMessageConstant.ACTION_NOTIFY_CONTACTS_DELETE,
                    entity.getId(), entity.getUserName());
        }
        // 校验是否允许删除
        final Boolean checkNotifyContactsTarget = opsNotifyPolicyDao.checkNotifyTarget(List.of(id));
        BizAssertUtils.isTrue(
                checkNotifyContactsTarget,
                CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_CANNOTDELETE,
                MonitorFieldKeyConstant.POLICY,
                MonitorFieldKeyConstant.NOTIFY_TARGET
        );
        // 查询联系人关联的组
        final List<OpsTableNotifyTargetGroupContactsRelResultDto> opsTableNotifyTargetGroupContactsRelResultDtoList =
                opsNotifyTargetGroupContactsRelDao.selectList(
                        OpsNotifyTargetGroupContactsRelQueryDto.builder()
                                .opsNotifyTargetContactsId(id)
                                .build()
                );
        if (CollectionUtil.isNotEmpty(opsTableNotifyTargetGroupContactsRelResultDtoList)) {
            final List<Long> opsNotifyTargetGroupId = opsTableNotifyTargetGroupContactsRelResultDtoList.stream()
                    .filter(Objects::nonNull)
                    .map(OpsTableNotifyTargetGroupContactsRelResultDto::getOpsNotifyTargetGroupId)
                    .collect(Collectors.toList());
            // 校验联系人组是否允许删除
            if (CollectionUtil.isNotEmpty(opsNotifyTargetGroupId)) {
                final Boolean checkNotifyGroupTarget = opsNotifyPolicyDao.checkNotifyTarget(opsNotifyTargetGroupId);
                BizAssertUtils.isTrue(
                        checkNotifyGroupTarget,
                        CommonMsgConstant.COMMON_ERROR_VALIDATION_CONSTRAINTS_CANNOTDELETE,
                        MonitorFieldKeyConstant.POLICY,
                        MonitorFieldKeyConstant.NOTIFY_TARGET
                );
            }
        }

        // 验证数据是否存在
        Boolean result = false;
        // 删除关联数据
        result = opsNotifyTargetGroupContactsRelDao.delete(
                OpsNotifyTargetGroupContactsRelQueryDto.builder()
                        .opsNotifyTargetContactsId(id)
                        .build()
        );
        result = opsNotifyTargetContactsDao.deleteById(id);
        return RightCloudResult.success(result);
    }

    @Override
    public RightCloudResult<PageResult<OpsNotifyTargetContactUserPageResult>> getAlarmContactsUserPage(OpsNotifyTargetContactUserPageParam param) {
        // 查询所有告警联系人的邮箱、电话
        final List<OpsTableNotifyTargetContactsResultDto> opsTableNotifyTargetContactsResultDtoList
                = opsNotifyTargetContactsDao.selectList(null);
        PageResult<OpsNotifyTargetContactUserPageResult> pageResult = new PageResult<>();
        pageResult.setPageNo(pageResult.getPageNo());
        pageResult.setPageSize(pageResult.getPageSize());
        pageResult.setTotalPages(0);
        pageResult.setTotal(0L);
        pageResult.setList(new ArrayList<>());
        final ManagerListSelectReq managerListSelectReq = BeanHelperUtil.copyForBean(ManagerListSelectReq::new, param);
        if (CollectionUtil.isNotEmpty(opsTableNotifyTargetContactsResultDtoList)) {
            // 联系人电话
            if (Objects.nonNull(managerListSelectReq)) {
                final List<String> userPhoneList = opsTableNotifyTargetContactsResultDtoList.stream()
                        .filter(Objects::nonNull)
                        .map(OpsTableNotifyTargetContactsResultDto::getUserPhone)
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(userPhoneList)) {
                    managerListSelectReq.setNotMobile(userPhoneList);
                }
                // 联系人邮箱
                final List<String> userEmailList = opsTableNotifyTargetContactsResultDtoList.stream()
                        .filter(Objects::nonNull)
                        .map(OpsTableNotifyTargetContactsResultDto::getUserEmail)
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(userEmailList)) {
                    managerListSelectReq.setNotEmail(userEmailList);
                }
            }
            List<String> constantUserIds = opsTableNotifyTargetContactsResultDtoList
                    .stream()
                    .filter(t -> StringUtils.isNotEmpty(
                            t.getImportUserId()))
                    .map(OpsTableNotifyTargetContactsResultDto::getImportUserId)
                    .distinct()
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(constantUserIds)) {
                List<Long> constantUserIdList = constantUserIds.stream()
                                                               .map(Long::parseLong)       // 将 String 转换为 Long
                                                               .collect(Collectors.toList());
                managerListSelectReq.setUserSidsNot(constantUserIdList);
            }
        }
        final Rest<com.cloudstar.rightcloud.monitor.common.feign.result.PageResult<ManagerDetailResponse>>
                managerListSelectResult = serverClient.getManagerList(managerListSelectReq);
        if (Objects.nonNull(managerListSelectResult) && Objects.nonNull(managerListSelectResult.getData())) {
            final com.cloudstar.rightcloud.monitor.common.feign.result.PageResult<ManagerDetailResponse>
                    pageFeignResultData = managerListSelectResult.getData();
            pageResult.setPageNo(pageFeignResultData.getPageNo());
            pageResult.setPageSize(pageFeignResultData.getPageSize());
            pageResult.setTotalPages(pageFeignResultData.getTotalPages());
            pageResult.setTotal(pageFeignResultData.getTotal().longValue());
            if (CollectionUtil.isNotEmpty(pageFeignResultData.getList())) {
                final List<OpsNotifyTargetContactUserPageResult> opsNotifyTargetContactUserPageResults = BeanHelperUtil.copyForList(
                        OpsNotifyTargetContactUserPageResult::new,
                        pageFeignResultData.getList()
                );
                if (CollectionUtil.isNotEmpty(opsNotifyTargetContactUserPageResults)) {
                    pageResult.setList(opsNotifyTargetContactUserPageResults);
                }
            }
        }
        return RightCloudResult.success(pageResult);
    }

    @Override
    public RightCloudResult<Boolean> checkAlarmContactsUser(OpsAlarmContactsUserCheckParam param) {
        BizAssertUtils.notNull(
                param,
                CommonMsgConstant.COMMON_ERROR_HANDLE_INVALIDPARAMETER
        );
        if (org.apache.commons.lang3.StringUtils.isNotBlank(param.getUserPhone())) {
            Pattern compile
                    = Pattern
                    .compile("(^(13[0-9]{9})|(18[0-9]{9})|(14[0-9]{9})|(17[0-9]{9})"
                            + "|(15[0-9]{9})|(19[0-9]{9})|(16[0-9]{9})$)|(^([1|6|8|9]{1}[0-9]{8})$)");
            Matcher matcher = compile.matcher(param.getUserPhone());
            BizAssertUtils.isTrue(matcher.find(),
                    CommonMsgConstant.COMMON_ERROR_HANDLE_INVALIDPARAMETER);
        }
        boolean result = true;
        // 校验邮箱
        if (StringUtils.isNotEmpty(param.getUserEmail())) {
            // 校验联系人邮箱
            final List<OpsTableNotifyTargetContactsResultDto> checkEmailList = opsNotifyTargetContactsDao.selectList(
                    OpsNotifyTargetContactsQueryDto.builder()
                            .checkUserEmail(param.getUserEmail())
                            .build()
            );
            result = CollectionUtil.isEmpty(checkEmailList);
        }
        // 校验手机号
        if (StringUtils.isNotEmpty(param.getUserPhone())) {
            // 校验联系人手机号
            final List<OpsTableNotifyTargetContactsResultDto> checkPhonelList = opsNotifyTargetContactsDao.selectList(
                    OpsNotifyTargetContactsQueryDto.builder()
                            .checkUserPhone(param.getUserPhone())
                            .build()
            );
            result = CollectionUtil.isEmpty(checkPhonelList);
        }
        return RightCloudResult.success(result);
    }
}
