#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (malloc) failed to allocate 32744 bytes. Error detail: Chunk::new
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (arena.cpp:168), pid=10640, tid=31552
#
# JRE version: OpenJDK Runtime Environment JBR-21.0.6+8-631.39-jcef (21.0.6+8) (build 21.0.6+8-b631.39)
# Java VM: OpenJDK 64-Bit Server VM JBR-21.0.6+8-631.39-jcef (21.0.6+8-b631.39, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: git4idea.http.GitAskPassApp Username for 'https://git.rightcloud.com.cn': 

Host: AMD Ryzen 5 5600U with Radeon Graphics         , 12 cores, 15G,  Windows 11 , 64 bit Build 22621 (10.0.22621.457)
Time: Wed May 28 19:19:35 2025  Windows 11 , 64 bit Build 22621 (10.0.22621.457) elapsed time: 2.010458 seconds (0d 0h 0m 2s)

---------------  T H R E A D  ---------------

Current thread (0x000002de77b96b90):  JavaThread "C2 CompilerThread0" daemon [_thread_in_native, id=31552, stack(0x000000f95a100000,0x000000f95a200000) (1024K)]


Current CompileTask:
C2:2010 1093       4       sun.security.ec.ECOperations::setDouble (463 bytes)

Stack: [0x000000f95a100000,0x000000f95a200000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6e5d39]
V  [jvm.dll+0x8c4133]
V  [jvm.dll+0x8c668e]
V  [jvm.dll+0x8c6d73]
V  [jvm.dll+0x288f76]
V  [jvm.dll+0xc66dd]
V  [jvm.dll+0xc6c13]
V  [jvm.dll+0x81f528]
V  [jvm.dll+0x6082fd]
V  [jvm.dll+0x60601b]
V  [jvm.dll+0x605c7d]
V  [jvm.dll+0x605b60]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x605cde]
V  [jvm.dll+0x60d08c]
V  [jvm.dll+0x2598d2]
V  [jvm.dll+0x259c8f]
V  [jvm.dll+0x252435]
V  [jvm.dll+0x24fc8e]
V  [jvm.dll+0x1cd6c4]
V  [jvm.dll+0x25f60c]
V  [jvm.dll+0x25db56]
V  [jvm.dll+0x3ff756]
V  [jvm.dll+0x86bd48]
V  [jvm.dll+0x6e453d]
C  [ucrtbase.dll+0x29363]
C  [KERNEL32.DLL+0x1244d]
C  [ntdll.dll+0x5df78]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000002de7c83fbd0, length=14, elements={
0x000002de5cda3890, 0x000002de77b90a70, 0x000002de77b914c0, 0x000002de77b93f20,
0x000002de77b94970, 0x000002de77b953c0, 0x000002de77b95e10, 0x000002de77b96b90,
0x000002de77b97620, 0x000002de7c10a7c0, 0x000002de7c10e410, 0x000002de7c7eb4a0,
0x000002de7c88bee0, 0x000002de7c894d80
}

Java Threads: ( => current thread )
  0x000002de5cda3890 JavaThread "main"                              [_thread_blocked, id=13756, stack(0x000000f959300000,0x000000f959400000) (1024K)]
  0x000002de77b90a70 JavaThread "Reference Handler"          daemon [_thread_blocked, id=2336, stack(0x000000f959b00000,0x000000f959c00000) (1024K)]
  0x000002de77b914c0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=15512, stack(0x000000f959c00000,0x000000f959d00000) (1024K)]
  0x000002de77b93f20 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=17312, stack(0x000000f959d00000,0x000000f959e00000) (1024K)]
  0x000002de77b94970 JavaThread "Attach Listener"            daemon [_thread_blocked, id=21116, stack(0x000000f959e00000,0x000000f959f00000) (1024K)]
  0x000002de77b953c0 JavaThread "Service Thread"             daemon [_thread_blocked, id=30188, stack(0x000000f959f00000,0x000000f95a000000) (1024K)]
  0x000002de77b95e10 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=5288, stack(0x000000f95a000000,0x000000f95a100000) (1024K)]
=>0x000002de77b96b90 JavaThread "C2 CompilerThread0"         daemon [_thread_in_native, id=31552, stack(0x000000f95a100000,0x000000f95a200000) (1024K)]
  0x000002de77b97620 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=12152, stack(0x000000f95a200000,0x000000f95a300000) (1024K)]
  0x000002de7c10a7c0 JavaThread "Notification Thread"        daemon [_thread_blocked, id=29088, stack(0x000000f95a300000,0x000000f95a400000) (1024K)]
  0x000002de7c10e410 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=27048, stack(0x000000f95a400000,0x000000f95a500000) (1024K)]
  0x000002de7c7eb4a0 JavaThread "HttpClient-1-SelectorManager" daemon [_thread_in_native, id=12620, stack(0x000000f95a500000,0x000000f95a600000) (1024K)]
  0x000002de7c88bee0 JavaThread "HttpClient-1-Worker-0"      daemon [_thread_blocked, id=25980, stack(0x000000f95a700000,0x000000f95a800000) (1024K)]
  0x000002de7c894d80 JavaThread "HttpClient-1-Worker-1"      daemon [_thread_blocked, id=24488, stack(0x000000f95a800000,0x000000f95a900000) (1024K)]
Total: 14

Other Threads:
  0x000002de77b73560 VMThread "VM Thread"                           [id=18096, stack(0x000000f959a00000,0x000000f959b00000) (1024K)]
  0x000002de77b62970 WatcherThread "VM Periodic Task Thread"        [id=30932, stack(0x000000f959900000,0x000000f959a00000) (1024K)]
  0x000002de5ce07910 WorkerThread "GC Thread#0"                     [id=8808, stack(0x000000f959400000,0x000000f959500000) (1024K)]
  0x000002de5ce19d10 ConcurrentGCThread "G1 Main Marker"            [id=18712, stack(0x000000f959500000,0x000000f959600000) (1024K)]
  0x000002de5ce1b720 WorkerThread "G1 Conc#0"                       [id=26704, stack(0x000000f959600000,0x000000f959700000) (1024K)]
  0x000002de77a35f30 ConcurrentGCThread "G1 Refine#0"               [id=27304, stack(0x000000f959700000,0x000000f959800000) (1024K)]
  0x000002de77a37760 ConcurrentGCThread "G1 Service"                [id=14440, stack(0x000000f959800000,0x000000f959900000) (1024K)]
Total: 7

Threads with active compile tasks:
C2 CompilerThread0  2057 1093       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Total: 1

VM state: not at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread: None

Heap address: 0x000000070ae00000, size: 3922 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x000002de00000000-0x000002de00d00000-0x000002de00d00000), size 13631488, SharedBaseAddress: 0x000002de00000000, ArchiveRelocationMode: 1.
Compressed class space mapped at: 0x000002de01000000-0x000002de41000000, reserved size: 1073741824
Narrow klass base: 0x000002de00000000, Narrow klass shift: 0, Narrow klass range: 0x100000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096
 CPUs: 12 total, 12 available
 Memory: 15681M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (Zero based)
 Heap Region Size: 2M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 246M
 Heap Max Capacity: 3922M
 Pre-touch: Disabled
 Parallel Workers: 10
 Concurrent Workers: 3
 Concurrent Refinement Workers: 10
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 251904K, used 14336K [0x000000070ae00000, 0x0000000800000000)
  region size 2048K, 8 young (16384K), 0 survivors (0K)
 Metaspace       used 8109K, committed 8384K, reserved 1114112K
  class space    used 927K, committed 1024K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x000000070ae00000, 0x000000070ae00000, 0x000000070b000000|  0%| F|  |TAMS 0x000000070ae00000| PB 0x000000070ae00000| Untracked 
|   1|0x000000070b000000, 0x000000070b000000, 0x000000070b200000|  0%| F|  |TAMS 0x000000070b000000| PB 0x000000070b000000| Untracked 
|   2|0x000000070b200000, 0x000000070b200000, 0x000000070b400000|  0%| F|  |TAMS 0x000000070b200000| PB 0x000000070b200000| Untracked 
|   3|0x000000070b400000, 0x000000070b400000, 0x000000070b600000|  0%| F|  |TAMS 0x000000070b400000| PB 0x000000070b400000| Untracked 
|   4|0x000000070b600000, 0x000000070b600000, 0x000000070b800000|  0%| F|  |TAMS 0x000000070b600000| PB 0x000000070b600000| Untracked 
|   5|0x000000070b800000, 0x000000070b800000, 0x000000070ba00000|  0%| F|  |TAMS 0x000000070b800000| PB 0x000000070b800000| Untracked 
|   6|0x000000070ba00000, 0x000000070ba00000, 0x000000070bc00000|  0%| F|  |TAMS 0x000000070ba00000| PB 0x000000070ba00000| Untracked 
|   7|0x000000070bc00000, 0x000000070bc00000, 0x000000070be00000|  0%| F|  |TAMS 0x000000070bc00000| PB 0x000000070bc00000| Untracked 
|   8|0x000000070be00000, 0x000000070be00000, 0x000000070c000000|  0%| F|  |TAMS 0x000000070be00000| PB 0x000000070be00000| Untracked 
|   9|0x000000070c000000, 0x000000070c000000, 0x000000070c200000|  0%| F|  |TAMS 0x000000070c000000| PB 0x000000070c000000| Untracked 
|  10|0x000000070c200000, 0x000000070c200000, 0x000000070c400000|  0%| F|  |TAMS 0x000000070c200000| PB 0x000000070c200000| Untracked 
|  11|0x000000070c400000, 0x000000070c400000, 0x000000070c600000|  0%| F|  |TAMS 0x000000070c400000| PB 0x000000070c400000| Untracked 
|  12|0x000000070c600000, 0x000000070c600000, 0x000000070c800000|  0%| F|  |TAMS 0x000000070c600000| PB 0x000000070c600000| Untracked 
|  13|0x000000070c800000, 0x000000070c800000, 0x000000070ca00000|  0%| F|  |TAMS 0x000000070c800000| PB 0x000000070c800000| Untracked 
|  14|0x000000070ca00000, 0x000000070ca00000, 0x000000070cc00000|  0%| F|  |TAMS 0x000000070ca00000| PB 0x000000070ca00000| Untracked 
|  15|0x000000070cc00000, 0x000000070cc00000, 0x000000070ce00000|  0%| F|  |TAMS 0x000000070cc00000| PB 0x000000070cc00000| Untracked 
|  16|0x000000070ce00000, 0x000000070ce00000, 0x000000070d000000|  0%| F|  |TAMS 0x000000070ce00000| PB 0x000000070ce00000| Untracked 
|  17|0x000000070d000000, 0x000000070d000000, 0x000000070d200000|  0%| F|  |TAMS 0x000000070d000000| PB 0x000000070d000000| Untracked 
|  18|0x000000070d200000, 0x000000070d200000, 0x000000070d400000|  0%| F|  |TAMS 0x000000070d200000| PB 0x000000070d200000| Untracked 
|  19|0x000000070d400000, 0x000000070d400000, 0x000000070d600000|  0%| F|  |TAMS 0x000000070d400000| PB 0x000000070d400000| Untracked 
|  20|0x000000070d600000, 0x000000070d600000, 0x000000070d800000|  0%| F|  |TAMS 0x000000070d600000| PB 0x000000070d600000| Untracked 
|  21|0x000000070d800000, 0x000000070d800000, 0x000000070da00000|  0%| F|  |TAMS 0x000000070d800000| PB 0x000000070d800000| Untracked 
|  22|0x000000070da00000, 0x000000070da00000, 0x000000070dc00000|  0%| F|  |TAMS 0x000000070da00000| PB 0x000000070da00000| Untracked 
|  23|0x000000070dc00000, 0x000000070dc00000, 0x000000070de00000|  0%| F|  |TAMS 0x000000070dc00000| PB 0x000000070dc00000| Untracked 
|  24|0x000000070de00000, 0x000000070de00000, 0x000000070e000000|  0%| F|  |TAMS 0x000000070de00000| PB 0x000000070de00000| Untracked 
|  25|0x000000070e000000, 0x000000070e000000, 0x000000070e200000|  0%| F|  |TAMS 0x000000070e000000| PB 0x000000070e000000| Untracked 
|  26|0x000000070e200000, 0x000000070e200000, 0x000000070e400000|  0%| F|  |TAMS 0x000000070e200000| PB 0x000000070e200000| Untracked 
|  27|0x000000070e400000, 0x000000070e400000, 0x000000070e600000|  0%| F|  |TAMS 0x000000070e400000| PB 0x000000070e400000| Untracked 
|  28|0x000000070e600000, 0x000000070e600000, 0x000000070e800000|  0%| F|  |TAMS 0x000000070e600000| PB 0x000000070e600000| Untracked 
|  29|0x000000070e800000, 0x000000070e800000, 0x000000070ea00000|  0%| F|  |TAMS 0x000000070e800000| PB 0x000000070e800000| Untracked 
|  30|0x000000070ea00000, 0x000000070ea00000, 0x000000070ec00000|  0%| F|  |TAMS 0x000000070ea00000| PB 0x000000070ea00000| Untracked 
|  31|0x000000070ec00000, 0x000000070ec00000, 0x000000070ee00000|  0%| F|  |TAMS 0x000000070ec00000| PB 0x000000070ec00000| Untracked 
|  32|0x000000070ee00000, 0x000000070ee00000, 0x000000070f000000|  0%| F|  |TAMS 0x000000070ee00000| PB 0x000000070ee00000| Untracked 
|  33|0x000000070f000000, 0x000000070f000000, 0x000000070f200000|  0%| F|  |TAMS 0x000000070f000000| PB 0x000000070f000000| Untracked 
|  34|0x000000070f200000, 0x000000070f200000, 0x000000070f400000|  0%| F|  |TAMS 0x000000070f200000| PB 0x000000070f200000| Untracked 
|  35|0x000000070f400000, 0x000000070f400000, 0x000000070f600000|  0%| F|  |TAMS 0x000000070f400000| PB 0x000000070f400000| Untracked 
|  36|0x000000070f600000, 0x000000070f600000, 0x000000070f800000|  0%| F|  |TAMS 0x000000070f600000| PB 0x000000070f600000| Untracked 
|  37|0x000000070f800000, 0x000000070f800000, 0x000000070fa00000|  0%| F|  |TAMS 0x000000070f800000| PB 0x000000070f800000| Untracked 
|  38|0x000000070fa00000, 0x000000070fa00000, 0x000000070fc00000|  0%| F|  |TAMS 0x000000070fa00000| PB 0x000000070fa00000| Untracked 
|  39|0x000000070fc00000, 0x000000070fc00000, 0x000000070fe00000|  0%| F|  |TAMS 0x000000070fc00000| PB 0x000000070fc00000| Untracked 
|  40|0x000000070fe00000, 0x000000070fe00000, 0x0000000710000000|  0%| F|  |TAMS 0x000000070fe00000| PB 0x000000070fe00000| Untracked 
|  41|0x0000000710000000, 0x0000000710000000, 0x0000000710200000|  0%| F|  |TAMS 0x0000000710000000| PB 0x0000000710000000| Untracked 
|  42|0x0000000710200000, 0x0000000710200000, 0x0000000710400000|  0%| F|  |TAMS 0x0000000710200000| PB 0x0000000710200000| Untracked 
|  43|0x0000000710400000, 0x0000000710400000, 0x0000000710600000|  0%| F|  |TAMS 0x0000000710400000| PB 0x0000000710400000| Untracked 
|  44|0x0000000710600000, 0x0000000710600000, 0x0000000710800000|  0%| F|  |TAMS 0x0000000710600000| PB 0x0000000710600000| Untracked 
|  45|0x0000000710800000, 0x0000000710800000, 0x0000000710a00000|  0%| F|  |TAMS 0x0000000710800000| PB 0x0000000710800000| Untracked 
|  46|0x0000000710a00000, 0x0000000710a00000, 0x0000000710c00000|  0%| F|  |TAMS 0x0000000710a00000| PB 0x0000000710a00000| Untracked 
|  47|0x0000000710c00000, 0x0000000710c00000, 0x0000000710e00000|  0%| F|  |TAMS 0x0000000710c00000| PB 0x0000000710c00000| Untracked 
|  48|0x0000000710e00000, 0x0000000710e00000, 0x0000000711000000|  0%| F|  |TAMS 0x0000000710e00000| PB 0x0000000710e00000| Untracked 
|  49|0x0000000711000000, 0x0000000711000000, 0x0000000711200000|  0%| F|  |TAMS 0x0000000711000000| PB 0x0000000711000000| Untracked 
|  50|0x0000000711200000, 0x0000000711200000, 0x0000000711400000|  0%| F|  |TAMS 0x0000000711200000| PB 0x0000000711200000| Untracked 
|  51|0x0000000711400000, 0x0000000711400000, 0x0000000711600000|  0%| F|  |TAMS 0x0000000711400000| PB 0x0000000711400000| Untracked 
|  52|0x0000000711600000, 0x0000000711600000, 0x0000000711800000|  0%| F|  |TAMS 0x0000000711600000| PB 0x0000000711600000| Untracked 
|  53|0x0000000711800000, 0x0000000711800000, 0x0000000711a00000|  0%| F|  |TAMS 0x0000000711800000| PB 0x0000000711800000| Untracked 
|  54|0x0000000711a00000, 0x0000000711a00000, 0x0000000711c00000|  0%| F|  |TAMS 0x0000000711a00000| PB 0x0000000711a00000| Untracked 
|  55|0x0000000711c00000, 0x0000000711c00000, 0x0000000711e00000|  0%| F|  |TAMS 0x0000000711c00000| PB 0x0000000711c00000| Untracked 
|  56|0x0000000711e00000, 0x0000000711e00000, 0x0000000712000000|  0%| F|  |TAMS 0x0000000711e00000| PB 0x0000000711e00000| Untracked 
|  57|0x0000000712000000, 0x0000000712000000, 0x0000000712200000|  0%| F|  |TAMS 0x0000000712000000| PB 0x0000000712000000| Untracked 
|  58|0x0000000712200000, 0x0000000712200000, 0x0000000712400000|  0%| F|  |TAMS 0x0000000712200000| PB 0x0000000712200000| Untracked 
|  59|0x0000000712400000, 0x0000000712400000, 0x0000000712600000|  0%| F|  |TAMS 0x0000000712400000| PB 0x0000000712400000| Untracked 
|  60|0x0000000712600000, 0x0000000712600000, 0x0000000712800000|  0%| F|  |TAMS 0x0000000712600000| PB 0x0000000712600000| Untracked 
|  61|0x0000000712800000, 0x0000000712800000, 0x0000000712a00000|  0%| F|  |TAMS 0x0000000712800000| PB 0x0000000712800000| Untracked 
|  62|0x0000000712a00000, 0x0000000712a00000, 0x0000000712c00000|  0%| F|  |TAMS 0x0000000712a00000| PB 0x0000000712a00000| Untracked 
|  63|0x0000000712c00000, 0x0000000712c00000, 0x0000000712e00000|  0%| F|  |TAMS 0x0000000712c00000| PB 0x0000000712c00000| Untracked 
|  64|0x0000000712e00000, 0x0000000712e00000, 0x0000000713000000|  0%| F|  |TAMS 0x0000000712e00000| PB 0x0000000712e00000| Untracked 
|  65|0x0000000713000000, 0x0000000713000000, 0x0000000713200000|  0%| F|  |TAMS 0x0000000713000000| PB 0x0000000713000000| Untracked 
|  66|0x0000000713200000, 0x0000000713200000, 0x0000000713400000|  0%| F|  |TAMS 0x0000000713200000| PB 0x0000000713200000| Untracked 
|  67|0x0000000713400000, 0x0000000713400000, 0x0000000713600000|  0%| F|  |TAMS 0x0000000713400000| PB 0x0000000713400000| Untracked 
|  68|0x0000000713600000, 0x0000000713600000, 0x0000000713800000|  0%| F|  |TAMS 0x0000000713600000| PB 0x0000000713600000| Untracked 
|  69|0x0000000713800000, 0x0000000713800000, 0x0000000713a00000|  0%| F|  |TAMS 0x0000000713800000| PB 0x0000000713800000| Untracked 
|  70|0x0000000713a00000, 0x0000000713a00000, 0x0000000713c00000|  0%| F|  |TAMS 0x0000000713a00000| PB 0x0000000713a00000| Untracked 
|  71|0x0000000713c00000, 0x0000000713c00000, 0x0000000713e00000|  0%| F|  |TAMS 0x0000000713c00000| PB 0x0000000713c00000| Untracked 
|  72|0x0000000713e00000, 0x0000000713e00000, 0x0000000714000000|  0%| F|  |TAMS 0x0000000713e00000| PB 0x0000000713e00000| Untracked 
|  73|0x0000000714000000, 0x0000000714000000, 0x0000000714200000|  0%| F|  |TAMS 0x0000000714000000| PB 0x0000000714000000| Untracked 
|  74|0x0000000714200000, 0x0000000714200000, 0x0000000714400000|  0%| F|  |TAMS 0x0000000714200000| PB 0x0000000714200000| Untracked 
|  75|0x0000000714400000, 0x0000000714400000, 0x0000000714600000|  0%| F|  |TAMS 0x0000000714400000| PB 0x0000000714400000| Untracked 
|  76|0x0000000714600000, 0x0000000714600000, 0x0000000714800000|  0%| F|  |TAMS 0x0000000714600000| PB 0x0000000714600000| Untracked 
|  77|0x0000000714800000, 0x0000000714800000, 0x0000000714a00000|  0%| F|  |TAMS 0x0000000714800000| PB 0x0000000714800000| Untracked 
|  78|0x0000000714a00000, 0x0000000714a00000, 0x0000000714c00000|  0%| F|  |TAMS 0x0000000714a00000| PB 0x0000000714a00000| Untracked 
|  79|0x0000000714c00000, 0x0000000714c00000, 0x0000000714e00000|  0%| F|  |TAMS 0x0000000714c00000| PB 0x0000000714c00000| Untracked 
|  80|0x0000000714e00000, 0x0000000714e00000, 0x0000000715000000|  0%| F|  |TAMS 0x0000000714e00000| PB 0x0000000714e00000| Untracked 
|  81|0x0000000715000000, 0x0000000715000000, 0x0000000715200000|  0%| F|  |TAMS 0x0000000715000000| PB 0x0000000715000000| Untracked 
|  82|0x0000000715200000, 0x0000000715200000, 0x0000000715400000|  0%| F|  |TAMS 0x0000000715200000| PB 0x0000000715200000| Untracked 
|  83|0x0000000715400000, 0x0000000715400000, 0x0000000715600000|  0%| F|  |TAMS 0x0000000715400000| PB 0x0000000715400000| Untracked 
|  84|0x0000000715600000, 0x0000000715600000, 0x0000000715800000|  0%| F|  |TAMS 0x0000000715600000| PB 0x0000000715600000| Untracked 
|  85|0x0000000715800000, 0x0000000715800000, 0x0000000715a00000|  0%| F|  |TAMS 0x0000000715800000| PB 0x0000000715800000| Untracked 
|  86|0x0000000715a00000, 0x0000000715a00000, 0x0000000715c00000|  0%| F|  |TAMS 0x0000000715a00000| PB 0x0000000715a00000| Untracked 
|  87|0x0000000715c00000, 0x0000000715c00000, 0x0000000715e00000|  0%| F|  |TAMS 0x0000000715c00000| PB 0x0000000715c00000| Untracked 
|  88|0x0000000715e00000, 0x0000000715e00000, 0x0000000716000000|  0%| F|  |TAMS 0x0000000715e00000| PB 0x0000000715e00000| Untracked 
|  89|0x0000000716000000, 0x0000000716000000, 0x0000000716200000|  0%| F|  |TAMS 0x0000000716000000| PB 0x0000000716000000| Untracked 
|  90|0x0000000716200000, 0x0000000716200000, 0x0000000716400000|  0%| F|  |TAMS 0x0000000716200000| PB 0x0000000716200000| Untracked 
|  91|0x0000000716400000, 0x0000000716400000, 0x0000000716600000|  0%| F|  |TAMS 0x0000000716400000| PB 0x0000000716400000| Untracked 
|  92|0x0000000716600000, 0x0000000716600000, 0x0000000716800000|  0%| F|  |TAMS 0x0000000716600000| PB 0x0000000716600000| Untracked 
|  93|0x0000000716800000, 0x0000000716800000, 0x0000000716a00000|  0%| F|  |TAMS 0x0000000716800000| PB 0x0000000716800000| Untracked 
|  94|0x0000000716a00000, 0x0000000716a00000, 0x0000000716c00000|  0%| F|  |TAMS 0x0000000716a00000| PB 0x0000000716a00000| Untracked 
|  95|0x0000000716c00000, 0x0000000716c00000, 0x0000000716e00000|  0%| F|  |TAMS 0x0000000716c00000| PB 0x0000000716c00000| Untracked 
|  96|0x0000000716e00000, 0x0000000716e00000, 0x0000000717000000|  0%| F|  |TAMS 0x0000000716e00000| PB 0x0000000716e00000| Untracked 
|  97|0x0000000717000000, 0x0000000717000000, 0x0000000717200000|  0%| F|  |TAMS 0x0000000717000000| PB 0x0000000717000000| Untracked 
|  98|0x0000000717200000, 0x0000000717200000, 0x0000000717400000|  0%| F|  |TAMS 0x0000000717200000| PB 0x0000000717200000| Untracked 
|  99|0x0000000717400000, 0x0000000717400000, 0x0000000717600000|  0%| F|  |TAMS 0x0000000717400000| PB 0x0000000717400000| Untracked 
| 100|0x0000000717600000, 0x0000000717600000, 0x0000000717800000|  0%| F|  |TAMS 0x0000000717600000| PB 0x0000000717600000| Untracked 
| 101|0x0000000717800000, 0x0000000717800000, 0x0000000717a00000|  0%| F|  |TAMS 0x0000000717800000| PB 0x0000000717800000| Untracked 
| 102|0x0000000717a00000, 0x0000000717a00000, 0x0000000717c00000|  0%| F|  |TAMS 0x0000000717a00000| PB 0x0000000717a00000| Untracked 
| 103|0x0000000717c00000, 0x0000000717c00000, 0x0000000717e00000|  0%| F|  |TAMS 0x0000000717c00000| PB 0x0000000717c00000| Untracked 
| 104|0x0000000717e00000, 0x0000000717e00000, 0x0000000718000000|  0%| F|  |TAMS 0x0000000717e00000| PB 0x0000000717e00000| Untracked 
| 105|0x0000000718000000, 0x0000000718000000, 0x0000000718200000|  0%| F|  |TAMS 0x0000000718000000| PB 0x0000000718000000| Untracked 
| 106|0x0000000718200000, 0x0000000718200000, 0x0000000718400000|  0%| F|  |TAMS 0x0000000718200000| PB 0x0000000718200000| Untracked 
| 107|0x0000000718400000, 0x0000000718400000, 0x0000000718600000|  0%| F|  |TAMS 0x0000000718400000| PB 0x0000000718400000| Untracked 
| 108|0x0000000718600000, 0x0000000718600000, 0x0000000718800000|  0%| F|  |TAMS 0x0000000718600000| PB 0x0000000718600000| Untracked 
| 109|0x0000000718800000, 0x0000000718800000, 0x0000000718a00000|  0%| F|  |TAMS 0x0000000718800000| PB 0x0000000718800000| Untracked 
| 110|0x0000000718a00000, 0x0000000718a00000, 0x0000000718c00000|  0%| F|  |TAMS 0x0000000718a00000| PB 0x0000000718a00000| Untracked 
| 111|0x0000000718c00000, 0x0000000718c00000, 0x0000000718e00000|  0%| F|  |TAMS 0x0000000718c00000| PB 0x0000000718c00000| Untracked 
| 112|0x0000000718e00000, 0x0000000718e00000, 0x0000000719000000|  0%| F|  |TAMS 0x0000000718e00000| PB 0x0000000718e00000| Untracked 
| 113|0x0000000719000000, 0x0000000719000000, 0x0000000719200000|  0%| F|  |TAMS 0x0000000719000000| PB 0x0000000719000000| Untracked 
| 114|0x0000000719200000, 0x0000000719200000, 0x0000000719400000|  0%| F|  |TAMS 0x0000000719200000| PB 0x0000000719200000| Untracked 
| 115|0x0000000719400000, 0x0000000719551f78, 0x0000000719600000| 66%| E|  |TAMS 0x0000000719400000| PB 0x0000000719400000| Complete 
| 116|0x0000000719600000, 0x0000000719800000, 0x0000000719800000|100%| E|CS|TAMS 0x0000000719600000| PB 0x0000000719600000| Complete 
| 117|0x0000000719800000, 0x0000000719a00000, 0x0000000719a00000|100%| E|CS|TAMS 0x0000000719800000| PB 0x0000000719800000| Complete 
| 118|0x0000000719a00000, 0x0000000719c00000, 0x0000000719c00000|100%| E|CS|TAMS 0x0000000719a00000| PB 0x0000000719a00000| Complete 
| 119|0x0000000719c00000, 0x0000000719e00000, 0x0000000719e00000|100%| E|CS|TAMS 0x0000000719c00000| PB 0x0000000719c00000| Complete 
| 120|0x0000000719e00000, 0x000000071a000000, 0x000000071a000000|100%| E|CS|TAMS 0x0000000719e00000| PB 0x0000000719e00000| Complete 
| 121|0x000000071a000000, 0x000000071a200000, 0x000000071a200000|100%| E|CS|TAMS 0x000000071a000000| PB 0x000000071a000000| Complete 
| 122|0x000000071a200000, 0x000000071a400000, 0x000000071a400000|100%| E|CS|TAMS 0x000000071a200000| PB 0x000000071a200000| Complete 

Card table byte_map: [0x000002de70a40000,0x000002de711f0000] _byte_map_base: 0x000002de6d1e9000

Marking Bits: (CMBitMap*) 0x000002de5ce09f20
 Bits: [0x000002de711f0000, 0x000002de74f38000)

Polling page: 0x000002de5acf0000

Metaspace:

Usage:
  Non-class:      7.01 MB used.
      Class:    927.56 KB used.
       Both:      7.92 MB used.

Virtual space:
  Non-class space:       64.00 MB reserved,       7.19 MB ( 11%) committed,  1 nodes.
      Class space:        1.00 GB reserved,       1.00 MB ( <1%) committed,  1 nodes.
             Both:        1.06 GB reserved,       8.19 MB ( <1%) committed. 

Chunk freelists:
   Non-Class:  8.41 MB
       Class:  15.00 MB
        Both:  23.41 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 21.00 MB
CDS: on
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 0.
num_arena_births: 224.
num_arena_deaths: 0.
num_vsnodes_births: 2.
num_vsnodes_deaths: 0.
num_space_committed: 131.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 0.
num_chunks_taken_from_freelist: 374.
num_chunk_merges: 0.
num_chunk_splits: 213.
num_chunks_enlarged: 92.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=377Kb max_used=377Kb free=119622Kb
 bounds [0x000002de68810000, 0x000002de68a80000, 0x000002de6fd40000]
CodeHeap 'profiled nmethods': size=120000Kb used=1714Kb max_used=1714Kb free=118285Kb
 bounds [0x000002de60d40000, 0x000002de60fb0000, 0x000002de68270000]
CodeHeap 'non-nmethods': size=5760Kb used=1429Kb max_used=1457Kb free=4330Kb
 bounds [0x000002de68270000, 0x000002de684e0000, 0x000002de68810000]
 total_blobs=1616 nmethods=1101 adapters=420
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 1.611 Thread 0x000002de7c8599f0 nmethod 1090 0x000002de6886c910 code [0x000002de6886caa0, 0x000002de6886cbd8]
Event: 1.693 Thread 0x000002de7c851c40 1092       4       sun.security.ec.ECOperations$PointMultiplier::lookup (84 bytes)
Event: 1.722 Thread 0x000002de7c851c40 nmethod 1092 0x000002de6886cd10 code [0x000002de6886cf00, 0x000002de6886d9f8]
Event: 1.835 Thread 0x000002de77b96b90 1093       4       sun.security.ec.ECOperations::setDouble (463 bytes)
Event: 1.836 Thread 0x000002de77b97620 1094       3       sun.security.util.math.intpoly.IntegerPolynomial::get0 (10 bytes)
Event: 1.836 Thread 0x000002de77b97620 nmethod 1094 0x000002de60eea610 code [0x000002de60eea7c0, 0x000002de60eea9c8]
Event: 1.836 Thread 0x000002de77b97620 1095       3       sun.security.util.math.intpoly.IntegerPolynomial::get0 (5 bytes)
Event: 1.836 Thread 0x000002de77b97620 nmethod 1095 0x000002de60eeab10 code [0x000002de60eeacc0, 0x000002de60eeafb0]
Event: 1.888 Thread 0x000002de77b97620 1097       3       sun.security.ec.ECOperations$PointMultiplier$Secp256R1GeneratorMultiplier::bit (13 bytes)
Event: 1.888 Thread 0x000002de77b97620 nmethod 1097 0x000002de60eeb110 code [0x000002de60eeb2a0, 0x000002de60eeb3c8]
Event: 1.889 Thread 0x000002de77b97620 1098       3       java.nio.HeapByteBuffer::get (14 bytes)
Event: 1.889 Thread 0x000002de77b97620 nmethod 1098 0x000002de60eeb490 code [0x000002de60eeb660, 0x000002de60eeb938]
Event: 1.889 Thread 0x000002de77b97620 1099       3       java.nio.Buffer::nextGetIndex (30 bytes)
Event: 1.889 Thread 0x000002de77b97620 nmethod 1099 0x000002de60eebb10 code [0x000002de60eebcc0, 0x000002de60eebe90]
Event: 1.893 Thread 0x000002de77b97620 1100       3       java.util.LinkedList$ListItr::hasNext (20 bytes)
Event: 1.893 Thread 0x000002de77b97620 nmethod 1100 0x000002de60eebf90 code [0x000002de60eec140, 0x000002de60eec2b0]
Event: 1.896 Thread 0x000002de77b97620 1101       3       java.lang.Enum::hashCode (21 bytes)
Event: 1.896 Thread 0x000002de77b97620 nmethod 1101 0x000002de60eec390 code [0x000002de60eec540, 0x000002de60eec6c8]
Event: 1.918 Thread 0x000002de77b97620 1103       3       java.util.concurrent.atomic.AtomicLong::compareAndSet (13 bytes)
Event: 1.919 Thread 0x000002de77b97620 nmethod 1103 0x000002de60eec790 code [0x000002de60eec920, 0x000002de60eeca48]

GC Heap History (0 events):
No events

Dll operation events (2 events):
Event: 0.010 Loaded shared library D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\java.dll
Event: 0.056 Loaded shared library D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\zip.dll

Deoptimization events (18 events):
Event: 0.393 Thread 0x000002de5cda3890 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002de68825314 relative=0x0000000000000074
Event: 0.393 Thread 0x000002de5cda3890 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002de68825314 method=java.lang.CharacterDataLatin1.toLowerCase(I)I @ 16 c2
Event: 0.393 Thread 0x000002de5cda3890 DEOPT PACKING pc=0x000002de68825314 sp=0x000000f9593fae60
Event: 0.393 Thread 0x000002de5cda3890 DEOPT UNPACKING pc=0x000002de682c46a2 sp=0x000000f9593fadf0 mode 2
Event: 0.699 Thread 0x000002de5cda3890 DEOPT PACKING pc=0x000002de60d85707 sp=0x000000f9593fdc20
Event: 0.699 Thread 0x000002de5cda3890 DEOPT UNPACKING pc=0x000002de682c4e42 sp=0x000000f9593fd0b8 mode 0
Event: 0.706 Thread 0x000002de5cda3890 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000002de68828c28 relative=0x0000000000000548
Event: 0.706 Thread 0x000002de5cda3890 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000002de68828c28 method=java.lang.AbstractStringBuilder.inflateIfNeededFor(Ljava/lang/String;)V @ 14 c2
Event: 0.706 Thread 0x000002de5cda3890 DEOPT PACKING pc=0x000002de68828c28 sp=0x000000f9593fdb20
Event: 0.706 Thread 0x000002de5cda3890 DEOPT UNPACKING pc=0x000002de682c46a2 sp=0x000000f9593fd9d0 mode 2
Event: 0.854 Thread 0x000002de5cda3890 DEOPT PACKING pc=0x000002de60d597cc sp=0x000000f9593fc820
Event: 0.854 Thread 0x000002de5cda3890 DEOPT UNPACKING pc=0x000002de682c4e42 sp=0x000000f9593fbc40 mode 0
Event: 0.967 Thread 0x000002de5cda3890 DEOPT PACKING pc=0x000002de60d57fcc sp=0x000000f9593fb480
Event: 0.967 Thread 0x000002de5cda3890 DEOPT UNPACKING pc=0x000002de682c4e42 sp=0x000000f9593fa8e8 mode 0
Event: 1.251 Thread 0x000002de7c88bee0 Uncommon trap: trap_request=0xffffffde fr.pc=0x000002de6885dc5c relative=0x0000000000000edc
Event: 1.251 Thread 0x000002de7c88bee0 Uncommon trap: reason=class_check action=maybe_recompile pc=0x000002de6885dc5c method=java.util.concurrent.ConcurrentHashMap.putVal(Ljava/lang/Object;Ljava/lang/Object;Z)Ljava/lang/Object; @ 159 c2
Event: 1.251 Thread 0x000002de7c88bee0 DEOPT PACKING pc=0x000002de6885dc5c sp=0x000000f95a7fd590
Event: 1.251 Thread 0x000002de7c88bee0 DEOPT UNPACKING pc=0x000002de682c46a2 sp=0x000000f95a7fd510 mode 2

Classes loaded (20 events):
Event: 1.912 Loading class sun/security/ssl/OutputRecord$T13PaddingHolder
Event: 1.912 Loading class sun/security/ssl/OutputRecord$T13PaddingHolder done
Event: 1.912 Loading class sun/security/ssl/Ciphertext
Event: 1.912 Loading class sun/security/ssl/Ciphertext done
Event: 1.912 Loading class sun/security/ssl/PostHandshakeContext
Event: 1.912 Loading class sun/security/ssl/PostHandshakeContext done
Event: 1.912 Loading class javax/net/ssl/SSLEngineResult$Status
Event: 1.913 Loading class javax/net/ssl/SSLEngineResult$Status done
Event: 1.913 Loading class javax/net/ssl/SSLEngineResult
Event: 1.913 Loading class javax/net/ssl/SSLEngineResult done
Event: 1.914 Loading class java/lang/NoSuchFieldError
Event: 1.914 Loading class java/lang/NoSuchFieldError done
Event: 1.918 Loading class sun/nio/ch/IOVecWrapper
Event: 1.918 Loading class sun/nio/ch/IOVecWrapper done
Event: 1.918 Loading class sun/nio/ch/AllocatedNativeObject
Event: 1.918 Loading class sun/nio/ch/NativeObject
Event: 1.918 Loading class sun/nio/ch/NativeObject done
Event: 1.918 Loading class sun/nio/ch/AllocatedNativeObject done
Event: 1.918 Loading class sun/nio/ch/IOVecWrapper$Deallocator
Event: 1.918 Loading class sun/nio/ch/IOVecWrapper$Deallocator done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 0.824 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719d7bcf0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int, int)'> (0x0000000719d7bcf0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.831 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719d83300}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, long)'> (0x0000000719d83300) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.833 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719d89d98}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, float, float)'> (0x0000000719d89d98) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.834 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719d90828}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, double, double)'> (0x0000000719d90828) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.835 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719d94eb0}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000719d94eb0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.843 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719dbea28}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000719dbea28) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.843 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719dc23b8}: 'java.lang.Object java.lang.invoke.DelegatingMethodHandle$Holder.delegate(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x0000000719dc23b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.847 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719dd1928}: 'int java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000719dd1928) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.854 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719a047c8}: 'long java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object)'> (0x0000000719a047c8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.856 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719a0b098}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000719a0b098) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 0.857 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719a0e4b8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000719a0e4b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.033 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719ba0970}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x0000000719ba0970) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.085 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x0000000719999b90}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int)'> (0x0000000719999b90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.086 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x000000071999d4f8}: 'java.lang.Object java.lang.invoke.Invokers$Holder.linkToTargetMethod(java.lang.Object, java.lang.Object, int, java.lang.Object)'> (0x000000071999d4f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.095 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x00000007199cb5a0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x00000007199cb5a0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.133 Thread 0x000002de7c7eb4a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007198994f8}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object, int)'> (0x00000007198994f8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.143 Thread 0x000002de7c7eb4a0 Exception <a 'java/lang/NoSuchMethodError'{0x00000007198d35b0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, java.lang.Object)'> (0x00000007198d35b0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.145 Thread 0x000002de5cda3890 Exception <a 'java/lang/NoSuchMethodError'{0x000000071965b298}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000071965b298) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.217 Thread 0x000002de7c88bee0 Exception <a 'java/lang/NoSuchMethodError'{0x000000071970da50}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeInterface(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000071970da50) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 1.235 Thread 0x000002de7c88bee0 Exception <a 'java/lang/NoSuchMethodError'{0x000000071975cf08}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeVirtual(java.lang.Object, java.lang.Object)'> (0x000000071975cf08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (10 events):
Event: 0.136 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.136 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.148 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.148 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 0.685 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 0.685 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.137 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 1.137 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 1.137 Executing VM operation: Cleanup
Event: 1.137 Executing VM operation: Cleanup done

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (20 events):
Event: 0.076 Thread 0x000002de5cda3890 Thread added: 0x000002de77b94970
Event: 0.076 Thread 0x000002de5cda3890 Thread added: 0x000002de77b953c0
Event: 0.076 Thread 0x000002de5cda3890 Thread added: 0x000002de77b95e10
Event: 0.076 Thread 0x000002de5cda3890 Thread added: 0x000002de77b96b90
Event: 0.076 Thread 0x000002de5cda3890 Thread added: 0x000002de77b97620
Event: 0.103 Thread 0x000002de5cda3890 Thread added: 0x000002de7c10a7c0
Event: 0.106 Thread 0x000002de5cda3890 Thread added: 0x000002de7c10e410
Event: 0.116 Loaded shared library D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\net.dll
Event: 0.123 Loaded shared library D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\nio.dll
Event: 0.132 Loaded shared library D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\zip.dll
Event: 0.259 Loaded shared library D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\jimage.dll
Event: 0.279 Loaded shared library D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\sunmscapi.dll
Event: 1.012 Loaded shared library D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\extnet.dll
Event: 1.026 Thread 0x000002de5cda3890 Thread added: 0x000002de7c7eb4a0
Event: 1.142 Thread 0x000002de7c7eb4a0 Thread added: 0x000002de7c88bee0
Event: 1.253 Thread 0x000002de7c7eb4a0 Thread added: 0x000002de7c894d80
Event: 1.436 Thread 0x000002de77b97620 Thread added: 0x000002de7c851c40
Event: 1.438 Thread 0x000002de77b96b90 Thread added: 0x000002de7c8599f0
Event: 1.835 Thread 0x000002de7c8599f0 Thread exited: 0x000002de7c8599f0
Event: 1.836 Thread 0x000002de7c851c40 Thread exited: 0x000002de7c851c40


Dynamic libraries:
0x00007ff78ada0000 - 0x00007ff78adaa000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\java.exe
0x00007ffda1bf0000 - 0x00007ffda1e04000 	C:\Windows\SYSTEM32\ntdll.dll
0x00007ffda0020000 - 0x00007ffda00e2000 	C:\Windows\System32\KERNEL32.DLL
0x00007ffd9f0f0000 - 0x00007ffd9f48c000 	C:\Windows\System32\KERNELBASE.dll
0x00007ffd9f5a0000 - 0x00007ffd9f6b1000 	C:\Windows\System32\ucrtbase.dll
0x00007ffd92090000 - 0x00007ffd920ab000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\VCRUNTIME140.dll
0x00007ffd808a0000 - 0x00007ffd808b8000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\jli.dll
0x00007ffda13d0000 - 0x00007ffda157b000 	C:\Windows\System32\USER32.dll
0x00007ffd9f950000 - 0x00007ffd9f976000 	C:\Windows\System32\win32u.dll
0x00007ffd8cf90000 - 0x00007ffd8d21e000 	C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.317_none_a9434687c10c9fa2\COMCTL32.dll
0x00007ffd9fdc0000 - 0x00007ffd9fde9000 	C:\Windows\System32\GDI32.dll
0x00007ffda03d0000 - 0x00007ffda0477000 	C:\Windows\System32\msvcrt.dll
0x00007ffd9f830000 - 0x00007ffd9f942000 	C:\Windows\System32\gdi32full.dll
0x00007ffd9f500000 - 0x00007ffd9f59a000 	C:\Windows\System32\msvcp_win.dll
0x00007ffd9fc70000 - 0x00007ffd9fca1000 	C:\Windows\System32\IMM32.DLL
0x00007ffd9bcb0000 - 0x00007ffd9bcbc000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\vcruntime140_1.dll
0x00007ffd40f20000 - 0x00007ffd40fad000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\msvcp140.dll
0x00007ffd15fb0000 - 0x00007ffd16d71000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\server\jvm.dll
0x00007ffda0b40000 - 0x00007ffda0bee000 	C:\Windows\System32\ADVAPI32.dll
0x00007ffda0580000 - 0x00007ffda0624000 	C:\Windows\System32\sechost.dll
0x00007ffda1580000 - 0x00007ffda1695000 	C:\Windows\System32\RPCRT4.dll
0x00007ffd9ff90000 - 0x00007ffda0001000 	C:\Windows\System32\WS2_32.dll
0x00007ffd9ef00000 - 0x00007ffd9ef4d000 	C:\Windows\SYSTEM32\POWRPROF.dll
0x00007ffd983a0000 - 0x00007ffd983d4000 	C:\Windows\SYSTEM32\WINMM.dll
0x00007ffd96d50000 - 0x00007ffd96d5a000 	C:\Windows\SYSTEM32\VERSION.dll
0x00007ffd9eee0000 - 0x00007ffd9eef3000 	C:\Windows\SYSTEM32\UMPDC.dll
0x00007ffd9e0b0000 - 0x00007ffd9e0c8000 	C:\Windows\SYSTEM32\kernel.appcore.dll
0x00007ffd98e50000 - 0x00007ffd98e5a000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\jimage.dll
0x00007ffd9cb40000 - 0x00007ffd9cd6e000 	C:\Windows\SYSTEM32\DBGHELP.DLL
0x00007ffda0750000 - 0x00007ffda0ad9000 	C:\Windows\System32\combase.dll
0x00007ffd9fb80000 - 0x00007ffd9fc57000 	C:\Windows\System32\OLEAUT32.dll
0x00007ffd8d890000 - 0x00007ffd8d8c2000 	C:\Windows\SYSTEM32\dbgcore.DLL
0x00007ffd9f980000 - 0x00007ffd9f9fb000 	C:\Windows\System32\bcryptPrimitives.dll
0x00007ffd92070000 - 0x00007ffd92090000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\java.dll
0x00007ffda0bf0000 - 0x00007ffda13cb000 	C:\Windows\System32\SHELL32.dll
0x00007ffd9d090000 - 0x00007ffd9d945000 	C:\Windows\SYSTEM32\windows.storage.dll
0x00007ffd9cf50000 - 0x00007ffd9d08d000 	C:\Windows\SYSTEM32\wintypes.dll
0x00007ffd9fa00000 - 0x00007ffd9faf1000 	C:\Windows\System32\SHCORE.dll
0x00007ffd9fd60000 - 0x00007ffd9fdbe000 	C:\Windows\System32\shlwapi.dll
0x00007ffd9ef60000 - 0x00007ffd9ef81000 	C:\Windows\SYSTEM32\profapi.dll
0x00007ffd8f6e0000 - 0x00007ffd8f6f8000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\zip.dll
0x00007ffd98df0000 - 0x00007ffd98e00000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\net.dll
0x00007ffd9b5d0000 - 0x00007ffd9b6ff000 	C:\Windows\SYSTEM32\WINHTTP.dll
0x00007ffd9e510000 - 0x00007ffd9e579000 	C:\Windows\system32\mswsock.dll
0x00007ffd8f680000 - 0x00007ffd8f696000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\nio.dll
0x00007ffd8e1c0000 - 0x00007ffd8e1ce000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\sunmscapi.dll
0x00007ffd9f6c0000 - 0x00007ffd9f826000 	C:\Windows\System32\CRYPT32.dll
0x00007ffd9e8e0000 - 0x00007ffd9e90d000 	C:\Windows\SYSTEM32\ncrypt.dll
0x00007ffd9e8a0000 - 0x00007ffd9e8d7000 	C:\Windows\SYSTEM32\NTASN1.dll
0x00007ffd9e750000 - 0x00007ffd9e76b000 	C:\Windows\SYSTEM32\CRYPTSP.dll
0x00007ffd9e020000 - 0x00007ffd9e055000 	C:\Windows\system32\rsaenh.dll
0x00007ffd9e600000 - 0x00007ffd9e628000 	C:\Windows\SYSTEM32\USERENV.dll
0x00007ffd9e910000 - 0x00007ffd9e938000 	C:\Windows\SYSTEM32\bcrypt.dll
0x00007ffd9e770000 - 0x00007ffd9e77c000 	C:\Windows\SYSTEM32\CRYPTBASE.dll
0x00007ffd9dbe0000 - 0x00007ffd9dc0d000 	C:\Windows\SYSTEM32\IPHLPAPI.DLL
0x00007ffda0500000 - 0x00007ffda0509000 	C:\Windows\System32\NSI.dll
0x00007ffd820a0000 - 0x00007ffd820a8000 	C:\Windows\system32\wshunix.dll
0x00007ffd8e1b0000 - 0x00007ffd8e1b9000 	D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\extnet.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin;C:\Windows\SYSTEM32;C:\Windows\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.22621.317_none_a9434687c10c9fa2;D:\Work\IntelliJ IDEA 2024.3.4\jbr\bin\server

VM Arguments:
java_command: git4idea.http.GitAskPassApp Username for 'https://git.rightcloud.com.cn': 
java_class_path (initial): D:/Work/IntelliJ IDEA 2024.3.4/plugins/vcs-git/lib/git4idea-rt.jar;D:/Work/IntelliJ IDEA 2024.3.4/lib/externalProcess-rt.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 3                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 10                                        {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 257949696                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4112515072                                {product} {ergonomic}
   size_t MaxNewSize                               = 2466250752                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4112515072                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
JAVA_HOME=D:\Work\JDK\jdk-11
CLASSPATH=.;D:\Work\JDK\jdk-11\lib;D:\Work\JDK\jdk-11\lib\tools.jar;
PATH=C:/Program Files/Git/mingw64/libexec/git-core;C:/Program Files/Git/mingw64/libexec/git-core;C:\Program Files\Git\mingw64\bin;C:\Program Files\Git\usr\bin;C:\Users\<USER>\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\Git\cmd;D:\Work\JDK\jdk-11\bin;D:\Work\JDK\jdk-11\jre\bin;D:\Work\Erlang OTP\bin;D:\Work\Nodejs\;D:\Work\Maven\apache-maven-3.9.9\bin;C:\Program Files\TortoiseSVN\bin;D:\Work\protoc-30.2-win64\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Work\Microsoft VS Code\bin
USERNAME=qinpeng
DISPLAY=:0.0
LC_ALL=en_US.UTF-8
TERM=xterm-256color
TMPDIR=C:\Users\<USER>\AppData\Local\Temp
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 25 Model 80 Stepping 0, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 21, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 78912K (0% of 16057404K total physical memory with 1488512K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader bootstrap                                                                       : 6501K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 1592K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 16400B

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 22621 (10.0.22621.457)
OS uptime: 41 days 9:20 hours

CPU: total 12 (initial active 12) (12 cores per cpu, 2 threads per core) family 25 model 80 stepping 0 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, rdpid, fsrm, f16c, pku, cet_ss
Processor Information for the first 12 processors :
  Max Mhz: 2301, Current Mhz: 2301, Mhz Limit: 2301

Memory: 4k page, system-wide physical 15681M (1453M free)
TotalPageFile size 29725M (AvailPageFile size 5M)
current process WorkingSet (physical memory assigned to process): 77M, peak: 77M
current process commit charge ("private bytes"): 353M, peak: 354M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+8-b631.39) for windows-amd64 JRE (21.0.6+8-b631.39), built on 2025-02-04 by "builduser" with MS VC++ 16.10 / 16.11 (VS2019)

END.
