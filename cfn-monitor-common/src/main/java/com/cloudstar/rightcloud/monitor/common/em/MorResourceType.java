package com.cloudstar.rightcloud.monitor.common.em;

import com.cloudstar.rightcloud.monitor.common.util.StringUtils;

import lombok.AllArgsConstructor;

/**
 * @author: hjy
 * @date: 2024/01/18 19:09
 */
@AllArgsConstructor
public enum MorResourceType {
    /**
     * 云主机
     */
    ECS("ECS", "云主机", "ECS"),
    /**
     * 弹性IP
     */
    EIP("EIP", "弹性IP", "EIP"),
    /**
     * 云数据库
     */
    RDS("RDS", "云数据库", "RDS")

    ;




    public final String value;
    public final String zhCn;
    public final String enUs;


    public static MorResourceType buildResourceType(String value) {
        if (StringUtils.isNotEmpty(value)) {
            for (MorResourceType type : MorResourceType.values()) {
                if (StringUtils.equals(value, type.value)) {
                    return type;
                }
            }
        }
        return null;
    }

}
