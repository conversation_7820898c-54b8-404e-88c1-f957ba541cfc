package com.cloudstar.rightcloud.monitor.common.constant;

import cn.hutool.core.util.StrUtil;
import com.cloudstar.rightcloud.monitor.common.em.ExporterEnvType;

public class MorDataSourceHttpUrlConstant {

    public static final String CLOUD_TOWER_VLAN = "/api/sks-proxy/api/v1/clusters/{}/proxy/api/v1/namespaces/"
            + "sks-system-monitoring/services/http:prometheus-sks:web/proxy/";

    public static String getUrlPath(String envCode, String envVersion, String resName) {
        if (StrUtil.equals(envCode, ExporterEnvType.CLOUD_TOWER_VLAN.getCode())
                && StrUtil.equals(envVersion, ExporterEnvType.CLOUD_TOWER_VLAN.getVersion())){
            return StrUtil.format(CLOUD_TOWER_VLAN, resName);
        } else if (StrUtil.equals(envCode, ExporterEnvType.CLOUD_TOWER_4_3.getCode())
                && StrUtil.equals(envVersion, ExporterEnvType.CLOUD_TOWER_4_3.getVersion())){
            return StrUtil.format(CLOUD_TOWER_VLAN, resName);
        }
        return "";
    }

}
