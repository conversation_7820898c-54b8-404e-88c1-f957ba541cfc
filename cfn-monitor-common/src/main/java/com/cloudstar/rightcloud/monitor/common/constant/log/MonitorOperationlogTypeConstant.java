package com.cloudstar.rightcloud.monitor.common.constant.log;

/**
 * 操作日志操作类型
 * [actionlog.type.+模块名称+操作]
 *
 * <AUTHOR> hjy
 * @date : 2023/11/16
 */
public class MonitorOperationlogTypeConstant {
    // 性能分析-创建考核规则
    public static final String CREATE_EXAMINE_RULE = "actionlog.type.examineRule.create";
    // 性能分析-修改考核规则
    public static final String UPDATE_EXAMINE_RULE = "actionlog.type.examineRule.update";
    // 性能分析-启用禁用考核规则
    public static final String UPDATE_EXAMINE_RULE_STATUS = "actionlog.type.examineRule.status";
    // 性能分析-删除考核规则
    public static final String DELETE_EXAMINE_RULE = "actionlog.type.examineRule.delete";

    // 性能分析-创建环境性能统计策略
    public static final String CREATE_ENV_POLICY = "actionlog.type.envPolicy.create";
    // 性能分析-修改环境性能统计策略
    public static final String UPDATE_ENV_POLICY = "actionlog.type.envPolicy.update";
    // 性能分析-启用禁用环境性能统计策略
    public static final String UPDATE_ENV_POLICY_STATUS = "actionlog.type.envPolicy.status";
    // 性能分析-删除环境性能统计策略
    public static final String DELETE_ENV_POLICY = "actionlog.type.envPolicy.delete";

    // 性能分析-创建天性能汇总指标
    public static final String CREATE_DAY_METRIC = "actionlog.type.dayMetric.create";
    // 性能分析-修改天性能汇总指标
    public static final String UPDATE_DAY_METRIC = "actionlog.type.dayMetric.update";
    // 性能分析-启用禁用环天性能汇总指标
    public static final String UPDATE_DAY_METRIC_STATUS = "actionlog.type.dayMetric.status";
    // 性能分析-删除天性能汇总指标
    public static final String DELETE_DAY_METRIC = "actionlog.type.dayMetric.delete";

    // 性能分析-创建小时性能汇总指标
    public static final String CREATE_HOUR_METRIC = "actionlog.type.hourMetric.create";
    // 性能分析-修改小时性能汇总指标
    public static final String UPDATE_HOUR_METRIC = "actionlog.type.hourMetric.update";
    // 性能分析-启用禁用小时性能汇总指标
    public static final String UPDATE_HOUR_METRIC_STATUS = "actionlog.type.hourMetric.status";
    // 性能分析-删除小时性能汇总指标
    public static final String DELETE_HOUR_METRIC = "actionlog.type.hourMetric.delete";
    // 性能分析-执行小时汇聚任务
    public static final String EXECUTE_TASK_HOUR_METRIC = "actionlog.type.hourMetric.executeTask";
    // 性能分析-执行小时汇聚总任务
    public static final String EXECUTE_TOTAL_TASK_HOUR_METRIC = "actionlog.type.hourMetric.executeTotalTask";
    // 性能分析-执行天汇聚总任务
    public static final String EXECUTE_TOTAL_TASK_DAY_METRIC = "actionlog.type.dayMetric.executeTotalTask";

    // 性能分析-导出小时汇聚任务日志
    public static final String EXPORT_HOUR_LOG = "actionlog.type.hourLog.export";
    // 性能分析-删除小时汇聚任务日志
    public static final String DELETE_HOUR_LOG = "actionlog.type.hourLog.delete";

    // 性能分析-创建分析汇总定时任务
    public static final String CREATE_ANALYSIS_TASK = "actionlog.type.analysisTask.create";

    // 云环境监控-数据补采
    public static final String RESTOCK_ENV_MONITOR = "actionlog.type.envMonitor.restock";
    // 云环境监控-数据补采
    public static final String PUSH_WEBHOOK_ENV_MONITOR = "actionlog.type.envMonitor.pushWebhook";
    // 云环境监控-接入云环境监控
    public static final String ACCESS_ENV_MONITOR = "actionlog.type.envMonitor.access";
    // 云环境监控-接入云环境监控资源
    public static final String ACCESS_RESOURCE_ENV_MONITOR = "actionlog.type.envMonitor.accessEnv";

    // 告警屏蔽规则-创建告警屏蔽规则
    public static final String CREATE_SHIELD_RULE = "actionlog.type.shieldRule.create";
    // 告警屏蔽规则-修改告警屏蔽规则
    public static final String UPDATE_SHIELD_RULE = "actionlog.type.shieldRule.update";
    // 告警屏蔽规则-启用禁用告警屏蔽规则
    public static final String UPDATE_SHIELD_RULE_STATUS = "actionlog.type.shieldRule.status";
    // 告警屏蔽规则-删除告警屏蔽规则
    public static final String DELETE_SHIELD_RULE = "actionlog.type.shieldRule.delete";

    // 告警数据-处理告警数据
    public static final String PROCESS_ALARM_DATA= "actionlog.type.alarmData.process";
    // 告警数据-导出告警数据
    public static final String EXPORT_ALARM_DATA= "actionlog.type.alarmData.export";
    // 告警数据-删除告警数据
    public static final String DELETE_ALARM_DATA= "actionlog.type.alarmData.delete";

    // 原始告警数据-创建原始告警数据
    public static final String CREATE_ORIGIN_ALARM_DATA= "actionlog.type.originAlarmData.create";

    // 告警通知策略-创建告警通知策略
    public static final String CREATE_NOTIFY_POLICY = "actionlog.type.notifyPolicy.create";
    // 告警通知策略-修改告警通知策略
    public static final String UPDATE_NOTIFY_POLICY = "actionlog.type.notifyPolicy.update";
    // 告警通知策略-启用禁用告警通知策略
    public static final String UPDATE_NOTIFY_POLICY_STATUS = "actionlog.type.notifyPolicy.status";
    // 启用告警通知策略
    public static final String NOTIFY_POLICY_ENABLE = "actionlog.type.notifyPolicy.status.enable";
    // 禁用告警通知策略
    public static final String NOTIFY_POLICY_DISABLE = "actionlog.type.notifyPolicy.status.disable";
    // 告警通知策略-删除告警通知策略
    public static final String DELETE_NOTIFY_POLICY = "actionlog.type.notifyPolicy.delete";

    // 告警通知联系人-创建告警通知联系人
    public static final String CREATE_NOTIFY_CONTACTS = "actionlog.type.notifyContacts.create";
    // 告警通知联系人-修改告警通知联系人
    public static final String UPDATE_NOTIFY_CONTACTS = "actionlog.type.notifyContacts.update";
    // 告警通知联系人-添加已有用户
    public static final String ADD_USER_NOTIFY_CONTACTS = "actionlog.type.notifyContacts.addUser";
    // 告警通知联系人-删除告警通知联系人
    public static final String DELETE_NOTIFY_CONTACTS = "actionlog.type.notifyContacts.delete";


    // 告警通知组-创建告警通知组
    public static final String CREATE_NOTIFY_GROUP = "actionlog.type.notifyGroup.create";
    // 告警通知组-设置告警通知组联系人
    public static final String SET_CONTACTS_NOTIFY_GROUP = "actionlog.type.notifyGroup.setContacts";
    // 设置告警通知对象联系人组联系人
    public static final String SET_CONTACTS_NOTIFY_OBJECT = "actionlog.type.notifyObject.setContacts";
    // 告警通知组-修改告警通知组
    public static final String UPDATE_NOTIFY_GROUP = "actionlog.type.notifyGroup.update";
    // 告警通知组-删除告警通知组
    public static final String DELETE_NOTIFY_GROUP = "actionlog.type.notifyGroup.delete";
    // 告警通知组-移除告警通知组联系人
    public static final String REMOVE_CONTACTS_NOTIFY_GROUP = "actionlog.type.notifyGroup.removeContacts";

    // 告警规则-创建告警规则
    public static final String CREATE_ALARM_RULE = "actionlog.type.alarmRule.create";
    // 告警规则-修改告警规则
    public static final String UPDATE_ALARM_RULE = "actionlog.type.alarmRule.update";
    // 告警规则-启用禁用告警规则
    public static final String UPDATE_ALARM_RULE_STATUS = "actionlog.type.alarmRule.status";
    // 告警规则-启用告警规则
    public static final String ENABLE_ALARM_RULE_STATUS = "actionlog.type.alarmRule.status.enable";
    // 告警规则-禁用告警规则
    public static final String DISABLE_ALARM_RULE_STATUS = "actionlog.type.alarmRule.status.disable";
    // 告警规则-删除告警规则
    public static final String DELETE_ALARM_RULE = "actionlog.type.alarmRule.delete";
    // 告警规则-校验告警规则promql表达式
    public static final String CHECK_PROMQL_ALARM_RULE = "actionlog.type.alarmRule.checkPromql";

    // 原生告警指标分类-创建原生告警指标分类
    public static final String CREATE_METRIC_CATEGORY = "actionlog.type.metricCategory.create";
    // 原生告警指标分类-修改原生告警指标分类
    public static final String UPDATE_METRIC_CATEGORY = "actionlog.type.metricCategory.update";
    // 原生告警指标分类-删除原生告警指标分类
    public static final String DELETE_METRIC_CATEGORY = "actionlog.type.metricCategory.delete";

    // 原生告警指标-创建原生告警指标
    public static final String CREATE_ALARM_METRIC = "actionlog.type.alarmMetric.create";
    // 原生告警指标-修改原生告警指标
    public static final String UPDATE_ALARM_METRIC = "actionlog.type.alarmMetric.update";
    // 原生告警指标-启用禁用原生告警指标
    public static final String UPDATE_ALARM_METRIC_STATUS = "actionlog.type.alarmMetric.status";
    // 告警通知策略-删除原生告警指标
    public static final String DELETE_ALARM_METRIC = "actionlog.type.alarmMetric.delete";
    //设置指标值
    public static final String SET_METRIC_VALUE = "actionlog.type.set.metric.value";
    // 原生告警指标-修改原始告警指标通知策略
    public static final String UPDATE_ALARM_METRIC_POLICY = "actionlog.type.alarmMetric.updatePolicy";
    // 告警通知策略-删除原生指标通知策略
    public static final String DELETE_ALARM_METRIC_POLICY = "actionlog.type.alarmMetric.deletePolicy";

    // 监控采集指标-创建监控采集指标
    public static final String CREATE_COLLECT_METRIC = "actionlog.type.collectMetric.create";
    // 监控采集指标-修改监控采集指标
    public static final String UPDATE_COLLECT_METRIC = "actionlog.type.collectMetric.update";
    // 监控采集指标-启用采集规则采集指标
    public static final String UPDATE_COLLECT_METRIC_STATUS = "actionlog.type.collectMetric.status";

    // 监控采集指标-禁用采集规则采集指标
    public static final String UPDATE_COLLECT_METRIC_STATUS_DISABLE = "actionlog.type.collectMetric.status.disable";

    // 监控采集指标-修改监控采集指标默认展示状态
    public static final String UPDATE_COLLECT_METRIC_DISPLAY = "actionlog.type.collectMetric.updateDisplay";
    // 监控采集指标-删除监控采集指标
    public static final String DELETE_COLLECT_METRIC = "actionlog.type.collectMetric.delete";

    // 采集规则-创建采集规则
    public static final String CREATE_COLLECT_RULE = "actionlog.type.collectRule.create";
    // 采集规则-修改采集规则
    public static final String UPDATE_COLLECT_RULE = "actionlog.type.collectRule.update";
    // 采集规则-启用采集规则
    public static final String UPDATE_COLLECT_RULE_STATUS = "actionlog.type.collectRule.status";
    // 采集规则-禁用采集规则
    public static final String UPDATE_COLLECT_RULE_STATUS_DISABLE = "actionlog.type.collectRule.status.disable";
    // 采集规则-删除采集规则
    public static final String DELETE_COLLECT_RULE = "actionlog.type.collectRule.delete";

    // 采集规则维度-创建采集规则维度
    public static final String CREATE_COLLECT_DIMENSION = "actionlog.type.collectDimension.create";
    // 采集规则配置编辑采集维度
    public static final String UPDATE_COLLECT_DIMENSION = "actionlog.type.collectDimension.update";
    // 采集规则维度-删除采集规则维度
    public static final String DELETE_COLLECT_DIMENSION = "actionlog.type.collectDimension.delete";

    // 采集任务-创建采集任务
    public static final String CREATE_COLLECT_TASK = "actionlog.type.collectTask.create";
    // 采集任务-修改采集任务
    public static final String UPDATE_COLLECT_TASK = "actionlog.type.collectTask.update";
    // 采集任务-启用采集任务
    public static final String UPDATE_COLLECT_TASK_STATUS = "actionlog.type.collectTask.status";
    // 采集任务-禁用采集任务
    public static final String UPDATE_COLLECT_TASK_STATUS_DISABLE = "actionlog.type.collectTask.status.disable";
    // 采集任务-删除采集任务
    public static final String DELETE_COLLECT_TASK = "actionlog.type.collectTask.delete";

    // 创建采集组件
    public static final String CREATE_COLLECT_EXPORTER = "actionlog.type.collectExporter.create";
    // 采集Exporter组件-编辑采集组件配置
    public static final String UPDATE_COLLECT_EXPORTER = "actionlog.type.collectExporter.update";
    // 采集Exporter组件-删除采集Exporter组件
    public static final String DELETE_COLLECT_EXPORTER = "actionlog.type.collectExporter.delete";
    // 删除采集组件
    public static final String COLLECT_ASSEMBLY_CONFIG_DELETE = "actionlog.type.collect.assembly.config.delete";
    // 云资源分析-导出组织维度云资源分析
    public static final String EXPORT_ORG_ANALYSIS= "actionlog.type.orgResAnalysis.export";
    // 云资源分析-导出项目维度云资源分析
    public static final String EXPORT_PROJECT_ANALYSIS= "actionlog.type.projectAnalysis.export";
    // 云资源分析-导出资源维度云资源分析
    public static final String EXPORT_RESOURCE_ANALYSIS= "actionlog.type.resourceAnalysis.export";
    // 云资源分析-导出资源维度云资源分析
    public static final String EXPORT_RES_POOL_ANALYSIS= "actionlog.type.resPoolAnalysis.export";

    /**
     * 导出原始告警明细
     */
    public static final String EXPORT_ALARM_ORIGIN= "actionlog.type.alarm.export";
    /**
     * 查询组织维度云资源分析
     */
    public static final String CLOUD_RESOURCE_ANALYSLS_QUERY_ORG = "actionlog.type.cloud.resource.analysis.query.org";
    /**
     *  组织维度性能统计
     */
    public static final String CLOUD_RESOURCE_ANALYSLS_ORG_PROPERTY_STATISTICS = "actionlog.type.cloud.resource.analysis.org.property.statistics";
    /**
     *  查询项目维度云资源分析
     */
    public static final String CLOUD_RESOURCE_ANALYSLS_PROJECT = "actionlog.type.cloud.resource.analysis.project";
    /**
     *  项目性能统计
     */
    public static final String CLOUD_RESOURCE_ANALYSLS_PRO1JECT_PROPERTY_STATISTICS = "actionlog.type.cloud.resource.analysis.project.property.statistics";
    /**
     *  查询资源维度云资源分析
     */
    public static final String CLOUD_RESOURCE_ANALYSLS_QUERY = "actionlog.type.cloud.resource.analysis.query";
    /**
     *  资源池维度性能统计
     */
    public static final String CLOUD_RESOURCE_ANALYSLS_STATISTICS = "actionlog.type.cloud.resource.analysis.statistics";
    /**
     *  获取隐藏header
     */
    public static final String CLOUD_RESOURCE_ANALYSLS_GET_HEADER = "actionlog.type.cloud.resource.analysis.get.header";
    /**
     *  查询通用指标分页列表
     */
    public static final String COMMON_INDEX_PAGE = "actionlog.type.common.index.page";
    /**
     *  查询通用指标列表
     */
    public static final String COMMON_INDEX_LIST = "actionlog.type.common.index.list";
    /**
     *  查询通用指标详情
     */
    public static final String COMMON_INDEX_INFO = "actionlog.type.common.index.info";
    /**
     *  创建通用指标
     */
    public static final String COMMON_INDEX_CREATE = "actionlog.type.common.index.create";
    /**
     *  修改通用指标
     */
    public static final String COMMON_INDEX_UPDATE = "actionlog.type.common.index.update";
    /**
     *  删除通用指标
     */
    public static final String COMMON_INDEX_DEL = "actionlog.type.common.index.del";
    /**
     *  修改状态
     */
    public static final String COMMON_INDEX_UPDATE_STATUS = "actionlog.type.common.index.update.status";
    /**
     *  根据类型获取数据源
     */
    public static final String DATASOURCE_INTERFACE_LIST_TYPE = "actionlog.type.datasource.interface.list.type";
    /**
     *  根据名称和类型获取数据源
     */
    public static final String DATASOURCE_INTERFACE_NAME_TYPE = "actionlog.type.datasource.interface.name.type";
    /**
     *  根据类型获取默认数据源
     */
    public static final String DATASOURCE_INTERFACE_TYPE = "actionlog.type.datasource.interface.type";
    /**
     *  查询优化建议列表
     */
    public static final String OPTIMIZATION_SUGGESTION_PAGE = "actionlog.type.optimization.suggestion.page";
    /**
     *  查询优化建议汇总
     */
    public static final String OPTIMIZATION_SUGGESTION_SUM = "actionlog.type.optimization.suggestion.sum";
    /**
     *  查询优化建议详情
     */
    public static final String OPTIMIZATION_SUGGESTION_INFO = "actionlog.type.optimization.suggestion.info";
    /**
     *  导出优化建议
     */
    public static final String OPTIMIZATION_SUGGESTION_EXPORT = "actionlog.type.optimization.suggestion.export";
    /**
     *  查询优化策略配置
     */
    public static final String OPTIMAL_POLICY_CONFIG_QUERY = "actionlog.type.optimal.policy.config.query";
    /**
     *  更新优化策略配置
     */
    public static final String OPTIMAL_POLICY_CONFIG_UPDATE = "actionlog.type.optimal.policy.config.update";
    /**
     *  查询优化策略列表（分页）
     */
    public static final String OPTIMAL_POLICY_PAGE = "actionlog.type.optimal.policy.page";
    /**
     *  查询优化策略列表
     */
    public static final String OPTIMAL_POLICY_LIST = "actionlog.type.optimal.policy.list";
    /**
     *  查询优化策略详情
     */
    public static final String OPTIMAL_POLICY_INFO = "actionlog.type.optimal.policy.info";
    /**
     *  创建优化策略
     */
    public static final String OPTIMAL_POLICY_CREATE = "actionlog.type.optimal.policy.create";
    /**
     *  修改优化策略
     */
    public static final String OPTIMAL_POLICY_UPDATE = "actionlog.type.optimal.policy.update";
    /**
     *  启用禁用优化策略
     */
    public static final String OPTIMAL_POLICY_ENABLE = "actionlog.type.optimal.policy.enable";
    /**
     *  删除优化策略
     */
    public static final String OPTIMAL_POLICY_DEL = "actionlog.type.optimal.policy.del";
    /**
     *  重新生成优化建议
     */
    public static final String OPTIMAL_POLICY_REGENARATE = "actionlog.type.optimal.policy.regenerate";
    // 查询环境性能统计策略列表
    public static final String ENV_POLICY_PAGE = "actionlog.type.env.policy.page";
    // 查询环境策略配置统计规则详情信息
    public static final String ENV_POLICY_INFO = "actionlog.type.env.policy.info";
    // 查询天性能汇总指标策略列表
    public static final String DAY_METRIC_PAGE = "actionlog.type.day.metric.page";
    // 查询天性能汇总指标策略详情
    public static final String DAY_METRIC_INFO = "actionlog.type.day.metric.info";
    // 查询分析统计指标配置
    public static final String DAY_METRIC_CONFIG_LIST = "actionlog.type.day.metric.config.list";
    // 查询小时汇总任务执行状态
    public static final String DAY_METRIC_HOUR_TASK = "actionlog.type.day.metric.hour.task";
    // 查询考核规则策略配置列表
    public static final String EXAMINE_RULE_PAGE =  "actionlog.type.examine.rule.page";
    // 查询考核规则策略详情信息
    public static final String EXAMINE_RULE_INFO = "actionlog.type.examine.rule.info";
    // 查询考核项配置
    public static final String EXAMINE_RULE_CONFIG = "actionlog.type.examine.rule.config";
    // 分页查询小时汇聚任务日志
    public static final String HOUR_LOG_PAGE = "actionlog.type.hour.log.page";
    // 分页查询小时性能汇总指标策略
    public static final String HOUR_METRIC_PAGE = "actionlog.type.hour.metric.page";
    // 查询小时性能汇总指标策略详情信息
    public static final String HOUR_METRIC_INFO =  "actionlog.type.hour.metric.info";
    // 查询天汇总任务执行状态
    public static final String HOUR_METRIC_TASK_STATE = "actionlog.type.hour.metric.task.state";
    // 获取云环境
    public static final String ANALYSIS_TASK_GET_CLOUD = "actionlog.type.analysis.task.get.cloud";
    // 获取监控指标列表
    public static final String ANALYSIS_TASK_MONITOR_INDICATOR = "actionlog.type.analysis.task.monitor.indicator";
    // 查询云平台列表
    public static final String ANALYSIS_TASK_GET_CLOUD_LIST =  "actionlog.type.analysis.task.get.cloud.list";
}
