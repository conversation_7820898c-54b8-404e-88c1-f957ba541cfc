package com.cloudstar.rightcloud.monitor.common.properties;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * exporter线程池配置
 *
 * @author: wanglang
 * @date: 2023/8/7 11:22
 */
@ConfigurationProperties(prefix = "spring.monitor.thread")
@Data
@NoArgsConstructor
@Slf4j
@Configuration
public class ThreadFactoryProperties {

    /**
     * 核心线程池数量
     */
    private Integer corePoolSize;

    /**
     * 最大线程数量
     */
    private Integer maximumPoolSize;

    /**
     * 当线程数大于核心数时，这是多余的空闲线程在终止之前等待新任务的最长时间
     */
    private Long keepAliveTime;

    /**
     * 最大队列
     */
    private Integer maxQueueSize;

    /**
     * keepAliveTime参数的时间单位
     */
    private TimeUnit unit;

    public Integer getCorePoolSize() {
        if (Objects.isNull(corePoolSize)) {
            corePoolSize = 2;
        }
        return corePoolSize;
    }

    public Integer getMaximumPoolSize() {
        if (Objects.isNull(maximumPoolSize)) {
            maximumPoolSize = 3;
        }
        return maximumPoolSize;
    }

    public Long getKeepAliveTime() {
        if (Objects.isNull(keepAliveTime)) {
            keepAliveTime = 30L;
        }
        return keepAliveTime;
    }

    public TimeUnit getUnit() {
        if (Objects.isNull(unit)) {
            unit = TimeUnit.SECONDS;
        }
        return unit;
    }

    public Integer getMaxQueueSize() {
        if (Objects.isNull(maxQueueSize)) {
            maxQueueSize = 1000;
        }
        return maxQueueSize;
    }
}
