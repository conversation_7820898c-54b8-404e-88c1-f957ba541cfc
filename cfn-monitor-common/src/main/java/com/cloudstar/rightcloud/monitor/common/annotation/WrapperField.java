package com.cloudstar.rightcloud.monitor.common.annotation;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.cloudstar.rightcloud.monitor.common.em.WrapperType;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 条件
 *
 * @author: wanglang
 * @date: 2023/9/23 10:22
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.FIELD, ElementType.ANNOTATION_TYPE})
public @interface WrapperField {

    /**
     * 字段名（该值可无）
     */
    String value() default "";

    /**
     * 字段类型
     */
    WrapperType type() default WrapperType.FIELD_TYPE;

    /**
     * 是否为数据库表字段
     * <p>
     * 默认 true 存在，false 不存在
     */
    boolean exist() default true;

    /**
     * 更新策略
     */
    FieldStrategy updateStrategy() default FieldStrategy.DEFAULT;
}
