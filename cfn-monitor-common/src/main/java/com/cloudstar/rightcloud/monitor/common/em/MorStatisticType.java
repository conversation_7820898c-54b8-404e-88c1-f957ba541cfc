package com.cloudstar.rightcloud.monitor.common.em;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 统计时间单位
 *
 * @author: wanglang
 * @date: 2023/12/20 20:12
 */
@Getter
@AllArgsConstructor
public enum MorStatisticType {


    /**
     * 云环境
     */
    ENV("env_id", "envId"),

    /**
     * 告警等级
     */
    LEVEL("alarm_level_status", "alarmLevelStatus"),

    /**
     * 资源类型
     */
    RES_TYPE("object_type", "objectType"),


    /**
     * 状态
     */
    STATUS("status", "status"),

    /**
     * 资源id
     */
    RES_ID("object_id", "objectInstanceId");

    private final String sqlFiledName;

    private final String filedName;


}
