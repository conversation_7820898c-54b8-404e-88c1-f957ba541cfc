package com.cloudstar.common.file.storage.service.base;


import com.cloudstar.common.file.storage.bean.enums.MimeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

import java.io.IOException;


/**
 * 基础工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class BaseService {
    
    /**
     * 地址分割符
     */
    public final String separator = "/";
    
    
    /**
     * zip压缩文件后缀
     */
    public final String zipSuffix = ".zip";
    
    
    /**
     * 获取文件contentType
     *
     * @param fileName 文件名
     */
    public String getContentType(String fileName) throws IOException {
        String contentType = MimeTypeEnum.getContentType(fileName);
        if (ObjectUtils.isEmpty(contentType)) {
            throw new IOException("获取文件contentType失败，尝试文件名中加入文件类型(.png/.jpg/.zip...)");
        }
        return contentType;
    }
    
    /**
     * 生成文件名
     *
     * @param fileName 文件名
     * @param suffix   文件后缀
     */
    public String createFileName(String fileName, String suffix) {
        StringBuilder name = new StringBuilder();
        if (!ObjectUtils.isEmpty(fileName)) {
            name.append(fileName);
        }
        if (!ObjectUtils.isEmpty(suffix)) {
            name.append(suffix);
        }
        if (ObjectUtils.isEmpty(name)) {
            return null;
        }
        return name.toString();
    }
    
    
}
