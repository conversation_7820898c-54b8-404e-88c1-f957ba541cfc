package com.cloudstar.common.file.storage.utils;


import com.cloudstar.common.file.storage.bean.vo.Base64Data;

import org.springframework.util.Base64Utils;
import org.springframework.util.ObjectUtils;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class StorageUtil {
    
    public static final String SEPARATOR = "/";

    //文件类型限制
    public static List<String> fileType;

    //用户认证文件类型限制
    public static List<String> authFileType;


    static {
        fileType = Arrays.asList("jpeg", "JPEG", "png", "PNG", "jpg", "JPG", "doc", "DOC", "docx", "DOCX", "pdf", "PDF");
        authFileType = Arrays.asList("jpeg", "JPEG", "png", "PNG", "jpg", "JPG");
    }
    
    /**
     * 生成根目录
     *
     * @param prefix        目录（相应业务简称/英文） example: test/picture
     * @param timeDirectory 是否添加时间目录
     *
     * <AUTHOR>
     */
    public static String createRootDirectory(String prefix, boolean timeDirectory, String dateFormat) {
        final SimpleDateFormat directory = new SimpleDateFormat("yyyyMMdd");
        if (!timeDirectory) {
            return prefix;
        }
        long time = System.currentTimeMillis();
        String path = directory.format(time);
        if (!ObjectUtils.isEmpty(dateFormat)) {
            path = new SimpleDateFormat(dateFormat).format(time);
        }
        if (ObjectUtils.isEmpty(prefix)) {
            return path;
        }
        return prefix + SEPARATOR + path;
    }
    
    /**
     * 生成文件名
     *
     * @param fileName  文件名
     * @param useOrigin 是否使用原名
     *
     * <AUTHOR>
     */
    public static String createFileName(String fileName, boolean useOrigin) {
        String randomStr = UUID.randomUUID().toString().replaceAll("-", "").substring(0, 20);
        if (ObjectUtils.isEmpty(fileName)) {
            return randomStr;
        }
        String[] nameSplit = fileName.split("\\.");
        if (nameSplit.length != 2) {
            return randomStr;
        }
        if (!useOrigin) {
            return randomStr + "." + nameSplit[1];
        }
        return fileName;
    }
    
    
    /**
     * 读取流信息为字符串
     *
     * @param inputStream 文件
     * <AUTHOR>
     */
    public static String readInputStreamToStr(InputStream inputStream) {
        InputStreamReader in = new InputStreamReader(inputStream);
        return new BufferedReader(in).lines().collect(Collectors.joining(System.lineSeparator()));
    }
    
    
    /**
     * 解析base64数据
     *
     * @param base64Data base64数据字符串
     *
     * <AUTHOR>
     */
    public static Base64Data decodeBase64(String base64Data) {
        byte[] bs = null;
        String suffix = "";
        String contentType = "";
        try {
            if (ObjectUtils.isEmpty(base64Data)) {
                throw new Exception("上传失败，上传图片数据为空");
            }
            String[] d = base64Data.split("base64,");
            if (d.length != 2) {
                throw new Exception("上传失败，数据不合法");
            }
            String dataPrix = d[0];
            String data = d[1];
            if ("data:image/jpeg;".equalsIgnoreCase(dataPrix)) {
                suffix = ".jpg";
            } else if ("data:image/x-icon;".equalsIgnoreCase(dataPrix)) {
                suffix = ".ico";
            } else if ("data:image/gif;".equalsIgnoreCase(dataPrix)) {
                suffix = ".gif";
            } else if ("data:image/png;".equalsIgnoreCase(dataPrix)) {
                suffix = ".png";
            } else {
                throw new Exception("上传图片格式不合法");
            }
            contentType = dataPrix.replace("data:", "").replace(";", "");
            //因为BASE64Decoder的jar问题，此处使用spring框架提供的工具包
            bs = Base64Utils.decodeFromString(data);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return Base64Data.builder().suffix(suffix).base64Data(bs).contentType(contentType).build();
    }


    /**
     * 初始化HttpServletResponse<br>
     *
     * <p>任意文件
     *
     * <AUTHOR>
     */
    public static void fillResponse(HttpServletResponse response, String fileName) {
        response.setContentType("application/octet-stream");
        response.addHeader("Content-Disposition",
                           "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
    }
    
}
