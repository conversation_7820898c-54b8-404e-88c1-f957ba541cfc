package com.cloudstar.common.file.storage.config;


import com.cloudstar.common.file.storage.StorageService;
import com.cloudstar.common.file.storage.service.impl.LocalServiceImpl;
import com.cloudstar.common.file.storage.service.impl.MinioServiceImpl;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * oss配置项
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties({StorageConfig.class})
public class OssConfigure {
    
    @Bean
    @ConditionalOnProperty(prefix = "file.storage", name = "active", havingValue = "minio")
    public StorageService minio(StorageConfig storageConfig) {
        return new MinioServiceImpl(storageConfig);
    }
    
    @Bean
    @ConditionalOnProperty(prefix = "file.storage", name = "active", havingValue = "local")
    public StorageService local(StorageConfig storageConfig) {
        return new LocalServiceImpl(storageConfig);
    }
    
    
}
