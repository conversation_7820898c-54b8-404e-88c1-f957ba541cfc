package com.cloudstar.common.file.storage.service.base;


import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.exception.BizError;
import com.cloudstar.common.base.util.ZipUtil;
import com.cloudstar.common.file.storage.bean.vo.StorageResult;
import com.cloudstar.common.file.storage.config.OssConfig;
import com.cloudstar.common.file.storage.utils.StorageUtil;

import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyStore;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.zip.Adler32;
import java.util.zip.CheckedOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSocketFactory;
import javax.net.ssl.TrustManager;
import javax.net.ssl.TrustManagerFactory;
import javax.net.ssl.X509TrustManager;

import cn.hutool.core.io.FileUtil;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.minio.BucketExistsArgs;
import io.minio.GetObjectArgs;
import io.minio.GetObjectResponse;
import io.minio.ListObjectsArgs;
import io.minio.MakeBucketArgs;
import io.minio.MinioClient;
import io.minio.ObjectWriteResponse;
import io.minio.PutObjectArgs;
import io.minio.RemoveObjectArgs;
import io.minio.Result;
import io.minio.SetBucketPolicyArgs;
import io.minio.messages.Item;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;


/**
 * minio，基础方法
 *
 * <AUTHOR>
 */
@SuppressFBWarnings("WEAK_HOSTNAME_VERIFIER")
@Slf4j
public class MinioBaseService extends BaseService {
    
    /**
     * minio公钥key，加密用
     */
    protected final String minioAccessKey = "minio.access.key";
    
    /**
     * minio私钥key，加密用
     */
    protected final String minioSecretKey = "minio.secret.key";
    
    /**
     * minio地址key，加密用
     */
    protected final String minioEndpoint = "minio.endpoint";
    
    /**
     * 取消minio ssl验证
     *
     * <AUTHOR>
     */
    public static OkHttpClient getAuthOkHttpClient() throws Exception {
        //TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance(
        //     TrustManagerFactory.getDefaultAlgorithm());
        TrustManagerFactory trustManagerFactory = TrustManagerFactory.getInstance("SunX509");
        trustManagerFactory.init((KeyStore) null);
        TrustManager[] trustManagers = trustManagerFactory.getTrustManagers();
        if (trustManagers.length != 1 || !(trustManagers[0] instanceof X509TrustManager)) {
            throw new IllegalStateException("Unexpected default trust managers:" + Arrays.toString(trustManagers));
        }
        X509TrustManager trustManager = (X509TrustManager) trustManagers[0];
        SSLContext sslContext = SSLContext.getInstance("TLSv1.2");
        sslContext.init(null, new TrustManager[] {trustManager}, null);
        SSLSocketFactory sslSocketFactory = sslContext.getSocketFactory();
        
        return new OkHttpClient.Builder().sslSocketFactory(sslSocketFactory, trustManager)
                .hostnameVerifier((host, session) -> true).build();
    }
    
    /**
     * 基础上传方法
     *
     * <AUTHOR>
     */
    protected StorageResult saveBaseFile(MinioClient minioClient, OssConfig ossConfig, String bucketName, String path,
            String name, InputStream inputStream, long length, String contentType, boolean useOrigin,
            boolean timeDirectory, String dateFormat) {
        try {
            if (ObjectUtils.isEmpty(contentType)) {
                contentType = getContentType(name);
            }
            //检查桶
            confirmBucket(minioClient, bucketName);
            //生成目录
            path = StorageUtil.createRootDirectory(path, timeDirectory, dateFormat);
            //生成文件名
            name = StorageUtil.createFileName(name, useOrigin);
            ObjectWriteResponse response = minioClient.putObject(
                    PutObjectArgs.builder()
                                 .bucket(bucketName)
                                 .object(path + separator + name)
                                 .stream(inputStream, length, -1)
                                 .contentType(contentType)
                                 .build());
            return getMinioStorageResult(bucketName, ossConfig.getInterceptUrl(), ossConfig.getEndpoint(),
                    response.object(), length, null);
        } catch (Exception e) {
            log.error("minio upload file failed: {}", e.getMessage());
            BizError.e(BizErrorEnum.MSG_1026_UPLOAD_FILE_FAILED);
        } finally {
            IOUtils.closeQuietly(inputStream);
        }
        return null;
    }
    
    
    /**
     * 生成指定桶的策略（舍弃）<br>
     *
     * <p>目前使用 {@link com.cloudstar.common.file.storage.bean.enums.StoragePathEnum}<br>
     *
     * <p>的  initMinioPolicy(String bucketName) 方法
     *
     * @param bucketName 桶
     */
    @Deprecated
    public String createBucketPolicy(String bucketName) {
        return "{\n" + "    \"Statement\": [\n" + "        {\n" + "            \"Action\": [\n"
                + "                \"s3:GetBucketLocation\",\n" + "                \"s3:ListBucket\"\n"
                + "            ],\n" + "            \"Effect\": \"Allow\",\n" + "            \"Principal\": \"*\",\n"
                + "            \"Resource\": \"arn:aws:s3:::" + bucketName + "\"\n" + "        },\n" + "        {\n"
                + "            \"Action\": \"s3:GetObject\",\n" + "            \"Effect\": \"Allow\",\n"
                + "            \"Principal\": \"*\",\n" + "            \"Resource\": \"arn:aws:s3:::" + bucketName
                + "/*\"\n" + "        }\n" + "    ],\n" + "    \"Version\": \"2012-10-17\"\n" + "}\n";
    }
    
    
    /**
     * 检查桶是否存在，不存在则创建
     *
     * @param minioClient minio客户端
     * @param bucketName  桶
     * <AUTHOR>
     */
    protected void confirmBucket(MinioClient minioClient, String bucketName) {
        try {
            boolean exist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            if (!exist) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                //设置桶策略，舍弃
                String policy = createBucketPolicy(bucketName);
                //根据地址前缀定义生成捅策略，（有安全需要的时候打开）
                //String policy = StoragePathEnum.initMinioPolicy(bucketName);
                minioClient.setBucketPolicy(SetBucketPolicyArgs.builder().bucket(bucketName).config(policy).build());
            }
        } catch (Exception e) {
            log.error("minio create bucket failed: {}", e.getMessage());
        }
    }
    
    /**
     * 基础获取文件方法
     *
     * @param minioClient minio客户端
     * @param ossConfig   minio配置信息
     * @param bucketName  桶
     * @param fileUrl     文件目录 + 文件名  example：test/20220316/test.png
     * <AUTHOR>
     */
    protected StorageResult getBaseFile(MinioClient minioClient, OssConfig ossConfig, String bucketName,
            String fileUrl) {
        try {
            //切割路径，确保相对路径
            fileUrl = cutRelativeUrlBase(fileUrl, ossConfig);
            GetObjectResponse response = minioClient.getObject(
                    GetObjectArgs.builder().bucket(bucketName).object(fileUrl).build());
            return getMinioStorageResult(bucketName, ossConfig.getInterceptUrl(), ossConfig.getEndpoint(),
                    response.object(), response.available(), response);
        } catch (Exception e) {
            log.error("minio get file failed: {}", e.getMessage());
            BizError.e(BizErrorEnum.MSG_1024_DOWNLOAD_FILE_FAILED);
        }
        return null;
    }
    
    /**
     * 基础删除文件方法
     *
     * @param minioClient minio客户端
     * @param bucketName  桶
     * @param fileUrl     文件目录 + 文件名  example：test/20220316/test.png
     * <AUTHOR>
     */
    protected void deleteBaseFile(MinioClient minioClient, String bucketName, String fileUrl) {
        try {
            minioClient.removeObject(RemoveObjectArgs.builder().bucket(bucketName).object(fileUrl).build());
        } catch (Exception e) {
            log.error("minio failed delete to file: {}", e.getMessage());
            BizError.e(BizErrorEnum.MSG_1025_DELETE_FILE_FAILED);
        }
    }
    
    /**
     * 基础根据目录获取文件信息方法
     *
     * @param minioClient minio客户端
     * @param ossConfig   配置文件
     * @param bucketName  桶
     * @param directory   文件目录  example: test/20220316/
     * <AUTHOR>
     */
    protected List<StorageResult> getBaseFilesByDirectory(MinioClient minioClient, OssConfig ossConfig,
            String bucketName, String directory) {
        try {
            Iterable<Result<Item>> objects = minioClient.listObjects(
                    ListObjectsArgs.builder().bucket(bucketName).prefix(directory).recursive(false).useApiVersion1(true)
                            .build());
            if (ObjectUtils.isEmpty(objects)) {
                return null;
            }
            List<StorageResult> infos = new ArrayList<>();
            objects.forEach(it -> {
                try {
                    Item item = it.get();
                    //如果是文件夹，则遍历继续拿下面的文件
                    if (ObjectUtils.isEmpty(item.etag())) {
                        List<StorageResult> baseFiles = getBaseFilesByDirectory(minioClient, ossConfig, bucketName,
                                item.objectName());
                        if (!ObjectUtils.isEmpty(baseFiles)) {
                            infos.addAll(baseFiles);
                        }
                        return;
                    }
                    StorageResult baseFile = getBaseFile(minioClient, ossConfig, bucketName, item.objectName());
                    infos.add(baseFile);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
            return infos;
        } catch (Exception e) {
            log.error("minio get file by directory failed: {}", e.getMessage());
            BizError.e(BizErrorEnum.MSG_1024_DOWNLOAD_FILE_FAILED);
        }
        return null;
    }
    
    /**
     * 基础根据目录获取文件并压缩
     *
     * @param minioClient minio客户端
     * @param ossConfig   配置文件
     * @param bucketName  桶
     * @param directory   文件目录  example: test/20220316/
     * <AUTHOR>
     */
    protected InputStream compressedBaseFilesByDirectory(MinioClient minioClient, OssConfig ossConfig,
            String bucketName, String directory) {
        try {
            //获取指定目录文件信息
            List<StorageResult> files = getBaseFilesByDirectory(minioClient, ossConfig, bucketName, directory);
            return compressedFiles(files, directory);
        } catch (Exception e) {
            log.error("minio get file's stream by directory failed or compressed files failed: {}", e.getMessage());
            BizError.e(BizErrorEnum.MSG_1024_DOWNLOAD_FILE_FAILED);
        }
        return null;
    }
    
    
    /**
     * 压缩目标文件夹下所有文件成一个zip
     *
     * @param files     文件
     * @param directory 需压缩的文件目录
     * <AUTHOR>
     */
    protected static InputStream compressedFiles(List<StorageResult> files, String directory) throws Exception {
        //创建临时zip文件
        File zip = File.createTempFile(UUID.randomUUID().toString().replaceAll("-", ""), "zip");
        FileOutputStream out = new FileOutputStream(zip);
        CheckedOutputStream cos = new CheckedOutputStream(out, new Adler32());
        //用于将数据压缩成Zip文件格式
        ZipOutputStream zos = new ZipOutputStream(cos);
        try {
            files.forEach(it -> {
                try {
                    if (ObjectUtils.isEmpty(directory)) {
                        zos.putNextEntry(new ZipEntry(it.getFileName()));
                    } else {
                        String[] split = it.getRelativeUrl().split(directory);
                        zos.putNextEntry(new ZipEntry(split[split.length - 1]));
                    }
                    int count = 0;
                    InputStream in = it.getInputStream();
                    while ((count = in.read()) != -1) {
                        zos.write(count);
                    }
                    it.getInputStream().close();
                    zos.closeEntry();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
            return new FileInputStream(zip);
        } catch (Exception e) {
            log.error("compressed files failed: {}", e.getMessage());
            BizError.e(BizErrorEnum.MSG_1022_COMPRESSION_FAILED);
        } finally {
            IOUtils.closeQuietly(zos);
            IOUtils.closeQuietly(out);
            IOUtils.closeQuietly(cos);
            IOUtils.closeQuietly(zos);
            FileUtil.del(zip);
        }
        return null;
    }
    
    
    /**
     * 切割绝对路径为相对路径 注：切割 nginx拦截路径，bucket, oss地址
     *
     * @param fileUrl   文件相对路径
     * @param ossConfig 配置文件
     * <AUTHOR>
     */
    protected String cutRelativeUrlBase(String fileUrl, OssConfig ossConfig) {
        if (ObjectUtils.isEmpty(fileUrl)) {
            return null;
        }
        //nginx静态资源拦截路径
        String interceptUrl =
                ObjectUtils.isEmpty(ossConfig.getInterceptUrl()) ? "" : ossConfig.getInterceptUrl() + separator;
        //桶路径
        String bucket = ossConfig.getBucketName() + separator;
        //获取minio地址
        String endpoint = ossConfig.getEndpoint() + separator;
        //去除以上两处
        if (!ObjectUtils.isEmpty(interceptUrl) && fileUrl.contains(interceptUrl)) {
            fileUrl = fileUrl.replace(interceptUrl, "");
        }
        if (fileUrl.contains(bucket)) {
            fileUrl = fileUrl.replace(bucket, "");
        }
        if (fileUrl.contains(endpoint)) {
            fileUrl = fileUrl.replace(endpoint, "");
        }
        return fileUrl;
    }
    
    
    /**
     * 构建返回参数 By minio 适用于minio
     */
    public StorageResult getMinioStorageResult(String bucketName, String interceptUrl, String endpoint, String url,
            long size, InputStream inputStream) {
        //对//转换成/
        url = url.replace(separator + separator, separator);
        String[] urlSplit = url.split(separator);
        //获取路径前缀，不包含url,bucket,文件名
        StringBuilder directory = new StringBuilder();
        for (int i = 0; i < urlSplit.length; i++) {
            if (i == urlSplit.length - 1) {
                continue;
            }
            directory.append(urlSplit[i]).append(separator);
        }
        return StorageResult.builder()
                            .fileUrl(endpoint + separator + bucketName + separator + url)
                            .relativeUrl(url)
                            .relativeBucketUrl(bucketName + separator + url)
                            .relativeNginxUrl(
                                    (ObjectUtils.isEmpty(interceptUrl) ? "" : interceptUrl + separator) + bucketName
                                            + separator
                                            + url)
                            .bucketName(bucketName)
                            .directory(directory.toString())
                            .directoryBucket(bucketName + separator + directory)
                            .fileName(urlSplit[urlSplit.length - 1])
                            .size(size)
                            .inputStream(inputStream)
                            .build();
    }
    
    /**
     * 压缩上传基本方法
     *
     * @param map         String:原本文件名，inputStream 文件流
     * @param password    压缩密码
     * @param path        上传目录
     * @param minioClient minio客户端
     * @param ossConfig   oss配置
     */
    protected StorageResult saveCompressBase(Map<String, InputStream> map, String password, String path,
            MinioClient minioClient, OssConfig ossConfig) throws IOException {
        InputStream compress = ZipUtil.compress(map, password, true);
        return saveBaseFile(minioClient, ossConfig, ossConfig.getBucketName(), path, zipSuffix, compress,
                compress.available(), null, false, true, null);
    }
    
}
