package com.cloudstar.common.file.storage.utils;


import com.cloudstar.common.file.storage.bean.enums.StoragePathEnum;
import com.cloudstar.common.file.storage.bean.vo.MinioPolicyParam;

import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;


/**
 * minio策略生成工具类
 *
 * <AUTHOR>
 */
public class PolicyUtil {

    private static final String GET_OBJECT = "s3:GetObject";
    private static final String LIST_BUCKET = "s3:ListBucket";
    private static final String PUT_OBJECT = "s3:PutObject";
    private static final String GET_BUCKET_LOCATION = "s3:GetBucketLocation";
    private static final String DELETE_OBJECT = "s3:DeleteObject";
    private static final String ABORT_MULTIPART_UPLOAD = "s3:AbortMultipartUpload";
    private static final String LIST_BUCKET_MULTIPART_UPLOADS = "s3:ListBucketMultipartUploads";
    private static final String LIST_MULTIPART_UPLOAD_PARTS = "s3:ListMultipartUploadParts";

    /**
     * 基本策略
     */
    private static final List<String> BASE_ACTION = Arrays.asList(LIST_BUCKET_MULTIPART_UPLOADS, GET_BUCKET_LOCATION);

    /**
     * 只写策略
     */
    private static final List<String> WRITE_ACTION = Arrays.asList(ABORT_MULTIPART_UPLOAD, DELETE_OBJECT, PUT_OBJECT,
                                                                   LIST_MULTIPART_UPLOAD_PARTS);

    /**
     * 可读可写和只写的读取bucket策略
     */
    private static final List<String> BUCKET_ACTION = Collections.singletonList(LIST_BUCKET);

    /**
     * 可读可写策略
     */
    private static final List<String> READ_AND_WRITE_ACTION = Arrays.asList(GET_OBJECT, LIST_MULTIPART_UPLOAD_PARTS,
                                                                            PUT_OBJECT, ABORT_MULTIPART_UPLOAD,
                                                                            DELETE_OBJECT);

    /**
     * 只读策略
     */
    private static final List<String> READ_ACTION = Collections.singletonList(GET_OBJECT);


    /**
     * Test
     */
    public static void main(String[] args) {
        String young = initMinioPolicy(MinioPolicyParam.builder()
                                                       .bucketName("test")
                                                       .readAndWrite(Arrays.asList("1", "2", "3"))
                                                       .readOnly(Arrays.asList("4", "5"))
                                                       .writeOnly(Arrays.asList("6", "7"))
                                                       .build());
        System.out.println(young);
        String policy = StoragePathEnum.initMinioPolicy("test");
        System.out.println(policy);
    }


    /**
     * 生成策略json<br>
     *
     * <p>注：待用，如果有安全顾虑会将所有需要私有化地址全部私有，前端则无法访问，只能通过后端去获取</p>
     *
     * <AUTHOR>
     */
    public static String initMinioPolicy(MinioPolicyParam param) {
        if (ObjectUtils.isEmpty(param.getBucketName())) {
            return null;
        }
        List<MinioStatement> minioStatements = new ArrayList<>();
        //基本
        minioStatements.add(MinioStatement.builder()
                                          .action(BASE_ACTION)
                                          .resource(createResource(null, BASE_ACTION, param.getBucketName()))
                                          .build());

        //只写
        if (!ObjectUtils.isEmpty(param.getWriteOnly())) {
            minioStatements.add(MinioStatement.builder()
                                              .action(WRITE_ACTION)
                                              .resource(createResource(param.getWriteOnly(), WRITE_ACTION,
                                                                       param.getBucketName()))
                                              .build());
        }

        //可读可写和可写的读取bucket
        if (!ObjectUtils.isEmpty(param.getReadAndWrite()) || !ObjectUtils.isEmpty(param.getReadOnly())) {
            List<String> listBucketPrefix = new ArrayList<>();
            if (!ObjectUtils.isEmpty(param.getReadAndWrite())) {
                listBucketPrefix.addAll(new ArrayList<>(param.getReadAndWrite()));
            }
            if (!ObjectUtils.isEmpty(param.getReadOnly())) {
                listBucketPrefix.addAll(new ArrayList<>(param.getReadOnly()));
            }
            minioStatements.add(MinioStatement.builder()
                                              .action(BUCKET_ACTION)
                                              .resource(createResource(listBucketPrefix, BUCKET_ACTION,
                                                                       param.getBucketName()))
                                              .condition(createCondition(listBucketPrefix))
                                              .build());

        }

        //可读可写
        if (!ObjectUtils.isEmpty(param.getReadAndWrite())) {
            minioStatements.add(MinioStatement.builder()
                                              .action(READ_AND_WRITE_ACTION)
                                              .resource(createResource(param.getReadAndWrite(), READ_AND_WRITE_ACTION,
                                                                       param.getBucketName()))
                                              .build());
        }

        //只读
        if (!ObjectUtils.isEmpty(param.getReadOnly())) {
            minioStatements.add(MinioStatement.builder()
                                              .action(READ_ACTION)
                                              .resource(createResource(param.getReadOnly(), READ_ACTION,
                                                                       param.getBucketName()))
                                              .build());
        }

        //策略
        MinioPolicy policy = MinioPolicy.builder().statement(minioStatements).build();
        //生成策略json
        return JSONUtil.toJsonStr(policy, JSONConfig.create().setIgnoreCase(false).setIgnoreNullValue(true));
    }

    @Data
    @Builder
    static class MinioPolicy {

        @Default
        @Alias("Version")
        private String version = "2012-10-17";

        @Alias("Statement")
        private List<MinioStatement> statement;

    }


    @Data
    @Builder
    static class MinioStatement {

        @Default
        @Alias("Effect")
        private String effect = "Allow";

        @Default
        @Alias("Principal")
        private Map<String, List<String>> principal = MapUtil.of("AWS", Collections.singletonList("*"));

        @Alias("Action")
        private List<String> action;

        @Alias("Resource")
        private List<String> resource;

        @Alias("Condition")
        private Map<String, Object> condition;
    }

    private static List<String> createResource(List<String> prefix, List<String> policies, String bucketName) {
        if (policies.stream().allMatch(v -> v.contains("Bucket"))) {
            return Collections.singletonList("arn:aws:s3:::" + bucketName);
        }
        return prefix.stream().map(v -> {
            return "arn:aws:s3:::" + bucketName + "/" + v + "*";
        }).collect(Collectors.toList());
    }

    private static Map<String, Object> createCondition(List<String> prefix) {
        return MapUtil.of("StringEquals", MapUtil.of("s3:prefix", prefix));
    }


}
