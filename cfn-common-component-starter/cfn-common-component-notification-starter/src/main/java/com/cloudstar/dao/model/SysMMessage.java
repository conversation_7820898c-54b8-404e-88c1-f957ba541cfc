package com.cloudstar.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

import lombok.Builder;
import lombok.Data;

/**
 * 系统消息表
 * @TableName sys_m_message
 */
@TableName(value = "sys_m_message")
@Data
@Builder
public class SysMMessage implements Serializable {
    /**
     * 消息SID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long msgSid;

    /**
     * 消息标题
     */
    private String msgTitle;

    /**
     * 消息类型;以逗号分隔,mail:邮件,sms:短信,默认站内信,station
     */
    private String msgType;

    /**
     * 发件人
     */
    private String fromUser;

    /**
     * 收件人;manager:管理员,user:租户
     */
    private String toUser;

    /**
     * 消息内容
     */
    private String msgContent;

    /**
     * 已读/未读
     */
    private Boolean readFlag;

    /**
     * 消息模板id;success:发送成功，fail:发送失败
     */
    private String templateId;

    /**
     * 收件人sid
     */
    private Long toUserId;

    /**
     * 乐观锁
     */
    private String version;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;

    /**
     * 逻辑删除
     */
    private Boolean isDeleted;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}