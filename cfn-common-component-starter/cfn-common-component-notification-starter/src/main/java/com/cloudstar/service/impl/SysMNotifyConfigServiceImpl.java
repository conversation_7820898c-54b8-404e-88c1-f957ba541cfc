package com.cloudstar.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.dao.model.SysMNotifyConfig;
import com.cloudstar.service.SysMNotifyConfigService;
import com.cloudstar.dao.mapper.SysMNotifyConfigMapper;
import org.springframework.stereotype.Service;

/**
 * 消息通知配置
* <AUTHOR>
* @description 针对表【sys_m_notify_config(系统消息通知配置表;通知消息配置表)】的数据库操作Service实现
* @createDate 2022-09-28 16:48:40
*/
@Service
public class SysMNotifyConfigServiceImpl extends ServiceImpl<SysMNotifyConfigMapper, SysMNotifyConfig> implements SysMNotifyConfigService {

}




