<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.AttachmentMapper">

    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.Attachment">
            <id property="attachmentSid" column="attachment_sid" jdbcType="BIGINT"/>
            <result property="attachmentName" column="attachment_name" jdbcType="VARCHAR"/>
            <result property="attachmentUrl" column="attachment_url" jdbcType="VARCHAR"/>
            <result property="attachmentLocation" column="attachment_location" jdbcType="VARCHAR"/>
            <result property="attachmentType" column="attachment_type" jdbcType="VARCHAR"/>
            <result property="attachmentDesc" column="attachment_desc" jdbcType="VARCHAR"/>
            <result property="originalName" column="original_name" jdbcType="VARCHAR"/>
            <result property="extName" column="ext_name" jdbcType="VARCHAR"/>
            <result property="attachmentSize" column="attachment_size" jdbcType="BIGINT"/>
            <result property="uploadDate" column="upload_date" jdbcType="TIMESTAMP"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        attachment_sid,attachment_name,attachment_url,
        attachment_location,attachment_type,attachment_desc,
        original_name,ext_name,attachment_size,
        upload_date,created_by,created_dt,
        updated_by,updated_dt,version
    </sql>
</mapper>
