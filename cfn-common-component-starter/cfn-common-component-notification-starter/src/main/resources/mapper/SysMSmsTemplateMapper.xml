<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.SysMSmsTemplateMapper">
    
    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.SysMSmsTemplate">
        <id property="sid" column="sid" jdbcType="BIGINT"/>
        <result property="smsName" column="sms_name" jdbcType="VARCHAR"/>
        <result property="smsTemplateId" column="sms_template_id" jdbcType="VARCHAR"/>
        <result property="smsContent" column="sms_content" jdbcType="VARCHAR"/>
        <result property="smsPlatform" column="sms_platform" jdbcType="VARCHAR"/>
        <result property="smsPlatformCode" column="sms_platform_code" jdbcType="VARCHAR"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        sid,sms_name,sms_template_id,
        sms_content,sms_platform,sms_platform_code,
        version,created_by,created_dt,
        updated_by,updated_dt
    </sql>
</mapper>
