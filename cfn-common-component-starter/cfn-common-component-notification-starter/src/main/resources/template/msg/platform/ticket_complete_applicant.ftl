<#if isTitle == "true" >[${serviceType}]处理通知</#if>
<#if isTitle != "true" >
    return h('div', {class: 'notification-wrap'}, [
    h('p', { class: '' }, [
    h ('span', {class: ''},  '您的[${serviceType}]工单，工单编号：${ticketNo},已经分配给[${auditUserName}]处理'),
    h ('a', {class: 'a-label', href:'/workManage/workOrderManage/detail/${id}'}, '点击查看详情')
    ]),
    h('p', { class: 'split-line' }, [
    ]),
    h('p', { class: '' }, [
    h ('span', {class: 'margin-right-20'},  '工单标题：${ticketTitle}'),
    h ('span', {class: 'margin-right-20'}, '提交人：${userName}'),
    h ('span', {class: 'margin-left-20'}, '提交时间：${createdTime}')
    ]),
    ])
</#if>