package com.cloudstar.service.sysmcode.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cloudstar.dao.mapper.SysMCodeMapper;
import com.cloudstar.dao.model.SysMCode;
import com.cloudstar.service.sysmcode.SysMCodeService;

import org.springframework.stereotype.Service;

import java.util.List;

import lombok.RequiredArgsConstructor;

/**
 * sys mcode服务impl
 *
 * <AUTHOR>
 * @date 2022/08/05
 * @since 2022-07-22
 */
@Service
@RequiredArgsConstructor
public class SysMCodeServiceImpl extends ServiceImpl<SysMCodeMapper, SysMCode> implements SysMCodeService {
    
    private static final String CODE_CATEGORY = "code_category";
    
    private static final String CODE_STATUS = "enabled";
    
    private static final int STATUS_TRUE = 1;
    
    /**
     * 得到sys mcode代码
     *
     * @param code 代码
     * @return {@link List}<{@link SysMCode}>
     */
    @Override
    public List<SysMCode> getSysMCodeByCode(String code) {
        return baseMapper.selectList(new QueryWrapper<SysMCode>().eq(CODE_CATEGORY, code).eq(CODE_STATUS, STATUS_TRUE));
    }
}
