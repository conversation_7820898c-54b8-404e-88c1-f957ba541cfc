package com.cloudstar.service.sysmcode.impl;

import com.cloudstar.service.sysmcode.MCodeExecuteService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.PostConstruct;

import lombok.RequiredArgsConstructor;

/**
 * mcode上下文
 *
 * <AUTHOR>
 * @date 2022/08/05
 */
@Service
@RequiredArgsConstructor
public class MCodeContext {
    
    Map<String, MCodeExecuteService> mCodeMap = new ConcurrentHashMap<>();
    
    /**
     * 注入spring容器对象
     */
    @Autowired
    ApplicationContext applicationContext;
    
    @PostConstruct
    private void init() {
        Map<String, MCodeExecuteService> beanMap = applicationContext.getBeansOfType(MCodeExecuteService.class);
        beanMap.forEach((key, value) -> mCodeMap.put(value.getSysMCode(), value));
    }
    
    /**
     * 执行
     *
     * @param type 类型
     */
    public void execute(String type) {
        mCodeMap.get(type).execute();
    }
    
}
