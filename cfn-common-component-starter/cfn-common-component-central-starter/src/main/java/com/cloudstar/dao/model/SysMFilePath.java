package com.cloudstar.dao.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cloudstar.common.encryptdata.annotation.EncryptDecryptClass;
import com.cloudstar.common.encryptdata.annotation.EncryptDecryptField;

import java.io.Serializable;
import java.util.Date;

import lombok.Builder;
import lombok.Data;

/**
 * 文件上传
 *
 * <AUTHOR>
 * @date 2022-08-12 10:02
 */
@TableName("sys_m_file_path")
@Data
@Builder
@EncryptDecryptClass
public class SysMFilePath implements Serializable {

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 文件编号
     */
    private String fileNum;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 组织ID
     */
    private Long orgSid;

    /**
     * 用户ID
     */
    private Long userSid;

    /**
     * 用户类型
     */
    private String userType;

    /**
     * 操作类型;操作类型（工单文件,实名认证文件）
     */
    private String operationType;

    /**
     * 操作类型对应ID;操作类型对应ID（例：工单交流ID）
     */
    private Long operationId;

    /**
     * 文件路径
     */
    @EncryptDecryptField
    private String filePath;

    /**
     * 描述
     */
    private String remark;

    /**
     * 表示顺序
     */
    private Integer sortOrder;

    /**
     * 压缩解压密码;加密存储
     */
    @EncryptDecryptField
    private String compressPassword;

    /**
     * 乐观锁
     */
    private String version;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private Date createdDt;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private Date updatedDt;
}
