package com.cloudstar.common.component.elasticsearch.config;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.ssl.SSLContextBuilder;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.config.AbstractElasticsearchConfiguration;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;

import java.security.cert.X509Certificate;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;

/**
 * es config
 *
 * <AUTHOR>
 * @date 2025/3/5 9:56
 */
@Configuration
public class ElasticsearchConfig extends AbstractElasticsearchConfiguration {

    @Value("${spring.elasticsearch.uris}")
    private String uris;

    @Value("${spring.elasticsearch.username}")
    private String username;

    @Value("${spring.elasticsearch.password}")
    private String password;

    @Value("${spring.elasticsearch.connection-timeout:5000}")
    private int connectionTimeout;

    @Value("${spring.elasticsearch.socket-timeout:30000}")
    private int socketTimeout;

    @Bean
    public RestHighLevelClient elasticsearchClient() {
        // 配置 ES 服务器地址
        // 解析 uris 为 HttpHost 数组
        String[] uriArray = uris.split(",");
        HttpHost[] httpHosts = new HttpHost[uriArray.length];
        for (int i = 0; i < uriArray.length; i++) {
            String uri = uriArray[i].trim();
            String[] parts = uri.split(":");
            String scheme = parts[0];
            String host = parts[1].replace("//", "");
            int port = Integer.parseInt(parts[2]);
            httpHosts[i] = new HttpHost(host, port, scheme);
        }
        // 创建一个凭证提供者（如果需要认证）
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(username, password));
        // 禁用主机名验证
        HostnameVerifier allHostsValid = new NoopHostnameVerifier();

        // 创建 RestHighLevelClient 实例
        return new RestHighLevelClient(
                RestClient.builder(httpHosts)
                          .setHttpClientConfigCallback(httpClientBuilder -> httpClientBuilder
                                  .setSSLContext(createSslContext())
                                  .setDefaultCredentialsProvider(credentialsProvider)
                                  .setSSLHostnameVerifier(allHostsValid)
                          )
                          .setRequestConfigCallback(requestConfigBuilder -> {
                              requestConfigBuilder.setConnectTimeout(connectionTimeout);
                              requestConfigBuilder.setSocketTimeout(socketTimeout);
                              return requestConfigBuilder;
                          })
        );
    }


    /**
     * 创建一个忽略证书验证的 SSLContext
     */
    private SSLContext createSslContext() {
        try {
            SSLContext sslContext = SSLContextBuilder.create()
                                                     .loadTrustMaterial((X509Certificate[] chain, String authType) -> true) // 信任所有证书
                                                     .build();
            return sslContext;
        } catch (Exception e) {
            throw new RuntimeException("Failed to create SSL context", e);
        }
    }

    @Bean
    public ElasticsearchRestTemplate elasticsearchRestTemplate() {
        return new ElasticsearchRestTemplate(elasticsearchClient());
    }
}

