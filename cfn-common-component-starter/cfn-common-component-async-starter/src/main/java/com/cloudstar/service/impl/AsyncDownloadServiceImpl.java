package com.cloudstar.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cloudstar.AsyncDownloadService;
import com.cloudstar.AsyncTaskService;
import com.cloudstar.NotificationService;
import com.cloudstar.bean.constants.MailTemplateConstants;
import com.cloudstar.bean.dto.BizDownloadDto;
import com.cloudstar.bean.enums.DownloadStatus;
import com.cloudstar.bean.enums.MessageType;
import com.cloudstar.bean.pojo.AsyncFileInfoParam;
import com.cloudstar.bean.pojo.AsyncInfoParam;
import com.cloudstar.bean.pojo.NotificationParam;
import com.cloudstar.bean.req.BizDownloadReq;
import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.exception.BizError;
import com.cloudstar.common.base.util.excel.ExcelUtil;
import com.cloudstar.common.file.storage.StorageService;
import com.cloudstar.common.file.storage.bean.enums.StoragePathEnum;
import com.cloudstar.common.file.storage.bean.vo.StorageResult;
import com.cloudstar.common.util.page.PageResult;
import com.cloudstar.common.util.wrapper.SpecWrapperUtil;
import com.cloudstar.dao.mapper.BizDownloadMapper;
import com.cloudstar.dao.model.BizDownload;

import org.apache.commons.compress.utils.IOUtils;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapBuilder;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 异步下载
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AsyncDownloadServiceImpl implements AsyncDownloadService, DisposableBean {

    private final StorageService storageService;

    private final NotificationService notificationService;

    private final BizDownloadMapper bizDownloadMapper;

    private final SimpleDateFormat dataFormat = new SimpleDateFormat("yyyyMMddHHmmssSSS");
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            5,
            5,
            1000,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(300),
            Executors.defaultThreadFactory(),
            new ThreadPoolExecutor.AbortPolicy());

    private String generateNumber() {
        synchronized (dataFormat) {
            return "BAT" + dataFormat.format(System.currentTimeMillis());
        }
    }

    @Override
    public void asyncTask(AsyncTaskService asyncTaskService) {
        //检查参数
        checkParam(asyncTaskService);
        AsyncInfoParam info = asyncTaskService.info();
        AsyncFileInfoParam fileInfo = asyncTaskService.fileInfo();
        BizDownload bizDownload = BizDownload.builder()
                .downloadNum(generateNumber())
                .userId(info.getUserId())
                .userType(info.getUserType())
                .orgSid(info.getOrgSid())
                .operationType(info.getOperationType())
                .fileName(getFileName(fileInfo.getFileName()))
                .compressPassword(fileInfo.getCompressPassword())
                .status(DownloadStatus.DOWNLOADING)
                .createdBy(info.getUsername())
                .createdDt(new Date()).updatedDt(new Date()).build();
        bizDownloadMapper.insert(bizDownload);
        //异步执行导出任务
        asyncDoTask(bizDownload.getDownloadId(), asyncTaskService);
    }


    /**
     * 获取（重构）文件名
     */
    private String getFileName(String fileName) {
        if (ObjectUtils.isEmpty(fileName)) {
            return fileName;
        }
        synchronized (dataFormat) {
            return dataFormat.format(System.currentTimeMillis())
                + FileUtil.getPrefix(fileName)
                + ExcelUtil.XLSX;
        }
    }

    /**
     * 检查必要参数是否为空
     */
    private void checkParam(AsyncTaskService asyncTaskService) {
        AsyncInfoParam info = asyncTaskService.info();
        if (ObjectUtils.isEmpty(info)) {
            log.error("async download AsyncInfoParam cannot be empty");
            BizError.e(BizErrorEnum.MSG_1017_PARAMETER_ERROR);
        }
        if (ObjectUtils.isEmpty(info.getOperationType())) {
            log.error("async download AsyncInfoParam's operationType cannot be empty");
            BizError.e(BizErrorEnum.MSG_1017_PARAMETER_ERROR);
        }
        if (ObjectUtils.isEmpty(info.getUserId())) {
            log.error("async download AsyncInfoParam's userSid cannot be empty");
            BizError.e(BizErrorEnum.MSG_1017_PARAMETER_ERROR);
        }
        AsyncFileInfoParam fileInfo = asyncTaskService.fileInfo();
        if (ObjectUtils.isEmpty(fileInfo)) {
            log.error("async download asyncFileInfoParam cannot be empty");
            BizError.e(BizErrorEnum.MSG_1017_PARAMETER_ERROR);
        }
        if (ObjectUtils.isEmpty(fileInfo.getFileName())) {
            log.error("async download asyncFileInfoParam's fileName cannot be empty");
            BizError.e(BizErrorEnum.MSG_1017_PARAMETER_ERROR);
        }
        if (ObjectUtils.isEmpty(fileInfo.getCompressPassword())) {
            log.error("async download asyncFileInfoParam's compressPassword cannot be empty");
            BizError.e(BizErrorEnum.MSG_1017_PARAMETER_ERROR);
        }
    }


    /**
     * 异步执行导出任务
     */
    private void asyncDoTask(Long downloadId, AsyncTaskService asyncTaskService) {
        Runnable thread = () -> {
            BizDownload bizDownload = bizDownloadMapper.selectById(downloadId);
            if (ObjectUtils.isEmpty(bizDownload)) {
                return;
            }
            InputStream in = null;
            try {
                //执行操作返回文件地址
                in = asyncTaskService.doTaskService();
                if (ObjectUtils.isEmpty(in)) {
                    //更新失败信息
                    updateFailed(bizDownload, bizDownload.getOperationType().getName() + "数据为空");
                    return;
                }
                //存入
                StorageResult result = storageService.saveCompress(in, bizDownload.getFileName(),
                        bizDownload.getCompressPassword(), StoragePathEnum.EXCEL.getPrefix());
                bizDownload.setDownloadPath(result.getRelativeNginxUrl());
                bizDownload.setStatus(DownloadStatus.SUCCESS);
                bizDownload.setUpdatedDt(new Date());
                //执行成功，修改数据
                bizDownloadMapper.updateById(bizDownload);
            } catch (Exception e) {
                //更新失败信息
                updateFailed(bizDownload, "下载异常，异常原因：" + e.getMessage());
            } finally {
                IOUtils.closeQuietly(in);
            }
        };
        executor.execute(thread);
    }

    /**
     * 更新失败信息数据
     */
    public void updateFailed(BizDownload bizDownload, String errorMsg) {
        bizDownload.setStatus(DownloadStatus.FAILED);
        bizDownload.setRemark(errorMsg);
        bizDownloadMapper.updateById(bizDownload);
    }


    /**
     * 发送邮件
     *  @param email 接收邮箱
     * @param bizDownloadId 文件id
     */
    public void sendMessage(String email, Long bizDownloadId) {
        BizDownload bizDownload = bizDownloadMapper.selectById(bizDownloadId);
        //发送邮箱地址
        Map<String, String> sendAddress = MapBuilder.<String, String>create()
                .put("email", email)
                .build();
        //发送密码 邮件
        Map<String, String> content = MapBuilder.<String, String>create()
                .put("fileName", bizDownload.getFileName())
                .put("password", bizDownload.getCompressPassword())
                .build();
        //发送
        notificationService.sendNotification(NotificationParam.builder()
                .messageType(Collections.singletonList(MessageType.EMAIL))
                .isCC(false)
                .sendUserSid(bizDownload.getUserId())
                .sendAddress(sendAddress)
                .messageContent(content)
                .messageTemplateId(MailTemplateConstants.SEND_ZIP_COMPRESS_CIPHER)
                .build());
    }

    /**
     * 查文件列表
     */
    @Override
    public PageResult<BizDownloadDto> selectByQuery(BizDownloadReq bizDownloadReq) {
        Page<BizDownload> bizDownloads = bizDownloadMapper.selectPage(bizDownloadReq.pageRequest(false, BizDownload::getCreatedDt),
                SpecWrapperUtil.filter(bizDownloadReq, bizDownloadReq));
        return PageResult.of(bizDownloads, BizDownloadDto.class);
    }

    /**
     * 获取文件详情
     */
    @Override
    public BizDownloadDto selectById(Long downloadId) {
        BizDownload bizDownload = bizDownloadMapper.selectById(downloadId);
        if (ObjectUtils.isEmpty(bizDownload)) {
            BizError.notFound();
        }
        return BeanUtil.copyProperties(bizDownload, BizDownloadDto.class);
    }



    /**
     * 下载文件
     */
    @Override
    public void downloadById(Long downloadId, HttpServletResponse response, String email) {
        BizDownloadDto download = selectById(downloadId);
        ExcelUtil.fillResponseZip(response, download.getFileName(), false);
        StorageResult storageResult = storageService.getFile(download.getDownloadPath());
        try {
            IOUtils.copy(storageResult.getInputStream(), response.getOutputStream());
            //发送消息
            sendMessage(email, downloadId);
        } catch (Exception e) {
            log.error("download file failed:{}", e.getMessage());
            BizError.e(BizErrorEnum.MSG_1024_DOWNLOAD_FILE_FAILED);
        } finally {
            IOUtils.closeQuietly(storageResult.getInputStream());
        }
    }


    /**
     * 再次发送邮件
     */
    @Override
    public void sendCompressPassword(Long downloadId, String email, Long userId) {
        BizDownloadDto bizDownloadDto = selectById(downloadId);
        //发送人id必须和记录里的用户id相同
        if (bizDownloadDto.getUserId().equals(userId)) {
            //发送邮件
            sendMessage(email, downloadId);
        }
    }

    @Override
    public void destroy() {
        executor.shutdown();
    }
}
