<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cloudstar.dao.mapper.SysMConfigMapper">
    
    <resultMap id="BaseResultMap" type="com.cloudstar.dao.model.SysMConfig">
        <id property="configSid" column="config_sid" jdbcType="BIGINT"/>
        <result property="configType" column="config_type" jdbcType="VARCHAR"/>
        <result property="configName" column="config_name" jdbcType="VARCHAR"/>
        <result property="configKey" column="config_key" jdbcType="VARCHAR"/>
        <result property="configValue" column="config_value" jdbcType="VARCHAR"/>
        <result property="dataType" column="data_type" jdbcType="VARCHAR"/>
        <result property="displayType" column="display_type" jdbcType="VARCHAR"/>
        <result property="unit" column="unit" jdbcType="VARCHAR"/>
        <result property="valueDomain" column="value_domain" jdbcType="VARCHAR"/>
        <result property="valueIncrement" column="value_increment" jdbcType="VARCHAR"/>
        <result property="sortRank" column="sort_rank" jdbcType="SMALLINT"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="display" column="display" jdbcType="SMALLINT"/>
        <result property="version" column="version" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdDt" column="created_dt" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedDt" column="updated_dt" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <sql id="Base_Column_List">
        config_sid,config_type,config_name,
        config_key,config_value,data_type,
        display_type,unit,value_domain,
        value_increment,sort_rank,description,
        display,version,created_by,
        created_dt,updated_by,updated_dt
    </sql>

    <sql id="Example_Where_Clause">
        <trim prefix="where" prefixOverrides="and|or">
            <if test="condition.configTypeLike != null">
                and CONFIG_TYPE like CONCAT('%',#{condition.configTypeLike},'%')
            </if>
            <if test="condition.configType != null">
                and CONFIG_TYPE = #{condition.configType}
            </if>
            <if test="condition.configTypeIn != null">
                and CONFIG_TYPE IN
                <foreach collection="condition.configTypeIn" open="(" close=")" item="item" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="condition.configKey != null">
                and CONFIG_KEY = #{condition.configKey}
            </if>
            <if test="condition.configKeyLike != null">
                and CONFIG_KEY like CONCAT('%',#{condition.configKeyLike},'%')
            </if>
            and display = 1
        </trim>
    </sql>

    <select id="displaySystemConfigList" resultType="com.cloudstar.dao.model.SysMConfig">
        SELECT
        S.CONFIG_SID,S.CONFIG_TYPE,S.CONFIG_NAME,S.CONFIG_KEY,S.CONFIG_VALUE,S.DATA_TYPE,S.VALUE_DOMAIN, S.DESCRIPTION,
        S.SORT_RANK,S.UNIT
        FROM SYS_M_CONFIG S
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        order by S.SORT_RANK
    </select>


</mapper>
