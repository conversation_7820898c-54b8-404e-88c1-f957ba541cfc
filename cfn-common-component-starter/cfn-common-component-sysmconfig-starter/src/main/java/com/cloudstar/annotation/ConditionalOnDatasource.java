package com.cloudstar.annotation;

import com.cloudstar.condition.TableExistsCondition;

import org.springframework.context.annotation.Conditional;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 数据库条件注解
 * 如果数据库中存在指定的表或者数据源库名一致，则进行Bean的注入
 *
 * <AUTHOR>
 * @date 2024/07/04
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Conditional(TableExistsCondition.class)
public @interface ConditionalOnDatasource {

    /**
     * 表名称
     *
     * @return {@link String }
     */
    String tableName() default  "";

    /**
     * 库名
     *
     * @return {@link String }
     */
    String libraryName() default "";
}
