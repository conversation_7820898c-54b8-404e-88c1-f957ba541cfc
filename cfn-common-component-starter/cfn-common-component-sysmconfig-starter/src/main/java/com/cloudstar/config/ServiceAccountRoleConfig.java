package com.cloudstar.config;


import com.cloudstar.annotation.ConfigBean;
import com.cloudstar.annotation.ConfigDes;
import com.cloudstar.bean.pojo.ConfigProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ConfigBean(type = "service_account_role_config", description = "服务账户角色配置")
public class ServiceAccountRoleConfig implements Serializable {

    @ConfigDes(key = "api.groups", name = "api组")
    private ConfigProperty apiGroups;

    @ConfigDes(key = "resources", name = "资源")
    private ConfigProperty resources;

    @ConfigDes(key = "verbs", name = "操作")
    private ConfigProperty verbs;
}
