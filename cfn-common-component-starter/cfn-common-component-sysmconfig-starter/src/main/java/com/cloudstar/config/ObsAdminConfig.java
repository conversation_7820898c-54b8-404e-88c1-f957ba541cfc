package com.cloudstar.config;

import com.cloudstar.annotation.ConfigBean;
import com.cloudstar.annotation.ConfigDes;
import com.cloudstar.bean.pojo.ConfigProperty;
import lombok.Data;


/**
 * obs管理员配置
 *
 * <AUTHOR>
 * @date 2024/06/17
 */
@Data
@ConfigBean(type = "obs_config", description = "OBS管理员配置", libraryName = "agent")
public class ObsAdminConfig {

    @ConfigDes(description = "账号", key = "obs.access.key")
    private ConfigProperty accessKey;

    @ConfigDes(description = "密码", key = "obs.secret.key")
    private ConfigProperty secretKey;

    @ConfigDes(description = "管理地址", key = "obs.url")
    private ConfigProperty obsUrl;

}
