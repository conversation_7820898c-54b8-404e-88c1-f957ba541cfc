package com.cloudstar.config;

import com.cloudstar.annotation.ConfigBean;
import com.cloudstar.annotation.ConfigDes;
import com.cloudstar.bean.pojo.ConfigProperty;

import java.io.Serializable;

import lombok.Data;

/**
 * 协同作业配置
 *
 * <AUTHOR>
 * @date 2022/11/23
 */
@Data
@ConfigBean(type = "coordination_config", description = "协同作业配置")
public class CoordinationConfig implements Serializable {

    @ConfigDes(key = "server.ip", name = "服务端ip")
    private ConfigProperty serverIp;
}
