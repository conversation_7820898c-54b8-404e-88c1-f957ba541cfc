package com.cloudstar.config;


import com.cloudstar.annotation.ConfigBean;
import com.cloudstar.annotation.ConfigDes;
import com.cloudstar.bean.pojo.ConfigProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ConfigBean(type = "my_operation_management_config", description = "运营支撑管理端配置")
public class MyOperationManagementConfig implements Serializable {
    @ConfigDes(key = "my.assets.url", name = "我的资产地址")
    private ConfigProperty myAssets;

    @ConfigDes(key = "my.subscription.management.url", name = "订阅管理地址")
    private ConfigProperty mySubscriptionManagement;

    @ConfigDes(key = "my.subscription.url", name = "我的订阅地址")
    private ConfigProperty mySubscription;

    @ConfigDes(key = "my.asset.approval.url", name = "资产审批地址")
    private ConfigProperty myAssetApproval;

    @ConfigDes(key = "bare.metal.computing.power.url", name = "算力裸机地址")
    private ConfigProperty bareMetalComputingPower;

    @ConfigDes(key = "vite_my_orders_url", name = "我的订单地址")
    private ConfigProperty viteMyOrdersUrl;

    @ConfigDes(key = "vite_income_expense_detail_url", name = "收支明细地址")
    private ConfigProperty viteIncomeExpenseDetailUrl;

    @ConfigDes(key = "vite_my_bills_url", name = "我的账单地址")
    private ConfigProperty viteMyBillsUrl;
}
