package com.cloudstar.config;


import com.cloudstar.annotation.ConfigBean;
import com.cloudstar.annotation.ConfigDes;
import com.cloudstar.bean.pojo.ConfigProperty;

import java.io.Serializable;

import lombok.Data;

@Data
@ConfigBean(type = "minio_config", description = "minio配置项")
public class MinioConfig implements Serializable {
    
    @ConfigDes(key = "minio.access.key", name = "minio公钥", defaultValue = "E6z+C7EdydOzqBqke2QD9S8z4xw8bOVOsAS3UOrnkhwTuSyu2SNK+Ew18Jh+Zw==")
    private ConfigProperty access;
    
    @ConfigDes(key = "minio.secret.key", name = "minio私钥", defaultValue = "N3HG1u0zOcwTKIcnb04hzSYvHP7cfUK5fs9IrtA4qJG/n96YSa+cZfjmWX9s")
    private ConfigProperty secret;
    
    @ConfigDes(key = "minio.endpoint",
            name = "minio地址", defaultValue = "8mTAnUlcQa43Gip0Ry61Iq73uxC5uUmU+gglPfUsHV+znSTqlfs8xWPrLOYa+7/69BTanYXIT/43HBVSFjUkFY3x3Tj8wLEhcUto")
    private ConfigProperty endpoint;
    
}
