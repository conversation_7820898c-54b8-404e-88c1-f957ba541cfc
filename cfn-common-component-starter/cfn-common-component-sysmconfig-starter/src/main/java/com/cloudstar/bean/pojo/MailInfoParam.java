package com.cloudstar.bean.pojo;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 发送邮件基本信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MailInfoParam {


    /**
     * 是否启用
     */
    private boolean enable;

    /**
     * smtp服务器
     */
    private String smtp;

    /**
     * 发送人邮件地址
     */
    private String account;

    /**
     * 发件人邮件账户
     */
    private String username;

    /**
     * 发件人邮件密码
     */
    private String password;


    /**
     * 发件人昵称
     */
    private String nickname;

}
