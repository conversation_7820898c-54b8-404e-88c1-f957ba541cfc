package com.cloudstar.common.base.pojo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * IP访问控制-控制台类型信息
 *
 * @TableName sys_m_ip_control
 */
@Data
public class SysMIpControl implements Serializable {
    
    /**
     * This field was generated by MyBatis Generator. This field corresponds to the database table sys_m_ip_control
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private static final long serialVersionUID = 1L;
    
    /**
     * 主键ID
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private Long id;
    
    /**
     * 该IP所属类型 （mgt:管理员控制台 console：用户控制台）
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private String controlType;
    
    /**
     * 单一IP请求速率设置 1位数字并且范围在3-5（默认为3）
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private Integer ipRequestLimit;
    
    /**
     * 启用IP访问控制（ 0:关闭 1:启动）默认为0
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private Integer ipControlFlg;
    
    /**
     * 所有者ID
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private String ownerId;
    
    /**
     * 组织ID
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private Long orgSid;
    
    /**
     * 创建者组织ID
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private String createdOrgSid;
    
    /**
     * 版本号
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private Long version;
    
    /**
     * 创建人
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private String createdBy;
    
    /**
     * 创建时间
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private Date createdDt;
    
    /**
     * 更新人
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private String updatedBy;
    
    /**
     * 更新时间
     *
     * @mbg.generated 2021-09-14 17:11:21
     */
    private Date updatedDt;
}
