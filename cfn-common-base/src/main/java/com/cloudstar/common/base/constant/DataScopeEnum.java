package com.cloudstar.common.base.constant;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据权限范围定义
 *
 * <AUTHOR>
 * @date 2018-09-11
 */
public enum DataScopeEnum {
    
    DATA_SCOPE_ALL("1", "所有数据"),
    DATA_SCOPE_COMPANY_AND_CHILD("2", "所在组织及以下数据"),
    DATA_SCOPE_COMPANY("3", "所在组织数据"),
    DATA_SCOPE_SELF("8", "仅本人数据"),
    DATA_SCOPE_CUSTOM("9", "按明细设置");
    
    private String scope;
    
    private String desc;
    
    DataScopeEnum(String scope, String desc) {
        this.scope = scope;
        this.desc = desc;
    }
    
    /**
     * 映射
     *
     * @return {@link Map}<{@link String}, {@link Map}<{@link String}, {@link Object}>>
     */
    public static Map<String, Map<String, Object>> toMap() {
        DataScopeEnum[] ary = DataScopeEnum.values();
        Map<String, Map<String, Object>> enumMap = new HashMap<String, Map<String, Object>>();
        for (int num = 0; num < ary.length; num++) {
            Map<String, Object> map = new HashMap<String, Object>();
            String key = ary[num].name();
            map.put("desc", ary[num].getDesc());
            enumMap.put(key, map);
        }
        return enumMap;
    }
    
    /**
     * 列出
     *
     * @return {@link List}
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public static List toList() {
        DataScopeEnum[] ary = DataScopeEnum.values();
        List list = new ArrayList();
        for (int i = 0; i < ary.length; i++) {
            Map<String, String> map = new HashMap<String, String>();
            map.put("desc", ary[i].getDesc());
            map.put("name", ary[i].name());
            list.add(map);
        }
        return list;
    }
    
    /**
     * 得到枚举
     *
     * @param name 名字
     * @return {@link DataScopeEnum}
     */
    public static DataScopeEnum getEnum(String name) {
        DataScopeEnum[] arry = DataScopeEnum.values();
        for (int i = 0; i < arry.length; i++) {
            if (arry[i].name().equalsIgnoreCase(name)) {
                return arry[i];
            }
        }
        return null;
    }
    
    /**
     * 获得范围列表
     *
     * @param scope 范围
     * @return {@link List}
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    public static List getScopeList(String scope) {
        DataScopeEnum[] ary = DataScopeEnum.values();
        List list = new ArrayList();
        for (int i = 0; i < ary.length; i++) {
            if (ary[i].scope.equals(scope)) {
                Map<String, String> map = new HashMap<String, String>();
                map.put("desc", ary[i].getDesc());
                map.put("name", ary[i].name());
                list.add(map);
            }
        }
        return list;
    }
    
    /**
     * 得到json str 取枚举的json字符串
     *
     * @return {@link String}
     */
    public static String getJsonStr() {
        DataScopeEnum[] enums = DataScopeEnum.values();
        StringBuffer jsonStr = new StringBuffer("[");
        for (DataScopeEnum senum : enums) {
            if (!"[".equals(jsonStr.toString())) {
                jsonStr.append(",");
            }
            jsonStr.append("{id:'").append(senum).append("',desc:'").append(senum.getDesc()).append("'}");
        }
        jsonStr.append("]");
        return jsonStr.toString();
    }
    
    public String getScope() {
        return scope;
    }
    
    public void setScope(String scope) {
        this.scope = scope;
    }
    
    public String getDesc() {
        return desc;
    }
    
    public void setDesc(String desc) {
        this.desc = desc;
    }
}
