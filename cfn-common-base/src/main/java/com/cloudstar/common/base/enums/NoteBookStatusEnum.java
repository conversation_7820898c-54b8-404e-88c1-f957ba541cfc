package com.cloudstar.common.base.enums;

import com.google.common.base.Strings;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 开发环境状态枚举
 *
 * <AUTHOR> Created on 2022/8/25
 * @date 2022/08/25
 */
@Getter
@AllArgsConstructor
public enum NoteBookStatusEnum {
    /**
     * 创建中
     */
    CREATING("CREATING", "创建中", 1),
    /**
     * 排队中
     */
    QUEUING("QUEUING", "排队中", 2),
    /**
     * 运行中
     */
    RUNNING("RUNNING", "运行中", 3),
    /**
     * 运行失败
     */
    FAILED("FAILED", "运行失败", 100),

    /**
     * 停止中
     */
    STOPPING("STOPPING", "停止中", 6),
    /**
     * 已终止
     */
    TERMINATED("TERMINATED", "已终止", 100),
    /**
     * 删除中
     */
    DELETE("DELETE", "删除中", 6),
    /**
     * 已删除
     */
    DELETED("DELETED", "已删除", 100),

    /**
     * 保存镜像中
     */
    SAVING("SAVING", "保存镜像中", 100),

    /**
     * 保存镜像中
     */
    SAVE_SUCCEED("SAVE_SUCCEED", "镜像保存成功", 100),

    /**
     * 保存镜像中
     */
    SAVE_FAILED("SAVE_FAILED", "镜像保存失败", 100),
    /**
     * 启动中
     */
    STARTING("STARTING", "启动中", 100);


    private String type;

    private String desc;

    private Integer sort;

    public static Integer getSort(String type) {
        for (NoteBookStatusEnum value : NoteBookStatusEnum.values()) {
            if (value.type.equals(type)) {
                return value.sort;
            }
        }
        return 0;
    }


    public static String getDescByType(String type) {
        if (Strings.isNullOrEmpty(type)) {
            return StrUtil.EMPTY;
        }
        for (NoteBookStatusEnum value : NoteBookStatusEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return StrUtil.EMPTY;
    }

}
