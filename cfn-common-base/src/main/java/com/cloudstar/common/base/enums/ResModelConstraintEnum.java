package com.cloudstar.common.base.enums;

import cn.hutool.core.util.StrUtil;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 模型运行时依赖约束类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum ResModelConstraintEnum {

    /**
     * 等于
     */
    EQ("EQ", "等于"),
    /**
     * 大于等于
     */
    GE("GE", "大于等于"),
    /**
     * 小于等于
     */
    LE("LE", "小于等于");

    private final String type;

    private final String desc;

    public static String getDescByType(String type) {
        if (Strings.isNullOrEmpty(type)) {
            return StrUtil.EMPTY;
        }
        for (ResModelConstraintEnum value : ResModelConstraintEnum.values()) {
            if (value.type.equals(type)) {
                return value.desc;
            }
        }
        return StrUtil.EMPTY;
    }
}
