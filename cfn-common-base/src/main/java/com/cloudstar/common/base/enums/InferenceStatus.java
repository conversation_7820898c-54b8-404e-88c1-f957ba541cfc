package com.cloudstar.common.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 推理状态
 *
 * <AUTHOR>
 * @date 2024/07/31
 */
@Getter
@AllArgsConstructor
public enum InferenceStatus {

    RESTARTING("restarting", "重启中"),
    UPDATING("updating", "配置更新中"),
    CREATING("creating", "创建中"),
    STARTING("starting", "启动中"),
    RUNNING("running", "运行中"),
    FAILED("failed", "失败"),
    STOPPED("stopped", "已停止"),
    STOPPING("stopping", "停止中"),
    UNKNOWN("unknown", "未知"),
    DELETING("deleting", "删除中"),
    DELETED("deleted", "已删除");

    private final String code;
    private final String name;


    /**
     * 通过代码获取
     *
     * @param code 代码
     * @return {@link InferenceStatus }
     */
    public static InferenceStatus getByCode(String code) {
        for (InferenceStatus status : InferenceStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }

    /**
     * 按名称获取
     *
     * @param name 名称
     * @return {@link InferenceStatus }
     */
    public static InferenceStatus getByName(String name) {
        for (InferenceStatus status : InferenceStatus.values()) {
            if (status.getName().equals(name)) {
                return status;
            }
        }
        return UNKNOWN;
    }
}
