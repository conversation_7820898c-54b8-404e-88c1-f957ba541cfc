package com.cloudstar.common.base.enums;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.FieldDefaults;

@Getter
@AllArgsConstructor
@FieldDefaults(makeFinal = true, level = AccessLevel.PRIVATE)
public enum NoticeResponseEnum {


    COMPRESS_PASSWORD_SEND_NOTICE("COMPRESS_PASSWORD_SEND_NOTICE", "解压密码已发送到邮箱，请查收！"),

    EXPORT_FILE("EXPORT_FILE", "文件导出中，请稍后在下载任务中查看！"),

    VALIDATE_SUCCESS("VALIDATE_SUCCESS", "验证成功");

    String key;

    String desc;

}
