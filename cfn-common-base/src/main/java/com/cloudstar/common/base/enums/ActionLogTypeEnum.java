package com.cloudstar.common.base.enums;


/**
 * 日志操作类型枚举类
 *
 * <AUTHOR>
 * @date 2022.8.20
 */
public enum ActionLogTypeEnum {
    // 默认操作为空
    NONE("null", "null"),

    CREATE_MANAGER("CREATE_MANAGER", "创建管理员"),

    DELETE_MANAGER("DELETE_MANAGER", "删除管理员"),

    UPDATE_MANAGER_INFO("UPDATE_MANAGER", "管理员信息修改"),

    EXPORT_MANAGER_LIST("EXPORT_MANAGER_LIST", "导出管理员列表"),

    DOWNLOAD_MANAGER_LIST("DOWNLOAD_MANAGER_LIST", "下载指定的管理员列表"),

    UPDATE_MANAGER_PASSWORD("UPDATE_MANAGER_PASSWORD", "修改管理员密码"),

    RESET_MANAGER_PASSWORD("RESET_MANAGER_PASSWORD", "重置管理员密码"),

    FIND_MANAGER_PASSWORD("FIND_MANAGER_PASSWORD", "找回管理员密码"),

    UPDATE_MANAGER_STATUS("UPDATE_MANAGER_STATUS", "修改管理员状态"),

    UPDATE_MANAGER_MOBILE("UPDATE_MANAGER_MOBILE", "修改管理员手机号码"),

    UPDATE_MANAGER_EMAIL("UPDATE_MANAGER_EMAIL", "修改管理员邮箱"),

    QUERY_MANAGER_EMAIL("QUERY_MANAGER_EMAIL", "查询管理员邮箱"),

    QUERY_MANAGER_MOBILE("QUERY_MANAGER_MOBILE", "查询管理员手机号"),

    VALIDATE_MANAGER("VALIDATE_MANAGER", "管理员验证"),

    VALIDATE_PASSWORD("VALIDATE_PASSWORD", "验证密码"),

    UNLOCK_MANAGER("UNLOCK_MANAGER", "解除管理员锁定"),

    UPDATE_ROLE("UPDATE_ROLE", "编辑角色"),

    CREATE_ROLE("CREATE_ROLE", "创建角色"),

    DELETE_ROLE("DELETE_ROLE", "删除角色"),

    SAVE_MENU_AUTHORIZE("SAVE_MENU_AUTHORIZE", "授权保存菜单权限"),

    CREATE_USER("CREATE_USER", "创建租户"),

    DELETE_USER("DELETE_USER", "删除租户"),

    EXPORT_USER_LIST("EXPORT_USER_LIST", "导出用户列表"),

    DOWNLOAD_USER_LIST("DOWNLOAD_USER_LIST", "下载租户列表"),

    EXPORT_USER_JOB_LIST("EXPORT_USER_JOB_LIST", "导出用户训练作业列表"),

    DOWNLOAD_USER_JOB_LIST("DOWNLOAD_USER_JOB_LIST", "下载租户训练作业列表"),

    UPDATE_USER_INFO("UPDATE_USER_INFO", "修改租户信息"),

    UPDATE_USER_PASSWORD("UPDATE_USER_PASSWORD", "修改租户密码"),

    FIND_USER_PASSWORD("FIND_USER_PASSWORD", "找回租户密码"),

    RESET_USER_PASSWORD("RESET_USER_PASSWORD", "重置租户密码"),

    RESET_SUB_USER_PASSWORD("RESET_SUB_USER_PASSWORD", "重置子账号密码"),

    UPDATE_USER_STATUS("UPDATE_USER_STATUS", "修改用户状态"),

    UPDATE_USER_MOBILE("UPDATE_USER_MOBILE", "修改用户手机号码"),

    UPDATE_USER_EMAIL("UPDATE_USER_EMAIL", "修改用户邮箱"),

    QUERY_USER_MOBILE("QUERY_USER_MOBILE", "查询用户手机号码"),

    QUERY_USER_EMAIL("QUERY_USER_EMAIL", "查询用户邮箱"),

    QUERY_USER_NAME("QUERY_USER_NAME", "查询用户姓名"),

    VALIDATE_USER(" VALIDATE_USER", "验证租户"),

    UNLOCK_USER("UNLOCK_USER", "解锁租户"),

    CREAT_JOB("CREAT_JOB", "创建训练作业"),
    // 默认操作为空
    CREAT_COORDINATION_JOB("CREAT_COORDINATION_JOB", "创建协同作业"),
    DELETE_COORDINATION_JOB("DELETE_COORDINATION_JOB", "删除协同作业"),
    CREAT_JOB_GROUP("CREAT_JOB_GROUP", "创建作业组"),

    DELETE_JOB_GROUP("DELETE_JOB_GROUP", "删除作业组"),
    START_JOB_GROUP("START_JOB_GROUP", "启动作业组"),
    STOP_JOB_GROUP("STOP_JOB_GROUP", "停止作业组"),
    CREAT_DATA_STORAGE("CREAT_DATA_STORAGE", "创建数据资源"),
    DELETE_DATA_STORAGE("DELETE_DATA_STORAGE", "删除数据资源"),
    UPDATE_DATA_STORAGE("UPDATE_DATA_STORAGE", "编辑数据资源"),
    CREAT_TICKET("CREAT_TICKET", "创建工单"),

    DELETE_TICKET("DELETE_TICKET", "删除工单"),
    MESSAGE_TICKET("MESSAGE_TICKET", "处理工单"),

    ALLOCATION_TICKET("ALLOCATION_TICKET", "分配工单"),
    UPDATE_STATUS_TICKET("UPDATE_STATUS_TICKET", "修改工单状态"),
    CREATE_USER_AUTH("CREATE_USER_AUTH", "申请实名认证"),
    REQUEST_FROM_EDIT("REQUEST_FROM_EDIT", "审核"),

    REQUEST_FROM_INFO("REQUEST_FROM_INFO", "审核详情"),

    RESEND_NOTIFY("RESEND_NOTIFY", "重新发送通知"),
    SAVE_NOTICE("SAVE_NOTICE", "保存公告"),
    DELETE_NOTICE("DELETE_NOTICE", "删除公告"),
    UPDATE_NOTICE_STATUS("UPDATE_NOTICE_STATUS", "更新公告状态"),

    SYSTEM_CONFIG_BASE_LIST("SYSTEM_CONFIG_BASE_LIST", "基础配置列表"),
    SYSTEM_CONFIG_MESSAGE_LIST("SYSTEM_CONFIG_MESSAGE_LIST", "消息网关配置列表"),
    SYSTEM_CONFIG_SAFE_LIST("SYSTEM_CONFIG_SAFE_LIST", "安全配置列表"),
    UPDATE_SYSTEM_CONFIG_BASE("UPDATE_SYSTEM_CONFIG_BASE", "更新基础配置"),
    UPDATE_SYSTEM_CONFIG_MESSAGE("UPDATE_SYSTEM_CONFIG_MESSAGE", "更新消息网关配置"),
    UPDATE_SYSTEM_CONFIG_SAFE("UPDATE_SYSTEM_CONFIG_SAFE", "更新安全配置"),
    UPDATE_SYSTEM_CONFIG_BUSINESS("UPDATE_SYSTEM_CONFIG_BUSINESS", "更新业务配置"),
    OBS_TEMPORARY_VOUCHER("OBS_TEMPORARY_VOUCHER", "obs临时授权凭证"),
    OBS_QUERY_ENTITIES("OBS_QUERY_ENTITIES", "obs列举对象"),
    OBS_CREATE_FOLDER("OBS_CREATE_FOLDER", "obs创建目录"),
    OBS_DELETE("OBS_DELETE", "obs删除对象"),
    USER_REGISTER("USER_REGISTER", "租户注册"),
    USER_LOGIN("USER_LOGIN", "租户登录"),
    USER_LOGOUT("USER_LOGOUT", "租户退出登录"),
    MANAGER_LOGIN("MANAGER_LOGIN", "管理员登录"),
    MANAGER_LOGOUT("MANAGER_LOGOUT", "管理员退出登录"),
    LOGIN_AUTH("LOGIN_AUTH", "登录鉴权"),
    ACCOUNT_SMS("ACCOUNT_SMS", "根据账号获取短信验证码"),
    TRAINING_JOB_STOP("TRAINING_JOB_STOP", "停止作业"),
    TRAINING_JOB_DELETE("TRAINING_JOB_DELETE", "删除作业"),
    TRAINING_JOB_CANCEL("TRAINING_JOB_CANCEL", "取消作业"),
    CLUSTER_ADD("CLUSTER_ADD", "新增集群"),
    CLUSTER_UPDATE("CLUSTER_UPDATE", "修改集群"),
    CLUSTER_DELETE("CLUSTER_DELETE", "删除集群"),
    CLUSTER_SYNC("CLUSTER_SYNC", "集群同步"),
    CLUSTER_STATUS_UPDATE("CLUSTER_STATUS_UPDATE", "集群上下线"),
    CLUSTER_ENGINE_UPDATE("CLUSTER_ENGINE_UPDATE", "集群引擎关联规格"),
    CLUSTER_FLAVOR_UPDATE("CLUSTER_FLAVOR_UPDATE", "集群规格修改"),
    CLUSTER_FLAVOR_CREATE("CLUSTER_FLAVOR_CREATE", "集群规格新增"),
    CLUSTER_FLAVOR_DELETE("CLUSTER_FLAVOR_DELETE", "集群规格删除"),
    CLUSTER_POOL_ADD("CLUSTER_POOL_ADD", "集群资源池新增"),
    CLUSTER_POOL_UPDATE("CLUSTER_POOL_UPDATE", "集群资源池修改"),
    CLUSTER_POOL_DELETE("CLUSTER_POOL_DELETE", "集群资源池删除"),
    CLUSTER_POOL_SYNC("CLUSTER_POOL_SYNC", "集群资源池同步"),
    SCHEDULE_POLICY_UPDATE("SCHEDULE_POLICY_UPDATE", "调度策略修改"),
    SCHEDULE_POLICY_ENABLE("SCHEDULE_POLICY_ENABLE", "调度策略禁用启用"),
    CLUSTER_NODE_CREAT("CLUSTER_NODE_CREAT", "创建集群节点"),
    CLUSTER_NODE_UPDATE("CLUSTER_NODE_UPDATE", "修改集群节点"),
    CLUSTER_NODE_DELETE("CLUSTER_NODE_DELETE", "删除集群节点"),
    CLUSTER_ENGINE_CREAT("CLUSTER_ENGINE_CREAT", "创建集群引擎"),
    CLUSTER_ENGINE_DELETE("CLUSTER_ENGINE_DELETE", "删除集群引擎"),

    MAPPING_CLUSTER_USER("MAPPING_CLUSTER_USER", "租户映射"),

    DELETE_MESSAGE("DELETE_MESSAGE", "删除消息"),

    IGNORE_ALL_MESSAGE("IGNORE_ALL_MESSAGE", "忽略全部消息"),

    READ_MESSAGE("READ_MESSAGE", "已读消息"),

    SUB_USER_GROUP_ADD("SUB_USER_GROUP_ADD", "创建用户组"),
    SUB_USER_ADD("SUB_USER_ADD", "子用户创建"),
    SUB_USER_DELETE("SUB_USER_DELETE", "子用户删除"),
    SUB_USER_EDIT_PWD("SUB_USER_EDIT_PWD", "更改子用户密码"),
    CONFIG_POLICY_GROUP("CONFIG_POLICY_GROUP", "配置策略组"),
    SUB_USER_STATUS_RESET("SUB_USER_STATUS_RESET", "子用户状态变更"),
    SUB_USER_DISTRIBUTION("SUB_USER_DISTRIBUTION", "用户组关联用户"),
    SUB_USER_JURISDICTION_DISTRIBUTION("SUB_USER_JURISDICTION_DISTRIBUTION", "权限测率组关联用户组"),

    CREATE_JOB_PARAM("CREATE_JOB_PARAM", "创建超参"),
    DELETE_JOB_PARAM("DELETE_JOB_PARAM", "删除超参"),
    UPLOAD_FILE("UPLOAD_FILE", "上传文件"),

    BILL_ITEM_EXPORT("BILL_ITEM_EXPORT", "作业计量明细导出"),

    BILL_ITEM_EXPORT_DOWN_LOAD("BILL_ITEM_EXPORT_DOWN_LOAD", "下载作业计量明细导出文件"),
    BILL_SUMMARY_EXPORT("BILL_SUMMARY_EXPORT", "作业计量周期导出"),
    BILL_SUMMARY_EXPORT_DOWN_LOAD("BILL_SUMMARY_EXPORT_DOWN_LOAD", "下载作业计量周期导出文件"),

    UPDATE_DATA_STORAGE_STATUS("UPDATE_DATA_STORAGE_STATUS", "更新镜像数据状态"),
    CREATE_ALGORITHM("CREATE_ALGORITHM", "创建算法"),
    UPDATE_ALGORITHM("CREATE_ALGORITHM", "修改算法"),
    DELETE_ALGORITHM("DELETE_ALGORITHM", "删除算法"),
    CREATE_ALGORITHM_SUBSCRIBE("CREATE_ALGORITHM_SUBSCRIBE", "创建订阅"),

    SUBMIT_IMAGE_DATA_AUDIT("SUBMIT_IMAGE_DATA_AUDIT", "镜像文件提交审核"),

    OBS_SET_BUCKET_POLICY("OBS_SET_BUCKET_POLICY", "设置桶策略"),

    EXPORT_ORG_ANALYSIS("EXPORT_ORG_ANALYSIS", "云资源分析-导出组织维度云资源分析"),
    EXPORT_PROJECT_ANALYSIS("EXPORT_PROJECT_ANALYSIS", "云资源分析-导出项目维度云资源分析"),
    EXPORT_RESOURCE_ANALYSIS("EXPORT_RESOURCE_ANALYSIS", "云资源分析-导出资源维度云资源分析"),
    EXPORT_RES_POOL_ANALYSIS("EXPORT_RES_POOL_ANALYSIS", "云资源分析-导出资源维度云资源分析"),
    DELETE_ALARM_DATA("DELETE_ALARM_DATA", "告警数据-删除告警数据"),
    PROCESS_ALARM_DATA("PROCESS_ALARM_DATA", "告警数据-处理告警数据"),
    EXPORT_ALARM_DATA("EXPORT_ALARM_DATA", "告警数据-导出告警数据"),
    EXPORT_ALARM_ORIGIN("EXPORT_ALARM_ORIGIN", "导出原始告警明细"),
    CREATE_NOTIFY_POLICY("CREATE_NOTIFY_POLICY", "告警通知策略-创建告警通知策略"),
    UPDATE_NOTIFY_POLICY("UPDATE_NOTIFY_POLICY", "告警通知策略-修改告警通知策略"),
    UPDATE_NOTIFY_POLICY_STATUS("UPDATE_NOTIFY_POLICY_STATUS", "告警通知策略-启用禁用告警通知策略"),
    DELETE_NOTIFY_POLICY("DELETE_NOTIFY_POLICY", "告警通知策略-删除告警通知策略"),
    CREATE_NOTIFY_CONTACTS("CREATE_NOTIFY_CONTACTS", "告警通知联系人-创建告警通知联系人"),
    ADD_USER_NOTIFY_CONTACTS("ADD_USER_NOTIFY_CONTACTS", "告警通知联系人-添加已有用户"),
    UPDATE_NOTIFY_CONTACTS("UPDATE_NOTIFY_CONTACTS", "告警通知联系人-修改告警通知联系人"),
    DELETE_NOTIFY_CONTACTS("DELETE_NOTIFY_CONTACTS", "告警通知联系人-删除告警通知联系人"),
    CREATE_NOTIFY_GROUP("CREATE_NOTIFY_GROUP", "告警通知组-创建告警通知组"),
    UPDATE_NOTIFY_GROUP("UPDATE_NOTIFY_GROUP", "告警通知组-修改告警通知组"),
    DELETE_NOTIFY_GROUP("DELETE_NOTIFY_GROUP", "告警通知组-删除告警通知组"),
    REMOVE_CONTACTS_NOTIFY_GROUP("REMOVE_CONTACTS_NOTIFY_GROUP", "告警通知组-移除告警通知组联系人"),
    UPDATE_ALARM_RULE_STATUS("UPDATE_ALARM_RULE_STATUS", "告警规则-启用禁用告警规则"),
    UPDATE_ALARM_RULE("UPDATE_ALARM_RULE", "告警规则-修改告警规则"),
    CREATE_ALARM_RULE("CREATE_ALARM_RULE", "告警规则-创建告警规则"),
    CHECK_PROMQL_ALARM_RULE("CHECK_PROMQL_ALARM_RULE", "告警规则-校验告警规则promql表达式"),
    CREATE_METRIC_CATEGORY("CREATE_METRIC_CATEGORY", "原生告警指标分类-创建原生告警指标分类"),
    UPDATE_METRIC_CATEGORY("UPDATE_METRIC_CATEGORY", "原生告警指标分类-修改原生告警指标分类"),
    DELETE_METRIC_CATEGORY("DELETE_METRIC_CATEGORY", "原生告警指标分类-删除原生告警指标分类"),
    CREATE_ALARM_METRIC("CREATE_ALARM_METRIC", "原生告警指标-创建原生告警指标"),
    UPDATE_ALARM_METRIC("UPDATE_ALARM_METRIC", "原生告警指标-修改原生告警指标"),
    DELETE_ALARM_METRIC("DELETE_ALARM_METRIC", "告警通知策略-删除原生告警指标"),
    DELETE_ALARM_METRIC_POLICY("DELETE_ALARM_METRIC_POLICY", "告警通知策略-删除原生指标通知策略"),
    UPDATE_ALARM_METRIC_STATUS("UPDATE_ALARM_METRIC_STATUS", "原生告警指标-启用禁用原生告警指标"),
    EXPORT_MONITO_ALARM_DATA_LIST("EXPORT_USER_LIST", "导出告警列表"),
    DOWNLOAD_ALARM_DATA_LIST("DOWNLOAD_ALARM_DATA_LIST", "下载告警数据列表"),
    COMMON_INDEX_CREATE("COMMON_INDEX_CREATE", "创建通用指标"),
    COMMON_INDEX_UPDATE("COMMON_INDEX_UPDATE", "修改通用指标"),
    COMMON_INDEX_DEL("COMMON_INDEX_DEL", "删除通用指标");


    private final String type;

    private final String desc;

    ActionLogTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    public String getType() {
        return type;
    }
}
