package com.cloudstar.common.base.exception;

import com.cloudstar.common.base.constant.BizErrorEnum;
import com.cloudstar.common.base.pojo.result.Rest;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceAware;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * The type ExceptionHandler.
 * <p>
 *
 * <AUTHOR>
 * @date 2019/6/27
 */
@Slf4j
@RestControllerAdvice
@ConditionalOnClass(value = HttpServletRequest.class)
public class GlobalExceptionHandler implements MessageSourceAware {

    private MessageSource messageSource;

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Rest<String> handle(MethodArgumentNotValidException exception, HttpServletRequest request) {
        BindingResult bindingResult = exception.getBindingResult();
        log.error("Bad Request:", exception);
        return handleBindResult(bindingResult);
    }

    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Rest<String> handle(BindException exception, HttpServletRequest request) {
        BindingResult bindingResult = exception.getBindingResult();
        log.error("Bad Request:", exception);
        return handleBindResult(bindingResult);
    }

    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Rest<String> handle(ConstraintViolationException exception, HttpServletRequest request) {

        Set<ConstraintViolation<?>> constraintViolations = exception.getConstraintViolations();
        Iterator<ConstraintViolation<?>> iterator = constraintViolations.iterator();
        List<String> msgList = Lists.newArrayList();
        while (iterator.hasNext()) {
            ConstraintViolation<?> cvl = iterator.next();
            String[] str = cvl.getPropertyPath().toString().split("\\.");
            if (str.length > 0) {
                msgList.add(str[str.length - 1] + "：" + cvl.getMessage());
            }
        }
        String msg = Joiner.on(",").join(msgList);
        log.error("Bad Request:", exception);
        return Rest.e(msg);
    }

    @ExceptionHandler(MissingServletRequestParameterException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Rest<String> handle(MissingServletRequestParameterException exception, HttpServletRequest request) {
        log.error("Bad Request:", exception);
        return Rest.e(exception.getMessage());
    }

    @ExceptionHandler(BizException.class)
    @ResponseStatus(HttpStatus.OK)
    public Rest<String> businessException(BizException e) {
        log.error("BizException:", e);
        if (Objects.nonNull(e.getMessage())) {
            return Rest.e(e.getMessage());
        }
        log.error("业务异常:[{}]", getErrorMessage(e.getCode()));
        return Rest.e(getErrorMessage(e.getCode()));
    }


    @ExceptionHandler(CustomException.class)
    @ResponseStatus(HttpStatus.OK)
    public Rest<String> handle(CustomException e) {
        log.error("系统异常:[{}]", getErrorMessage(e.getMsg()));
        log.error("系统异常:", e);
        return Rest.e(e.getCode(), e.getMsg());
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.OK)
    public Rest<String> handleException(Exception e) {
        log.error("系统内部异常，异常信息:", e);
        return Rest.e(getErrorMessage(BizErrorEnum.DEFAULT_EXCEPTION_MSG.getMsgKey()));
    }

    private Rest<String> handleBindResult(BindingResult bindingResult) {
        if (bindingResult != null && bindingResult.hasErrors()) {
            String errorMessage = bindingResult.getAllErrors().stream()
                                               .map(error -> ((FieldError) error).getField() + " "
                                                       + error.getDefaultMessage())
                                               .collect(Collectors.joining(","));

            return Rest.e(errorMessage);
        }

        return Rest.e("参数校验未通过");
    }

    private String getErrorMessage(String errorCode) {
        return messageSource.getMessage(errorCode, null, Locale.getDefault());
    }

    @Override
    public void setMessageSource(@NonNull MessageSource messageSource) {
        this.messageSource = messageSource;
    }
}
