package com.cloudstar.common.base.constant;

/**
 * 共同消息 国际化信息key
 *
 * <AUTHOR>
 * @date 2023/6/14
 */
public class CommonMsgConstant {

    /**
     * 操作成功
     */
    public static final String COMMON_INFO_HANDLE_SUCCESS = "common.info.handle.success";

    /**
     * 登录成功
     */
    public static final String COMMON_INFO_HANDLE_LOGIN = "common.info.handle.login";

    /**
     * 登出成功
     */
    public static final String COMMON_INFO_HANDLE_LOGOUT = "common.info.handle.logout";

    /**
     * 下发成功
     */
    public static final String COMMON_INFO_ISSUE_SUCCESS = "common.info.issue.success";

    /**
     * 部分操作成功
     */
    public static final String COMMON_WARN_HANDLE_PARTSUCCESS = "common.warn.handle.partSuccess";

    /**
     * 没有操作权限
     */
    public static final String COMMON_WARN_HANDLE_NOPERMISSION = "common.warn.handle.noPermission";

    /**
     * 操作失败
     */
    public static final String COMMON_ERROR_HANDLE_FAIL = "common.error.handle.fail";

    /**
     * 无效参数
     */
    public static final String COMMON_ERROR_HANDLE_INVALIDPARAMETER = "common.error.handle.invalidParameter";

    /**
     * 无效请求方式
     */
    public static final String COMMON_ERROR_HANDLE_INVALIDMETHOD = "common.error.handle.invalidMethod";

    /**
     * 数据库错误
     */
    public static final String COMMON_ERROR_DB_ERROR = "common.error.db.error";

    /**
     * 系统异常
     */
    public static final String COMMON_ERROR_SYSTEM_ERROR = "common.error.system.error";

    /**
     * 导出文件失败
     */
    public static final String COMMON_ERROR_HANDLE_OUTPUTFAIL = "common.error.handle.outputFail";

    /**
     * 连接失败
     */
    public static final String COMMON_ERROR_HANDLE_CONNECTFAIL = "common.error.handle.connectFail";

    /**
     * 文件上传失败
     */
    public static final String COMMON_ERROR_HANDLE_FILEUPLOADFAIL = "common.error.handle.fileUploadFail";

    /**
     * 内部服务调用异常
     */
    public static final String COMMON_ERROR_HANDLE_INTERNALSERVICEERROR = "common.error.handle.internalServiceError";

    /**
     * 系统默认数据不允许删除
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_CANNOTDELETEDEFAULT = "common.error.validation.constraints.cannotDeleteDefault";

    /**
     * {0}数据已经存在
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_ALREADYEXISTS = "common.error.validation.constraints.alreadyExists";

    /**
     * {0}数据不存在
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_NONEXISTENT = "common.error.validation.constraints.nonexistent";

    /**
     * {0}存在{1}关联数据，不允许删除
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_CANNOTDELETE = "common.error.validation.constraints.cannotDelete";

    /**
     * {0}未启用
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTENABLED = "common.error.validation.constraints.notEnabled";

    /**
     * {0}不能为空
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTEMPTY = "common.error.validation.constraints.notEmpty";

    /**
     * 数据导出成功
     */
    public static final String COMMON_EXPORT_HANDLE_SUCCESS = "common.export.handle.success";

    /**
     * 导出数据超出{0}条限制条数
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_OUTPUTLIMIT = "common.error.validation.constraints.outputLimit";

    /**
     * 文件超过最大限制：{0}MB
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_FILEUPLOADFILESIZE = "common.error.validation.constraints.fileUploadFileSize";

    /**
     * 附件上传数量最大支持{0}个
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_FILEUPLOADMAX = "common.error.validation.constraints.fileUploadMax";

    /**
     * 没有符合条件的导出数据
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_NODATA = "common.error.validation.constraints.noData";

    /**
     * 代理不存在,请重新设置代理
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_NOPROXY = "common.error.validation.constraints.noProxy";

    /**
     * 服务未开启
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_NOTTURNONSERVICE = "common.error.validation.constraints.notTurnOnService";

    /**
     * 非法排序参数
     */
    public static final String COMMON_ERROR_VALIDATION_CONSTRAINTS_SORT_ORDER = "common.error.validation.constraints.sortOrder";

    /**
     * 重要操作未二次认证
     */
    public static final String COMMON_ERROR_CRITICAL_AUTH = "common.error.critical.auth";

    /**
     * 资源操作拦截执行审批失败
     */
    public static final String COMMON_RES_ACTION_FILTER_ERROR = "common.res.action.filter.error";

    /**
     * 资源操作拦截执行失败,资源已在申请单中
     */
    public static final String COMMON_RES_ACTION_FILTER_RESOURCE_EXISTS_ERROR = "common.res.action.filter.resource.exists.error";

    /**
     * 资源操作拦截执行审批流程
     */
    public static final String COMMON_RES_ACTION_FILTER = "common.res.action.filter";

    /**
     * 敏感数据查看未二次认证
     */
    public static final String COMMON_ERROR_SENSITIVE_AUTH = "common.error.sensitive.auth";


    /**
     * 密码长度不符合规则
     */
    public static final String PASSWORD_ERROR_INVALID_LENGTH = "password.error.invalidLength";

    /**
     * 配额已满
     */
    public static final String QUOTA_FULL = "quota.full";

    /**
     * 下发成功，请到任务中心查看详情
     */
    public static final String COMMON_INFO_DELIVERED_SUCCESS = "common.info.delivered.success";

    /**
     * 文件名违规，存在禁用的HTML标签或特殊字符
     */
    public static final String COMMON_FILE_NAME_VIOLATION = "common.file.name.violation";

    /**
     * 不支持该文件格式
     */
    public static final String COMMON_FILE_FORMAT_UNSUPPORTED = "common.file.format.unsupported";

    /**
     * 文件不存在
     */
    public static final String COMMON_FILE_NOT_EXIST = "common.file.not.exist";

    /**
     * 该接口不存在或尚未在环境中发布
     */
    public static final String COMMON_INTERFACE_NOT_FOUND = "common.interface.not.found";



    /**
     * 数据不存在或权限不足
     */
    public static final String COMMON_ERROR_HORIZONTAL_AUTH = "common.error.horizontal.auth";
}
