package com.cloudstar.common.base.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * 返回体id类型转换
 *
 * <AUTHOR>
 * @date 2022.8.18
 */
@Slf4j
//@JsonComponent
public class LongToString extends JsonSerializer<Long> {
    
    @Override
    public void serialize(Long l, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
            throws IOException {
        String text = (l == null ? null : String.valueOf(l));
        if (text != null) {
            jsonGenerator.writeString(text);
        }
    }
    
}
