/*
 * Copyright (c) 2018 Cloud-Star, Inc. All Rights Reserved..
 */

package com.cloudstar.common.base.util;



import cn.hutool.core.util.ClassLoaderUtil;
import com.cloudstar.common.util.CrytoUtilSimple;

import org.apache.commons.compress.utils.IOUtils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Properties;

/**
 * 实例化config.properties方法
 *
 * <p>不再支持读取数据库sys_m_config表
 *
 * <AUTHOR>
 */
@Slf4j
public class PropertiesUtil {

    private static Properties properties = new Properties();


    //调用方法将配置文件转化为类
    static {
        InputStreamReader reader = null;
        InputStream is = ClassLoaderUtil.getClassLoader().getResourceAsStream("config/config.properties");
        if (is != null) {
            try {
                reader = new InputStreamReader(is, StandardCharsets.UTF_8);
                properties.load(reader);
            } catch (IOException e) {
                log.error(e.getMessage());
            } finally {
                IOUtils.closeQuietly(reader);
                IOUtils.closeQuietly(is);
            }
        }
    }

    /**
     * 获取参数值
     *
     * @param key 参数名称
     * @param defaultValue 返回默认值
     */
    public static String getProperty(String key, String defaultValue) {
        String value = getProperty(key);
        if (StrUtil.isBlank(value)) {
            return defaultValue;
        }
        return value;
    }

    /**
     * 获取参数值
     *
     * @param key 参数名称
     *
     * @return the property
     */
    public static String getProperty(String key) {
        String result = "";
        try {
            if (properties.containsKey(key)) {
                result = CrytoUtilSimple.decrypt(properties.getProperty(key));
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return result;
    }

    /**
     * 得到属性
     *
     * @return the properties
     */
    public static Properties getProperties() {
        return properties;
    }

    /**
     * 设置属性
     *
     * @param properties the properties to set
     */
    public static void setProperties(Properties properties) {
        PropertiesUtil.properties = properties;
    }




}
