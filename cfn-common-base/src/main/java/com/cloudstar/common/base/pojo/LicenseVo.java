package com.cloudstar.common.base.pojo;

import lombok.Data;

/**
 * 许可证实体类
 *
 * <AUTHOR>
 * @date 2022/12/23 10:49
 */
@Data
public class LicenseVo {

    /**
     * 过期时间
     */
    private String expireDate;

    /**
     * 算力中心数量
     */
    private Integer clusterCount;

    /**
     * 算力总数
     */
    private Integer computePower;

    /**
     * 版权信息
     */
    private String versionInfo;

    /**
     * 当前版本
     */
    private String version;

    /**
     * 产品硬件特征码，来源于安装机器CMP许可证页面
     **/
    private String productSN;

    /**
     * 授予对象
     **/
    private String licenseFor;

    /**
     * 授权模块,多个以，分隔
     **/
    private String modules;

    /**
     * 售后支持
     */
    private String support;


}
