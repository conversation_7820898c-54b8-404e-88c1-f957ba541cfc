package com.cloudstar.common.base.enums;

import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public enum MirrorUseType {
    /**
     * 镜像
     */
    IMAGE("image", "镜像"),

    /**
     * 版本
     */
    VERSION("version", "版本");

    private final String enName;
    private final String cnName;

    MirrorUseType(String enName, String cnName) {
        this.enName = enName;
        this.cnName = cnName;
    }
}
