package com.cloudstar.common.base.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 镜像构建状态枚举
 *
 * <AUTHOR>
 * @date 2024/11/11
 */
@Getter
@AllArgsConstructor
public enum MirrorStatusEnum {
    CREATING("creating", "创建中"),
    QUEUED("queued", "排队中"),
    BUILDING("building", "构建中"),
    BUILDFAILED("buildFailed", "构建失败"),
    FINISHED("finished", "构建完成");

    private final String code;
    private final String description;

}
