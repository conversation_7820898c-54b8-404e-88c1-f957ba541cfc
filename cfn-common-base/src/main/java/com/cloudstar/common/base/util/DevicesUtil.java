package com.cloudstar.common.base.util;

import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.List;

/**
 * devices util
 *
 * <AUTHOR>
 * @date 2025/05/22
 */
public class DevicesUtil {
    private static final List<String> GPU_FORMAT = Arrays.asList("NVIDIA", "GPU");
    private static final List<String> NPU_FORMAT = Arrays.asList("ASCEND", "NPU");

    /**
     * is gpu devices 判断卡型号是否是gpu
     *
     * @param computeProduct compute product
     * @return boolean
     */
    public static boolean isGpuDevices(String computeProduct) {
        if (StrUtil.isNotEmpty(computeProduct)) {
            for (String s : GPU_FORMAT) {
                if (computeProduct.toLowerCase().contains(s.trim().toLowerCase())) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * is npu devices 判断卡型号是否是NPU
     *
     * @param computeProduct compute product
     * @return boolean
     */
    public static boolean isNpuDevices(String computeProduct) {
        if (StrUtil.isNotEmpty(computeProduct)) {
            for (String s : NPU_FORMAT) {
                if (computeProduct.toLowerCase().contains(s.trim().toLowerCase())) {
                    return true;
                }
            }
        }
        return false;
    }




}
