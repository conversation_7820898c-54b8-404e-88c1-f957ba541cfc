package com.cloudstar.common.base.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * json解析跑龙套
 *
 * <AUTHOR>
 * @date 2023/05/19
 */
@Slf4j
public class JsonParseUtil {

    /**
     * 通过路径 避免从json中获取值时，值为空时警告
     *
     * @param json json
     * @param path 路径
     * @param defaultValue 默认值
     *
     * @return {@link String}
     */
    public static String getByPath(JSON json, String path, String defaultValue) {
        String result = JSONUtil.getByPath(json, path, defaultValue);
        if (StrUtil.isBlank(result)) {
            log.warn("获取对应值为空,path:{},json:{}", path, json);
        }
        return result;
    }
}
