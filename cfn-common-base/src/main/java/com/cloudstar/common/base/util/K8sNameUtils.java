package com.cloudstar.common.base.util;

import java.util.regex.Pattern;


/**
 * Kubernetes名称工具类
 */
public class K8sNameUtils {

    private static final Pattern INVALID_CHARS = Pattern.compile("[^a-z0-9.-]");
    private static final int MAX_LENGTH = 63;

    /**
     * 将输入字符串转换为Kubernetes名称格式
     */
    public static String toK8sName(String input) {
        if (input == null || input.isBlank()) {
            throw new IllegalArgumentException("输入不能为null或空白");
        }

        // 转换为小写
        String lowerCase = input.toLowerCase();

        // 替换非法字符为连字符
        String sanitized = INVALID_CHARS.matcher(lowerCase).replaceAll("-");

        // 移除首尾的非法字符
        String trimmed = sanitized.replaceAll("^[-.]+|[-.]+$", "");

        // 限制最大长度
        if (trimmed.length() > MAX_LENGTH) {
            trimmed = trimmed.substring(0, MAX_LENGTH);
        }

        return trimmed;
    }
}
