#
# A fatal error has been detected by the Java Runtime Environment:
#
#  EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffcd45ef6e3, pid=11800, tid=30292
#
# JRE version: OpenJDK Runtime Environment (11.0+28) (build 11+28)
# Java VM: OpenJDK 64-Bit Server VM (11+28, mixed mode, tiered, compressed oops, g1 gc, windows-amd64)
# Problematic frame:
# V  [jvm.dll+0x38f6e3]
#
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#
# If you would like to submit a bug report, please visit:
#   http://bugreport.java.com/bugreport/crash.jsp
#

---------------  S U M M A R Y ------------

Command Line: -agentlib:jdwp=transport=dt_socket,address=127.0.0.1:52276,suspend=y,server=n -Dcloudstar.db.url=***********:31645/cfn -Dcloudstar.db.username=cfn -Dcloudstar.db.password=QGrePyjOZs8eYf8LR7RCZjYU1Qd^^q -Dcloudstar.redis.host=127.0.0.1 -Dcloudstar.redis.password= -Dcloudstar.redis.port=6379 -Dcloudstar.mq.host=************* -Dcloudstar.key.path=D:\Work\cloustar\cfn-common\cfn-common-crypto\src\main\resources\dev\key -Dcloudstar.mq.port=5673 -Dcloudstar.mq.username=guest -Dcloudstar.mq.password=guest -agentpath:C:\Users\<USER>\AppData\Local\Temp\idea_libasyncProfiler_dll_temp_folder1686\libasyncProfiler.dll=version,jfr,event=wall,interval=10ms,cstack=no,file=C:\Users\<USER>\IdeaSnapshots\IamApplication_2025_05_28_191218.jfr,dbghelppath=C:\Users\<USER>\AppData\Local\Temp\idea_dbghelp_dll_temp_folder2\dbghelp.dll,log=C:\Users\<USER>\AppData\Local\Temp\IamApplication_2025_05_28_191218.jfr.log.txt,logLevel=DEBUG -XX:TieredStopAtLevel=1 -Xverify:none -Dspring.output.ansi.enabled=always -Dcom.sun.management.jmxremote -Dspring.jmx.enabled=true -Dspring.liveBeansView.mbeanDomain -Dspring.application.admin.enabled=true -Dmanagement.endpoints.jmx.exposure.include=* -javaagent:C:\Users\<USER>\AppData\Local\JetBrains\IntelliJIdea2024.3\captureAgent\debugger-agent.jar -Dkotlinx.coroutines.debug.enable.creation.stack.trace=false -Ddebugger.agent.enable.coroutines=true -Dkotlinx.coroutines.debug.enable.flows.stack.trace=true -Dkotlinx.coroutines.debug.enable.mutable.state.flows.stack.trace=true -Dfile.encoding=UTF-8 com.cloudstar.IamApplication

Host: AMD Ryzen 5 5600U with Radeon Graphics         , 12 cores, 15G,  Windows 10 , 64 bit Build 22621 (10.0.22621.457)
Time: Wed May 28 19:19:48 2025 �й���׼ʱ�� elapsed time: 450 seconds (0d 0h 7m 30s)

---------------  T H R E A D  ---------------

Current thread (0x000001d58261c800):  JavaThread "org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#1-162" [_thread_in_vm, id=30292, stack(0x000000c82d100000,0x000000c82d200000)]

Stack: [0x000000c82d100000,0x000000c82d200000],  sp=0x000000c82d1fbb20,  free space=1006k
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x38f6e3]
V  [jvm.dll+0x63acd2]
V  [jvm.dll+0x38f42e]
V  [jvm.dll+0x3e4947]
