package com.cloudstar.rightcloud.monitor.web.opsalarm.form;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 通知策略通知对象
 *
 * @author: 卢泳舟
 * @date: 2023/6/5 9:12
 */
@Data
public class OpsNotifyPolicyTargetForm {

    /**
     * 联系人类别
     */
    @NotBlank
    private String notifyContactsCategory;

    /**
     * 通知id根据类别区分是联系人组id还是联系人id
     */
    @NotEmpty
    private List<Long> notifyIds;

    /**
     * 通知方式
     */
    @NotEmpty
    private List<String> opsNotificationType;

}
