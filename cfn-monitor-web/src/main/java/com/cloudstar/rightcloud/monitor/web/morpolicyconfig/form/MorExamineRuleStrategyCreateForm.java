package com.cloudstar.rightcloud.monitor.web.morpolicyconfig.form;

import com.cloudstar.common.validated.safe.SafeHtml;
import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorExamineRuleResult;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;

import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 考核规则策略创建参数
 *
 * @author: hjy
 * @date: 2023/11/1 10:15
 */
@Data
public class MorExamineRuleStrategyCreateForm implements Serializable {

    /**
     * 考核类型(考核项)
     */
    @NotEmpty
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.EXAMINE_TYPE)
    @Length(max = 32)
    private String examineType;

    /**
     * 评分项
     */
    @NotEmpty
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.EXAMINE_ITEM)
    @Length(max = 32)
    private String examineItem;

    /**
     * 评分规则列表
     */
    @NotNull
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.EXAMINE_RULES)
    private List<MorExamineRuleResult> examineRules;

    /**
     * 记分模式
     */
    @Length(max = 32)
    private String  statisticType;

    /**
     * 描述
     */
    @SafeHtml
    @Length(max = 2048)
    private String description;

}
