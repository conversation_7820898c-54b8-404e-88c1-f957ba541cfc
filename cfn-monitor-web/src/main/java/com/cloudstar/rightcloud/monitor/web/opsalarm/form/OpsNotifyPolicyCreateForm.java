package com.cloudstar.rightcloud.monitor.web.opsalarm.form;


import org.hibernate.validator.constraints.Length;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

import lombok.Data;

/**
 * 创建通知策略详情
 *
 * @author: 卢泳舟
 * @date: 2023/6/5 9:11
 */
@Data
public class OpsNotifyPolicyCreateForm {

    /**
     * 通知策略名称
     */
    @NotBlank
    @Length(max = 64, min = 2)
    private String name;

    /**
     * 通知对象
     */
    @NotEmpty
    @Valid
    private List<OpsNotifyPolicyTargetForm> opsNotifyPolicyTargetForms;

    /**
     * 告警恢复通知状态
     */
    @NotBlank
    @Length(max = 50)
    private String alarmRestorationNotifyStatus;

    /**
     * 告警通知模版类型code
     */
    @NotBlank
    @Length(max = 255)
    private String alarmNotifyTemplateTypeCode;

    /**
     * 告警恢复模版类型code
     */
    @NotBlank
    @Length(max = 255)
    private String alarmRestorationTemplateTypeCode;

    /**
     * 告警通知开始时间段
     */
    @NotBlank
    private String alarmNotifyStartTimePeriod;

    /**
     * 告警通知结束时间段
     */
    @NotBlank
    private String alarmNotifyEndTimePeriod;

    /**
     * 重复通知状态
     */
    @NotBlank
    @Length(max = 50)
    private String repetitionNotifyStatus;

    /**
     * 重复通知间隔时间
     */
    private String repetitionNotifyIntervalTime;

    /**
     * 告警升级状态
     */
    private String alarmNotifyUpgradePolicyStatus;

    /**
     * 告警升级策略id
     */
    @Digits(integer = 22, fraction = 0)
    private Long alarmNotifyUpgradePolicyId;
}
