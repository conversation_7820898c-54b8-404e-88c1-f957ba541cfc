package com.cloudstar.rightcloud.monitor.web.opscollectrule.form;

import lombok.Data;

import java.io.Serializable;

/**
 * 监控指标查询条件参数
 *
 * @author: wanglang
 * @date: 2023/6/16 11:38
 */
@Data
public class OpsCollectMetricGetListForm implements Serializable {


    /**
     * 指标名称
     */
    private String name;

    /**
     * 状态
     */
    private String status;

    /**
     * 维度id
     */
    private Long opsCollectRuleDimensionId;

    /**
     * 资源类型
     */
    private String resTypeCode;

    /**
     * 云平台
     */
    private String envCode;

    /**
     * 云平台编码
     */
    private String envVersion;


    /**
     * 采集指标统一编码
     */
    private String unifiedCoding;


}
