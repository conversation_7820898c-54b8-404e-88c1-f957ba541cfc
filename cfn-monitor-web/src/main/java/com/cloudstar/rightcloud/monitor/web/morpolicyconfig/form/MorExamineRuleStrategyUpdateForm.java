package com.cloudstar.rightcloud.monitor.web.morpolicyconfig.form;

import com.cloudstar.common.validated.safe.SafeHtml;
import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorExamineRuleResult;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

/**
 * 考核规则策略修改参数
 *
 * @author: hjy
 * @date: 2023/11/1 16:15
 */
@Data
public class MorExamineRuleStrategyUpdateForm implements Serializable {

    /**
     * 考核类型(考核项)
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.EXAMINE_TYPE)
    @Length(max = 32)
    private String examineType;

    /**
     * 评分项
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.EXAMINE_ITEM)
    private String examineItem;

    /**
     * 评分规则列表
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.EXAMINE_RULES)
    private List<MorExamineRuleResult> examineRules;

    /**
     * 记分模式
     */
    private String  statisticType;

    /**
     * 描述
     */
    @SafeHtml
    @Length(max = 2048)
    private String description;

}
