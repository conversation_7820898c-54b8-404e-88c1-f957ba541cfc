package com.cloudstar.rightcloud.monitor.web.mortasklog.controller;

import cn.hutool.core.bean.BeanUtil;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.mortasklog.param.MorCollectTaskLogParams;
import com.cloudstar.rightcloud.monitor.client.mortasklog.result.MorCollectTaskLogListResult;
import com.cloudstar.rightcloud.monitor.client.mortasklog.service.MorCollectTaskLogService;
import com.cloudstar.rightcloud.monitor.web.mortasklog.form.MorCollectTaskLogForm;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/task/log")
@AllArgsConstructor
@Validated
public class MorCollectTaskLogController {

    private MorCollectTaskLogService taskLogService;


    /**
     * 获取监控采集日志
     */
    @GetMapping("/{id}")
    public RightCloudResult<MorCollectTaskLogListResult> lastExecuteLogs(@PathVariable @Validated Long id,
                                                                         @Validated MorCollectTaskLogForm form) {
        MorCollectTaskLogParams params = BeanUtil.copyProperties(form, MorCollectTaskLogParams.class);
        params.setTaskId(id);
        return taskLogService.getLastExecuteLogs(params);
    }

}
