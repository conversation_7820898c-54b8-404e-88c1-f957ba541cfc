package com.cloudstar.rightcloud.monitor.web.opsalarm.controller;

import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.log.common.annotation.OperationLog;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsOriginAlarmMetricCategoryCreateParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsOriginAlarmMetricCategoryTreeParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.param.OpsOriginAlarmMetricCategoryUpdateParam;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsOriginAlarmMetricCategoryDetailsResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.result.OpsOriginAlarmMetricCategoryTreeListResult;
import com.cloudstar.rightcloud.monitor.client.opsalarm.service.OpsOriginAlarmMetricCategoryService;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationMessageConstant;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationlogSourceConstant;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationlogTypeConstant;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import com.cloudstar.rightcloud.monitor.web.opsalarm.form.OpsOriginAlarmMetricCategoryCreateForm;
import com.cloudstar.rightcloud.monitor.web.opsalarm.form.OpsOriginAlarmMetricCategoryTreeFrom;
import com.cloudstar.rightcloud.monitor.web.opsalarm.form.OpsOriginAlarmMetricCategoryUpdateForm;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 原生告警指标分类
 *
 * @author: wanglang
 * @date: 2023/7/31 11:48
 */
@RestController
@RequestMapping("/alarm/metric/category")
@AllArgsConstructor
@Validated
public class OpsOriginAlarmMetricCategoryController {

    private final OpsOriginAlarmMetricCategoryService opsOriginAlarmMetricCategoryService;

    /**
     * 创建原始告警指标分类[内部]
     *
     * @param form 原生指标分类数据
     * @return Long 指标分类id
     */
    @PostMapping
    @OperationLog(type = MonitorOperationlogTypeConstant.CREATE_METRIC_CATEGORY, objectName = "#form.name",
            resource = MonitorOperationlogSourceConstant.METRIC_CATEGORY,
            msg = MonitorOperationMessageConstant.ACTION_METRIC_CATEGORY_CREATE, msgParams = {"#form.name"})
    public RightCloudResult<Long> createOriginAlarmMetricCategory(@RequestBody @Validated OpsOriginAlarmMetricCategoryCreateForm form) {
        final OpsOriginAlarmMetricCategoryCreateParam opsOriginAlarmMetricCategoryCreateParam =
                BeanHelperUtil.copyForBean(OpsOriginAlarmMetricCategoryCreateParam::new, form);
        return opsOriginAlarmMetricCategoryService.createOriginAlarmMetricCategory(opsOriginAlarmMetricCategoryCreateParam);
    }

    /**
     * 编辑原始告警指标分类[内部]
     *
     * @param id   id
     * @param form 原生指标分类数据
     * @return
     */
    @PutMapping("/{id}")
    @OperationLog(type = MonitorOperationlogTypeConstant.UPDATE_METRIC_CATEGORY, objectName = "#form.name", objectId = "#id",
            resource = MonitorOperationlogSourceConstant.ALARM_METRIC_MANAGER,
            msg = MonitorOperationMessageConstant.ACTION_METRIC_CATEGORY_UPDATE, msgParams = {"#id"})
    public RightCloudResult<Boolean> updateOriginAlarmMetricCategory(@PathVariable("id") @NotNull Long id,
                                                                     @RequestBody @Validated OpsOriginAlarmMetricCategoryUpdateForm form) {
        final OpsOriginAlarmMetricCategoryUpdateParam opsOriginAlarmMetricCategoryCreateParam =
                BeanHelperUtil.copyForBean(OpsOriginAlarmMetricCategoryUpdateParam::new, form);
        opsOriginAlarmMetricCategoryCreateParam.setId(id);
        return opsOriginAlarmMetricCategoryService.updateOriginAlarmMetricCategory(opsOriginAlarmMetricCategoryCreateParam);
    }

    /**
     * 删除原始告警指标分类[内部]
     *
     * @param id id
     * @return true 删除成功 false 删除失败
     */
    @DeleteMapping("/{id}")
    @OperationLog(type = MonitorOperationlogTypeConstant.DELETE_METRIC_CATEGORY,
            objectId = "#id", objectName = "delete Origin Alarm MetricCategory",
            resource = MonitorOperationlogSourceConstant.ALARM_METRIC_MANAGER,
            msg = MonitorOperationMessageConstant.ACTION_METRIC_CATEGORY_DELETE, msgParams = {"#id"})
    public RightCloudResult<Boolean> deleteOriginAlarmMetricCategory(@PathVariable("id")
                                                                     @Validated
                                                                     @NotNull
                                                                     @I18nProperty(propertyKey = MonitorFieldKeyConstant.ID)
                                                                     Long id) {
        return opsOriginAlarmMetricCategoryService.deleteOriginAlarmMetricCategory(id);
    }

    /**
     * 查询原生告警指标分类详情[内部]
     *
     * @param id id
     * @return OpsOriginAlarmMetricCategoryDetailsResult 原生告警指标分类详情数据
     */
    @GetMapping("/{id}")
    public RightCloudResult<OpsOriginAlarmMetricCategoryDetailsResult> getOriginAlarmMetricCategoryDetails(@PathVariable("id")
                                                                                                           @Validated
                                                                                                           @I18nProperty(propertyKey =
                                                                                                                   MonitorFieldKeyConstant.ID)
                                                                                                           Long id) {
        return opsOriginAlarmMetricCategoryService.getOriginAlarmMetricCategoryDetails(id);
    }

    /**
     * 查询原始告警指标分类列表[内部]
     *
     * @param from 条件参数
     * @return OpsOriginAlarmMetricCategoryTreeListResult 告警指标分类数据
     */
    @GetMapping
    public RightCloudResult<List<OpsOriginAlarmMetricCategoryTreeListResult>> getOriginAlarmMetricCategoryList(OpsOriginAlarmMetricCategoryTreeFrom
                                                                                                                       from) {
        final OpsOriginAlarmMetricCategoryTreeParam opsOriginAlarmMetricCategoryTreeParam =
                BeanHelperUtil.copyForBean(OpsOriginAlarmMetricCategoryTreeParam::new, from);
        return opsOriginAlarmMetricCategoryService.getOriginAlarmMetricCategoryList(opsOriginAlarmMetricCategoryTreeParam);
    }


}
