package com.cloudstar.rightcloud.monitor.web.moranalysis.controller;

import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.log.common.annotation.OperationLog;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorAnalysisGetHideHeaderGeParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorOrgResAnalysisExportParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorOrgResAnalysisPageParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorProjectResAnalysisExportParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorProjectResAnalysisPageParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorResPoolAnalysisExportParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorResPoolAnalysisPageParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorResourceAnalysisExportParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.param.MorResourceAnalysisPageParam;
import com.cloudstar.rightcloud.monitor.client.moranalysis.result.MorAnalysisGetHideHeaderResult;
import com.cloudstar.rightcloud.monitor.client.moranalysis.result.MorOrgResAnalysisPageResult;
import com.cloudstar.rightcloud.monitor.client.moranalysis.result.MorProjectResAnalysisPageResult;
import com.cloudstar.rightcloud.monitor.client.moranalysis.result.MorResPoolAnalysisPageResult;
import com.cloudstar.rightcloud.monitor.client.moranalysis.result.MorResourceAnalysisPageResult;
import com.cloudstar.rightcloud.monitor.client.moranalysis.service.MorCloudResAnalysisService;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationMessageConstant;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationlogSourceConstant;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationlogTypeConstant;
import com.cloudstar.rightcloud.monitor.web.moranalysis.form.MorAnalysisGetHideHeaderGeForm;
import com.cloudstar.rightcloud.monitor.web.moranalysis.form.MorOrgResAnalysisExportForm;
import com.cloudstar.rightcloud.monitor.web.moranalysis.form.MorOrgResAnalysisPageForm;
import com.cloudstar.rightcloud.monitor.web.moranalysis.form.MorProjectResAnalysisExportForm;
import com.cloudstar.rightcloud.monitor.web.moranalysis.form.MorProjectResAnalysisPageForm;
import com.cloudstar.rightcloud.monitor.web.moranalysis.form.MorResPoolAnalysisExportForm;
import com.cloudstar.rightcloud.monitor.web.moranalysis.form.MorResPoolAnalysisPageForm;
import com.cloudstar.rightcloud.monitor.web.moranalysis.form.MorResourceAnalysisExportForm;
import com.cloudstar.rightcloud.monitor.web.moranalysis.form.MorResourceAnalysisPageForm;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.bean.BeanUtil;
import lombok.AllArgsConstructor;

/**
 * 云资源分析
 *
 * @author: hjy
 * @date: 2023/11/23 11:25
 */
@RestController
@AllArgsConstructor
@RequestMapping("/res/analysis")
public class MorCloudAnalysisController {

    private final MorCloudResAnalysisService cloudResAnalysisService;

    /**
     * 查询组织维度云资源分析
     */
    @GetMapping("/org")
    public RightCloudResult<MorOrgResAnalysisPageResult> getOrgCloudResAnalysis(
            @Validated MorOrgResAnalysisPageForm form) {
        MorOrgResAnalysisPageParam param = BeanUtil.copyProperties(form, MorOrgResAnalysisPageParam.class);
        return this.cloudResAnalysisService.getOrgCloudResAnalysis(param);
    }

    /**
     * 组织维度性能统计
     */
    @GetMapping("/org/list")
    public RightCloudResult<List<Map<String, Object>>> getOrgCloudResAnalysisList(@Validated MorOrgResAnalysisPageForm form) {
        MorOrgResAnalysisPageParam param = BeanUtil.copyProperties(form, MorOrgResAnalysisPageParam.class);
        return this.cloudResAnalysisService.getOrgCloudResAnalysisList(param);
    }

    /**
     * 导出组织维度云资源分析
     *
     * @param form     组织维度性能统计导出入参
     * @param response HttpServletResponse
     */
    @GetMapping("/org/export")
    @OperationLog(type = MonitorOperationlogTypeConstant.EXPORT_ORG_ANALYSIS,
            objectName = "云资源分析-组织维度列表",
            resource = MonitorOperationlogSourceConstant.ORG_ANALYSIS,
            msg = MonitorOperationMessageConstant.ACTION_ORG_ANALYSIS_EXPORT)
    public RightCloudResult<Void> exportOrgCloudResAnalysis(
            @Validated MorOrgResAnalysisExportForm form, HttpServletResponse response) {
        MorOrgResAnalysisExportParam param = BeanUtil.copyProperties(form, MorOrgResAnalysisExportParam.class);
        return this.cloudResAnalysisService.exportOrgCloudResAnalysis(param, response);
    }

    /**
     * 查询项目维度云资源分析
     */
    @GetMapping("/project")
    public RightCloudResult<MorProjectResAnalysisPageResult> getProjectCloudResAnalysis(
            @Validated MorProjectResAnalysisPageForm form) {
        MorProjectResAnalysisPageParam param = BeanUtil.copyProperties(form, MorProjectResAnalysisPageParam.class);

        RightCloudResult<MorProjectResAnalysisPageResult> statistics =
                cloudResAnalysisService.getProjectCloudResAnalysis(param);

        return statistics;
    }

    /**
     * 项目性能统计
     */
    @GetMapping("/project/list")
    public RightCloudResult<List<Map<String, Object>>> getProjectCloudResAnalysisList(@Validated MorProjectResAnalysisPageForm form) {
        MorProjectResAnalysisPageParam param = BeanUtil.copyProperties(form, MorProjectResAnalysisPageParam.class);
        return cloudResAnalysisService.getProjectCloudResAnalysisList(param);
    }

    /**
     * 导出项目维度云资源分析
     *
     * @param form     项目维度性能统计导出入参
     * @param response HttpServletResponse
     */
    @GetMapping("/project/export")
    @OperationLog(type = MonitorOperationlogTypeConstant.EXPORT_PROJECT_ANALYSIS,
            objectName = "云资源分析-项目维度列表",
            resource = MonitorOperationlogSourceConstant.PROJECT_ANALYSIS,
            msg = MonitorOperationMessageConstant.ACTION_PROJECT_ANALYSIS_EXPORT)
    public RightCloudResult<Void> exportProjectCloudResAnalysis(@Validated MorProjectResAnalysisExportForm form,
                                                                HttpServletResponse response) {
        MorProjectResAnalysisExportParam param = BeanUtil.copyProperties(form, MorProjectResAnalysisExportParam.class);
        return this.cloudResAnalysisService.exportProjectCloudResAnalysis(param, response);
    }

    /**
     * 查询资源维度云资源分析
     */
    @GetMapping("/summary")
    public RightCloudResult<MorResourceAnalysisPageResult> getResourceCloudResAnalysis(
            @Validated MorResourceAnalysisPageForm form) {
        MorResourceAnalysisPageParam param = BeanUtil.copyProperties(form, MorResourceAnalysisPageParam.class);

        RightCloudResult<MorResourceAnalysisPageResult> statistics =
                cloudResAnalysisService.getResourceCloudResAnalysis(param);

        return statistics;
    }

    /**
     * 导出资源维度云资源分析
     *
     * @param form     资源维度性能统计导出入参
     * @param response HttpServletResponse
     */
    @GetMapping("/summary/export")
    @OperationLog(type = MonitorOperationlogTypeConstant.EXPORT_RESOURCE_ANALYSIS,
            objectName = "云资源分析-资源维度列表",
            resource = MonitorOperationlogSourceConstant.RESOURCE_ANALYSIS,
            msg = MonitorOperationMessageConstant.ACTION_RESOURCE_ANALYSIS_EXPORT)
    public RightCloudResult<Void> exportResourceCloudResAnalysis(@Validated MorResourceAnalysisExportForm form,
                                                                 HttpServletResponse response) {
        MorResourceAnalysisExportParam param = BeanUtil.copyProperties(form, MorResourceAnalysisExportParam.class);
        return this.cloudResAnalysisService.exportResourceCloudResAnalysis(param, response);
    }

    // /**资源闲置率统计*/
    //@GetMapping("/idle")
    //public RightCloudResult<MorResourceIdlePageResult> getResourceIdle(
    //        MorResourceIdlePageForm form) {
    //    MorResourceIdlePageParam param = BeanUtil.copyProperties(form, MorResourceIdlePageParam.class);

    //    RightCloudResult<MorResourceIdlePageResult> statistics =
    //            cloudResAnalysisService.getResourceIdle(param);

    //    return statistics;
    //}

    /**
     * 资源池维度性能统计
     */
    @GetMapping("/pool")
    public RightCloudResult<MorResPoolAnalysisPageResult> getResourcePoolCloudAnalysis(
            @Validated MorResPoolAnalysisPageForm form) {
        MorResPoolAnalysisPageParam param = BeanUtil.copyProperties(form, MorResPoolAnalysisPageParam.class);

        RightCloudResult<MorResPoolAnalysisPageResult> statistics =
                cloudResAnalysisService.getResourcePoolAnalysis(param);

        return statistics;
    }

    /**
     * 导出资源池维度云资源分析
     *
     * @param form     资源池维度性能统计导出入参
     * @param response HttpServletResponse
     */
    @GetMapping("/pool/export")
    @OperationLog(type = MonitorOperationlogTypeConstant.EXPORT_RES_POOL_ANALYSIS,
            objectName = "云资源分析-资源池维度列表",
            resource = MonitorOperationlogSourceConstant.RES_POOL_ANALYSIS,
            msg = MonitorOperationMessageConstant.ACTION_RES_POOL_ANALYSIS_EXPORT)
    public RightCloudResult<Void> exportResPoolCloudResAnalysis(@Validated MorResPoolAnalysisExportForm form,
                                                                HttpServletResponse response) {
        MorResPoolAnalysisExportParam param = BeanUtil.copyProperties(form, MorResPoolAnalysisExportParam.class);
        return this.cloudResAnalysisService.exportResPoolCloudResAnalysis(param, response);
    }

    /**
     * 获取隐藏header
     *
     * @param form 请求参数
     * @return MorAnalysisGetHideHeaderResult header数据
     */
    @PostMapping("/hide/header")
    public RightCloudResult<List<MorAnalysisGetHideHeaderResult>>  getHideHeaderList(@RequestBody MorAnalysisGetHideHeaderGeForm form) {
        MorAnalysisGetHideHeaderGeParam morAnalysisGetHideHeaderGeParam = BeanHelperUtil.copyForBean(MorAnalysisGetHideHeaderGeParam::new, form);
        return this.cloudResAnalysisService.getHideHeaderList(morAnalysisGetHideHeaderGeParam);
    }


    /**
     * 组织项目性能统计
     */
    @GetMapping("/org_union_project/list")
    public RightCloudResult<List<Map<String, Object>>> getOrgUnionProjectResAnalysisList(@Validated MorOrgResAnalysisPageForm form) {
        MorOrgResAnalysisPageParam param = BeanUtil.copyProperties(form, MorOrgResAnalysisPageParam.class);
        return cloudResAnalysisService.getOrgUnionProjectResAnalysisList(param);
    }
}
