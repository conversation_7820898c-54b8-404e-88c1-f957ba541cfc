package com.cloudstar.rightcloud.monitor.web.morpolicyconfig.controller;

import cn.hutool.core.bean.BeanUtil;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.log.common.annotation.OperationLog;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MonHourStatisticsStrategyTaskParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorHourStatisticsMetricPageParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorHourStatisticsMetricStrategyCreateParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorHourStatisticsMetricStrategyUpdateParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.param.MorHourStatisticsMetricStrategyUpdateStatusParam;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorHourStatisticsMetricStrategyDetailResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.result.MorHourStatisticsMetricStrategyPageResult;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.service.MorPerformanceAnalysisHourPolicyService;
import com.cloudstar.rightcloud.monitor.client.morpolicyconfig.service.MorStatisticsHourMetricStrategyService;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationMessageConstant;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationlogSourceConstant;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationlogTypeConstant;
import com.cloudstar.rightcloud.monitor.web.morpolicyconfig.form.MonHourStatisticsStrategyTaskForm;
import com.cloudstar.rightcloud.monitor.web.morpolicyconfig.form.MorHourStatisticsMetricStrategyCreateForm;
import com.cloudstar.rightcloud.monitor.web.morpolicyconfig.form.MorHourStatisticsMetricStrategyPageForm;
import com.cloudstar.rightcloud.monitor.web.morpolicyconfig.form.MorHourStatisticsMetricStrategyUpdateForm;
import com.cloudstar.rightcloud.monitor.web.morpolicyconfig.form.MorHourStatisticsMetricStrategyUpdateStatusForm;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 小时性能汇总指标策略配置
 *
 * @author: wanglang
 * @date: 2023/11/3 17:05
 */
@RestController
@RequestMapping("/performance/metric/aggregate/policy/hour")
@AllArgsConstructor
@Validated
public class MorStatisticsHourMetricStrategyController {


    private final MorStatisticsHourMetricStrategyService morStatisticsHourMetricStrategyService;


    private final MorPerformanceAnalysisHourPolicyService morPerformanceAnalysisHourPolicyService;

    /**
     * 分页查询小时性能汇总指标策略
     *
     * @param form 查询参数
     * @return 小时性能汇总指标策略分页数据
     */
    @GetMapping
    public RightCloudResult<PageResult<MorHourStatisticsMetricStrategyPageResult>> getPage(
            @Validated MorHourStatisticsMetricStrategyPageForm form) {
        MorHourStatisticsMetricPageParam param = BeanUtil.copyProperties(form,
                MorHourStatisticsMetricPageParam.class);
        return morStatisticsHourMetricStrategyService.getPage(param);
    }

    /**
     * 查询小时性能汇总指标策略详情信息
     *
     * @param id id
     */
    @GetMapping("/{id}")
    public RightCloudResult<MorHourStatisticsMetricStrategyDetailResult> getDetail(
            @PathVariable(value = "id") @NotNull Long id) {
        return morStatisticsHourMetricStrategyService.getDetail(id);
    }

    /**
     * 创建小时性能汇总指标策略
     *
     * @param form 小时性能汇总指标策略参数
     */
    @PostMapping
    @OperationLog(type = MonitorOperationlogTypeConstant.CREATE_HOUR_METRIC,
            objectName = "#form.resTypeCode",
            resource = MonitorOperationlogSourceConstant.HOUR_METRIC,
            msg = MonitorOperationMessageConstant.ACTION_HOUR_METRIC_CREATE, msgParams = {"#form.resTypeCode"})
    public RightCloudResult<Long> create(@RequestBody @Validated MorHourStatisticsMetricStrategyCreateForm form) {
        return morStatisticsHourMetricStrategyService.create(
                BeanUtil.copyProperties(form, MorHourStatisticsMetricStrategyCreateParam.class));
    }

    /**
     * 修改小时性能汇总指标策略
     *
     * @param updateFrom 修改参数
     */
    @PutMapping("/{id}")
    @OperationLog(type = MonitorOperationlogTypeConstant.UPDATE_HOUR_METRIC, objectId = "#id",
            resource = MonitorOperationlogSourceConstant.HOUR_METRIC,
            msg = MonitorOperationMessageConstant.ACTION_HOUR_METRIC_UPDATE, msgParams = {"#id"})
    public RightCloudResult<Void> update(@PathVariable(value = "id") @NotNull Long id,
                                         @RequestBody @Validated MorHourStatisticsMetricStrategyUpdateForm updateFrom) {
        MorHourStatisticsMetricStrategyUpdateParam updateParam = BeanUtil
                .copyProperties(updateFrom, MorHourStatisticsMetricStrategyUpdateParam.class);
        updateParam.setId(id);
        return morStatisticsHourMetricStrategyService.update(updateParam);
    }

    /**
     * 启用/禁用小时性能汇总指标策略
     *
     * @param updateStatusForm 修改参数
     */
    @PutMapping("/status/{id}")
    @OperationLog(type = MonitorOperationlogTypeConstant.UPDATE_HOUR_METRIC_STATUS, objectId = "#id",
            resource = MonitorOperationlogSourceConstant.HOUR_METRIC,
            msg = MonitorOperationMessageConstant.ACTION_HOUR_METRIC_STATUS, msgParams = {"#id"})
    public RightCloudResult<Void> updateStatus(@PathVariable(value = "id") @NotNull Long id,
                                               @RequestBody @Validated MorHourStatisticsMetricStrategyUpdateStatusForm
                                                       updateStatusForm) {
        MorHourStatisticsMetricStrategyUpdateStatusParam updateStatusParam = BeanUtil
                .copyProperties(updateStatusForm, MorHourStatisticsMetricStrategyUpdateStatusParam.class);
        updateStatusParam.setId(id);
        return morStatisticsHourMetricStrategyService.updateStatus(updateStatusParam);
    }

    /**
     * 删除小时性能汇总指标策略
     */
    @DeleteMapping("/{id}")
    @OperationLog(type = MonitorOperationlogTypeConstant.DELETE_HOUR_METRIC, objectId = "#id",
            resource = MonitorOperationlogSourceConstant.HOUR_METRIC,
            msg = MonitorOperationMessageConstant.ACTION_HOUR_METRIC_DELETE, msgParams = {"#id"})
    public RightCloudResult<Long> delete(@PathVariable @NotNull Long id) {
        morStatisticsHourMetricStrategyService.delete(id);
        return RightCloudResult.success();
    }

    /**
     * 执行小时分析汇总任务
     *
     * @param form 任务参数
     * @return 操作结果
     */
    @PutMapping("/execute/task")
    @OperationLog(type = MonitorOperationlogTypeConstant.EXECUTE_TOTAL_TASK_HOUR_METRIC,
            objectName = "#form.resTypeCode",
            resource = MonitorOperationlogSourceConstant.HOUR_METRIC,
            msg = MonitorOperationMessageConstant.ACTION_HOUR_METRIC_EXECUTE_TOTAL_TASK,
            msgParams = {"#form.resTypeCode"})
    public RightCloudResult<Boolean> executeHourTak(@RequestBody @Validated MonHourStatisticsStrategyTaskForm form) {
        final MonHourStatisticsStrategyTaskParam param = BeanHelperUtil.copyForBean(
                MonHourStatisticsStrategyTaskParam::new,
                form
        );
        morPerformanceAnalysisHourPolicyService.executeHourTak(param);
        return RightCloudResult.success(true);
    }

    /**
     * 执行小时分析汇任务
     *
     * @param id 任务id
     * @return 操作结果
     */
    @PutMapping("/execute/task/{id}")
    @OperationLog(type = MonitorOperationlogTypeConstant.EXECUTE_TASK_HOUR_METRIC, objectId = "#id",
            resource = MonitorOperationlogSourceConstant.HOUR_METRIC,
            msg = MonitorOperationMessageConstant.ACTION_HOUR_METRIC_EXECUTE_TASK, msgParams = {"#id"})
    public RightCloudResult<Boolean> executeHourTakById(@PathVariable("id") @NotNull Long id) {
        return morPerformanceAnalysisHourPolicyService.executeHourTakById(id);
    }

    /**
     * 查询天汇总任务执行状态
     *
     * @return String 任务执行状态
     */
    @GetMapping("/execute/task/status")
    public RightCloudResult<Boolean> getExecuteHourTakStatus() {
        return morPerformanceAnalysisHourPolicyService.getExecuteHourTakStatus();
    }
}
