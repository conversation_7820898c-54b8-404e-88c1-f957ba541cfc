package com.cloudstar.rightcloud.monitor.web.opsexporter.form;

import com.cloudstar.common.validated.safe.SafeHtml;
import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Digits;
import java.io.Serializable;
import java.util.List;

/**
 * 采集器修改参数
 *
 * @author: wanglang
 * @date: 2023/6/19 15:22
 */
@Data
public class OpsExporterUpdateForm implements Serializable {

    /**
     * 组件名称
     */
    @SafeHtml
    @Length(max = 255)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.EXPORTER_COMPONENT_NAME)
    private String name;

    /**
     * exporter名称
     */
    @SafeHtml
    @Length(max = 255)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.EXPORTER_NAME)
    private String exporterName;

    /**
     * 采集频率（秒）
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.EXPORTER_COLLECT_INTERVAL)
    private String collectInterval;

    /**
     * 超时时间
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.EXPORTER_TIMEOUT)
    private String timeout;

    /**
     * 云平台
     */
    private String envCode;

    /**
     * 云平台版本号
     */
    private String envVersion;

    /**
     * 描述
     */
    @SafeHtml
    @Length(max = 255)
    private String description;

    /**
     * 采集器名称
     */
    @Length(max = 255)
    private String collectorName;

    /**
     * 采集器id
     */
    @Digits(integer = 22, fraction = 0)
    private Long collectorId;

    /**
     * 云资源类型
     */
    private List<String> resTypeCodes;

    /**
     * 采集路径
     */
    @SafeHtml
    @Length(max = 255)
    private String collectorPath;

    /**
     * 类型
     */
    private String type;

    /**
     * exporter修改参数
     */
    private List<OpsExporterUpdateParamsForm> exporterUpdateParamsForms;


}
