package com.cloudstar.rightcloud.monitor.web.opscollectrule.form;

import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import lombok.Data;

import java.io.Serializable;

/**
 * 采集规则维度创建参数
 *
 * @author: wanglang
 * @date: 2023/7/19 17:39
 */
@Data
public class OpsCollectRuleDimensionsConditionCreateForm implements Serializable {
    /**
     * 资源实例编码
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_DIMENSION_CONDITION_RES_TYPE_INSTANCE_CODE)
    private String resTypeInstanceCode;
    /**
     * 运算符;gt > ,ge >= ,lt< ,le <=, equals =, not_equals!=
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_DIMENSION_CONDITION_OPERATOR)
    private String operator;
    /**
     * 比对值
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_DIMENSION_CONDITION_ALIGNMENT_VALUE)
    private String alignmentValue;
    /**
     * 逻辑运算符;&& 并且 ||或者
     */
    private String logicOperator;


}
