package com.cloudstar.rightcloud.monitor.web.opsviews;

import com.cloudstar.rightcloud.common.constant.message.CommonMsgConstant;
import com.cloudstar.rightcloud.common.pojo.page.PageResult;
import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.common.utils.base.BeanHelperUtil;
import com.cloudstar.rightcloud.data.util.IdWorker;
import com.cloudstar.rightcloud.log.common.annotation.OperationLog;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.MorAlarmResTypeTrendStatisticParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.param.MorViewsResAlarmStatisticDataParam;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.MorViewsAlarmCountTopResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.MorViewsAlarmEnvLevelStatisticResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.MorViewsAlarmEnvTrendStatisticResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.MorViewsAlarmResTypeCountStatisticResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.MorViewsAlarmResTypeTrendStatisticInfoResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.MorViewsAlarmResTypeTrendStatisticResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.MorViewsAlarmStatisticTotalResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.MorViewsAlarmTrendLevelResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.MorViewsLastAlarmResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.result.MorViewsResAlarmStatisticDataResult;
import com.cloudstar.rightcloud.monitor.client.opsviews.service.MorViewsAlarmService;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationMessageConstant;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationlogSourceConstant;
import com.cloudstar.rightcloud.monitor.common.constant.log.MonitorOperationlogTypeConstant;
import com.cloudstar.rightcloud.monitor.common.em.MorTimeStatistic;
import com.cloudstar.rightcloud.monitor.common.em.MorTopStatistic;
import com.cloudstar.rightcloud.monitor.web.opsviews.form.MorAlarmResTypeTrendStatisticForm;
import com.cloudstar.rightcloud.monitor.web.opsviews.form.MorViewsResAlarmStatisticDataForm;
import lombok.AllArgsConstructor;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Locale;
import java.util.Objects;

/**
 * 告警报表统计接口
 *
 * @author: wanglang
 * @date: 2023/8/18 14:18
 */
@RestController
@RequestMapping("/views/alarm/statistic")
@AllArgsConstructor
public class MorViewsAlarmController {

    private final MorViewsAlarmService morViewsAlarmService;

    /**
     * 查询最新告警信息
     *
     * @param time 查询时间单位 :1w、1d、10m {@link MorTimeStatistic} {@link MorTimeStatistic#WEEK}
     */
    @GetMapping("/last")
    public RightCloudResult<List<MorViewsLastAlarmResult>> getMorViewsLastAlarmGetListResult(String time) {
        return morViewsAlarmService.getMorViewsLastAlarmGetListResult(time);
    }


    /**
     * 按照告警等级统计告警数量
     *
     * @param time 查询时间单位 :1w、1d、10m {@link MorTimeStatistic} {@link MorTimeStatistic#WEEK}
     */
    @GetMapping("/count")
    public RightCloudResult<MorViewsAlarmStatisticTotalResult> getMorViewsAlarmStatisticTotalGetResult(String time) {
        return morViewsAlarmService.getMorViewsAlarmStatisticTotalGetResult(time);
    }


    /**
     * 按照告警等级统计告警趋势
     *
     * @param time 查询时间单位 :1w、1d、10m {@link MorTimeStatistic} {@link MorTimeStatistic#WEEK}
     */
    @GetMapping("/trend")
    public RightCloudResult<List<MorViewsAlarmTrendLevelResult>> getMorViewsAlarmTrendLevelGetListResult(String time) {
        return morViewsAlarmService.getMorViewsAlarmTrendLevelGetListResult(time);
    }


    /**
     * 按照top统计告警次数展示
     *
     * @param top top {@link MorTopStatistic} {@link MorTopStatistic#TOP5}
     */
    @GetMapping("/top")
    public RightCloudResult<List<MorViewsAlarmCountTopResult>> getMorViewsAlarmCountTopListGetResult(Integer top) {
        return morViewsAlarmService.getMorViewsAlarmCountTopListGetResult(top);
    }

    /**
     * 按照云环境类型统计告警等级
     *
     * @param time 查询时间单位 :1w、1d、10m {@link MorTimeStatistic} {@link MorTimeStatistic#WEEK}
     */
    @GetMapping("/env/level")
    public RightCloudResult<List<MorViewsAlarmEnvLevelStatisticResult>> getMorViewsAlarmEnvLevelStatisticGetListResult(String time) {
        return morViewsAlarmService.getMorViewsAlarmEnvLevelStatisticGetListResult(time);
    }

    /**
     * 按照云环境类型统计告警趋势
     *
     * @param time 查询时间单位 :1w、1d、10m {@link MorTimeStatistic} {@link MorTimeStatistic#WEEK}
     */
    @GetMapping("/env/trend")
    public RightCloudResult<List<MorViewsAlarmEnvTrendStatisticResult>> getMorViewsAlarmEnvTrendStatisticGetListResult(String time) {
        return morViewsAlarmService.getMorViewsAlarmEnvTrendStatisticGetListResult(time);
    }

    /**
     * 按照资源类型统计告警趋势
     *
     * @param form 告警查询参数
     */
    @GetMapping("/res_type/trend")
    public RightCloudResult<List<MorViewsAlarmResTypeTrendStatisticResult>> getMorViewsAlarmResTypeTrendStatisticGetListResult(
            MorAlarmResTypeTrendStatisticForm form) {
        return morViewsAlarmService.getMorViewsAlarmResTypeTrendStatisticGetListResult(BeanHelperUtil
                .copyForBean(MorAlarmResTypeTrendStatisticParam::new, form));
    }


    /**
     * 按照资源类型统计告警数量
     *
     * @param time 查询时间单位 :1w、1d、10m {@link MorTimeStatistic} {@link MorTimeStatistic#WEEK}
     */
    @GetMapping("/res_type/count")
    public RightCloudResult<List<MorViewsAlarmResTypeCountStatisticResult>> getMorViewsAlarmResTypeCountStatisticGetListResult(String time) {
        return morViewsAlarmService.getMorViewsAlarmResTypeCountStatisticGetListResult(time);
    }


    /**
     * 按照查询条件查询告警列表数据
     *
     * @param form 查询条件
     */
    @GetMapping
    public RightCloudResult<PageResult<MorViewsResAlarmStatisticDataResult>> getResStatGetListResult(MorViewsResAlarmStatisticDataForm form) {
        return morViewsAlarmService.getResStatGetListResult(BeanHelperUtil.copyForBean(MorViewsResAlarmStatisticDataParam.class, form));
    }

    /**
     * 按照资源类型统计告警趋势资源信息
     */
    @GetMapping("/types")
    public RightCloudResult<List<MorViewsAlarmResTypeTrendStatisticInfoResult>> getMorViewsAlarmResTypeTrendStatisticInfoGetListResult() {
        return morViewsAlarmService.getMorViewsAlarmResTypeTrendStatisticInfoGetListResult();
    }

    /**
     * 导出告警数据
     *
     * @param resAlarmStatisticDataForm 导出告警数据条件
     * @return
     */
    @GetMapping("/export")
    @OperationLog(type = MonitorOperationlogTypeConstant.EXPORT_ALARM_DATA, objectName = "alarmData",
            resource = MonitorOperationlogSourceConstant.ALARM_VIEW_DATA,
            msg = MonitorOperationMessageConstant.ACTION_ALARM_DATA_EXPORT)
    public RightCloudResult<Void> exportAlarmData(MorViewsResAlarmStatisticDataForm resAlarmStatisticDataForm) {
        final MorViewsResAlarmStatisticDataParam morViewsResAlarmStatisticDataParam =
                BeanHelperUtil.copyForBean(MorViewsResAlarmStatisticDataParam::new, resAlarmStatisticDataForm);
        // 导出生成任务
        final RightCloudResult<Long> exportAlarmDataCount = morViewsAlarmService.getExportAlarmDataCount(morViewsResAlarmStatisticDataParam);
        if (Objects.nonNull(exportAlarmDataCount)
                && Objects.nonNull(exportAlarmDataCount.getData())
                && exportAlarmDataCount.getData() > 0) {
            final RightCloudResult<Long> longRightCloudResult = morViewsAlarmService.exportAlarmData();
            if (Objects.nonNull(longRightCloudResult) && Objects.nonNull(longRightCloudResult.getData())) {
                final Long taskId = longRightCloudResult.getData();
                morViewsResAlarmStatisticDataParam.setTaskId(taskId);
                Locale currentLocaleLanguage = LocaleContextHolder.getLocale();
                morViewsAlarmService.exportAsyncAlarmData(morViewsResAlarmStatisticDataParam, currentLocaleLanguage);
            } else {
                return RightCloudResult.fail();
            }
        } else {
            return RightCloudResult.success(CommonMsgConstant.COMMON_ERROR_HANDLE_OUTPUTFAIL, null);
        }
        return RightCloudResult.success(CommonMsgConstant.COMMON_EXPORT_HANDLE_SUCCESS, null);
    }


    public static void main(String[] args) {
        for (int i = 0; i < 10; i++) {
            System.out.println(IdWorker.generateId());

        }
    }

}
