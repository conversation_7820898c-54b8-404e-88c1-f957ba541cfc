package com.cloudstar.rightcloud.monitor.web.mordashboardcategoryrela.controller;

import com.cloudstar.rightcloud.common.pojo.result.RightCloudResult;
import com.cloudstar.rightcloud.monitor.client.mordashboardcategoryrela.result.MorDashboardCategoryRelaListResult;
import com.cloudstar.rightcloud.monitor.client.mordashboardcategoryrela.service.MorDashboardCategoryRelaService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 数据源接口
 *
 * @author: wanglang
 * @date: 2024/4/15 15:10
 */
@RestController
@RequestMapping("/dashboard/category/rela")
@AllArgsConstructor
public class MorDashboardCategoryRelaController {

    private final MorDashboardCategoryRelaService morDatasourceService;

    /**
     * 如果后续需要支持云环境为空，则 envCode 传入 - ，后面逻辑加上这个条件判断
     * <AUTHOR>
     * @date 2024/12/3 18:20
     */
    @GetMapping("/listByTag/{envCode}/{categoryCode}/{tag}")
    public RightCloudResult<List<MorDashboardCategoryRelaListResult>> listByTag(@PathVariable("envCode") String envCode,
                                                                                @PathVariable("categoryCode") String categoryCode,
                                                                                @PathVariable("tag") String tag) {
        return morDatasourceService.selectByTag(envCode, categoryCode, tag);
    }

    @GetMapping("/listByParam")
    public RightCloudResult<List<MorDashboardCategoryRelaListResult>> listByParam(String envCode, String categoryCode, String tag) {
        return morDatasourceService.selectByTag(envCode, categoryCode, tag);
    }

}

