package com.cloudstar.rightcloud.monitor.web.opscollectrule.form;


import com.cloudstar.common.validated.safe.SafeHtml;
import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;

import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import lombok.Data;

/**
 * 采集规则维度创建参数
 *
 * @author: wanglang
 * @date: 2023/7/20 20:02
 */
@Data
public class OpsCollectRuleDimensionsCreateForm implements Serializable {

    /**
     * 维度名称
     */
    @NotBlank
    @SafeHtml
    @Length(max = 255)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_DIMENSION_NAME)
    private String name;

    /**
     * 编码
     */
    @NotBlank
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_DIMENSION_CODE)
    private String code;

    /**
     * 资源类型
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.RES_TYPE_CODE)
    private String resTypeCode;

    /**
     * 采集规则id
     */
    @NotNull
    @Digits(integer = 22, fraction = 0)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_ID)
    private Long opsCollectRuleId;

    /**
     * 描述
     */
    @SafeHtml
    @Length(max = 1024)
    private String description;

    /**
     * 排序编码
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_DIMENSION_SORT_RANK)
    private Integer sortRank;


    /**
     * 采集规则维度条件参数
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_DIMENSION_CONDITION)
    private List<OpsCollectRuleDimensionsConditionCreateForm> opsCollectRuleDimensionsConditionCreateForms;
}
