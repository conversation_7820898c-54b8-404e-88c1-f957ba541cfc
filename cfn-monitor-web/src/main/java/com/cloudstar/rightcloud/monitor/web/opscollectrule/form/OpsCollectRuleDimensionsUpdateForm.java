package com.cloudstar.rightcloud.monitor.web.opscollectrule.form;

import com.cloudstar.common.validated.safe.SafeHtml;
import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

/**
 * 采集规则维度修改参数
 *
 * @author: wanglang
 * @date: 2023/7/20 20:02
 */
@Data
public class OpsCollectRuleDimensionsUpdateForm implements Serializable {

    /**
     * 维度名称
     */
    @SafeHtml
    @Length(max = 255)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_DIMENSION_NAME)
    private String name;

    /**
     * 编码
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_DIMENSION_CODE)
    private String code;

    /**
     * 资源类型
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.RES_TYPE_CODE)
    private String resTypeCode;

    /**
     * 采集规则id
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_ID)
    private Long opsCollectRuleId;

    /**
     * 描述
     */
    @SafeHtml
    @Length(max = 1024)
    private String description;

    /**
     * 排序编码
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_DIMENSION_SORT_RANK)
    private Integer sortRank;

    /**
     * 采集规则维度条件参数
     */
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.COLLECT_RULE_DIMENSION_CONDITION)
    private List<OpsCollectRuleDimensionsConditionUpdateForm> opsCollectRuleDimensionsConditionUpdateForms;


}
