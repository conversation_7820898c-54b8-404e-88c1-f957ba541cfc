package com.cloudstar.rightcloud.monitor.web.morprometheus.form;

import com.cloudstar.common.validated.safe.SafeHtml;
import com.cloudstar.rightcloud.common.annotation.I18nProperty;
import com.cloudstar.rightcloud.monitor.common.constant.msg.MonitorFieldKeyConstant;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * prometheus query 查询参数
 *
 * @author: wanglang
 * @date: 2023/8/18 14:18
 */
@Data
public class MorPrometheusQueryForm {
    /**
     * 查询指标数据
     */
    @NotBlank
    @SafeHtml
    @Length(max = 255)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.PROMETHEUS_QUERY)
    private String query;

    /**
     * 时间
     */
    @NotNull
    @SafeHtml
    @Length(max = 255)
    @I18nProperty(propertyKey = MonitorFieldKeyConstant.PROMETHEUS_TIME)
    private Long time;
}
